<template>
    <div>
        <div class="core-ui-custom-header">
            <div class="title">
                <breadcrumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <service-create-process :current="1" :serviceName="serviceName" />

        <div class="container u-p-30 u-m-t-30">
            <div class="u-font-18 u-text-center color-2 bold">
                请填写基本信息
            </div>
            <form-builder
                class="form"
                ref="formBuilder"
                label-position="right"
                label-width="140px"
                :onValueChange="onValueChange"
            ></form-builder>
            <div class="u-flex u-m-t-10 u-row-center u-p-l-80">
                <el-button type="primary" plain @click="cancel">
                    取消
                </el-button>
                <el-button type="primary" :loading="loading" @click="confirm">
                    确定
                </el-button>
            </div>
            <div class="u-m-t-20 u-text-center u-p-l-80" style="color: #bc904b">
                只有保存基本配置后才可进行服务对象、通知、任务工具的配置
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import {
        buildFormSections,
        FormController,
        FormItem,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Breadcrumb from "@/views/components/breadcrumb/index.vue"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
    } from "@/views/pages/single-page/components/tags-view"
    import { find } from "lodash"
    import { prefilter } from "uniplat-sdk"
    import { Component } from "vue-property-decorator"
    import ServiceCreateProcess from "../components/create-process.vue"
    import { TaskType } from "../service-detail/task"

    const taskFormItem: FormItem[] = [
        {
            type: FormType.Text,
            label: "所属项目",
            prop: "project_id",
            hide: true,
        },
        {
            type: FormType.Text,
            label: "所属项目",
            prop: "project",
            hide: true,
        },
        {
            type: FormType.Select,
            label: "服务内容",
            prop: "pc_id",
        },
        {
            prop: "tip1",
            type: FormType.Tip,
            option: {
                placeholder:
                    "不选服务内容项时，任务的服务记录数据仅会被统计到服务中，不能在某个服务项中呈现",
            },
            itemStyle: {
                color: "#BC904B",
            },
        },
        {
            label: "任务名称",
            type: FormType.Text,
            prop: "p_name",
        },
        {
            label: "任务开始时间",
            type: FormType.DatePicker,
            prop: "start_date",
        },
        {
            label: "任务结束时间",
            type: FormType.DatePicker,
            prop: "end_date",
        },
        {
            label: "任务工具",
            type: FormType.Select,
            prop: "task_type",
            required: true,
        },
        {
            prop: "tip2",
            type: FormType.Tip,
            option: {
                placeholder: "",
            },
            itemStyle: {
                color: "#BC904B",
            },
        },
        {
            label: "跟进人员所属部门",
            type: FormType.Cascader,
            prop: "xg_org_id",
            option: {
                elProps: { checkStrictly: true },
                filterable: true,
            },
            needListen: true,
        },
        {
            label: "跟进处理人",
            prop: "follow_ups",
            type: FormType.Select,
            required: true,
            option: {
                filterable: true,
                multiple: true,
            },
        },
        {
            prop: "tip1",
            type: FormType.Tip,
            option: {
                placeholder:
                    "服务记录的处理人，可对服务对象提供电话、一对一、面访等服务",
            },
            itemStyle: {
                color: "#BC904B",
            },
        },
    ]
    const contentTaskFormItem: FormItem[] = [
        {
            type: FormType.Text,
            label: "所属项目",
            prop: "project",
            hide: true,
        },
        {
            type: FormType.Text,
            label: "服务内容",
            prop: "project_content",
            noEdit: true,
            option: {
                disabled: true,
            },
        },
        {
            label: "任务名称",
            type: FormType.Text,
            prop: "p_name",
        },
        {
            label: "任务开始时间",
            type: FormType.DatePicker,
            prop: "start_date",
        },
        {
            label: "任务结束时间",
            type: FormType.DatePicker,
            prop: "end_date",
        },
        {
            label: "任务工具",
            type: FormType.Select,
            prop: "task_type",
            required: true,
        },
        {
            label: "跟进人员所属部门",
            type: FormType.Cascader,
            prop: "xg_org_id",
            option: {
                elProps: { checkStrictly: true },
                filterable: true,
            },
            needListen: true,
        },
        {
            label: "跟进处理人",
            prop: "follow_ups",
            type: FormType.Select,
            required: true,
            option: {
                filterable: true,
                multiple: true,
            },
        },
        {
            prop: "tip2",
            type: FormType.Tip,
            option: {
                placeholder: "",
            },
            itemStyle: {
                color: "#BC904B",
            },
        },
    ]

    const tipConfig = {
        [TaskType.数采工具]:
            "备注：该模版工具适合简单的数据采集通知，居民用户可进行数据采集和完善。如需要自定义，需要进行研发定制。",
        [TaskType.岗位推荐]:
            "备注：该模版工具适合简单的岗位通知，一次性可推荐多个岗位给居民用户。如需要自定义，需要进行研发定制。",
        [TaskType.内容通知]:
            "备注：该模版工具适合简单的内容通知居民用户，支持图文通知和html链接页面。如需要自定义，需要进行研发定制。",
        [TaskType.问卷工具]:
            "备注：该模版工具适合简单的问卷配置，居民用户可答卷。如需要自定义，需要进行研发定制。",
        [TaskType.政策推荐]:
            "备注：该模版工具适合简单的政策通知，一次性可推荐多个政策给居民用户。如需要自定义，需要进行研发定制。",
    }

    @Component({
        name: routesMap.groupService.serviceCreate,
        components: { FormBuilder, ServiceCreateProcess, Breadcrumb },
    })
    export default class Template extends FormController {
        private from = ""

        private get serviceName() {
            return this.$route.query.serviceName || ""
        }

        refreshConfig = {
            name: routesMap.groupService.serviceCreate,
            fun: this.init,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: this.title || "--",
                    to: {
                        name: routesMap.groupService.serviceCreate,
                    },
                },
            ]

            if (this.from) {
                d.unshift(...getCacheBreadcrumbsByRoutePath(this.from))
            }

            this.breadcrumbs = d
        }

        loading = false
        formLoading = false

        private get id() {
            return +(this.$route.query.id || 0)
        }

        private get title() {
            return this.id ? "修改任务" : "新增任务"
        }

        private get serviceContentId() {
            return +this.$route.query.serviceContentId
        }

        private get projectId() {
            return +this.$route.query.projectId
        }

        private getAction() {
            const prefilters: prefilter[] = []
            if (this.serviceContentId) {
                prefilters.push({
                    property: "pc_id",
                    value: this.serviceContentId,
                })
            }
            if (this.projectId) {
                prefilters.push({
                    property: "project_id",
                    value: this.projectId,
                })
            }
            return sdk.core
                .model("serve_task")
                .action(
                    this.id
                        ? "update"
                        : this.serviceContentId
                        ? "insert_for_content"
                        : "insert_for_project"
                )
                .updateInitialParams({
                    selected_list: this.id ? [{ id: this.id, v: 0 }] : [],
                    prefilters,
                })
        }

        mounted() {
            this.init()
        }

        cancel() {
            closeCurrentTap()
        }

        confirm() {
            this.validateForm((isValid) => {
                if (!isValid) {
                    return
                }
                this.loading = true
                const action = this.getAction()

                action.addInputs_parameter(this.getFormValues())
                action
                    .execute()
                    .then((r) => {
                        this.callRefresh(
                            routesMap.groupService.serviceManageDetail.task
                        )
                        this.$router.push({
                            name: routesMap.groupService.serviceManageDetail.task,
                            query: {
                                id: r.id,
                                from: this.from,
                            },
                        })
                    })
                    .finally(() => {
                        this.loading = false
                    })
            })
        }

        onValueChange(prop: string, v: any) {
            if (prop === "task_type") {
                this.setTip2(+v)
            }
        }

        setTip2(v: TaskType) {
            const form = find(this.formBuilder?.form, {
                prop: "tip2",
            })
            if (form) {
                form.option!.placeholder = tipConfig[v]
            }
        }

        private init() {
            this.from = this.$route.query.from as string
            this.formLoading = true
            return buildFormSections({
                action: this.getAction(),
                forms: this.projectId ? taskFormItem : contentTaskFormItem,
            }).then((r) => {
                this.buildFormFull(r)
                const taskType = this.formBuilder?.getValues().task_type
                if (taskType !== undefined || taskType !== "") {
                    this.setTip2(+taskType)
                }
                this.setBreadcrumbs()
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .container {
        background-color: #fff;
        .el-button + .el-button {
            margin-left: 40px;
        }
        .form {
            width: 460px;
            margin: 0 auto;
            margin-top: 30px;
        }
    }
</style>
