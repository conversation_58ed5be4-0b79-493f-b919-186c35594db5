<template>
    <container
        :smallTitle="smallTitle"
        :text="`${regionName}劳动力大数据分析应用平台`"
    >
        <div class="home-container flex-fill d-flex">
            <switcher />
            <div v-if="!isLiChuan" class="d-flex flex-column">
                <card-1 :dashboard="dashboardName" />
                <card-7
                    v-if="useNewModel"
                    :dashboard="dashboardName"
                    class="flex-fill"
                />
                <card-2
                    :dashboard="dashboardName"
                    class="flex-fill"
                    :style="styles"
                />
            </div>
            <div v-else class="d-flex flex-column">
                <card-1 :dashboard="dashboardName" />
                <CardV1 class="flex-fill"></CardV1>
                <CardV2 class="flex-fill"></CardV2>
            </div>
            <div class="flex-fill u-p-t-60 d-flex flex-column">
                <div class="d-flex justify-content-between headers">
                    <!-- <card-4 :dashboard="dashboardName" /> -->

                    <SingleCard
                        :dashboard="dashboardName"
                        label="已就业人数"
                        valueName="已就业人数"
                    ></SingleCard>
                    <SingleCard
                        :dashboard="dashboardName"
                        label="务农"
                        valueName="务农"
                    ></SingleCard>
                    <SingleCard
                        :dashboard="dashboardName"
                        label="入学"
                        valueName="入学"
                    ></SingleCard>
                    <SingleCard
                        :dashboard="dashboardName"
                        label="入伍"
                        valueName="入伍"
                    ></SingleCard>
                    <SingleCard
                        v-if="isHz"
                        :dashboard="dashboardName"
                        label="未就业人数"
                        valueName="未就业人数"
                    ></SingleCard>
                    <SingleCard
                        v-else
                        :dashboard="dashboardName"
                        label="未就业人数"
                        valueName="未就业人数"
                    ></SingleCard>
                </div>
                <div class="main d-flex flex-column flex-fill">
                    <map-container text="劳动力户籍地分布">
                        <common-map-turn-down
                            :showTurnDown="showTurnDown"
                            chatType="labor_area_count"
                            :dashboard="dashboardName"
                            :tooltipFormatter="tooltipFormatter"
                            :tooltipLoop="false"
                            @clickRegionCode="clickRegionCode"
                    /></map-container>
                </div>
            </div>
            <div v-if="!isLiChuan" class="d-flex flex-column">
                <card-6 v-if="!isHz" :dashboard="dashboardName" />
                <card-3
                    v-if="!isHz"
                    :dashboard="dashboardName"
                    class="flex-fill"
                />
                <card-32
                    v-if="isHz"
                    :dashboard="dashboardName"
                    class="flex-fill"
                />
                <card-9
                    v-if="isHz"
                    :dashboard="dashboardName"
                    class="flex-fill"
                />
            </div>
            <div v-else class="d-flex flex-column">
                <card-6 :dashboard="dashboardName" />
                <CardV3 :dashboard="dashboardName" class="flex-fill"></CardV3>
                <CardV4 :dashboard="dashboardName" class="flex-fill"></CardV4>
            </div>
        </div>
    </container>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { Component } from "vue-property-decorator"
    import Base from "../../common/base"
    import Container from "../../common/container-full.vue"
    import Switcher from "../../common/switcher.vue"
    import Card1 from "./劳动力人数.vue"
    import Card2 from "./年龄及性别分布.vue"
    import Card3 from "./文化程度分布.vue"
    import Card32 from "./文化程度分布-新.vue"
    import Card4 from "./已就业人数.vue"
    import Card5 from "./未就业人数.vue"
    import Card6 from "./退出劳动力市场人数.vue"
    import Card7 from "./特殊人群类型分布.vue"
    import Card8 from "./参保险种分布.vue"
    import Card9 from "./人员类别分布.vue"
    import SingleCard from "./single-card.vue"
    import CommonMapTurnDown from "../../common/map/index.vue"
    import MapContainer from "@/views/pages/big-screen/common/map-container.vue"
    import { ChartFormatter } from "../../model/chart"
    import { config, EnvProject } from "@/config"
    import { shiyanToolTipFormatter } from "."
    import CardV1 from "./components/年龄及性别分布.vue"
    import CardV2 from "./components/文化程度分布.vue"
    import CardV3 from "./components/技能特长分布.vue"
    import CardV4 from "./components/技能等级分布.vue"

    @Component({
        name: routesMap.bigScreen.report,
        components: {
            Container,
            Switcher,
            Card1,
            Card2,
            Card3,
            Card32,
            Card4,
            Card5,
            Card6,
            Card7,
            Card8,
            Card9,
            SingleCard,
            CommonMapTurnDown,
            MapContainer,
            CardV1,
            CardV2,
            CardV3,
            CardV4,
        },
    })
    export default class Index extends Base {
        private dashboardName = "dashboard_age_labor_summary"
        isHz = [EnvProject.黄州项目].includes(config.envProject)

        private isJz = [EnvProject.荆州项目].includes(config.envProject)
        private smallTitle = this.isJz ? "中国江汉平原农村人才市场" : ""

        private isLiChuan = [EnvProject.利川项目].includes(config.envProject)

        created() {
            const routerRegions = this.$route.query?.routerRegions
            if (routerRegions) {
                const t = JSON.parse(routerRegions as string)
                t.forEach((i: any) => {
                    this.pushRegionCode({
                        name: i.name,
                        code: i.code,
                    })
                })
            }
        }

        showTurnDown = !this.isHz
        private useNewModel =
            [EnvProject.咸丰项目].includes(config.envProject) ||
            localStorage.getItem("isXianFenEnv")

        private get styles() {
            return this.useNewModel
                ? {
                      height: "380px",
                  }
                : {}
        }

        private clickRegionCode(code: string) {
            if (code === "422802101209") {
                window.location.href =
                    "https://12333.beikesmart.com/bigScreen/#/bigscreen/preview?code=bigScreen_CPSeVNvZX6"
            }
        }

        tooltipFormatter(params: any) {
            if (config.envProject === EnvProject.十堰项目) {
                return shiyanToolTipFormatter(params, this)
            }
            return (
                `<p class="text-center" style="font-size:${this.px2rem(
                    14
                )};margin-bottom:${this.px2rem(10)}">${params.name}</p>` +
                this.buildTooltipItem(params.data?.key_value || 0)
            )
        }

        private buildTooltipItem(value: number) {
            return `<div class="text-center">${ChartFormatter.formatPersonNumber(
                value
            )} </div>`
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "../../common/style/style.less";

    .home-container {
        position: relative;
        padding: 90px 20px 20px 20px;
        background-repeat: no-repeat;
        background-image: url("./../../assets/bg2.png");
        background-position: center top;
        background-size: 100%;
    }

    .card {
        min-width: 385px;
        max-width: 385px;

        & + .card {
            margin-top: 20px;
        }
    }

    .headers {
        min-height: 140px;
        margin: 0 20px;
    }

    .switcher {
        position: fixed;
        left: 50%;
        top: 92px;
        transform: translateX(-50%);
        z-index: 2;
    }
</style>
