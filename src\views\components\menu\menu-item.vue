<template>
    <router-link
        v-if="link && !isExternal && !isOpenNewPage"
        :to="{
            name: menu.name,
        }"
    >
        <i v-if="menu.meta.icon" class="icon" :class="menu.meta.icon" />
        <img v-if="menu.meta.svgIcon" class="icon" :src="menu.meta.svgIcon" />
        <span>{{ menu.meta.title || "" }}</span>
    </router-link>
    <div v-else @click="clickMenu">
        <i v-if="menu.meta.icon" class="icon" :class="menu.meta.icon" />
        <img v-if="menu.meta.svgIcon" class="icon" :src="menu.meta.svgIcon" />
        <span>{{ menu.meta.title || "" }}</span>
    </div>
</template>

<script lang="ts">
    import { RouteConfig } from "@/router"
    import { isExternal } from "@/utils"
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ name: "MenuItem", components: {} })
    export default class MenuItem extends Vue {
        @Prop()
        private menu!: RouteConfig

        @Prop()
        private link!: boolean

        private get isExternal() {
            return isExternal(this.menu.path)
        }

        private get isOpenNewPage() {
            return this.menu.meta.newPage
        }

        private clickMenu() {
            if (this.isOpenNewPage) {
                const targetName = this.menu.meta?.targetName as string
                if (targetName) {
                    const path = this.$router.resolve({
                        name: targetName,
                    }).href

                    let suffix = ""
                    if (this.menu.meta?.targetQuery) {
                        const params = Object.keys(this.menu.meta.targetQuery || {})
                            .map((i) => {
                                return `${i}=${this.menu.meta.targetQuery[i]}`
                            })
                            .join("&")
                        suffix = `?${params}`
                    }

                    const tPath = path.replace(/\/+/g, "/")
                    window.open(tPath + suffix, "__bank")
                    return
                }
            }
            if (this.isExternal || this.isOpenNewPage) {
                window.open(
                    (this.menu.meta?.targetPath as string) || this.menu.path,
                    "_blank"
                )
            }
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .icon {
        margin-right: 8px;
        width: 15px;
        height: 15px;
        margin-top: -3px;
    }
</style>
