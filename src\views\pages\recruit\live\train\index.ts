import { sdk } from "@/service"
import { MessageBox } from "element-ui"

export enum LiveType {
    直播带岗 = 1,
    招聘会直播 = 2,
    综合直播 = 3,
}

export enum LiveStatus {
    即将开播 = 1,
    正在直播 = 2,
    直播结束 = 3,
}

export enum LiveAuditStatus {
    草稿 = 1,
    待审核 = 2,
    审核通过 = 3,
    审核不通过 = 4,
}

export enum LiveShelfStatus {
    未发布 = 1,
    已发布 = 2,
}

export const predict = {
    train_audit_status: "label",
    train_shelf_status: "label",
    train_title: "",
    train_type: "label",
    train_start_time: "",
    train_end_time: "",
    train_cover: "",
    train_profiles: "",
    train_blurb: "",
    job_fair_id: "job_fair#id",
    job_fair_title: "job_fair#title",
    train_propel_channel: "",
    train_name: "",
    train_major: "",
    train_date: "",
    train_agency: "",
    train_days: "",
    train_students: "",
    train_cost: "",
    train_content: "",
    train_manner: "",
    train_founder: "",
    train_certificate: "",
    train_contact: "",
    train_phone: "",
    train_QQ: "",
    train_wechat: "",
    province: "province#region_name",
    city: "city#region_name",
    area: "area#region_name",
    address_detail: "",
}

/** 上下架 */
export function toggleShell(row: any) {
    return new Promise((resolve) => {
        const action =
            row.train_shelf_status === LiveShelfStatus.未发布
                ? "onShelf"
                : "unShelf"
        MessageBox(
            `请确认是否${
                row.train_shelf_status === LiveShelfStatus.未发布
                    ? "上架"
                    : "下架"
            }`,
            "提示"
        ).then(() => {
            return sdk.core
                .model("training_record")
                .action(action)
                .updateInitialParams({
                    selected_list: [{ v: 0, id: row.id }],
                })
                .execute()
                .then(() => {
                    resolve(true)
                })
        })
    })
}

/** 提审 */
export function toAudit(row: any) {
    return new Promise((resolve) => {
        MessageBox(`请确认是否提交审核`, "提示").then(() => {
            return sdk.core
                .model("training_record")
                .action("submit")
                .updateInitialParams({ selected_list: [{ v: 0, id: row.id }] })
                .execute()
                .then(() => {
                    resolve(true)
                })
        })
    })
}
