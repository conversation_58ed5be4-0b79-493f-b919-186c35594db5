const path = require("path")
const { spawn } = require("cross-spawn")
const { AutoComplete } = require("enquirer")
const { configEnv } = require("./config.js")
const _ = require("lodash")
require("dotenv").config({ path: ".env.local" })
const yargs = require("yargs/yargs")
const { hideBin } = require("yargs/helpers")
const cliArgv = yargs(hideBin(process.argv)).argv
console.log(cliArgv)
function createProcess(args) {
    return new Promise((resolve, reject) => {
        console.log(args)
        const childProcess = spawn("cross-env", args, {
            stdio: "inherit",
        })

        childProcess.on("error", (error) => {
            console.error(`执行命令时出错: ${error}`)
        })

        // 监听子进程的关闭事件
        childProcess.on("close", (code) => {
            if (+code === 0) {
                resolve()
            } else {
                reject()
            }
        })

        childProcess.on("exit", (code) => {
            if (code !== 0) {
                reject()
                console.error(`命令执行失败，退出码: ${code}`)
            }
        })
    })
}

function buildCurProjectModulesPath() {
    const projectRoot = path.resolve(__dirname, ".")
    const binPath = path.join(projectRoot, "node_modules", ".bin")

    let currentPath = process.env.PATH || ""
    // console.log("currentPath", currentPath)

    if (!currentPath.includes(binPath)) {
        currentPath = `${binPath}${path.delimiter}${currentPath}`

        process.env.PATH = currentPath
        // console.log("process.env.PATH", process.env.PATH)
    }
}

async function chooseProject() {
    const p = cliArgv.p || process.env.ORG_CHOOSE_PROJECT
    if (p) {
        const val = configEnv.find((i) => i.name === p)
        if (!val) {
            throw Error("no ORG_CHOOSE_PROJECT")
        }
        return Promise.resolve(val)
    }
    const prompt = new AutoComplete({
        name: "环境",
        limit: false,
        message: "请选择一个操作：",
        choices: configEnv.map(
            (i, idx) => `${idx + 1}.${i.name}`
        ),
    })
    return prompt
        .run()
        .then((answer) => {
            const idx = +answer.split(".")[0]
            const val = configEnv[idx - 1]
            console.log("你选择了：", val.name)
            return val
        })
        .catch(console.error)
}

async function chooseApp(config) {
    const a = cliArgv.a || process.env.ORG_CHOOSE_APP
    if (a) {
        const val = config.app.find((i) => i.name === a)
        if (!val) {
            throw Error("no ORG_CHOOSE_APP")
        }
        return Promise.resolve(val)
    }
    const prompt = new AutoComplete({
        name: "环境",
        limit: false,
        message: "请选择一个操作：",
        choices: config.app?.map((i, idx) => `${idx + 1}.${i.name}${i.memo || ""}`),
    })
    return await prompt
        .run()
        .then((answer) => {
            const idx = +answer.split(".")[0]
            const val = config.app[idx - 1]
            console.log("你选择了：", val.name)
            return val
        })
        .catch(console.error)
}

async function chooseProjectAndApp(NODE_ENV) {
    const project = await chooseProject()
    const app = await chooseApp(project)
    const deployEnv = cliArgv.e || "test"
    if (!NODE_ENV) {
        NODE_ENV = deployEnv === "test" ? "development" : "production"
    }
    const projectEnvs = _.get(project, `env[${deployEnv}]`)
    const appEnvs = _.get(app, `env`)
    const deploy = _.get(app, `deploy[${cliArgv.d || deployEnv}]`)
    const deployEnvs = _.get(deploy, `env`)
    const CUSTOMER_OUTPUT_DIR = `${project.name}-${app.name}-${deployEnv}`
    const envs = {
        ...projectEnvs,
        ...appEnvs,
        ...deployEnvs,
        VUE_APP_ENV: deployEnv,
        VUE_APP_ENV_PROJECT: project.name,
        VUE_APP_ENV_APPLICATION: app.name,
        NODE_ENV,
        CUSTOMER_FILE_DIR: CUSTOMER_OUTPUT_DIR,
        CUSTOMER_OUTPUT_DIR: `output/${CUSTOMER_OUTPUT_DIR}`,
    }
    const args = []
    _.forEach(envs, (value, key) => {
        args.push(`${key}=${value}`)
    })
    return { project, app, envs, args, deploy, cliArgv }
}

module.exports = {
    createProcess,
    buildCurProjectModulesPath,
    chooseProjectAndApp,
}
