<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="详情"
        width="900px"
    >
        <div v-if="detail">
            <div class="u-p-x-20">
                <div class="content u-flex-none">
                    <div class="u-font-18 bold">申报详情</div>
                    <div class="u-p-t-10 u-p-x-20 u-p-b-20 info">
                        <div
                            class="u-flex-col u-col-top form-items"
                            v-if="detail"
                        >
                            <div class="u-flex-1">
                                <div class="d-flex align-items-start">
                                    <div class="label">政策名称</div>
                                    <div class="u-flex u-flex-none value">
                                        {{ detail["form.policy_name"] || "-" }}
                                    </div>
                                </div>
                            </div>
                            <div class="u-flex-1">
                                <div class="d-flex align-items-start">
                                    <div class="label">申报人</div>
                                    <div class="u-flex u-flex-none value">
                                        {{ detail.contact_person || "-" }}
                                    </div>
                                </div>
                            </div>
                            <div class="u-flex-1">
                                <div class="d-flex align-items-start">
                                    <div class="label">申报时间</div>
                                    <div class="u-flex u-flex-none value">
                                        {{
                                            formatTime(detail.create_time) ||
                                            "-"
                                        }}
                                    </div>
                                </div>
                            </div>
                            <div class="u-flex-1">
                                <div class="d-flex align-items-start">
                                    <div class="label">状态</div>
                                    <div class="u-flex u-flex-none value">
                                        {{ detail.status_label || "-" }}
                                    </div>
                                </div>
                            </div>
                            <div class="u-flex-1">
                                <div class="d-flex align-items-start">
                                    <div class="label">申报说明</div>
                                    <div class="u-flex u-flex-none value">
                                        {{ detail.apply_memo || "-" }}
                                    </div>
                                </div>
                            </div>
                            <div class="u-flex-1">
                                <div class="d-flex align-items-start">
                                    <div class="label">附件模板</div>
                                    <div class="u-flex u-flex-none value">
                                        <div
                                            v-for="(i, index) in attachments"
                                            :key="index"
                                        >
                                            <el-button
                                                type="text"
                                                class="btn-file"
                                                @click="downLoadFile(i, index)"
                                            >
                                                附件{{ index + 1 }}
                                            </el-button>
                                        </div>

                                        <div
                                            v-if="
                                                !attachments ||
                                                !attachments.length
                                            "
                                        >
                                            -
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="u-p-x-10"
                :key="detail.id"
                v-if="detail.form_questionnaire_id && detail.status === 0"
            >
                <div class="u-font-18 u-p-x-10 bold u-m-b-20">表单详情</div>
                <div class="preview">
                    <ExtraForm
                        ref="extraForm"
                        :detail="formDetail"
                        :profile="{}"
                        :companyData="{}"
                        :applyId="detail.id"
                        :isPreview="true"
                        :labelStyle="{
                            minWidth: '100px',
                            lineHeight: '24px',
                            textAlign: 'right',
                            marginRight: '10px',
                        }"
                    ></ExtraForm>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { downLoadFile, formatTime } from "@/utils/tools"
    import { Component, Prop } from "vue-property-decorator"
    import ExtraForm from "./extra-form.vue"

    @Component({ components: { FormBuilder, ExtraForm } })
    export default class ApplyPop extends DialogController {
        @Prop({ default: () => null })
        private detail!: any

        private get formDetail() {
            if (!this.detail) return {}
            return {
                id: this.detail.form_id,
                questionnaire_id: this.detail.form_questionnaire_id,
            }
        }

        protected onOpen() {
            this.init()
        }

        onClosing() {}

        private get attachments() {
            return (
                (this.detail &&
                    this.detail.attachments &&
                    this.detail.attachments.split(",")) ||
                []
            )
        }

        private downLoadFile(file: string, index: number) {
            const arr = file.split(".")
            const name = arr[arr.length - 1]
            const idx = index + 1
            downLoadFile(sdk.buildImage(file || ""), `附件${idx}.${name}`)
        }

        private formatTime(time: string) {
            if (!time) {
                return ""
            }
            return formatTime.seconds(time)
        }

        private init() {
            console.log("detail", JSON.parse(JSON.stringify(this.detail)))
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .btn {
        width: 100px;
        height: 36px;
    }

    .label {
        word-break: keep-all;
        min-width: 100px;
        padding-right: 10px;
        text-align: right;
        line-height: 24px;
    }

    .info {
        line-height: 24px;
    }

    .form-items {
        gap: 15px;
    }

    .btn-file {
        text-decoration: underline;
    }

    .value {
        flex: 1;
        word-break: break-all;
        flex-wrap: wrap;
        line-height: 24px;
    }
    .preview {
        height: 300px;
        overflow: scroll;
    }
    /deep/ .detail-row {
        .value {
            line-height: 24px !important;
        }
    }
</style>
