import { config, EnvProject } from "@/config"
import { buildSelectSource, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { RoleController } from "@/installer/role"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { find, flatMap, get } from "lodash"
import { ListTypes, TagManagerTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    {
        label: "企业名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "信用代码",
        type: FormType.Text,
        prop: "company_code",
    },
    {
        label: "法人",
        type: FormType.Text,
        prop: "legal_person",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "联系人",
        type: FormType.Text,
        prop: "contact_person",
        option: {
            placeholder: "请输入联系人姓名/电话",
        },
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "注册地",
        type: FormType.Cascader,
        prop: "province_code",
        option: {
            elProps: { checkStrictly: true },
            filterable: true,
        },
    },
    {
        label: "所属行业",
        type: FormType.MultipleCascader,
        prop: "industory_catalog",
        option: {
            elProps: { checkStrictly: true },
        },
        keyValueFilter: {
            match: ListTypes.filterMatchType.start,
        },
    },
    {
        label: "企业规模",
        type: FormType.Select,
        prop: "company_size",
    },
    {
        label: "是否登录",
        type: FormType.Select,
        prop: "is_register",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "已登录",
            },
            {
                key: "0",
                value: "未登录",
            },
        ]),
    },
    {
        label: "是否入驻",
        type: FormType.Select,
        prop: "is_create",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "已入驻",
            },
            {
                key: "0",
                value: "未入驻",
            },
        ]),
    },
    {
        label: "入驻方式",
        prop: "create_type_rewrite",
        type: FormType.Select,
        option: {
            multiple: true,
        },
    },
    {
        label: "入驻时间",
        prop: "settled_time",
        type: FormType.DatePicker,
        option: {
            type: "datetimerange",
        },
        col: {
            span: 28,
        },
    },
    {
        label: "营业状态",
        prop: "oper_status",
        type: FormType.Select,
        col: {
            span: 28,
        },
    },
    {
        label: "是否重点企业",
        prop: "is_focus",
        type: FormType.Select,
        col: {
            span: 28,
        },
    },
    {
        label: "企业标签",
        type: FormType.Cascader,
        prop: "tags",
        useTag: "*",
        option: {
            filterable: true,
            collapseTags: true,
            elProps: { multiple: true },
        },
    },
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_province_region_code",
        hide: config.envProject === EnvProject.黄州项目,
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
]

export const rowPredict = {
    agent_name: "",
    company_code: "",
    legal_person: "",
    legal_person_hide: "",
    region_name: "area#region_name",
    city: "city#region_name",
    province: "province#region_name",
    area: "area#region_name",
    address_detail: "",
    company_size: "label",
    contact_person: "",
    contact_mobile: "contact_mobile_hide",
    position_count: "label",
    industory_catalog: "label",
    create_type: "label",
    is_register: "label",
    remark: "",
    is_create: "label",
    _access_key: "",
    industory_catalog_display: "",
    oper_status: "enterprise#oper_status",
    is_focus: "label",
    address: "",
    tags: "tags",
    dealers: "label",
    found_time: "enterprise#found_time_label",
    reg_origin: "enterprise_info#reg_origin",
    pay_amount: "enterprise_info#pay_amount",
    reg_authority: "tg_enterprise#reg_authority",
    object_type: "tg_enterprise.object_type",
    is_black: false,
    mgt_province_region_name: "mgt_province#region_name",
    mgt_city_region_name: "mgt_city#region_name",
    mgt_area_region_name: "mgt_area#region_name",

    black_creator_ral_name: "black#creator#real_name",
    black_memo: "black#memo",
    black_create_time: "black#create_time_label",
    settled_time: "label",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("xg_agent").list("company_back_list"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        handleFilterData: (params) => {
            const industory_catalog = (
                params.industory_catalog || []
            ).toString()
            return { ...params, industory_catalog }
        },
    }
}

const enum CreateType {
    后台创建 = 1,
    服务商申请 = 2,
    探迹同步 = 3,
}

const enum IsFocus {
    否 = 0,
    是 = 1,
}

export interface Row {
    /** 企业名称 */
    agent_name: string
    /** 统—社会信用代码 */
    company_code: string
    /** 法人 */
    legal_person: string
    /** 区域名称 */
    region_name: string
    city: string
    province: string
    /** 企业所在地详细地址 */
    address_detail: string
    /** 企业规模 */
    company_size: string
    /** 联系人姓名 */
    contact_person: string
    /** 联系人姓名电话 */
    contact_mobile: string
    /** 岗位信息 */
    position_count: string
    /** 所属行业 */
    industory_catalog: string
    /** 企业来源 */
    create_type: CreateType
    /** 企业来源[文本] */
    create_type_label: string
    /** 是否注册 */
    is_register: number
    id: number
    v: number
    remark: string
    _access_key: string
    is_focus: IsFocus
    is_focus_label: string
    tags: { [key: string]: TagManagerTypes.TagInfo[] }
}

export const columns: TableColumn[] = [
    {
        label: "企业名称\n统一社会信用代码",
        prop: "agent_name",
        width: "220px",
        showOverflowTip: true,
        fixed: "left",
        render: (h, row) => {
            return h("div", [
                h(
                    "div",
                    {
                        class: "u-line-1",
                    },
                    row.agent_name
                ),
                h(
                    "div",
                    {
                        class: "u-line-1",
                    },
                    row.company_code
                ),
            ])
        },
    },
    {
        label: "法人",
        width: "100px",
        prop: "legal_person_hide",
        showOverflowTip: true,
        fixed: "left",
    },
    {
        label: "标签",
        prop: "tags",
        showOverflowTip: true,
        fixed: "left",
        minWidth: "200",
        render: (h, row: Row) => {
            const tags = flatMap(row.tags, (item) =>
                flatMap(item, (i) => i.tagName)
            ).join("，")
            return h(
                "div",
                {
                    class: "pre-line u-line-3",
                    directives: [
                        {
                            name: "customerPopover",
                            value: { content: tags },
                        },
                    ],
                    key: row._access_key,
                },
                tags || "-"
            )
        },
    },
    {
        label: "企业所在地",
        prop: "region_name",
        showOverflowTip: true,
        width: "90px",
        fixed: "left",
        render: (h, row: Row) => {
            const d = [row.province, row.city, row.region_name].filter(Boolean)
            return h(
                "div",
                d.map((i) => h("div", i))
            )
        },
    },
    {
        label: "是否重点企业",
        minWidth: "70",
        prop: "is_focus_label",
        showOverflowTip: true,
        fixed: "left",
    },
    {
        label: "管理区域",
        minWidth: "90px",
        prop: "mgt_region",
        showOverflowTip: true,
        fixed: "left",
        hide: config.envProject === EnvProject.黄州项目,
        render: (h, row: any) => {
            const d = [
                row.mgt_province_region_name,
                row.mgt_city_region_name,
                row.mgt_area_region_name,
            ].filter(Boolean)
            return h(
                "div",
                d.map((i) => h("div", i))
            )
        },
    },

    {
        label: "注册信息",
        prop: "注册信息",
        children: [
            {
                label: "是否登录",
                prop: "is_register_label",
                minWidth: "120",
                showOverflowTip: true,
            },
            {
                label: "是否注册",
                minWidth: "120",
                prop: "is_create_label",
                showOverflowTip: true,
            },
            {
                label: "入驻时间",
                minWidth: "140",
                prop: "settled_time_label",
                showOverflowTip: true,
                formatter: (row) => {
                    return formatTime.day(row.settled_time)
                },
            },
        ],
    },
    {
        label: "工商信息",
        prop: "工商信息",
        children: [
            {
                label: "联系人",
                prop: "contact_person",
                showOverflowTip: true,
                width: "100px",
            },
            {
                label: "营业状态",
                minWidth: "100",
                prop: "oper_status_label",
                showOverflowTip: true,
                fixed: "left",
            },
            {
                label: "企业规模",
                prop: "company_size_label",
                showOverflowTip: true,
                width: "120px",
            },
            {
                label: "所属行业",
                minWidth: "140",
                prop: "industory_catalog_display",
                showOverflowTip: true,
            },
            {
                label: "成立日期",
                minWidth: "140",
                prop: "found_time_label",
                showOverflowTip: true,
            },
            {
                label: "注册资本",
                minWidth: "120",
                prop: "reg_origin",
                showOverflowTip: true,
            },
            {
                label: "实缴资本",
                minWidth: "120",
                prop: "pay_amount",
                showOverflowTip: true,
            },
            {
                label: "登记机关",
                minWidth: "120",
                prop: "reg_authority",
                showOverflowTip: true,
            },

            {
                label: "企业详细地址",
                prop: "address",
                width: "200px",
                showOverflowTip: true,
            },

            {
                label: "入驻来源",
                minWidth: "130",
                prop: "create_type_label",
                showOverflowTip: true,
            },

            {
                label: "岗位信息",
                minWidth: "120",
                prop: "position_count_label",
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "拉黑信息",
        prop: "拉黑信息",
        children: [
            {
                label: "是否被拉黑",
                prop: "is_black",
                minWidth: "120",
                formatter: (row) => (row.is_black ? "是" : "否"),
            },
            {
                label: "拉黑操作人",
                minWidth: "120",
                prop: "black_creator_ral_name",
            },
            {
                label: "拉黑时间",
                minWidth: "140",
                prop: "black_create_time",
            },
        ],
    },

    {
        label: "操作",
        prop: "h",
        width: "100",
        fixed: "right",
        showOverflowTip: true,
    },
]

export function hasRole2ChangeArea(id = "") {
    if (!RoleController.has("model.xg_agent.action.update_mgt_region_code")) {
        return Promise.resolve()
    }
    return sdk.core
        .model("xg_agent")
        .action("update_mgt_region_code")
        .updateInitialParams({ selected_list: [{ v: 0, id }] })
        .query()
        .then((r) => {
            const inputs_parameters = get(
                r,
                "parameters.inputs_parameters",
                []
            ) as any
            return find(inputs_parameters, {
                property: "is_show",
            })?.default_value
        })
}
