import { TableColumn, TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"

export interface Row {
    id: number
    title: string
    type: string
    theme: string
    theme_type: string
    status: string
    start_time: string
    end_time: string
    sign_qr: string
    site_id: string
    place_detail: string
    place_lat: string
    place_lng: string
    contact_email: string
    contact_mobile: string
    contact_person: string
    co_organizer: string
    audit_time: string
    audit_status: string
    audit_memo: string
    apply_time: string
    hosted_by: string
    organizer: string
    region_name: string
    update_time: string
    contact_office_mobile: string
    image_mobile: string
    image_pc: string
    apply_agent_count: string
    place: string
    description: string
    apply_status: ApplyStatus
    apply_id: string
    [key: string]: any
    /** 职能 */
    function_categories: string

    /** 岗位职责 */
    function_detail: string

    /** 工作性质 */
    work_type: string

    /** 语言种类 */
    language: Language

    /** 语言种类[文本] */
    language_label: string

    /** 外语水平 */
    language_level: LanguageLevel

    /** 外语水平[文本] */
    language_level_label: string

    /** 招聘过期时间 */
    expired_date: string
}
export const predict = {
    title: "",
    type: "label",
    theme: "label",
    theme_type: "label",
    status: "label",
    start_time: "label",
    end_time: "label",
    sign_qr: "label",
    site_id: "label",
    place_detail: "",
    place_lat: "",
    place_lng: "",
    contact_email: "label",
    contact_mobile: "label",
    contact_person: "label",
    co_organizer: "label",
    audit_time: "label",
    audit_status: "label",
    audit_memo: "label",
    apply_time: "label",
    hosted_by: "label",
    organizer: "label",
    region_name: "last_place#region_name",
    update_time: "label",
    contact_office_mobile: "label",
    description: "",
    image_mobile: "label",
    image_pc: "label",
    apply_agent_count: "",
    place: "last_place#alias",
    cal_address_detail: "",
    show_status: "label",
    company_show_status: "label",
    apply_status: "label",
    apply_id: "",
    function_categories: "",
    function_detail: "label",
    work_type: "label",
    language: "label",
    language_level: "label",
    expired_date: "label",
}

export enum ApplyStatus {
    未报名 = -1,
    "已报名,审核中" = 0,
    已报名 = 1,
    "已报名,审核不通过" = 2,
}

export enum CompanyShowStatus {
    火热报名中 = 0,
    "报名截止，待开始" = 1,
    招聘会进行中 = 2,
    已结束 = 3,
}
export function tableConfig(isRunning = true): TableConfig {
    return {
        model: sdk.core.model("job_fair").list("for_agent"),
        filter: [],
        defaultPageSize: 4,
        predict: predict,
        preFilter: isRunning
            ? undefined
            : {
                  is_end: 1,
              },
    }
}

export const columns: TableColumn[] = [
    { label: "使用记录名称", prop: "id", align: "left", showOverflowTip: true },
]

export function computeTime(t1: string, t2: string) {
    const t = [new Date(t1), new Date(t2)]
    if (t[0].getDate() === t[1].getDate()) {
        return `${formatTime.default(t1)} - ${
            t[1].getHours() > 10 ? t[1].getHours() : `0${t[1].getHours()}`
        }:${
            t[1].getMinutes() > 10 ? t[1].getMinutes() : `0${t[1].getMinutes()}`
        }`
    }
    console.log(`${formatTime.default(t1)} - ${formatTime.default(t2)}`)
    return `${formatTime.default(t1)} - ${formatTime.default(t2)}`
}

const enum Language {
    英语 = 1,
    日语 = 2,
    俄语 = 3,
    法语 = 4,
    意大利语 = 5,
    德语 = 6,
    韩语 = 7,
    蒙古语 = 8,
    葡萄牙语 = 9,
    西班牙语 = 10,
    巴士克语 = 11,
    冰岛语 = 12,
    丹麦语 = 13,
    法罗语 = 14,
    芬兰语 = 15,
    荷兰语 = 16,
    加泰隆语 = 17,
    马来语 = 18,
    南非语 = 19,
    挪威语 = 20,
    瑞典语 = 21,
    斯瓦西里语 = 22,
    印度尼西亚语 = 23,
    汉语 = 24,
    其他语言 = 90,
}

const enum LanguageLevel {
    一般 = 1,
    熟练 = 2,
    精通 = 3,
}
