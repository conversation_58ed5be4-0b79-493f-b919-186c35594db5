import { config, EnvProject } from "@/config"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const train = [
    {
        path: "/train",
        name: routesMap.home.train,
        meta: {
            title: "培训管理",
            homeIcon: "/img/jz/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/laborInfoBase.svg"),
        },
        component: layout,
        children: [
            {
                path: "aspiration-collect",
                name: routesMap.labourManage.aspirationCollect,
                meta: {
                    title: "培训意愿收集",
                    role: [EnvProject.荆州项目].includes(config.envProject)
                        ? "/tablelist/user_profile_train_willingness/back_list"
                        : "/tablelist/user_profile_start_job/list_start_intent",
                    hidden: ![
                        EnvProject.荆州项目,
                        EnvProject.孝感项目,
                        EnvProject.掇刀项目,
                    ].includes(config.envProject),
                },
                component: () =>
                    ![EnvProject.掇刀项目, EnvProject.孝感项目].includes(
                        config.envProject
                    )
                        ? import(
                              "@/views/pages/labour-manage/aspiration-collect/index.vue"
                          )
                        : import(
                              "@/views/pages/labour-manage/aspiration-collect/tab-index.vue"
                          ),
            },
            {
                path: "index",
                name: routesMap.credit.index,
                meta: {
                    title: "培训机构管理",
                    role: "/tablelist/jz_training_org_agent",
                    hidden: ![EnvProject.荆州项目].includes(config.envProject),
                },
                component: () => import("@/views/pages/credit/index/index.vue"),
            },
            {
                path: "detail",
                name: routesMap.credit.detail,
                meta: {
                    title: "培训机构管理详情",
                    parentMenuName: routesMap.credit.index,
                    hidden: true,
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/credit/index/detail.vue"),
            },
            {
                path: "accredit",
                name: routesMap.credit.accredit,
                meta: {
                    title: "信用账户授信管理",
                    role: "/tablelist/jz_training_class_info",
                    hidden: ![EnvProject.荆州项目].includes(config.envProject),
                },
                component: () =>
                    import("@/views/pages/credit/accredit/index.vue"),
            },
            {
                path: "https://px.hbggzp.cn/pxdp/index.html?AAB299=421000",
                name: routesMap.credit.bigScreen,
                meta: {
                    title: "培训实时监测",
                    hidden: config.envProject !== EnvProject.荆州项目,
                    role: "/redirect/xg_project/px_statistic",
                },
            },
            {
                path: "accredit-detail",
                name: routesMap.credit.accreditDetail,
                meta: {
                    title: "信用账户班级详情",
                    parentMenuName: routesMap.credit.accredit,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/credit/accredit/detail.vue"),
            },
            {
                path: "train-index",
                name: routesMap.recruit.live.train,
                meta: {
                    title: "培训管理",
                    role: "/tablelist/training_record/list_operator",
                },
                component: () =>
                    import("@/views/pages/recruit/live/train/index.vue"),
            },
            {
                path: "train-add",
                name: routesMap.recruit.live.trainAdd,
                meta: {
                    title: "添加培训",
                    hidden: true,
                    parentMenuName: routesMap.recruit.live.train,
                },
                component: () =>
                    import("@/views/pages/recruit/live/train/add.vue"),
            },
            {
                path: "train-detail",
                name: routesMap.recruit.live.trainDetail,
                meta: {
                    title: "培训管理详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.live.train,
                },
                component: () =>
                    import("@/views/pages/recruit/live/train/detail.vue"),
            },
        ],
    },
]
