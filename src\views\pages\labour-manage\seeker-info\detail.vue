<template>
    <div class="core-ui-table-container" :key="refreshQueryParams" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div>
                <el-button
                    v-role="'model.serve_task.action.create_from_profile'"
                    type="primary"
                    @click="batchAddService"
                >
                    新建帮扶
                </el-button>
            </div>
        </div>
        <div>
            <detail-view
                :row="row"
                ref="detailView"
                @refresh="refresh"
            ></detail-view>
        </div>
        <div class="u-m-t-24 common-table">
            <div class="core-ui-table-container">
                <el-tabs v-model="curTab">
                    <el-tab-pane label="就业信息" name="1">
                        <recruit-view
                            @refresh="refresh"
                            :style="createStyle([98, 112, 98])"
                            :openid="row.id_card_openid"
                            :detail="row"
                        >
                        </recruit-view>
                    </el-tab-pane>
                    <el-tab-pane lazy label="培训信息" name="2">
                        <edu-view
                            @refresh="refresh"
                            :style="createStyle([140, 154, 154])"
                            :openid="row.id_card_openid"
                            :detail="row"
                        >
                        </edu-view>
                    </el-tab-pane>
                    <el-tab-pane lazy label="服务记录" name="3">
                        <record-view
                            :style="createStyle([98, 112, 98])"
                            :openid="row.id_card_openid"
                            :detail="row"
                        >
                        </record-view
                    ></el-tab-pane>
                    <el-tab-pane lazy label="档案修改记录" name="4">
                        <edit-history
                            :detail="row"
                            ref="historyBox"
                            :style="createStyle([98, 112, 98])"
                        >
                        </edit-history
                    ></el-tab-pane>
                    <el-tab-pane
                        lazy
                        label="参加招聘会记录"
                        name="5"
                        v-if="!isEZ"
                    >
                        <recruit-record
                            :style="createStyle([98, 112, 98])"
                            :uid="row.uid"
                            :detail="row"
                        >
                        </recruit-record
                    ></el-tab-pane>
                    <el-tab-pane
                        lazy
                        label="数据仓库详情"
                        name="6"
                        v-if="isXg || isEZ"
                    >
                        <data-storage
                            :style="createStyle([98, 112, 98])"
                            :uid="row.uid"
                            :detail="row"
                        >
                        </data-storage
                    ></el-tab-pane>
                    <el-tab-pane
                        lazy
                        label="孝创贷"
                        name="7"
                        v-if="isXg"
                        v-role="'/tablelist/t_loan_person_info/loan_list'"
                    >
                        <xcd-view :row="row"> </xcd-view
                    ></el-tab-pane>
                    <el-tab-pane
                        v-if="row.unionIds && showSubMessage && !isEZ"
                        lazy
                        label="微信小程序订阅消息"
                        name="8"
                    >
                        <WeiChatSubScribe
                            :style="createStyle([98, 112, 98])"
                            :unionIds="row.unionIds"
                        />
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { TableConfig } from "@/core-ui/component/table"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { Component, Ref, Vue } from "vue-property-decorator"
    import { Row, seekerInfoPredict } from "."
    import { recruitInfo, recruitListConfig } from "./components/detail"
    import DetailView from "./components/detail-view.vue"
    import EduView from "./components/edu-view.vue"
    import RecordView from "./components/record-view.vue"
    import EditHistory from "./components/edit-history.vue"
    import RecruitRecord from "./components/recruit-record.vue"
    import DataStorage from "./components/data-storage.vue"
    import RecruitView from "./components/recruit-view.vue"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import XcdView from "./components/xcd-view.vue"
    import { config, EnvProject } from "@/config"
    import WeiChatSubScribe from "./components/weichat-subscribe.vue"
    import { addServiceObj } from "../../group-service/school"

    @Component({
        name: routesMap.labourManage.seekerDetail,
        components: {
            DetailView,
            RecruitView,
            EduView,
            RecordView,
            EditHistory,
            RecruitRecord,
            DataStorage,
            XcdView,
            WeiChatSubScribe,
        },
    })
    export default class SeekerDetail extends Vue {
        @Ref("historyBox")
        private historyBox!: any

        private isXg = config.envProject === EnvProject.孝感项目
        private isEZ = config.envProject === EnvProject.鄂州项目
        private isDev = process.env.VUE_APP_ENV === "test"

        private recruitInfo = recruitInfo
        private recruitListConfig: TableConfig | null = null
        private row: Row | null = null
        id = ""
        private curTab = "1"

        private cacheId = ""

        showSubMessage = [EnvProject.孝感项目, EnvProject.荆州项目].includes(
            config.envProject
        )

        refreshConfig = {
            fun: () => {
                this.init(true)
            },
            name: "refreshSeekerInfo",
        }

        private refresh() {
            this.historyBox && this.historyBox.buildTableConfig(true)
            this.init()
            this.callRefresh(routesMap.labourManage.seekerInfo)
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `居民信息管理`,
                    to: {
                        name: routesMap.labourManage.seekerInfo,
                    },
                },
            ]
            if (this.from) {
                const routes: Record<string, string> = {
                    "person-detail": routesMap.recruit.personDetail,
                    "agents-detail": routesMap.recruit.agentsDetail,
                }
                d = [
                    ...getCacheBreadcrumbsByRoutePath(
                        routes[this.from] || this.from
                    ),
                ]
            }
            d = [
                ...d,
                {
                    label: "档案详情",
                    to: {
                        name: routesMap.labourManage.seekerDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.labourManage.seekerDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string | undefined
        }

        private get tab() {
            console.log("tab", this.$route.query.tab)
            return this.$route.query.tab as string | undefined
        }

        mounted() {
            this.init()
        }

        private init(force = false) {
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            if (this.cacheId !== this.id || force) {
                this.row = null
            }
            pageLoading(() => {
                return sdk.core
                    .model("user_profile_basic")
                    .detail(this.id, "second_version_detail")
                    .query()
                    .then((r) => {
                        this.cacheId = this.id
                        this.row = {
                            ...sdk.buildRow<Row>(r.row, seekerInfoPredict),
                            ...(r.row.object_data || {}),
                        } as unknown as Row

                        this.recruitListConfig = recruitListConfig(
                            this.row!.id_card_openid
                        )
                        if (this.tab) {
                            this.curTab = this.tab
                        }
                    })
            })
        }

        private createStyle(arr: string[]) {
            return {
                "--firstWidth": arr[0] + "px",
                "--secondWidth": arr[1] + "px",
                "--thirdWidth": arr[2] + "px",
            }
        }

        batchAddService() {
            addServiceObj.setStorage({
                user_info: this.row!.id_card,
            })
            this.$router.push({
                name: routesMap.groupService.school.add,
                query: {
                    id_card: this.row!.id_card,
                    from: this.$route.name,
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    @import "~@/css/common-table.less";
</style>
