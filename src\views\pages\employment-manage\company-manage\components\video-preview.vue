<template>
    <el-dialog
        append-to-body
        :visible="value"
        width="1000px"
        title="视频预览"
        @close="close"
    >
        <div class="d-flex flex-column">
            <div class="d-flex justify-content-center" style="min-width: 300px">
                <video
                    ref="video"
                    v-if="url"
                    :src="url"
                    controls
                    :style="style"
                ></video>
            </div>

            <div class="d-flex justify-content-center actions">
                <span
                    class="d-flex align-items-center justify-content-center"
                    @click="set2Default"
                    >1:1</span
                >
                <a
                    class="d-flex align-items-center justify-content-center"
                    :href="url"
                    :download="getAttachment"
                >
                    <i class="el-icon-download"></i>
                </a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
    import { Component, Prop, Ref } from "vue-property-decorator"
    import { DialogController } from "@/core-ui/controller/dialog-controller"

    @Component({ components: {} })
    export default class VideoPreview extends DialogController {
        @Prop()
        private url!: string

        @Ref("video")
        private video!: HTMLVideoElement

        private style: {
            "max-height": number | string
            "max-width": number | string
        } = {
            "max-height": "800px",
            "max-width": "800px",
        }

        protected close() {
            setTimeout(
                () =>
                    (this.style = { "max-height": "400px", "max-width": "730px" }),
                300
            )
            this.video?.pause()
            this.$emit("update", false)
            this.$emit("close", false)
            this.onClosing()
        }

        private set2Default() {
            this.style = { "max-height": "1400px", "max-width": "1400px" }
        }

        private get getAttachment() {
            return "视频下载"
        }

        protected onOpen() {
            if (this.value) {
                this.video?.load()
                setTimeout(() => this.video?.play(), 100)
            } else {
                this.video?.pause()
            }
        }
    }
</script>

<style lang="less" scoped>
    .actions {
        margin: 15px 0;

        > span,
        a {
            width: 30px;
            height: 30px;
            background-color: #7a7b7d;
            color: #fff;
            border-radius: 50%;
            cursor: pointer;

            i {
                color: #fff;
                font-size: 20px;
            }

            & + span {
                margin-left: 15px;
            }
        }

        > a {
            margin-left: 15px;
        }
    }
</style>
