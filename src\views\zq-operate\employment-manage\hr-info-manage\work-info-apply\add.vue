<template>
    <div class="container-box core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>

        <div class="page-detail u-p-t-30 u-p-b-30">
            <form-builder
                ref="formBuilder"
                label-position="right"
                label-width="140px"
            ></form-builder>
            <div class="d-flex align-items-center justify-content-center btns">
                <el-button type="primary" plain @click="cancel">取消</el-button>
                <el-button
                    type="primary"
                    @click="confirm"
                    class="u-m-l-40"
                    :loading="loading"
                    >确认创建</el-button
                >
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { closeCurrentTap } from "@/views/pages/single-page/components/tags-view"
    import {
        BuildFormConfig,
        buildFormSections,
        FormController,
    } from "@/core-ui/component/form"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { routesMap } from "@/router/direction"
    import { forms } from "."

    @Component({
        name: routesMap.employmentManage.hrInfoManage.workInfoApplyAdd,
        components: { FormBuilder },
    })
    export default class WorkInfoApplyAdd extends FormController {
        breadcrumbs = [
            {
                label: `企业用工信息填报`,
                to: {
                    name: routesMap.employmentManage.hrInfoManage.workInfoApply,
                },
            },
            {
                label: "新建任务",
            },
        ]

        private col = {
            span: 14,
            offset: 6,
        }

        private forms: BuildFormConfig = {
            forms: forms as any,
            sdkModel: "company_task",
            sdkAction: "insert_yonggong",
            select_list: [],
        }

        private loading = false

        mounted() {
            this.init()
        }

        private init() {
            return buildFormSections(this.forms).then((r) => {
                this.buildForm(r.forms)
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                this.loading = true
                return sdk.core
                    .model("company_task")
                    .action("insert_yonggong")
                    .addInputs_parameter(data)
                    .execute()
                    .then((r) => {
                        this.$message.success("提交成功")
                        this.callRefresh(
                            routesMap.employmentManage.hrInfoManage.workInfoApply
                        )
                        this.$router.push({
                            name: routesMap.employmentManage.hrInfoManage
                                .workInfoApplyDetail,
                            query: {
                                id: r.id,
                            },
                        })
                    })
                    .finally(() => (this.loading = false))
            })
        }

        private cancel() {
            closeCurrentTap()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .page-detail {
        background-color: #fff;
        padding-left: 20px;
        padding-right: 20px;
        min-height: 50%;
    }
</style>
