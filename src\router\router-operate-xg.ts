import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { RouteConfig } from "."
import { routesMap } from "./direction"
import { config, EnvProject } from "@/config"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

export const xg_project_operate: RouteConfig[] = [
    {
        path: `/home`,
        redirect: "home/page",
        name: routesMap.home.root,
        meta: { showOneChildren: true },
        component: layout,
        children: [
            {
                path: "page",
                name: routesMap.home.page,
                meta: {
                    title: "首页",
                    svgIcon: require("@/assets/icon/menu/home.svg"),
                    hideTag: true,
                    parentMenuName: routesMap.home.root,
                    single: true,
                },
                component: () => import("@/views/pages/home/<USER>"),
            },
        ],
    },
    // 市场主体管理改名企业信息管理
    {
        path: "/employmentManage",
        redirect: "employmentManage/company-mange",
        name: routesMap.employmentManage.employmentManage,
        meta: {
            svgIcon: require("@/assets/icon/menu/employmentManage.svg"),
            title: [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                config.envProject
            )
                ? "企业用户管理"
                : "企业信息管理",
        },
        component: layout,
        children: [
            {
                path: "company-mange",
                name: routesMap.employmentManage.companyManage,
                meta: {
                    title: "企业列表",
                    role: "/tablelist/xg_agent/company_back_list",
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/company-manage/index.vue"
                    ),
            },
            {
                path: "enterprise-settled-apply",
                name: routesMap.employmentManage.enterpriseSettledApply,
                meta: {
                    title: "企业入驻申请管理",
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/enterprise-settled-apply/index.vue"
                    ),
            },
            {
                path: "company-manage-add",
                name: routesMap.employmentManage.companyManageAdd,
                meta: {
                    title: "新增企业",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.companyManage,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/company-manage/add.vue"
                    ),
            },
            {
                path: "company-detail",
                name: routesMap.employmentManage.companyManageDetail,
                meta: {
                    title: "企业详情",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.companyManage,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/company-manage/detail.vue"
                    ),
            },
            {
                path: "human-resource-check",
                name: routesMap.employmentManage.humanResourceCheck,
                meta: {
                    title: "人力资源机构入驻审核",
                    role: "/tablelist/xg_human_agent/audit_back_list",
                    hidden: isYD,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/human-resource-check/index.vue"
                    ),
            },
            {
                path: "human-resource-check-detail",
                name: routesMap.employmentManage.humanResourceCheckDetail,
                meta: {
                    title: "入驻审核",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.humanResourceCheck,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/human-resource-check/detail.vue"
                    ),
            },
            {
                path: "human-resource",
                name: routesMap.employmentManage.humanResource,
                meta: {
                    title: "人力资源机构列表",
                    role: "/tablelist/xg_human_agent/operator_back_list",
                    hidden: isYD,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/human-resource/index.vue"
                    ),
            },
            {
                path: "human-resource-add",
                name: routesMap.employmentManage.humanResourceAdd,
                meta: {
                    title: "新增人力资源机构",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.humanResource,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/human-resource/add.vue"
                    ),
            },
            {
                path: "human-resource-detail",
                name: routesMap.employmentManage.humanResourceDetail,
                meta: {
                    title: "人力资源机构详情",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.humanResource,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/human-resource/detail.vue"
                    ),
            },
            {
                path: "questionnaire",
                name: routesMap.employmentManage.questionnaire,
                meta: {
                    title: "企业调查问卷管理",
                    role: "/tablelist/serve_task/company_questionnaire_for_operate",
                    hidden: isYD,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/questionnaire/index.vue"
                    ),
            },
            {
                path: "questionnaire-add",
                name: routesMap.employmentManage.questionnaireAdd,
                meta: {
                    title: "新增调查问卷",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.questionnaire,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/questionnaire/add.vue"
                    ),
            },
            {
                path: "questionnaire-statistics",
                name: routesMap.employmentManage.questionnaireStatistics,
                meta: {
                    title: "问卷统计",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.questionnaire,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/questionnaire/statistics.vue"
                    ),
            },
            {
                path: "questionnaire-detail",
                name: routesMap.employmentManage.questionnaireDetail,
                meta: {
                    title: "员工调查问卷",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.questionnaire,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/questionnaire/detail.vue"
                    ),
            },
            {
                path: "company-questionnaire",
                name: routesMap.employmentManage.companyQuestionnaire,
                meta: {
                    title: "已提交用工调查问卷",
                    role: "/tablelist/task_serve_record/for_agent_questionnaire_operate",
                    hidden: isYD,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/company-questionnaire/index.vue"
                    ),
            },
            {
                path: "questionnaire-detail-readonly",
                name: routesMap.employmentManage.questionnaireDetailReadonly,
                meta: {
                    title: "员工调查问卷",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.companyQuestionnaire,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/questionnaire/detail.vue"
                    ),
            },
            {
                path: "company-join-cms",
                name: routesMap.employmentManage.companyJoinCms,
                meta: {
                    title: "企业邀约入驻列表",
                    role: "/tablelist/invite_company_record",
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/company-join-cms/index.vue"
                    ),
            },
            {
                path: "company-blacklist",
                name: routesMap.employmentManage.blacklist,
                meta: {
                    title: "企业拉黑求职者管理",
                    hidden: [
                        EnvProject.荆州项目,
                        EnvProject.黄州项目,
                        EnvProject.宜都项目,
                    ].includes(config.envProject),
                    role: "/tablelist/xg_agent/black_list",
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/black-list/index.vue"
                    ),
            },
            {
                path: "company-blacklist-detail",
                name: routesMap.employmentManage.blacklistDetail,
                meta: {
                    title: "企业拉黑求职者详情",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.blacklist,
                    // role: "/tablelist/xg_agent/company_back_list",
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/black-list/detail.vue"
                    ),
            },
            {
                path: "tools-inner",
                name: routesMap.toolsInner.index,
                meta: {
                    title: "预处理",
                    hidden: true,
                },
                component: () => import("@/views/pages/tools-inner/index.vue"),
            },
            {
                path: "black-list",
                name: routesMap.blacklist.company,
                meta: {
                    title: "企业黑名单",
                    role: "/tablelist/black_list/manage",
                    hidden:
                        [
                            EnvProject.荆州项目,
                            EnvProject.黄州项目,
                            EnvProject.宜都项目,
                        ].includes(config.envProject) && !isDev,
                    // single: true,
                    // svgIcon: require("@/assets/icon/menu/labourManage.svg"),
                },
                component: () => import("@/views/pages/blacklist/company.vue"),
            },
        ],
    },

    // 居民管理
    {
        path: "/labourManage",
        redirect: "labourManage/seekerInfo",
        name: routesMap.labourManage.root,
        meta: {
            title: "居民管理",
            svgIcon: require("@/assets/icon/menu/labourManage.svg"),
        },
        component: layout,
        children: [
            {
                path: "seekerInfo",
                name: routesMap.labourManage.seekerInfo,
                meta: {
                    title: "居民信息查看",
                    role: "/tablelist/user_profile_basic",
                },
                component: () =>
                    import("@/views/pages/labour-manage/seeker-info/index.vue"),
            },
            {
                path: "seekerDetail",
                name: routesMap.labourManage.seekerDetail,
                meta: {
                    title: "档案详情",
                    hidden: true,
                    parentMenuName: routesMap.labourManage.seekerInfo,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/seeker-info/detail.vue"
                    ),
            },
            {
                path: "recordList",
                name: routesMap.labourManage.recordList,
                meta: {
                    title: "服务记录列表",
                    role: "/tablelist/task_serve_record/for_operate",
                    hidden: isYD,
                },
                component: () =>
                    import("@/views/pages/labour-manage/record-list/index.vue"),
            },
            {
                path: "recordDetail",
                name: routesMap.labourManage.recordDetail,
                meta: {
                    title: "服务记录详情",
                    hidden: true,
                    parentMenuName: routesMap.labourManage.recordList,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/record-list/detail.vue"
                    ),
            },
            {
                path: "seeker-question",
                name: routesMap.labourManage.seekerQuestion,
                meta: {
                    title: "居民问卷管理",
                    role: "/tablelist/general_questionnaire/list_for_operate_personal",
                    hidden: isYD,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/seeker-question/index.vue"
                    ),
            },
            {
                path: "seeker-question-detail",
                name: routesMap.labourManage.seekerQuestionModel,
                meta: {
                    title: "问卷统计",
                    hidden: true,
                    parentMenuName: routesMap.labourManage.seekerQuestion,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/seeker-question/detail.vue"
                    ),
            },
            {
                path: "seeker-question-statistics",
                name: routesMap.labourManage.seekerQuestionStatistics,
                meta: {
                    title: "问卷统计",
                    hidden: true,
                    parentMenuName: routesMap.labourManage.seekerQuestion,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/seeker-question/statistics.vue"
                    ),
            },
            {
                path: "seeker-question-service-list",
                name: routesMap.labourManage.seekerQuestionServiceList,
                meta: {
                    title: "服务对象列表",
                    hidden: true,
                    parentMenuName: routesMap.labourManage.seekerQuestion,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/seeker-question/service-list.vue"
                    ),
            },
            {
                path: "service-record-manage",
                name: routesMap.labourManage.serviceRecordManage,
                meta: {
                    title: "我的服务记录处理",
                    role: "/tablelist/task_serve_call_back/for_operate_user",
                    hidden: isYD,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/service-record-manage/index.vue"
                    ),
            },
            {
                path: "user-account",
                name: routesMap.labourManage.userAccount,
                meta: {
                    title: "注册居民管理",
                    role: "/tablelist/user_account/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/user-account/index.vue"
                    ),
            },
            {
                path: "user-account-detail",
                name: routesMap.labourManage.userAccountDetail,
                meta: {
                    title: "注册居民详情",
                    hidden: true,
                    parentMenuName: routesMap.labourManage.userAccount,
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/user-account/detail.vue"
                    ),
            },
            {
                path: "register-statistics",
                name: routesMap.labourManage.registerStatistics,
                meta: {
                    title: "实名数据统计",
                    hidden: [EnvProject.黄州项目, EnvProject.宜都项目].includes(
                        config.envProject
                    ),
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/register-statistics/index.vue"
                    ),
            },
            {
                path: "xcd-manage",
                name: routesMap.labourManage.xcdManage,
                meta: {
                    title: "孝创贷数据管理",
                    hidden: [
                        EnvProject.荆州项目,
                        EnvProject.黄州项目,
                        EnvProject.宜都项目,
                    ].includes(config.envProject),
                    role: "/tablelist/t_loan_person_info/loan_list",
                },
                component: () =>
                    import("@/views/pages/labour-manage/xcd/index.vue"),
            },
            {
                path: "xcd-detail",
                name: routesMap.labourManage.xcdDetail,
                meta: {
                    title: "孝创贷数据详情",
                    parentMenuName: routesMap.labourManage.xcdManage,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/labour-manage/xcd/detail.vue"),
            },
            {
                path: "black-list",
                name: routesMap.blacklist.index,
                meta: {
                    title: "居民黑名单",
                    role: "/tablelist/black_list/manage",
                    hidden:
                        [
                            EnvProject.荆州项目,
                            EnvProject.黄州项目,
                            EnvProject.宜都项目,
                        ].includes(config.envProject) && !isDev,
                    // single: true,
                    // svgIcon: require("@/assets/icon/menu/labourManage.svg"),
                },
                component: () => import("@/views/pages/blacklist/index.vue"),
            },
        ],
    },
    // 劳动力数据分析大屏
    {
        ...(EnvProject.孝感项目 === config.envProject
            ? {
                  path: "https://jy.xg12333.cn:28090/",
                  name: routesMap.dataAcquisition.labour,
                  meta: {
                      title: "劳动力数据分析大屏",
                      svgIcon: require("@/assets/icon/menu/screen.svg"),
                  },
              }
            : {
                  path: "/show-big-screen-report",
                  name: routesMap.dataAcquisition.labour,
                  meta: {
                      title: "劳动力数据分析大屏",
                      svgIcon: require("@/assets/icon/menu/screen.svg"),

                      newPage: true,
                      targetName: routesMap.bigScreen.report1,
                  },
              }),
    },
    // 市场主体信息库管理
    {
        path: "/enterprise-mange",
        redirect: "enterprise-mange/enterprise-database",
        name: routesMap.collectTaskManage.marketDatabaseManage.root,
        meta: {
            title: "市场主体信息库管理",
            svgIcon: require("@/assets/icon/menu/laborInfoBase.svg"),
            hidden: ![
                EnvProject.荆州项目,
                EnvProject.黄州项目,
                EnvProject.宜都项目,
                EnvProject.鄂州项目,
            ].includes(config.envProject),
        },
        component: layout,
        children: [
            {
                path: "enterprise-database",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .enterpriseDatabase.list,
                meta: {
                    title: "企业信息库",
                    role: "/tablelist/tg_enterprise/back_list2",
                    hidden: ![
                        EnvProject.荆州项目,
                        EnvProject.黄州项目,
                        EnvProject.宜都项目,
                        EnvProject.鄂州项目,
                    ].includes(config.envProject),
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/enterprise-database/index.vue"
                    ),
            },
            {
                path: "enterprise-database-filter",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .enterpriseDatabase.filter,
                meta: {
                    title: "企业信息库筛选",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.marketDatabaseManage
                            .enterpriseDatabase.list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/enterprise-database/filter.vue"
                    ),
            },
            {
                path: "enterprise-database-detail",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .enterpriseDatabase.detail,
                meta: {
                    title: "企业信息库详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.marketDatabaseManage
                            .enterpriseDatabase.list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/enterprise-database/detail.vue"
                    ),
            },
            {
                path: "staff-database",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .staffDatabase.list,
                meta: {
                    title: "企业员工信息库",
                    role: "/tablelist/company_employee_information/manage",
                    hidden: !isDev,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/staff-database/index.vue"
                    ),
            },
            {
                path: "staff-database-filter",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .staffDatabase.filter,
                meta: {
                    title: "企业员工信息库筛选",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.marketDatabaseManage
                            .staffDatabase.list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/staff-database/filter.vue"
                    ),
            },
            {
                path: "staff-database-detail",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .staffDatabase.detail,
                meta: {
                    title: "企业员工信息库详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.marketDatabaseManage
                            .staffDatabase.list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/staff-database/detail.vue"
                    ),
            },
            // {
            //     path: "employment-demand-database",
            //     name: routesMap.collectTaskManage.marketDatabaseManage
            //         .employmentDemandDatabase.list,
            //     meta: {
            //         title: "企业用工需求库",
            //         hidden: ![EnvProject.荆州项目, EnvProject.黄州项目].includes(config.envProject),
            //         role: "/tablelist/tg_enterprise/back_list2",
            //     },
            //     component: () =>
            //         import(
            //             "@/views/pages/collect-task-manage/employment-demand-database/index.vue"
            //         ),
            // },
            {
                path: "labor-services-brand",
                name: routesMap.laborInfoBaseManage.laborServicesBrand,
                meta: {
                    title: "劳务品牌信息库",
                    hidden: ![
                        EnvProject.荆州项目,
                        EnvProject.黄州项目,
                    ].includes(config.envProject),
                    role: "/tablelist/labor_services_brand",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/labor-services-brand/index.vue"
                    ),
            },
            {
                path: "labor-services-brand-create-brand",
                name: routesMap.laborInfoBaseManage
                    .laborServicesBrandCreateBrand,
                meta: {
                    title: "劳务品牌信息库编辑",
                    hidden: true,
                    parentMenuName:
                        routesMap.laborInfoBaseManage.laborServicesBrand,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/labor-services-brand/create-brand.vue"
                    ),
            },
            {
                path: "labor-services-brand-detail",
                name: routesMap.laborInfoBaseManage.laborServicesBrandDetail,
                meta: {
                    title: "劳务品牌信息库详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.laborInfoBaseManage.laborServicesBrand,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/labor-services-brand/detail.vue"
                    ),
            },
        ],
    },
    // 企业用工情况调查
    {
        path: "/hr-info-manage",
        redirect: "hr-info-manage/work-info-apply",
        name: routesMap.employmentManage.hrInfoManage.hrInfoManage,
        meta: {
            title: "企业用工情况调查",
            svgIcon: require("@/assets/icon/menu/hrInfoManage.svg"),
            hidden: [EnvProject.宜都项目].includes(config.envProject),
        },
        component: layout,
        children: [
            {
                path: "staff-list",
                name: routesMap.employmentManage.hrInfoManage.staffList,
                meta: {
                    title: "企业员工列表",
                    hidden: [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                        config.envProject
                    ),
                    role: "/tablelist/company_employee_information/manage",
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/staff-list/index.vue"
                    ),
            },
            {
                path: "staff-list-detail",
                name: routesMap.employmentManage.hrInfoManage.staffListDetail,
                meta: {
                    title: "企业员工列表详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.staffList,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/staff-list/detail.vue"
                    ),
            },
            {
                path: "dimission",
                name: routesMap.labourManage.dimission,
                meta: {
                    title: "离职人员登记表",
                    role: "/tablelist/leaving_person",
                    hidden: [EnvProject.黄州项目, EnvProject.宜都项目].includes(
                        config.envProject
                    ),
                },
                component: () =>
                    import("@/views/pages/labour-manage/dimission/index.vue"),
            },
            {
                path: "work-info-apply",
                name: routesMap.employmentManage.hrInfoManage.workInfoApply,
                meta: {
                    title: "用工信息填报",
                    role: "/tablelist/company_task",
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/index.vue"
                    ),
            },
            {
                path: "work-info-apply-add",
                name: routesMap.employmentManage.hrInfoManage.workInfoApplyAdd,
                meta: {
                    title: "新建任务",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workInfoApply,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/add.vue"
                    ),
            },
            {
                path: "work-info-apply-detail",
                name: routesMap.employmentManage.hrInfoManage
                    .workInfoApplyDetail,
                meta: {
                    title: "任务详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workInfoApply,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/detail.vue"
                    ),
            },
            {
                path: "work-info-apply-record",
                name: routesMap.employmentManage.hrInfoManage
                    .workInfoApplyRecord,
                meta: {
                    title: "填报记录",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workInfoApply,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/apply-record.vue"
                    ),
            },
            {
                path: "work-info-audit",
                name: routesMap.employmentManage.hrInfoManage.workInfoAudit,
                meta: {
                    title: "用工信息审核",
                    role: "/tablelist/company_task_record/list_for_plat_yonggong",
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-info-audit/index.vue"
                    ),
            },
            {
                path: "work-info-audit-check",
                name: routesMap.employmentManage.hrInfoManage
                    .workInfoAuditCheck,
                meta: {
                    title: "审核查看",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workInfoAudit,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-info-audit/audit.vue"
                    ),
            },
            {
                path: "work-demand-apply",
                name: routesMap.employmentManage.hrInfoManage.workDemandApply,
                meta: {
                    title: "用工需求填报",
                    role: "/tablelist/company_task/list_for_req",
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/index.vue"
                    ),
            },
            {
                path: "work-demand-apply-add",
                name: routesMap.employmentManage.hrInfoManage
                    .workDemandApplyAdd,
                meta: {
                    title: "新建任务",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workDemandApply,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/add.vue"
                    ),
            },
            {
                path: "work-demand-apply-detail",
                name: routesMap.employmentManage.hrInfoManage
                    .workDemandApplyDetail,
                meta: {
                    title: "任务详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workDemandApply,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/detail.vue"
                    ),
            },
            {
                path: "work-demand-apply-record",
                name: routesMap.employmentManage.hrInfoManage
                    .workDemandApplyRecord,
                meta: {
                    title: "填报记录",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workDemandApply,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/apply-record.vue"
                    ),
            },
            {
                path: "work-demand-audit",
                name: routesMap.employmentManage.hrInfoManage.workDemandAudit,
                meta: {
                    title: "用工需求审核",
                    role: "/tablelist/company_task_record/list_for_plat_xuqiu",
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-audit/index.vue"
                    ),
            },
            {
                path: "work-demand-audit-check",
                name: routesMap.employmentManage.hrInfoManage
                    .workDemandAuditCheck,
                meta: {
                    title: "审核查看",
                    hidden: true,
                    parentMenuName:
                        routesMap.employmentManage.hrInfoManage.workDemandAudit,
                },
                component: () =>
                    import(
                        "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-audit/audit.vue"
                    ),
            },
        ],
    },

    // 市场主体管理
    {
        path: "/market-main",
        redirect: "market-main",
        name: routesMap.employmentManage.marketMain,
        meta: {
            svgIcon: require("@/assets/icon/menu/employmentManage.svg"),
            title: "企业信息库",
            hidden: ![EnvProject.荆州项目, EnvProject.黄州项目].includes(
                config.envProject
            ),
        },
        component: layout,
        children: [
            {
                path: "market-main",
                name: routesMap.employmentManage.marketMain,
                meta: {
                    title: "市场主体",
                    role: "/tablelist/tg_enterprise/for_operate",
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/market-main/index.vue"
                    ),
            },
            {
                path: "market-main-detial",
                name: routesMap.employmentManage.marketMainDetail,
                meta: {
                    title: "市场主体详情",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.marketMain,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/market-main/detail.vue"
                    ),
            },
        ],
    },
    {
        path: "/job-deliver",
        redirect: "/job-deliver/job",
        name: routesMap.recruit.jobDeliverIndex,
        meta: {
            title: "岗位投递管理",
            svgIcon: require("@/assets/icon/menu/job-deliver.svg"),
        },
        component: layout,
        children: [
            {
                path: "statistics",
                name: routesMap.recruit.statistics,
                meta: {
                    title: "招聘数据统计",
                    role: "/redirect/xg_project/hr_statistic",
                    hidden: isYD,
                },
                component: () =>
                    import("@/views/pages/recruit/statistics/index.vue"),
            },
            {
                path: "grid-list",
                name: routesMap.recruit.gridList,
                meta: {
                    title: "网格推广统计详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.statistics,
                },
                component: () =>
                    import("@/views/pages/recruit/statistics/grid-list.vue"),
            },
            {
                path: "order-list",
                name: routesMap.recruit.orderList,
                meta: {
                    title: "招聘数据统计",
                    hidden: true,
                    parentMenuName: routesMap.recruit.statistics,
                },
                component: () =>
                    import("@/views/pages/recruit/statistics/order-list.vue"),
            },
            {
                path: "job",
                name: routesMap.recruit.job,
                meta: {
                    title: "招聘岗位列表",
                    role: "/tablelist/xg_company_position/manage",
                },
                component: () => import("@/views/pages/recruit/job/index.vue"),
            },
            {
                path: "job-deliver-index",
                name: routesMap.recruit.jobDeliver,
                meta: {
                    title: "岗位投递列表",
                    role: "/tablelist/xg_candidate_order/manage",
                },
                component: () =>
                    import("@/views/pages/recruit/job-deliver/index.vue"),
            },
            {
                path: "job-deliver-detail",
                name: routesMap.recruit.jobDeliverDetail,
                meta: {
                    title: "岗位投递详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.jobDeliver,
                },
                component: () =>
                    import("@/views/pages/recruit/job-deliver/detail.vue"),
            },
            {
                path: "job-detail",
                name: routesMap.recruit.jobDetail,
                meta: {
                    title: "招聘岗位详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.job,
                },
                component: () => import("@/views/pages/recruit/job/detail.vue"),
            },
            {
                path: "job-edit",
                name: routesMap.recruit.jobEdit,
                meta: {
                    title: "岗位编辑",
                    hidden: true,
                    parentMenuName: routesMap.recruit.job,
                },
                component: () =>
                    import("@/views/pages/recruit/job/job-edit.vue"),
            },
        ],
    },
    {
        path: "/public-job",
        redirect: "/public-job/index",
        name: routesMap.recruit.publicJobIndex,
        meta: {
            title: "公益性岗位",
            svgIcon: require("@/assets/icon/menu/publicJob.svg"),
            hidden:
                [
                    EnvProject.荆州项目,
                    EnvProject.黄州项目,
                    EnvProject.宜都项目,
                ].includes(config.envProject) && !isDev,
        },
        component: layout,
        children: [
            {
                path: "index",
                name: routesMap.recruit.publicJob,
                meta: {
                    title: "公益性岗位管理",
                    role: "/tablelist/xg_company_position/public_welfare_manage",
                    hidden:
                        [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                            config.envProject
                        ) && !isDev,
                },
                component: () =>
                    import("@/views/pages/recruit/job/public-job.vue"),
            },
            {
                path: "public-job-edit",
                name: routesMap.recruit.publicJobEdit,
                meta: {
                    title: "公益性岗位编辑",
                    hidden: true,
                    parentMenuName: routesMap.recruit.publicJob,
                },
                component: () =>
                    import("@/views/pages/recruit/job/public-job-edit.vue"),
            },
            {
                path: "public-job-deliver",
                name: routesMap.recruit.publicJobDeliver,
                meta: {
                    title: "公益性岗位投递列表",
                    role: "/tablelist/xg_candidate_order/public_welfare_manage",
                    hidden:
                        [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                            config.envProject
                        ) && !isDev,
                },
                component: () =>
                    import("@/views/pages/recruit/job-deliver/public-job.vue"),
            },
            {
                path: "public-job-deliver-detail",
                name: routesMap.recruit.publicJobDeliverDetail,
                meta: {
                    title: "岗位投递详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.publicJobDeliver,
                },
                component: () =>
                    import("@/views/pages/recruit/job-deliver/detail.vue"),
            },
            {
                path: "public-job-detail",
                name: routesMap.recruit.publicJobDetail,
                meta: {
                    title: "公益性岗位详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.publicJob,
                },
                component: () => import("@/views/pages/recruit/job/detail.vue"),
            },
        ],
    },
    {
        path: "/job",
        redirect: "/job/grid",
        name: routesMap.recruit.jobIndex,
        meta: {
            title: "招聘服务管理",
            svgIcon: require("@/assets/icon/menu/job.svg"),
        },
        component: layout,
        children: [
            {
                path: "grid",
                name: routesMap.recruit.grid,
                meta: {
                    title: "岗位网格推广",
                    role: "/tablelist/xg_company_position_recommend/manage_apply3_list",
                    hidden: true,
                },
                component: () => import("@/views/pages/recruit/grid/index.vue"),
            },
            {
                path: "grid-detail",
                name: routesMap.recruit.gridDetail,
                meta: {
                    title: "网格推广申请详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.grid,
                },
                component: () =>
                    import("@/views/pages/recruit/grid/detail.vue"),
            },
            {
                path: "group",
                name: routesMap.recruit.group,
                meta: {
                    title: "岗位社群推广",
                    role: "/tablelist/xg_company_position_recommend/manage_apply2_list",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/recruit/group/index.vue"),
            },
            {
                path: "group-detail",
                name: routesMap.recruit.groupDetail,
                meta: {
                    title: "社群推广申请详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.group,
                },
                component: () =>
                    import("@/views/pages/recruit/group/detail.vue"),
            },
            {
                path: "cooperation",
                name: routesMap.recruit.cooperation,
                meta: {
                    title: "机构代招服务",
                    role: "/tablelist/xg_company_position_recommend/manage_apply4_list",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/recruit/cooperation/index.vue"),
            },
            {
                path: "person",
                name: routesMap.recruit.person,
                meta: {
                    title: "智能人才推荐",
                    role: "/tablelist/xg_company_position_recommend/manage_apply1_list",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/recruit/person/index.vue"),
            },
            {
                path: "person-detail",
                name: routesMap.recruit.personDetail,
                meta: {
                    title: "人才推荐申请详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.person,
                },
                component: () =>
                    import("@/views/pages/recruit/person/detail.vue"),
            },
            {
                path: "personIntelligentRecommendDetail",
                name: routesMap.recruit.personIntelligentRecommendDetail,
                meta: {
                    title: "智能人才推荐",
                    hidden: true,
                    parentMenuName: routesMap.recruit.person,
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/person/intelligent-recommend/index.vue"
                    ),
            },
            {
                path: "personJobAutoMatch",
                name: routesMap.recruit.personJobAutoMatch,
                meta: {
                    title: "智能人岗匹配",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/person-job-auto-match/index.vue"
                    ),
            },
            {
                path: "personJobAutoMatchDetail",
                name: routesMap.recruit.personJobAutoMatchDetail,
                meta: {
                    title: "人岗匹配详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.personJobAutoMatch,
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/person-job-auto-match/company-detail.vue"
                    ),
            },
            {
                path: "personJobAutoMatchPositionDetail",
                name: routesMap.recruit.personJobAutoMatchPositionDetail,
                meta: {
                    title: "人岗匹配记录",
                    hidden: true,
                    parentMenuName: routesMap.recruit.personJobAutoMatch,
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/person-job-auto-match/position/index.vue"
                    ),
            },
            {
                path: "hot-job",
                name: routesMap.recruit.hotJob,
                meta: {
                    title: "每日热招岗位",
                    role: "/tablelist/xg_activity",
                    hidden: true, // 0914 隐藏该模块
                },
                component: () =>
                    import("@/views/pages/recruit/hot-job/index.vue"),
            },
            {
                path: "hot-job-detail",
                name: routesMap.recruit.hotJobDetail,
                meta: {
                    title: "热招岗位详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.hotJob,
                },
                component: () =>
                    import("@/views/pages/recruit/hot-job/detail.vue"),
            },
            {
                path: "cooperation-detail",
                name: routesMap.recruit.cooperationDetail,
                meta: {
                    title: "人力资源撮合申请详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.cooperation,
                },
                component: () =>
                    import("@/views/pages/recruit/cooperation/detail.vue"),
            },
            {
                path: "job-statistics-group",
                name: routesMap.recruit.jobStatisticsGroup,
                meta: {
                    title: "群内岗位点击统计",
                    hidden: [EnvProject.黄州项目, EnvProject.鄂州项目].includes(
                        config.envProject
                    ),
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/job-statistics-group/index.vue"
                    ),
            },
            {
                path: "DataResourceStatistics",
                name: routesMap.recruit.DataResourceStatistics,
                meta: {
                    title: "数据资源统计",
                    hidden: ![EnvProject.荆州项目].includes(config.envProject),
                    role: "/xg_project/dashboard_api/fetch_business_report_data",
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/data-resources-statistics/index.vue"
                    ),
            },
        ],
    },
    {
        path: "/job-fair",
        redirect: "/job-fair/page",
        name: routesMap.recruit.jobFairIndex,
        meta: {
            title: "招聘会",
            svgIcon: require("@/assets/icon/menu/jobFair.svg"),
            hidden: [EnvProject.宜都项目].includes(config.envProject),
        },
        component: layout,
        children: [
            {
                path: "page",
                name: routesMap.recruit.jobFair,
                meta: {
                    title: "招聘会管理",
                    role: "/tablelist/job_fair/for_operate",
                },
                component: () =>
                    import("@/views/pages/recruit/job-fair/index.vue"),
            },
            {
                path: "job-fair-statics",
                name: routesMap.recruit.jobFairStatics,
                meta: {
                    title: "招聘会统计",
                    role: "/xg_project/operate_api/get_job_fair_statistic",
                },
                component: () =>
                    import("@/views/pages/recruit/job-fair/statics.vue"),
            },
            {
                path: "specialRecruitment",
                name: routesMap.recruit.specialRecruitment,
                meta: {
                    hidden: ![EnvProject.荆州项目].includes(config.envProject),
                    title: "招聘活动汇总",
                    role: "/xg_project/back_api/get_job_fair_area",
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/job-fair/specialRecruitment/index.vue"
                    ),
            },
            {
                path: "recruitmentActivity",
                name: routesMap.recruit.recruitmentActivity,
                meta: {
                    hidden: ![EnvProject.荆州项目].includes(config.envProject),
                    title: "专场招聘活动统计",
                    role: "/xg_project/back_api/get_job_fair_activity",
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/job-fair/recruitmentActivity/index.vue"
                    ),
            },

            {
                path: "job-fair-detail",
                name: routesMap.recruit.jobFairDetail,
                meta: {
                    title: "招聘会管理详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.jobFair,
                },
                component: () =>
                    import("@/views/pages/recruit/job-fair/detail.vue"),
            },
            {
                path: "add-job-fair",
                name: routesMap.recruit.addJobFair,
                meta: {
                    title: "创建招聘会",
                    hidden: true,
                    parentMenuName: routesMap.recruit.jobFair,
                },
                component: () =>
                    import("@/views/pages/recruit/job-fair/add.vue"),
            },
            {
                path: "audit-detail",
                name: routesMap.recruit.auditDetail,
                meta: {
                    title: "招聘会审核详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.jobFair,
                },
                component: () =>
                    import("@/views/pages/recruit/job-fair/audit-detail.vue"),
            },
            {
                path: "agent-audit-detail",
                name: routesMap.recruit.agentAudit,
                meta: {
                    title: "审核企业",
                    hidden: true,
                    parentMenuName: routesMap.recruit.agents,
                },
                component: () =>
                    import("@/views/pages/recruit/agents/audit.vue"),
            },
            {
                path: "apply-detail",
                name: routesMap.recruit.applyDetail,
                meta: {
                    title: "投递详情",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/recruit/job-fair/apply-detail.vue"),
            },
            {
                path: "agents",
                name: routesMap.recruit.agents,
                meta: {
                    title: "参会信息审核",
                    hidden: true,
                    role: "/tablelist/job_fair_agent_apply/for_operate",
                },
                component: () =>
                    import("@/views/pages/recruit/agents/index.vue"),
            },
            {
                path: "agents-detail",
                name: routesMap.recruit.agentsDetail,
                meta: {
                    title: "企业详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.agents,
                },
                component: () =>
                    import("@/views/pages/recruit/agents/detail.vue"),
            },
            {
                path: "agents-job-detail",
                name: routesMap.recruit.agentsJobDetail,
                meta: {
                    title: "岗位详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.agents,
                },
                component: () =>
                    import("@/views/pages/recruit/agents/job-detail.vue"),
            },
            {
                path: "area",
                name: routesMap.recruit.area,
                meta: {
                    title: "场地管理",
                },
                component: () => import("@/views/pages/recruit/area/index.vue"),
            },
            {
                path: "area-detail",
                name: routesMap.recruit.areaDetail,
                meta: {
                    title: "场地详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.area,
                },
                component: () =>
                    import("@/views/pages/recruit/area/detail.vue"),
            },
            {
                path: "hz-job-fair-bigscreen",
                name: routesMap.jobStation.hzJobFairBigScreen,
                meta: {
                    role: "/redirect/xg_project/job-fair-bigscreen",
                    title: "招聘会统计大屏",
                    newPage: true,
                    // 鄂州和黄州已经部署
                    targetPath: `/bigScreen/#/bigscreen/preview?code=bigScreen_tyoVII4KbX`,
                },
            },
        ],
    },
    {
        path: "/work-index",
        redirect: "/work-index/work",
        name: routesMap.recruit.workIndex,
        meta: {
            title: "共享用工",
            svgIcon: require("@/assets/icon/menu/work.svg"),
            hidden: [EnvProject.宜都项目].includes(config.envProject),
        },
        component: layout,
        children: [
            {
                path: "work",
                name: routesMap.recruit.work,
                meta: {
                    title: "共享用工",
                    role: "/tablelist/share_employee_apply/list_for_workbench",
                },
                // 荆州使用孝感一样的共享用工逻辑
                // import("@/views/pages/recruit/work/manage.vue")
                component: () => import("@/views/pages/recruit/work/index.vue"),
            },
            {
                path: "work-detail",
                name: routesMap.recruit.workDetail,
                meta: {
                    title: "共享用工信息详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.work,
                },
                component: () =>
                    import("@/views/pages/recruit/work/detail.vue"),
            },
            {
                path: "work-add",
                name: routesMap.recruit.workAdd,
                meta: {
                    title: "发布共享申请审核",
                    hidden: true,
                    parentMenuName: routesMap.recruit.work,
                },
                component: () => import("@/views/pages/recruit/work/add.vue"),
            },
        ],
    },
    {
        path: "/complaint",
        redirect: "/complaint/manage",
        name: routesMap.recruit.complaintIndex,
        meta: {
            title: "投诉管理",
            svgIcon: require("@/assets/icon/menu/complaint.svg"),
            hidden: [EnvProject.宜都项目].includes(config.envProject),
        },
        component: layout,
        children: [
            {
                path: "manage",
                name: routesMap.recruit.complaint,
                meta: {
                    title: [EnvProject.荆州项目].includes(config.envProject)
                        ? "有问必答"
                        : "投诉专区",
                    role: "/tablelist/user_complaint/manage",
                },
                component: () =>
                    import("@/views/pages/recruit/complaint/index.vue"),
            },
            {
                path: "job-complaint-detail",
                name: routesMap.recruit.complaintDetail,
                meta: {
                    title: "投诉详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.complaint,
                },
                component: () =>
                    import("@/views/pages/recruit/complaint/detail.vue"),
            },
            {
                path: "labour-service-complaint",
                name: routesMap.recruit.labourServiceComplaint,
                meta: {
                    title: "劳务用工投诉管理",
                    role: "/tablelist/complaints/manage",
                    hidden: !isXg,
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/labour-service-complaint/index.vue"
                    ),
            },
            {
                path: "labour-service-complaint-detail",
                name: routesMap.recruit.labourServiceComplaintDetail,
                meta: {
                    title: "投诉详情",
                    hidden: true,
                    parentMenuName: routesMap.recruit.labourServiceComplaint,
                },
                component: () =>
                    import(
                        "@/views/pages/recruit/labour-service-complaint/detail.vue"
                    ),
            },
        ],
    },
    {
        path: "/position-collected",
        redirect: "/position-collected/index",
        name: routesMap.recruit.positionCollectedIndex,
        meta: {
            title: "岗位筹集",
            role: "/tablelist/xg_position_collected/manage",
            svgIcon: require("@/assets/icon/menu/positionCollected.svg"),
            hidden: [EnvProject.宜都项目, EnvProject.掇刀项目].includes(
                config.envProject
            ),
        },
        component: layout,
        children: [
            {
                path: "index",
                name: routesMap.recruit.positionCollected,
                meta: {
                    title: "岗位筹集管理",
                    role: "/tablelist/xg_position_collected/manage",
                    hidden: !isHz && !isDev,
                },
                component: () =>
                    import("@/views/pages/recruit/collected/index.vue"),
            },
            {
                path: "position-collected-add",
                name: routesMap.recruit.positionCollectedAdd,
                meta: {
                    title: "编辑岗位筹集",
                    parentMenuName: routesMap.recruit.positionCollected,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/recruit/collected/add.vue"),
            },
            {
                path: "position-collected-detail",
                name: routesMap.recruit.positionCollectedDetail,
                meta: {
                    title: "岗位筹集详情",
                    parentMenuName: routesMap.recruit.positionCollected,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/recruit/collected/detail.vue"),
            },
        ],
    },
    // 零工驿站管理
    {
        path: "/job-station",
        redirect: "job-station/manage",
        name: routesMap.jobStation.root,
        meta: {
            title: "零工驿站管理",
            svgIcon: require("@/assets/icon/menu/job-station.svg"),
            hidden: [EnvProject.荆州项目].includes(config.envProject) && !isDev,
            role: "/tablelist/xg_odd_job/manage",
        },
        component: layout,
        children: [
            {
                path: "statistics",
                name: routesMap.jobStation.statistics,
                meta: {
                    title: "驿站统计",
                    hidden: [EnvProject.荆州项目].includes(config.envProject),
                },
                component: () =>
                    import("@/views/pages/job-station/statistics/index.vue"),
            },
            {
                path: "jobstation-statistics-detail",
                name: routesMap.jobStation.jobStationStatisticsDetail,
                meta: {
                    title: "零工驿站统计详情",
                    hidden: true,
                    parentMenuName: routesMap.jobStation.statistics,
                },
                component: () =>
                    import(
                        "@/views/pages/job-station/statistics/jobstastion-detail.vue"
                    ),
            },
            {
                path: "recommend-statistics-detail",
                name: routesMap.jobStation.recommendStatisticsDetail,
                meta: {
                    title: "用工推荐统计详情",
                    hidden: true,
                    parentMenuName: routesMap.jobStation.statistics,
                },
                component: () =>
                    import(
                        "@/views/pages/job-station/statistics/recommend-detail.vue"
                    ),
            },
            {
                path: "share-statistics-detail",
                name: routesMap.jobStation.shareStatisticsDetail,
                meta: {
                    title: "驿站共享统计详情",
                    hidden: true,
                    parentMenuName: routesMap.jobStation.statistics,
                },
                component: () =>
                    import(
                        "@/views/pages/job-station/statistics/share-detail.vue"
                    ),
            },
            {
                path: "manage",
                name: routesMap.jobStation.manage,
                meta: {
                    title: "驿站信息",
                },
                component: () =>
                    import("@/views/pages/job-station/page/manage.vue"),
            },
            {
                path: "ban",
                name: routesMap.jobStation.ban,
                meta: {
                    title: "驿站封禁",
                },
                component: () =>
                    import("@/views/pages/job-station/page/ban.vue"),
            },
            {
                path: "invite-log",
                name: routesMap.jobStation.inviteLog,
                meta: {
                    title: "邀约记录",
                },
                component: () =>
                    import("@/views/pages/job-station/page/invite-log.vue"),
            },
            {
                path: "seek-info",
                name: routesMap.jobStation.publishSeekInfo,
                meta: {
                    title: "零工求职信息管理",
                },
                component: () =>
                    import("@/views/pages/job-station/page/seek-info.vue"),
            },
            {
                path: "recruitment-info",
                name: routesMap.jobStation.publishRecruitmentInfo,
                meta: {
                    title: "雇主招工信息管理",
                },
                component: () =>
                    import(
                        "@/views/pages/job-station/page/recruitment-info.vue"
                    ),
            },
            {
                path: "device-manage",
                name: routesMap.jobStation.deviceMange,
                meta: {
                    title: "设备管理",
                    role: "/tablelist/device_base_info/manager_back_v2",
                },
                component: () =>
                    import("@/views/pages/job-station/device/index.vue"),
            },
            {
                path: "device-manage-detail",
                name: routesMap.jobStation.deviceDetail,
                meta: {
                    title: "设备管理详情",
                    hidden: true,
                    parentMenuName: routesMap.jobStation.deviceMange,
                },
                component: () =>
                    import("@/views/pages/job-station/device/detail.vue"),
            },
            {
                path: "brand-manage",
                name: routesMap.jobStation.brandManage,
                meta: {
                    title: "品牌管理",
                    role: "/tablelist/device_brand/manager_back",
                },
                component: () =>
                    import("@/views/pages/job-station/device/brand.vue"),
            },
            {
                path: "hz-jobstation-bigscreen",
                name: routesMap.jobStation.hzBigScreen,
                meta: {
                    title: "零工驿站大屏",
                    newPage: true,
                    targetPath: `https://rtd.hzjycy.cn/shareDashboard/f2e7f632700e47798c235488bd28afea?type=NONE`,
                    hidden: ![EnvProject.黄州项目].includes(config.envProject),
                },
            },
        ],
    },
    // 重点人群跟踪服务
    {
        path: "/group-service",
        redirect: "group-service/service-manage",
        name: routesMap.groupService.groupService,
        meta: {
            title: "重点人群服务跟踪",
            svgIcon: require("@/assets/icon/menu/group-service.svg"),
        },
        component: layout,
        children: [
            {
                path: "service-manage",
                name: routesMap.groupService.serviceManage,
                meta: {
                    title: "重点人群就业服务",
                    role: "/tablelist/serve_project/for_operate",
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-statistics/index.vue"
                    ),
            },
            {
                path: "service-list",
                name: routesMap.groupService.serviceManageList,
                meta: {
                    title: "服务列表",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/index.vue"
                    ),
            },
            {
                path: "service-manage-edit",
                name: routesMap.groupService.serviceManageEdit,
                meta: {
                    title: "更改帮扶服务",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/index.vue"
                    ),
            },
            {
                path: "service-manage-create",
                name: routesMap.groupService.serviceManageCreate,
                meta: {
                    title: "新增帮扶服务",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/index.vue"
                    ),
            },
            {
                path: "service-create",
                name: routesMap.groupService.serviceCreate,
                meta: {
                    title: "新增服务内容项",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                    noCache: true,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/service.vue"
                    ),
            },
            {
                path: "service-task-create",
                name: routesMap.groupService.serviceTaskCreate,
                meta: {
                    title: "新增任务",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                    noCache: true,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/task.vue"
                    ),
            },
            {
                path: "serviceManageDetail",
                redirect: "serviceManageDetail/detail",
                name: routesMap.groupService.serviceManageDetail.root,
                meta: {
                    hidden: true,
                },
                component: RouteView,
                children: [
                    {
                        path: "detail",
                        name: routesMap.groupService.serviceManageDetail.detail,
                        meta: {
                            title: "重点人群帮扶服务详情",
                            parentMenuName:
                                routesMap.groupService.serviceManage,
                        },
                        component: () =>
                            import(
                                "@/views/pages/group-service/service-manage/service-detail/index.vue"
                            ),
                    },
                    {
                        path: "item",
                        name: routesMap.groupService.serviceManageDetail.item,
                        meta: {
                            title: "服务内容项详情",
                            parentMenuName:
                                routesMap.groupService.serviceManage,
                        },
                        component: () =>
                            import(
                                "@/views/pages/group-service/service-manage/service-detail/item.vue"
                            ),
                    },
                    {
                        path: "task",
                        name: routesMap.groupService.serviceManageDetail
                            .taskRoot,
                        meta: {
                            title: "任务详情",
                            parentMenuName:
                                routesMap.groupService.serviceManage,
                        },
                        component: RouteView,
                        children: [
                            {
                                path: "",
                                name: routesMap.groupService.serviceManageDetail
                                    .task,
                                meta: {
                                    title: "任务详情",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/index.vue"
                                    ),
                            },
                            {
                                path: "collect",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.collect,
                                meta: {
                                    title: "编辑任务-采集工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-collect/index.vue"
                                    ),
                            },
                            {
                                path: "job",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.job,
                                meta: {
                                    title: "编辑任务-岗位推荐工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-job/index.vue"
                                    ),
                            },
                            {
                                path: "policy",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.policy,
                                meta: {
                                    title: "编辑任务-政策推荐工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/policy/index.vue"
                                    ),
                            },
                            {
                                path: "question",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.question,
                                meta: {
                                    title: "编辑任务-问卷调查工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-question/index.vue"
                                    ),
                            },
                            {
                                path: "notify",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.notify,
                                meta: {
                                    title: "编辑任务-内容通知工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-notify/index.vue"
                                    ),
                            },
                            {
                                path: "object",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.serve_target_count,
                                meta: {
                                    title: "服务对象",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/object/index.vue"
                                    ),
                            },
                            {
                                path: "served",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.served_target_count,
                                meta: {
                                    title: "已服务对象",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/object-served/index.vue"
                                    ),
                            },
                            {
                                path: "serve-record",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.serve_record_count,
                                meta: {
                                    title: "已服务人次",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/serve-record/index.vue"
                                    ),
                            },
                            {
                                path: "questionnaire",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.answered_questionnaire_count,
                                meta: {
                                    title: "已填写问卷",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/questionnaire/index.vue"
                                    ),
                            },
                            {
                                path: "apply_job_count",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.apply_job_count,
                                meta: {
                                    title: "已申请岗位",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/job/index.vue"
                                    ),
                            },
                        ],
                    },
                ],
            },
            {
                path: "task-manage",
                name: routesMap.groupService.taskManage,
                meta: {
                    title: "重点人群任务管理",
                    role: "/tablelist/serve_task/manage_for_operate",
                },
                component: () =>
                    import("@/views/pages/group-service/task-manage/index.vue"),
            },
        ],
    },
    // 资金使用计划和内审
    {
        path: "/financial",
        redirect: "financial/check-list",
        name: routesMap.financial.financial,
        meta: {
            title: "资金使用计划和内审",
            svgIcon: require("@/assets/icon/menu/financial.svg"),
        },
        component: layout,
        children: [
            {
                path: "check-list",
                redirect: "check-list/policy-basis",
                name: routesMap.financial.toolkit.toolkit,
                meta: {
                    title: "规则库与工具包",
                },
                component: RouteView,
                children: [
                    {
                        path: "policy-basis",
                        name: routesMap.financial.toolkit.policyBasis,
                        meta: {
                            title: "政策依据管理",
                            role: "/tablelist/policy_record/for_operate",
                        },
                        component: () =>
                            import(
                                "@/views/pages/financial/toolkit/policy-basis/index.vue"
                            ),
                    },
                    {
                        path: "policy-basis-add",
                        name: routesMap.financial.toolkit.policyBasisAdd,
                        meta: {
                            title: "新增政策依据",
                            hidden: true,
                            parentMenuName:
                                routesMap.financial.toolkit.policyBasis,
                        },
                        component: () =>
                            import(
                                "@/views/pages/financial/toolkit/policy-basis/add.vue"
                            ),
                    },
                    {
                        path: "policy-basis-detail",
                        name: routesMap.financial.toolkit.policyBasisDetail,
                        meta: {
                            title: "政策依据详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.financial.toolkit.policyBasis,
                        },
                        component: () =>
                            import(
                                "@/views/pages/financial/toolkit/policy-basis/detail.vue"
                            ),
                    },
                    {
                        path: "check-list",
                        name: routesMap.financial.toolkit.checkList,
                        meta: {
                            title: "自检清单管理",
                            role: "/tablelist/project_self_check/for_operate",
                        },
                        component: () =>
                            import(
                                "@/views/pages/financial/toolkit/check-list/index.vue"
                            ),
                    },
                    {
                        path: "check-list-add",
                        name: routesMap.financial.toolkit.checkListAdd,
                        meta: {
                            title: "新建自检清单",
                            hidden: true,
                            parentMenuName:
                                routesMap.financial.toolkit.checkList,
                        },
                        component: () =>
                            import(
                                "@/views/pages/financial/toolkit/check-list/add.vue"
                            ),
                    },
                    {
                        path: "check-list-detail",
                        name: routesMap.financial.toolkit.checkListDetail,
                        meta: {
                            title: "项目自检清单详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.financial.toolkit.checkList,
                        },
                        component: () =>
                            import(
                                "@/views/pages/financial/toolkit/check-list/detail.vue"
                            ),
                    },
                ],
            },
            {
                path: "funds-plan",
                name: routesMap.financial.fundsPlan,
                meta: {
                    title: "资金使用计划",
                    role: "/tablelist/finance_plan/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/funds-plan/index.vue"),
            },
            {
                path: "funds-plan-add",
                name: routesMap.financial.fundsPlanAdd,
                meta: {
                    title: "创建资金使用计划",
                    hidden: true,
                    parentMenuName: routesMap.financial.fundsPlan,
                },
                component: () =>
                    import("@/views/pages/financial/funds-plan/add.vue"),
            },
            {
                path: "funds-plan-detail",
                name: routesMap.financial.fundsPlanDetail,
                meta: {
                    title: "资金使用计划详情",
                    hidden: true,
                    parentMenuName: routesMap.financial.fundsPlan,
                },
                component: () =>
                    import("@/views/pages/financial/funds-plan/detail.vue"),
            },
            {
                path: "funds-record",
                name: routesMap.financial.fundsRecord,
                meta: {
                    title: "资金使用记录",
                    role: "/tablelist/finance_record/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/funds-record/index.vue"),
            },
            {
                path: "funds-record-add",
                name: routesMap.financial.fundsRecordAdd,
                meta: {
                    title: "创建资金使用记录",
                    hidden: true,
                    parentMenuName: routesMap.financial.fundsRecord,
                },
                component: () =>
                    import("@/views/pages/financial/funds-record/add.vue"),
            },
            {
                path: "funds-record-detail",
                name: routesMap.financial.fundsRecordDetail,
                meta: {
                    title: "资金使用记录详情",
                    hidden: true,
                    parentMenuName: routesMap.financial.fundsRecord,
                },
                component: () =>
                    import("@/views/pages/financial/funds-record/detail.vue"),
            },
            {
                path: "manage",
                name: routesMap.financial.manage,
                meta: {
                    title: "资金内控项目类型管理",
                    // role: "/tablelist/finance_record/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/manage/index.vue"),
            },
            {
                path: "manage-detail",
                name: routesMap.financial.manageDetail,
                meta: {
                    title: "项目类型详情",
                    hidden: true,
                    parentMenuName: routesMap.financial.manage,
                    // role: "/tablelist/finance_record/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/manage/detail.vue"),
            },
            {
                path: "manage-add",
                name: routesMap.financial.manageAdd,
                meta: {
                    title: "创建项目类型",
                    hidden: true,
                    parentMenuName: routesMap.financial.manage,
                    // role: "/tablelist/finance_record/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/manage/add.vue"),
            },
            {
                path: "funds",
                name: routesMap.financial.funds,
                meta: {
                    title: "资金内控管理",
                    // role: "/tablelist/finance_record/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/funds/index.vue"),
            },
            {
                path: "funds-detail",
                name: routesMap.financial.fundsDetail,
                meta: {
                    title: "项目详情",
                    hidden: true,
                    parentMenuName: routesMap.financial.funds,
                    // role: "/tablelist/finance_record/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/funds/detail.vue"),
            },
            {
                path: "funds-add",
                name: routesMap.financial.fundsAdd,
                meta: {
                    title: "创建项目",
                    hidden: true,
                    parentMenuName: routesMap.financial.funds,
                    // role: "/tablelist/finance_record/for_operate",
                },
                component: () =>
                    import("@/views/pages/financial/funds/add.vue"),
            },
        ],
    },
    // 就业工作看板
    {
        path: "/job-boards",
        redirect: "job-boards/index",
        name: routesMap.jobBoards.root,
        meta: {
            title: "就业工作看板",
            svgIcon: require("@/assets/icon/menu/jobBoards.svg"),
        },
        component: layout,
        children: [
            {
                path: "index",
                name: routesMap.jobBoards.jobBoards,
                meta: {
                    title: "就业工作看板",
                    role: "/tablelist/data_work_board",
                },
                component: () => import("@/views/pages/job-boards/index.vue"),
            },
        ],
    },
    // 数据仓库及核查工具
    {
        path: "/data-storage",
        redirect: "data-storage/resource-manage",
        name: routesMap.dataStorage.dataStorage,
        meta: {
            title: isQj ? "数据仓库管理" : "数据仓库及核查工具",
            svgIcon: require("@/assets/icon/menu/dataStorage.svg"),
        },
        component: layout,
        children: [
            // {
            //     path: "resource-manage",
            //     name: routesMap.dataStorage.resourceManage,
            //     meta: {
            //         title: "数据资源库列表",
            //         role: "/tablelist/xg_resource_repository/manage",
            //     },
            //     component: () =>
            //         import(
            //             "@/views/pages/data-storage/data-resource-manage/index.vue"
            //         ),
            // },
            {
                path: "resource-manage",
                name: routesMap.dataStorage.collectManage,
                meta: {
                    title: "数据表管理",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-collect-manage/index.vue"
                    ),
            },
            {
                path: "collect-detail",
                name: routesMap.dataStorage.collectDetailManage,
                meta: {
                    title: "数据表详情",
                    parentMenuName: routesMap.dataStorage.collectManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-collect-manage/detail.vue"
                    ),
            },
            {
                path: "resource-manage-add",
                name: routesMap.dataStorage.resourceManageAdd,
                meta: {
                    title: "创建/修改资源库",
                    parentMenuName: routesMap.dataStorage.resourceManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-resource-manage/add.vue"
                    ),
            },
            {
                path: "resource-manage-detail",
                name: routesMap.dataStorage.resourceManageDetail,
                meta: {
                    title: "数据资源库详情",
                    parentMenuName: routesMap.dataStorage.resourceManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-resource-manage/detail.vue"
                    ),
            },
            {
                path: "resource-manage-add-item",
                name: routesMap.dataStorage.resourceManageAddItem,
                meta: {
                    title: "新增/修改数据项",
                    parentMenuName: routesMap.dataStorage.resourceManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-resource-manage/add-item.vue"
                    ),
            },
            {
                path: "comparison-manage",
                name: routesMap.dataStorage.comparisonManage,
                meta: {
                    title: "比对/查询功能列表",
                    role: "/tablelist/xg_resource_function/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-comparison-manage/index.vue"
                    ),
            },
            {
                path: "comparison-manage-add",
                name: routesMap.dataStorage.comparisonManageAdd,
                meta: {
                    title: "新增功能",
                    parentMenuName: routesMap.dataStorage.comparisonManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-comparison-manage/add.vue"
                    ),
            },
            {
                path: "comparison-manage-detail",
                name: routesMap.dataStorage.comparisonManageDetail,
                meta: {
                    title: "比对/查询功能详情",
                    parentMenuName: routesMap.dataStorage.comparisonManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-comparison-manage/detail.vue"
                    ),
            },
            // 暂时不要
            // {
            //     path: "data-apply-form",
            //     name: routesMap.dataStorage.applyForm,
            //     meta: {
            //         title: "数据使用申请单",
            //     },
            //     component: () =>
            //         import(
            //             "@/views/pages/data-storage/data-apply-form/index.vue"
            //         ),
            // },
            {
                path: "data-apply-add",
                name: routesMap.dataStorage.applyFormAdd,
                meta: {
                    title: "新建数据使用申请单",
                    parentMenuName: routesMap.dataStorage.applyForm,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-apply-form/add.vue"
                    ),
            },
            {
                path: "data-apply-detail",
                name: routesMap.dataStorage.applyFormDetail,
                meta: {
                    title: "失业补贴人员比对申请",
                    parentMenuName: routesMap.dataStorage.applyForm,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-apply-form/detail.vue"
                    ),
            },
            {
                path: "query-usage-record",
                name: routesMap.dataStorage.queryUsageRecord,
                meta: {
                    title: "比对/查询使用记录",
                    role: "/tablelist/xg_resource_function_use/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/query-usage-record/index.vue"
                    ),
            },
            {
                path: "query-usage-record-detail",
                name: routesMap.dataStorage.queryUsageRecordDetail,
                meta: {
                    title: "使用记录详情",
                    parentMenuName: routesMap.dataStorage.queryUsageRecord,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/query-usage-record/detail.vue"
                    ),
            },
        ],
    },
    // 政策资讯管理与宣传推广
    {
        path: "/policy",
        redirect: "policy/manage",
        name: routesMap.policy.manage,
        meta: {
            title: [EnvProject.鄂州项目].includes(config.envProject)
                ? "政策管理"
                : "政策资讯与业务管理",
            svgIcon: require("@/assets/icon/menu/policy.svg"),
        },
        component: layout,
        children: [
            {
                path: "manage",
                name: routesMap.policy.manage,
                meta: {
                    title: [EnvProject.鄂州项目].includes(config.envProject)
                        ? "政策管理"
                        : "就业资讯管理",
                    role: "/tablelist/g_policy_advice",
                },
                component: () =>
                    import("@/views/pages/policy/manage/index.vue"),
            },
            {
                path: "create",
                name: routesMap.policy.create,
                meta: {
                    parentMenuName: routesMap.policy.manage,
                    title: [EnvProject.鄂州项目].includes(config.envProject)
                        ? "政策文章"
                        : "政策文章",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/policy/manage/create/index.vue"),
            },
            {
                path: "update-pc-policy",
                name: routesMap.policy.updatePcPolicy,
                meta: {
                    parentMenuName: routesMap.policy.manage,
                    title: "就业创业政策汇编",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/policy/manage/create/update-pc-policy.vue"
                    ),
            },
            {
                path: "publish-policy-list",
                name: routesMap.publishPolicy.list,
                meta: {
                    title: "政策管理",
                    role: "/tablelist/policy_form/manage_list",
                },
                component: () =>
                    import("@/views/pages/publish-policy/list.vue"),
            },
            {
                path: "publish-policy-create",
                name: routesMap.publishPolicy.create,
                meta: {
                    title: "创建政策",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/create.vue"),
            },
            {
                path: "publish-policy-form-list",
                name: routesMap.publishPolicy.formList,
                meta: {
                    title: "政策表单维护",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/form-list.vue"),
            },
            {
                path: "publish-policy-form-detail",
                name: routesMap.publishPolicy.formDetail,
                meta: {
                    title: "表单详情",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/form-detail.vue"),
            },
            {
                path: "publish-policy-policy-detail",
                name: routesMap.publishPolicy.policyDetail,
                meta: {
                    title: "政策详情",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/detail.vue"),
            },
            {
                path: "policy-apply-record",
                name: routesMap.policyApplyRecord.list,
                meta: {
                    title: "政策申报管理",
                    role: "/tablelist/policy_form_apply/apply_manage",
                },
                component: () =>
                    import("@/views/pages/policy-apply-record/index.vue"),
            },
            {
                path: "policy-apply-record-detail",
                name: routesMap.policyApplyRecord.detail,
                meta: {
                    title: "申报内容",
                    hidden: true,
                    parentMenuName: routesMap.policyApplyRecord.list,
                },
                component: () =>
                    import("@/views/pages/policy-apply-record/detail.vue"),
            },
            {
                path: "subsidy",
                name: routesMap.employmentManage.subsidy,
                meta: {
                    title: "企业业务管理",
                    role: "/tablelist/company_subsidy/for_operate",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/employment-manage/subsidy/index.vue"),
            },
            {
                path: "apply",
                name: routesMap.employmentManage.apply,
                meta: {
                    title: "企业业务申请",
                    role: "/tablelist/company_subsidy_apply",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/employment-manage/apply/index.vue"),
            },
            {
                path: "subsidy-detail",
                name: routesMap.employmentManage.subsidyDetail,
                meta: {
                    title: "补贴业务详情",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.subsidy,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/subsidy/detail.vue"
                    ),
            },
            {
                path: "manage-detail",
                name: routesMap.policy.manageDetail,
                meta: {
                    title: "就业资讯管理详情",
                    hidden: true,
                    parentMenuName: routesMap.policy.manage,
                },
                component: () =>
                    import("@/views/pages/policy/manage/detail.vue"),
            },
            {
                path: "preferential-policy",
                name: routesMap.preferentialPolicy.manage,
                meta: {
                    title: "惠民政策管理",
                    role: "/tablelist/policy/for_operate",
                    // hidden: [EnvProject.荆州项目].includes(config.envProject),
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/preferential-policy/index.vue"),
            },
            {
                path: "create-preferential-policy",
                name: routesMap.preferentialPolicy.create,
                meta: {
                    parentMenuName: routesMap.preferentialPolicy.manage,
                    title: "创建惠民政策",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/preferential-policy/create/index.vue"
                    ),
            },
            {
                path: "preferential-policy-detail",
                name: routesMap.preferentialPolicy.manageDetail,
                meta: {
                    title: "惠民政策详情",
                    hidden: true,
                    parentMenuName: routesMap.preferentialPolicy.manage,
                },
                component: () =>
                    import("@/views/pages/preferential-policy/detail.vue"),
            },
            {
                path: "apply-preferential-policy",
                name: routesMap.preferentialPolicy.apply,
                meta: {
                    title: "政策办理申请",
                    role: "/tablelist/policy_apply/for_operate",
                    hidden: isYD,
                },
                component: () =>
                    import("@/views/pages/preferential-policy/apply/index.vue"),
            },
            {
                path: "gzwBanner",
                name: routesMap.base.gzwBanner,
                meta: {
                    title: "官网banner管理",
                    role: "/base/gzwBanner",
                    hidden: config.envProject !== EnvProject.荆州项目,
                },
                component: () =>
                    import("@/views/pages/base/mp/gzw/banner/index.vue"),
            },
            {
                path: "gzwGanggao",
                name: routesMap.base.gzwGanggao,
                meta: {
                    title: "官网广告位管理",
                    role: "/base/gzwGuanggao",
                    hidden: config.envProject !== EnvProject.荆州项目,
                },
                component: () =>
                    import("@/views/pages/base/mp/gzw/ganggao/index.vue"),
            },
        ],
    },
    // 基础数据管理
    {
        path: "/base",
        redirect: "base/org",
        name: routesMap.base.root,
        meta: {
            title: "基础数据管理",
            svgIcon: require("@/assets/icon/menu/base.svg"),
        },
        component: layout,
        children: [
            {
                path: "org",
                name: routesMap.base.org.list,
                meta: {
                    title: "组织架构",
                    role: "/tablelist/xg_organization/manager",
                },
                component: () => import("@/views/pages/base/org/index.vue"),
            },
            {
                path: "role",
                name: routesMap.base.role.list,
                meta: {
                    title: "角色管理",
                    role: "/tablelist/xg_user_role/manager",
                },
                component: () => import("@/views/pages/base/role/index.vue"),
            },
            {
                path: "roleDetail",
                name: routesMap.base.role.detail,
                meta: {
                    title: "角色详情",
                    hidden: true,
                    parentMenuName: routesMap.base.role.list,
                },
                component: () => import("@/views/pages/base/role/detail.vue"),
            },
            {
                path: "user",
                name: routesMap.base.user.list,
                meta: {
                    title: "用户管理",
                    role: "/tablelist/xg_login_user/manager",
                },
                component: () => import("@/views/pages/base/user/index.vue"),
            },
            // {
            //     path: "grid",
            //     name: routesMap.base.grid.list,
            //     meta: {
            //         title: "网格社工管理",
            //     },
            //     component: () => import("@/views/pages/base/grid/index.vue"),
            // },
            {
                path: "mp",
                name: routesMap.base.mp.list,
                meta: {
                    title: "客户端管理",
                    role: "/tablelist/g_wx_app/manager",
                },
                component: () => import("@/views/pages/base/mp/index.vue"),
            },
            {
                path: "grid-user-manage",
                name: routesMap.base.regionUserManage.manage,
                meta: {
                    title: "采集员管理",
                    role: "/tablelist/grid_user/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/base/region-user-manage/manage/index.vue"
                    ),
            },
            {
                path: "grid-user-manage-add",
                name: routesMap.base.regionUserManage.manageAdd,
                meta: {
                    title: "添加采集员",
                    hidden: true,
                    parentMenuName: routesMap.base.regionUserManage.manage,
                },
                component: () =>
                    import(
                        "@/views/pages/base/region-user-manage/manage/add.vue"
                    ),
            },
            {
                path: "region-user-manage",
                name: routesMap.base.regionUserManage.list,
                meta: {
                    title: "采集员区域管理",
                    role: "/tablelist/grid_user_service_region/manage",
                },
                component: () =>
                    import("@/views/pages/base/region-user-manage/index.vue"),
            },
            {
                path: "region-user-manage-add",
                name: routesMap.base.regionUserManage.add,
                meta: {
                    title: "添加采集员",
                    hidden: true,
                    parentMenuName: routesMap.base.regionUserManage.list,
                },
                component: () =>
                    import("@/views/pages/base/region-user-manage/add.vue"),
            },
            {
                path: "sms-template",
                name: routesMap.base.smsTemplate.list,
                meta: {
                    title: "短信模板",
                    role: "/tablelist/sms_template/for_operate",
                },
                component: () =>
                    import("@/views/pages/base/sms-template/index.vue"),
            },
            {
                path: "sms-send",
                name: routesMap.base.smsTemplate.send,
                meta: {
                    title: "短信发送任务",
                    role: "/tablelist/sms_send_history/for_operate_v2",
                },
                component: () =>
                    import("@/views/pages/base/sms-template/send.vue"),
            },
            {
                path: "sms-template-add",
                name: routesMap.base.smsTemplate.add,
                meta: {
                    title: "添加短信模板",
                    hidden: true,
                    parentMenuName: routesMap.base.smsTemplate.list,
                },
                component: () =>
                    import("@/views/pages/base/sms-template/add.vue"),
            },
            {
                path: "operate-log",
                name: routesMap.base.operateLog,
                meta: {
                    title: "操作日志",
                    role: "/tablelist/work_flow_log/manager_log",
                },
                component: () => import("@/views/pages/base/operate/index.vue"),
            },
            {
                path: "mp-detail",
                name: routesMap.base.mp.listDetail,
                meta: {
                    title: "客户端详情",
                    parentMenuName: routesMap.base.mp.list,
                    hidden: true,
                },
                component: () => import("@/views/pages/base/mp/detail.vue"),
            },
            {
                path: "business",
                name: routesMap.base.mp.business,
                meta: {
                    title: "业务应用管理",
                    parentMenuName: routesMap.base.mp.list,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/base/mp/business-app/index.vue"),
            },
            {
                path: "tools",
                name: routesMap.base.mp.tools,
                meta: {
                    title: "常用工具管理",
                    parentMenuName: routesMap.base.mp.list,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/base/mp/tools/index.vue"),
            },
            {
                path: "group-entrance",
                name: routesMap.base.mp.groupEntrance,
                meta: {
                    title: "推荐社群入口管理",
                    parentMenuName: routesMap.base.mp.list,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/base/mp/group-entrance/index.vue"),
            },
            {
                path: "advertising-position",
                name: routesMap.base.mp.advertisingPosition,
                meta: {
                    title: "广告位管理",
                    parentMenuName: routesMap.base.mp.list,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/base/mp/advertising-position/index.vue"
                    ),
            },
            {
                path: "sms",
                name: routesMap.base.sms.list,
                meta: {
                    title: "短信发送记录",
                    role: "/tablelist/sms_send_history/for_operate",
                },
                component: () => import("@/views/pages/base/sms/index.vue"),
            },
            {
                path: "sms-add",
                name: routesMap.base.sms.add,
                meta: {
                    title: "新增短信发送",
                    parentMenuName: routesMap.base.sms.list,
                    hidden: true,
                },
                component: () => import("@/views/pages/base/sms/add.vue"),
            },
            {
                path: "sms-detail",
                name: routesMap.base.sms.detail,
                meta: {
                    title: "短信发送详情",
                    parentMenuName: routesMap.base.sms.list,
                    hidden: true,
                },
                component: () => import("@/views/pages/base/sms/detail.vue"),
            },
            {
                path: "/questionnaire-design",
                name: routesMap.questionnaireDesign.index,
                meta: {
                    title: "问卷管理",
                    role: "/tablelist/general_questionnaire",
                    hidden: isYD,
                },
                component: () =>
                    import("@/views/pages/base/questionnaire-design/index.vue"),
            },
            {
                path: "/questionnaire-design/detail",
                name: routesMap.questionnaireDesign.detail,
                meta: {
                    title: "问卷详情",
                    hidden: true,
                    parentMenuName: routesMap.questionnaireDesign.index,
                },
                component: () =>
                    import(
                        "@/views/pages/base/questionnaire-design/detail.vue"
                    ),
            },
            {
                path: "register-user-index",
                name: routesMap.base.registerUser.list,
                meta: {
                    title: "注册用户管理",
                    role: "/tablelist/system_user",
                },
                component: () =>
                    import("@/views/pages/base/register-user/list.vue"),
            },
            {
                path: "register-user-detail",
                name: routesMap.base.registerUser.detail,
                meta: {
                    title: "用户详情",
                    parentMenuName: routesMap.base.registerUser.list,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/base/register-user/detail.vue"),
            },
            {
                path: "user-agreement",
                name: routesMap.base.userAgreement.list,
                meta: {
                    title: "协议管理",
                    role: "/tablelist/user_agreement_content/user_content_back",
                    hidden: !isDev,
                },
                component: () =>
                    import("@/views/pages/base/user-agreement/index.vue"),
            },
            {
                path: "user-agreement-add",
                name: routesMap.base.userAgreement.add,
                meta: {
                    title: "新增用户协议",
                    hidden: true,
                    parentMenuName: routesMap.base.userAgreement.list,
                },
                component: () =>
                    import("@/views/pages/base/user-agreement/add.vue"),
            },
            {
                path: "automatic",
                name: routesMap.base.automatic,
                meta: {
                    title: "终端信息",
                    role: "/redirect/xg_project/automatic",
                },
                component: () =>
                    import("@/views/pages/base/automatic/index.vue"),
            },
            {
                path: "equipmentManagement",
                name: routesMap.base.equipmentManagement,
                meta: {
                    title: "设备管理",
                    role: "/redirect/xg_project/equipmentManagement",
                },
                component: () =>
                    import("@/views/pages/base/equipmentManagement/index.vue"),
            },
            {
                path: "statisticalMnalysis",
                name: routesMap.base.statisticalMnalysis,
                meta: {
                    title: "统计分析",
                    role: "/redirect/xg_project/statisticalMnalysis",
                },
                component: () =>
                    import("@/views/pages/base/statisticalMnalysis/index.vue"),
            },
        ],
    },
    // 政务微信管理
    {
        path: "/wx",
        redirect: "wx/group",
        name: routesMap.wx.root,
        meta: {
            title: "政务微信管理",
            svgIcon: require("@/assets/icon/menu/wx.svg"),
        },
        component: layout,
        children: [
            {
                path: "statistics",
                name: routesMap.wx.statistics,
                meta: {
                    title: "政务微信运营数据统计",
                    role: "/redirect/xg_project/wx_manage1",
                },
                component: () =>
                    import("@/views/pages/wx/statistics/index.vue"),
            },
            {
                path: "group-statistics",
                name: routesMap.wx.groupStatistics,
                meta: {
                    title: "社群人数统计",
                    role: "wecom_core/client_api/fetch_group_statistics",
                },
                component: () =>
                    import("@/views/pages/wx/group/statistic/index.vue"),
            },
            {
                path: "statistics-list",
                name: routesMap.wx.statisticsList,
                meta: {
                    title: "协管员列表",
                    hidden: true,
                    parentMenuName: routesMap.wx.statistics,
                },
                component: () => import("@/views/pages/wx/statistics/list.vue"),
            },
            {
                path: "wx-friend-list",
                name: routesMap.wx.wxFriendList,
                meta: {
                    title: "政务微信好友列表",
                    hidden: true,
                    parentMenuName: routesMap.wx.statistics,
                },
                component: () =>
                    import("@/views/pages/wx/statistics/wx-friend-list.vue"),
            },
            {
                path: "group",
                name: routesMap.wx.group,
                meta: {
                    title: "政务微信群管理",
                    role: "/tablelist/wecom_group_chat/list_for_workbench",
                },
                component: () => import("@/views/pages/wx/group/index.vue"),
            },
            {
                path: "group-detail",
                name: routesMap.wx.groupDetail,
                meta: {
                    title: "政务微信群管理详情",
                    hidden: true,
                    parentMenuName: routesMap.wx.group,
                },
                component: () => import("@/views/pages/wx/group/detail.vue"),
            },
            {
                path: "friend",
                name: routesMap.wx.friend,
                meta: {
                    title: "居民微信管理",
                    role: "/tablelist/wecom_external_contact/list_for_workbench",
                },
                component: () => import("@/views/pages/wx/friend/index.vue"),
            },
            {
                path: "friend-detail",
                name: routesMap.wx.friendDetail,
                meta: {
                    title: "居民微信管理详情",
                    hidden: true,
                    parentMenuName: routesMap.wx.friend,
                },
                component: () => import("@/views/pages/wx/friend/detail.vue"),
            },
            {
                path: "assistant",
                name: routesMap.wx.assistant,
                meta: {
                    title: "政务微信内部成员管理",
                    role: "/tablelist/wecom_follow_user/list_for_workbench",
                },
                component: () => import("@/views/pages/wx/assistant/index.vue"),
            },
        ],
    },
    {
        path: "/messageNotify",
        redirect: "messageNotify/message",
        name: routesMap.messageNotify.root,
        meta: {
            title: "消息通知管理",
            showOneChildren: true,
            role: "/tablelist/serve_task/content_manager",
            hidden: isYD,
        },
        component: layout,
        children: [
            {
                path: "message",
                name: routesMap.messageNotify.message,
                meta: {
                    title: "消息通知管理",
                    parentMenuName: routesMap.messageNotify.root,
                    role: "/tablelist/serve_task/content_manager",
                    svgIcon: require("@/assets/icon/menu/message.svg"),
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/task-manage-4-message/index.vue"
                    ),
            },
            {
                path: "detail",
                name: routesMap.messageNotify.detail,
                meta: {
                    title: "消息通知",
                    parentMenuName: routesMap.messageNotify.root,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/task-manage-4-message/detail.vue"
                    ),
            },
        ],
    },
    // 数采任务管理
    {
        path: "/collect-task-manage",
        redirect: "collect-task-manage/data-statistics",
        name: routesMap.collectTaskManage.root,
        meta: {
            title: "数采任务管理",
            svgIcon: require("@/assets/icon/menu/collectTaskManage.svg"),
        },
        component: layout,
        children: [
            {
                path: "data-statistics",
                name: routesMap.collectTaskManage.dataStatistics,
                meta: {
                    title: "工作台",
                    role: "/workbench",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/data-statistics/index.vue"
                    ),
            },
            {
                path: "business-manage",
                name: routesMap.collectTaskManage.businessManage,
                meta: {
                    title: "项目列表",
                    role: "/tablelist/collect_project/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/business-manage/index.vue"
                    ),
            },
            {
                path: "business-manage-create",
                name: routesMap.collectTaskManage.businessManageCreate,
                meta: {
                    title: "项目创建",
                    parentMenuName: routesMap.collectTaskManage.businessManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/business-manage/create.vue"
                    ),
            },
            {
                path: "business-manage-detail",
                name: routesMap.collectTaskManage.businessManageDetail,
                meta: {
                    title: "项目详情",
                    parentMenuName: routesMap.collectTaskManage.businessManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/business-manage/detail.vue"
                    ),
            },
            {
                path: "task-info-list",
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo.list,
                meta: {
                    title: "任务列表",
                    role: "/tablelist/collect_task/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/task-info/index.vue"
                    ),
            },
            {
                path: "create-task",
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .create,
                meta: {
                    title: "项目创建",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.taskInfoManage.taskInfo
                            .list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/task-info/create-task.vue"
                    ),
            },

            {
                path: "record-list-detail",
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .recordListDetail,
                meta: {
                    parentMenuName: routesMap.collectTaskManage.businessManage,
                    title: "采集记录详情",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/task-info/record-list-detail.vue"
                    ),
            },
            {
                path: "record-list-detail-import",
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .detailImport,
                meta: {
                    parentMenuName: routesMap.collectTaskManage.businessManage,
                    title: "导入中心",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/task-info/import-center/index.vue"
                    ),
            },
            // {
            //     path: "collect-info-manage",
            //     name: routesMap.collectTaskManage.taskInfoManage
            //         .collectInfoManage.list,
            //     meta: {
            //         title: "采集信息管理",
            //     },
            //     component: () =>
            //         import(
            //             "@/views/pages/collect-task-manage/task-info-manage/collect-info-manage/index.vue"
            //         ),
            // },
            {
                path: "collect-info-manage-detail",
                name: routesMap.collectTaskManage.taskInfoManage
                    .collectInfoManage.detail,
                meta: {
                    parentMenuName: routesMap.collectTaskManage.businessManage,
                    title: "采集信息管理详情",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/collect-info-manage/detail.vue"
                    ),
            },

            {
                path: "manage-collect-list",
                name: routesMap.collectTaskManage.taskInfoManage.manageCollect
                    .list,
                meta: {
                    title: "采集任务",
                    role: "/tablelist/collect_task/manage_collect",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/manage-collect/index.vue"
                    ),
            },

            {
                path: "manage-collect-detail",
                name: routesMap.collectTaskManage.taskInfoManage.manageCollect
                    .detail,
                meta: {
                    title: "采集任务详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.taskInfoManage.manageCollect
                            .list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/manage-collect/detail.vue"
                    ),
            },
            {
                path: "collect-detail-import",
                name: routesMap.collectTaskManage.taskInfoManage.manageCollect
                    .detailImport,
                meta: {
                    parentMenuName:
                        routesMap.collectTaskManage.taskInfoManage.manageCollect
                            .list,
                    title: "导入中心",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/manage-collect/import-center/index.vue"
                    ),
            },
            {
                path: "task-info-detail",
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .detail,
                meta: {
                    parentMenuName:
                        routesMap.collectTaskManage.taskInfoManage.taskInfo
                            .list,
                    title: "任务信息",
                    role: "/tablelist/collect_task/manage",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/task-info/detail/index.vue"
                    ),
            },
            {
                path: "allocation",
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .allocation,
                meta: {
                    parentMenuName: routesMap.collectTaskManage.businessManage,
                    title: "分配任务",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/task-info-manage/task-info/detail/allocation.vue"
                    ),
            },

            {
                path: "export-task-manage",
                name: routesMap.collectTaskManage.exportTaskManage,
                meta: {
                    title: "导出任务管理",
                    role: "/tablelist/collect_export_task/manage",
                    hidden: isYD,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/export-task-manage/index.vue"
                    ),
            },

            {
                path: "export-task-manage-filter",
                name: routesMap.collectTaskManage.exportTaskManageFilter,
                meta: {
                    title: "筛选条件",
                    parentMenuName:
                        routesMap.collectTaskManage.exportTaskManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/export-task-manage/filter.vue"
                    ),
            },
        ],
    },
    // 劳动力信息库管理
    {
        path: "/labor-info-base-manage",
        redirect: "labor-info-base-manage/labor-info-base",
        name: routesMap.laborInfoBaseManage.root,
        meta: {
            title: "劳动力信息库管理",
            svgIcon: require("@/assets/icon/menu/laborInfoBase.svg"),
        },
        component: layout,
        children: [
            {
                path: "labor-info-base",
                name: routesMap.laborInfoBaseManage.laborInfoBase,
                meta: {
                    title: "劳动力信息库",
                    role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/labor-info-base/index.vue"
                    ),
            },
            {
                path: "labor-info-base-filter",
                name: routesMap.laborInfoBaseManage.laborInfoBaseFilter,
                meta: {
                    title: "劳动力信息库详情",
                    hidden: true,
                    parentMenuName: routesMap.laborInfoBaseManage.laborInfoBase,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/labor-info-base/filter.vue"
                    ),
            },
            {
                path: "labor-info-base-detail",
                name: routesMap.laborInfoBaseManage.laborInfoBaseDetail,
                meta: {
                    title: "劳动力信息库详情",
                    hidden: true,
                    parentMenuName: routesMap.laborInfoBaseManage.laborInfoBase,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/labor-info-base/detail.vue"
                    ),
            },
            {
                path: "manage-back-home",
                name: routesMap.laborInfoBaseManage.manageBackHome,
                meta: {
                    title: "返乡人员信息库",
                    role: "/tablelist/collect_user_profile/manage_back_home",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/manage-back-home/index.vue"
                    ),
            },
            {
                path: "manage-back-home-filter",
                name: routesMap.laborInfoBaseManage.manageBackHomeFilter,
                meta: {
                    title: "返乡人员信息查询",
                    hidden: true,
                    parentMenuName:
                        routesMap.laborInfoBaseManage.manageBackHome,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/manage-back-home/filter.vue"
                    ),
            },
            {
                path: "manage-back-home-detail",
                name: routesMap.laborInfoBaseManage.manageBackHomeDetail,
                meta: {
                    title: "返乡人员信息库详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.laborInfoBaseManage.manageBackHome,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/manage-back-home/detail.vue"
                    ),
            },
            {
                path: "manage-student",
                name: routesMap.laborInfoBaseManage.manageStudent,
                meta: {
                    title: "高校毕业生信息库",
                    role: "/tablelist/collect_user_profile/manage_student",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/manage-student/index.vue"
                    ),
            },
            {
                path: "manage-student-filter",
                name: routesMap.laborInfoBaseManage.manageStudentFilter,
                meta: {
                    title: "高校毕业生信息库查询",
                    hidden: true,
                    parentMenuName: routesMap.laborInfoBaseManage.manageStudent,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/manage-student/filter.vue"
                    ),
            },
            {
                path: "manage-student-detail",
                name: routesMap.laborInfoBaseManage.manageStudentDetail,
                meta: {
                    title: "高校毕业生信息库详情",
                    hidden: true,
                    parentMenuName: routesMap.laborInfoBaseManage.manageStudent,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/labor-info-base-manage/manage-student/detail.vue"
                    ),
            },
        ],
    },
    {
        path: "/show-big-screen-report-data",
        name: routesMap.dataAcquisitionLabour,
        meta: {
            title: "劳动力数据分析大屏",
            svgIcon: require("@/assets/icon/menu/screen.svg"),
            newPage: true,
            role: "/redirect/xg_project/data_show",
            targetName: routesMap.bigScreen.report1,
            hidden: isYD,
        },
    },
    // 报表管理
    {
        path: "/report-manage",
        redirect: "report-manage/report-manage-list",
        name: routesMap.reportManage.root,
        meta: {
            title: "报表管理",
            hidden: ![EnvProject.荆州项目, EnvProject.黄州项目].includes(
                config.envProject
            ),
        },
        component: layout,
        children: [
            {
                path: "report-manage-list",
                name: routesMap.reportManage.list.index,
                meta: {
                    title: "报表模板列表",
                    role: "/tablelist/xg_indicator_group_ref/for_operate",
                },
                component: () =>
                    import("@/views/pages/report-manage/list/index.vue"),
            },
            {
                path: "report-manage-list-detail",
                name: routesMap.reportManage.list.detail,
                meta: {
                    title: "模板详情",
                    hidden: true,
                    parentMenuName: routesMap.reportManage.list.index,
                },
                component: () =>
                    import("@/views/pages/report-manage/list/detail.vue"),
            },
            {
                path: "report-manage-task",
                name: routesMap.reportManage.creator.index,
                meta: {
                    title: "报表管理",
                    role: "/tablelist/xg_indicator_task/for_operate",
                },
                component: () =>
                    import("@/views/pages/report-manage/creator/index.vue"),
            },
            {
                path: "report-manage-task-detail",
                name: routesMap.reportManage.creator.detail,
                meta: {
                    title: "报表详情",
                    hidden: true,
                    parentMenuName: routesMap.reportManage.creator.index,
                },
                component: () =>
                    import("@/views/pages/report-manage/creator/detail.vue"),
            },
            {
                path: "report-manage-target",
                name: routesMap.reportManage.executor.index,
                meta: {
                    title: "报表填报",
                    role: "/tablelist/xg_indicator_task_target/for_operate",
                },
                component: () =>
                    import("@/views/pages/report-manage/executor/index.vue"),
            },
            {
                path: "report-manage-target-detail",
                name: routesMap.reportManage.executor.detail,
                meta: {
                    title: "报表详情",
                    hidden: true,
                    parentMenuName: routesMap.reportManage.executor.index,
                },
                component: () =>
                    import("@/views/pages/report-manage/executor/detail.vue"),
            },
        ],
    },
    {
        path: "/bi",
        redirect: "bi/setting",
        name: routesMap.biSetting.root,
        meta: {
            title: "数据大屏管理",
            showOneChildren: true,
            role: "/redirect/xg_project/data_screen_bi",
        },
        component: layout,
        children: [
            {
                path: "setting",
                name: routesMap.biSetting.setting,
                meta: {
                    title: "数据大屏管理",
                    parentMenuName: routesMap.biSetting.root,
                    role: "/redirect/xg_project/data_screen_bi",
                },
                component: () =>
                    import("@/views/pages/base/big-screen-bi/index.vue"),
            },
        ],
    },
    // 市场主体数据分析大屏
    {
        ...(EnvProject.孝感项目 === config.envProject
            ? {
                  path: "https://jy.xg12333.cn:28090/#/",
                  name: routesMap.dataAcquisition.returnHome,
                  meta: {
                      title: "市场主体数据分析大屏",
                      svgIcon: require("@/assets/icon/menu/screen.svg"),
                      role: "/redirect/xg_project/company_screen",
                  },
              }
            : {
                  path: "/show-big-screen",
                  name: routesMap.dataAcquisition.returnHome,
                  meta: {
                      title: "市场主体数据分析大屏",
                      svgIcon: require("@/assets/icon/menu/screen.svg"),
                      newPage: true,
                      targetName: routesMap.bigScreen.home,
                      hidden: [EnvProject.宜都项目].includes(config.envProject),
                      role: "/redirect/xg_project/company_screen",
                  },
              }),
    },
    {
        path: "/show-big-screen2",
        name: routesMap.dataAcquisition.empBigScreen,
        meta: {
            title: "企业用工监测分析",
            svgIcon: require("@/assets/icon/menu/screen.svg"),
            newPage: true,
            targetPath: `${process.env.BASE_URL}/big-screen/emp`.replace(
                "//",
                "/"
            ),
            role: "/redirect/xg_project/company_task_statistic",
            hidden:
                !isDev &&
                ![EnvProject.荆州项目, EnvProject.黄州项目].includes(
                    config.envProject
                ),
        },
    },
    {
        path: "xg-employment",
        name: routesMap.bigScreen.xgEmployment.screen,
        meta: {
            title: "就业地图大屏",
            newPage: true,
            svgIcon: require("@/assets/icon/menu/screen.svg"),
            targetPath:
                `${process.env.BASE_URL}/big-screen/employment/index`.replace(
                    "//",
                    "/"
                ),
            role: "/redirect/xg_project/device_map_screen",
        },
    },
    {
        path: "/show-big-screen3",
        name: routesMap.bigScreen.qjBigscreen.index,
        meta: {
            title: "惠企政策分析大屏",
            svgIcon: require("@/assets/icon/menu/screen.svg"),
            newPage: true,
            targetPath:
                `${process.env.BASE_URL}/big-screen/policy-company`.replace(
                    "//",
                    "/"
                ),
            hidden:
                true ||
                (!isDev && ![EnvProject.潜江项目].includes(config.envProject)),
        },
    },
    {
        path: "/credit",
        redirect: "credit/aspiration-collect",
        name: routesMap.credit.root,
        meta: {
            title: "信用账户管理",
            svgIcon: require("@/assets/icon/menu/credit.svg"),
        },
        component: layout,
        children: [
            {
                path: "aspiration-collect",
                name: routesMap.labourManage.aspirationCollect,
                meta: {
                    title: "培训意愿收集",
                    role: "/tablelist/user_profile_start_job/list_start_intent",
                    hidden: ![
                        EnvProject.荆州项目,
                        EnvProject.孝感项目,
                        EnvProject.掇刀项目,
                    ].includes(config.envProject),
                },
                component: () =>
                    import(
                        "@/views/pages/labour-manage/aspiration-collect/index.vue"
                    ),
            },
            {
                path: "index",
                name: routesMap.credit.index,
                meta: {
                    title: "培训机构管理",
                    // role: "/tablelist/collect_user_profile/manage",
                    hidden: ![EnvProject.荆州项目].includes(config.envProject),
                },
                component: () => import("@/views/pages/credit/index/index.vue"),
            },
            {
                path: "detail",
                name: routesMap.credit.detail,
                meta: {
                    title: "培训机构管理详情",
                    parentMenuName: routesMap.credit.index,
                    hidden: true,
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/credit/index/detail.vue"),
            },
            {
                path: "accredit",
                name: routesMap.credit.accredit,
                meta: {
                    title: "信用账户授信管理",
                    // role: "/tablelist/collect_user_profile/manage",
                    hidden: ![EnvProject.荆州项目].includes(config.envProject),
                },
                component: () =>
                    import("@/views/pages/credit/accredit/index.vue"),
            },
            {
                path: "https://px.hbggzp.cn/pxdp/index.html?AAB299=421000",
                name: routesMap.credit.bigScreen,
                meta: {
                    title: "培训实时监测",
                    hidden: config.envProject !== EnvProject.荆州项目,
                    // role: "/tablelist/collect_user_profile/manage",
                },
            },
            {
                path: "accredit-detail",
                name: routesMap.credit.accreditDetail,
                meta: {
                    title: "信用账户班级详情",
                    parentMenuName: routesMap.credit.accredit,
                    hidden: true,
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/credit/accredit/detail.vue"),
            },
        ],
    },
    {
        path: "/recruit-company",
        redirect: "recruit-company/index",
        name: routesMap.recruitCompany.root,
        meta: {
            title: "区域企业招聘需求管理",
            svgIcon: require("@/assets/icon/menu/recruit-company.svg"),
            hidden:
                ![EnvProject.荆州项目, EnvProject.黄州项目].includes(
                    config.envProject
                ) || !isDev,
        },
        component: layout,
        children: [
            {
                path: "index",
                name: routesMap.recruitCompany.index,
                meta: {
                    title: "企业招聘需求监测",
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/recruit-company/index/index.vue"),
            },
            {
                path: "company-list",
                name: routesMap.recruitCompany.companyList,
                meta: {
                    title: "企业列表",
                    parentMenuName: routesMap.recruitCompany.index,
                    hidden: true,
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/recruit-company/index/company-list.vue"
                    ),
            },
            {
                path: "company-detail",
                name: routesMap.recruitCompany.companyDetail,
                meta: {
                    title: "企业详情",
                    parentMenuName: routesMap.recruitCompany.index,
                    hidden: true,
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/recruit-company/index/company-detail.vue"
                    ),
            },
            {
                path: "job-list",
                name: routesMap.recruitCompany.jobList,
                meta: {
                    title: "岗位列表",
                    parentMenuName: routesMap.recruitCompany.index,
                    hidden: true,
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/recruit-company/index/job-list.vue"),
            },
            {
                path: "company-index",
                name: routesMap.recruitCompany.companyIndex,
                meta: {
                    title: "企业信息管理",
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/recruit-company/company/index.vue"),
            },
            {
                path: "company-search",
                name: routesMap.recruitCompany.companySearch,
                meta: {
                    title: "企业信息查询",
                    parentMenuName: routesMap.recruitCompany.companyIndex,
                    hidden: true,
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/recruit-company/company/search.vue"),
            },
            {
                path: "manage",
                name: routesMap.recruitCompany.manage,
                meta: {
                    title: "企业开发管理",
                    // role: "/tablelist/collect_user_profile/manage",
                },
                component: () =>
                    import("@/views/pages/recruit-company/manage/index.vue"),
            },
        ],
    },
    {
        path: "/baseService",
        name: routesMap.baseService.root,
        redirect: "/baseService/policy",
        meta: {
            title: "基层服务",
            svgIcon: require("@/assets/icon/menu/base-service.svg"),
            hidden:
                !isDev &&
                ![EnvProject.黄州项目, EnvProject.孝感项目].includes(
                    config.envProject
                ),
        },
        component: layout,
        children: [
            {
                path: "policy",
                name: routesMap.baseService.policy,
                meta: {
                    title: "政策推广服务",
                    role: "/tablelist/xg_company_position_recommend/manage_apply6_list",
                },
                component: () =>
                    import("@/views/pages/baseService/policy/index.vue"),
            },
            {
                path: "policyDetail",
                name: routesMap.baseService.policyDetail,
                meta: {
                    title: "政策推广详情",
                    parentMenuName: routesMap.baseService.policy,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/baseService/policy/detail.vue"),
            },
            {
                path: "jobFair",
                name: routesMap.baseService.jobFair,
                meta: {
                    title: "招聘会推广服务",
                    role: "/tablelist/xg_company_position_recommend/manage_apply6_list",
                    hidden: [EnvProject.孝感项目].includes(config.envProject),
                },
                component: () =>
                    import("@/views/pages/baseService/jobFair/index.vue"),
            },
            {
                path: "jobFairDetail",
                name: routesMap.baseService.jobFairDetail,
                meta: {
                    title: "招聘会推广详情",
                    parentMenuName: routesMap.baseService.jobFair,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/baseService/jobFair/detail.vue"),
            },
            {
                path: "grid",
                name: routesMap.baseService.grid,
                meta: {
                    title: "岗位网格推广",
                    role: "/tablelist/xg_company_position_recommend/manage_apply3_list",
                    hidden: [EnvProject.孝感项目].includes(config.envProject),
                },
                component: () => import("@/views/pages/recruit/grid/hz.vue"),
            },
            {
                path: "grid-detail",
                name: routesMap.baseService.gridDetail,
                meta: {
                    title: "网格推广申请详情",
                    hidden: true,
                    parentMenuName: routesMap.baseService.grid,
                },
                component: () =>
                    import("@/views/pages/recruit/grid/hz-detail.vue"),
            },
        ],
    },
    // 就业地图管理
    {
        path: "/branch-manage",
        redirect: "branch/service",
        name: routesMap.branch.root,
        meta: {
            title: "就业地图管理",
            svgIcon: require("@/assets/icon/menu/baseService.svg"),
            hidden: ![EnvProject.孝感项目, EnvProject.黄州项目].includes(
                config.envProject
            ),
        },
        component: layout,
        children: [
            {
                path: "service",
                name: routesMap.branch.service.index,
                meta: {
                    title: "服务网点管理",
                    role: "/tablelist/server_stage",
                },
                component: () =>
                    import("@/views/pages/branch/service-branch/index.vue"),
            },
            {
                path: "service-add",
                name: routesMap.branch.service.add,
                meta: {
                    title: "新增网点",
                    hidden: true,
                    parentMenuName: routesMap.branch.service.index,
                },
                component: () =>
                    import("@/views/pages/branch/service-branch/create.vue"),
            },
            {
                path: "service-detail",
                name: routesMap.branch.service.detail,
                meta: {
                    title: "网点详情",
                    hidden: true,
                    parentMenuName: routesMap.branch.service.index,
                },
                component: () =>
                    import("@/views/pages/branch/service-branch/detail.vue"),
            },
        ],
    },
]
