<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="titleText"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20">
            <form-builder
                ref="formBuilder"
                label-position="left"
                label-width="100px"
                modelName="dataset_register"
                actionName="insert"
            ></form-builder>
            <div class="u-flex u-m-t-30 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="confirm"
                    class="custom-btn btn u-m-0"
                >
                    保存
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { pageLoading } from "@/views/controller"
    import { sdk } from "@/service"

    @Component({ components: { FormBuilder } })
    export default class DialogAllocation extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: "" })
        private curId!: string

        @Prop({ default: "" })
        private curExceptionTitle!: string

        onOpen() {
            this.init()
        }

        private get titleText() {
            return this.curExceptionTitle
        }

        private init() {
            return buildFormSections({
                sdkModel: "collect_task_order_wait_detail",
                sdkAction: "update_data_type_exception_type",
                select_list: [
                    {
                        id: (this.curId + "") as any,
                        v: 0,
                    },
                ],
                forms: [],
            }).then((r) => {
                this.buildForm(r.forms)
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            this.submitSingle(data)
        }

        private submitSingle(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model("collect_task_order_wait_detail")
                    .action("update_data_type_exception_type")
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: [
                            {
                                id: (this.curId + "") as any,
                                v: 0,
                            },
                        ],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("标记成功！")
                        this.close()
                        this.$emit("refresh")
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .select {
        width: 320px;
    }
    .btn {
        width: 100px;
        height: 36px;
    }
    .create-position {
        ::v-deep .el-form-item__label {
            text-align: left;
            color: #555;
            font-size: 14px;
        }
    }
</style>
