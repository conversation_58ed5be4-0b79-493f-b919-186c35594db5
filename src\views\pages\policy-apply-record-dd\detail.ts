import { renDesensitizationView } from "@/views/components/common-comps"
import { ServeTargetType, Status } from "."
import { CreateElement } from "vue"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { get, map, split } from "lodash"
import moment from "moment"
import {
    createPreviewBtn,
    renderPreviewPop,
} from "@/views/components/preview-pop"

const statusColor: any = {
    [Status.待提交]: "#FF8B16",
    [Status.已提交审核]: "#5782EC",
    [Status.通过]: "#888888",
    [Status.不通过]: "#E04B2D",
}
export async function createFormList(
    obj = {
        row: {} as any,
        type: "首次来鄂",
        h: (() => {}) as CreateElement,
    }
) {
    const { h, row, type } = obj
    console.log("type", type)
    if (type === "linghuojiuy<PERSON><PERSON><PERSON>") {
        const l = map(get(row, "object_data.lhjy_properties", []), (e) => {
            return {
                label: e.label + "：",
                value: e.has_image ? undefined : e.value,
                vNode: e.has_image
                    ? h("div", { class: "u-flex u-col-top" }, [
                          h("div", { class: "u-m-r-20" }, e.value),
                          createPreviewBtn(h, e.image),
                          h(
                              "el-button",
                              {
                                  attrs: {
                                      type: "text",
                                  },
                                  on: {
                                      click: () => {
                                          window.open(
                                              sdk.buildImage(e.image),
                                              "_blank"
                                          )
                                      },
                                  },
                              },
                              "下载附件"
                          ),
                      ])
                    : undefined,
            }
        })
        return [
            {
                title: "审核信息",
                list: [
                    {
                        label: "审核状态：",
                        // value: row.status_label || "--",
                        vNode: h("span", {}, row.personal_audit_status_label),
                        span: 12,
                    },
                    {
                        label: "审核备注：",
                        value: row.audit_memo || "--",
                        span: 24,
                    },
                ],
            },
            {
                title: "街道审核信息",
                list: [
                    {
                        label: "初次享受年月：",
                        value: row.share_date_label || "--",
                        span: 12,
                    },
                    {
                        label: "累计享受月数：",
                        value: row.share_month_number || "--",
                        span: 12,
                    },
                    {
                        label: "本次补贴开始年月：",
                        value: row.the_begin_date_label || "--",
                        span: 12,
                    },
                    {
                        label: "本次补贴截止年月：",
                        value: row.the_end_date_label || "--",
                        span: 12,
                    },
                ],
            },
            {
                title: "申报信息",
                list: [
                    {
                        label: "政策名称：",
                        value: row.name || "--",
                        span: 12,
                    },
                    {
                        label: "申报时间：",
                        value:
                            moment(row.apply_submit_time_label).format(
                                "yyyy-MM-DD HH:mm"
                            ) || "--",
                        span: 12,
                    },
                    {
                        label: "人员类别：",
                        value: row.lhjy_personal_type_label || "--",
                        span: 12,
                    },
                    {
                        label: "申报人：",
                        value: row.person_name || "--",
                        span: 12,
                    },
                    {
                        label: "联系方式：",
                        value: row.person_mobile || "--",
                        span: 12,
                    },
                    {
                        label: "身份证号：",
                        vNode: renDesensitizationView(h, {
                            value: row.id_card,
                        }),
                        span: 12,
                    },
                    {
                        label: "户籍所在地：",
                        value: row.household_region_names || "--",
                        span: 12,
                    },
                    {
                        label: "常住地：",
                        value: row.permanent_area_names_label || "--",
                        span: 12,
                    },
                    // {
                    //     label: "管理街道：",
                    //     value: row.mgt_area_names_label || "--",
                    //     span: 12,
                    // },
                    {
                        label: "养老缴费年月：",
                        value: getDate(row, "social_pay") || "--",
                        span: 12,
                    },
                    // {
                    //     label: "养老补贴年月：",
                    //     value: getDate(row, "social_butie") || "--",
                    //     span: 12,
                    // },
                    // {
                    //     label: "养老补贴月数：",
                    //     value: row.social_butie_month_label || "--",
                    //     span: 12,
                    // },
                    {
                        label: "医疗缴费年月：",
                        value: getDate(row, "yiliao_pay") || "--",
                        span: 12,
                    },
                    // {
                    //     label: "医疗补贴年月：",
                    //     value: getDate(row, "yiliao_butie") || "--",
                    //     span: 12,
                    // },
                    // {
                    //     label: "医疗补贴月数：",
                    //     value: row.yiliao_butie_month_label || "--",
                    //     span: 12,
                    // },
                    // {
                    //     label: "工伤缴费年月：",
                    //     value: row.gongshang_pay_date_label || "--",
                    //     span: 12,
                    // },
                    ...l,
                    //   {
                    //       label: "录用企业：",
                    //       vNode: h("div", { class: "u-flex" }, [
                    //           h("div", {}, row.agent_name),
                    //           h("div", {}, "未入驻"),
                    //           h(
                    //               "el-button",
                    //               {
                    //                   attrs: {
                    //                       type: "text",
                    //                   },
                    //                   on: {
                    //                       click: () => {
                    //                           // 邀请入驻
                    //                       },
                    //                   },
                    //               },
                    //               "邀请入驻"
                    //           ),
                    //       ]),
                    //       span: 12,
                    //   },
                    //   {
                    //       label: "企业联系人：",
                    //       value: row.contact_mobile || "--",
                    //       span: 12,
                    //   },
                    //   {
                    //       label: "个人银行卡：",
                    //       vNode: row.bank_image
                    //           ? h(
                    //                 "el-button",
                    //                 {
                    //                     attrs: {
                    //                         type: "text",
                    //                     },
                    //                     on: {
                    //                         click: () => {
                    //                             window.open(
                    //                                 sdk.buildFilePath(
                    //                                     row.bank_image
                    //                                 ),
                    //                                 "_blank"
                    //                             )
                    //                         },
                    //                     },
                    //                 },
                    //                 "查看附件"
                    //             )
                    //           : h("span", "未上传"),
                    //       span: 12,
                    //   },
                    {
                        label: "社保卡号：",
                        value: row.bank_card_no || "--",
                        span: 12,
                    },
                    {
                        label: "开户银行：",
                        value: row.bank_name || "--",
                        span: 12,
                    },
                    // {
                    //     label: "开户行行号：",
                    //     value: row.bank_no || "--",
                    //     span: 12,
                    // },
                ],
            },
        ]
    } else {
        // if(row) // 判断居民或企业
        console.log("jso", JSON.parse(JSON.stringify(row)))
        const isCompany = row.apply_type_serve_type === ServeTargetType.企业
        return isCompany
            ? [
                  {
                      title: "申报信息",
                      list: [
                          {
                              label: "政策名称：",
                              value: row.name || "--",
                              span: 12,
                          },

                          {
                              label: "申报时间：",
                              value: row.apply_submit_time_label || "--",
                              span: 12,
                          },
                          {
                              label: "申报人：",
                              value: row.company_contact_person || "--",
                              span: 12,
                          },
                          {
                              label: "申报人电话：",
                              value: row.company_contact_mobile || "--",
                              span: 12,
                          },
                          {
                              label: "企业名称：",
                              value: row.company_name || "--",
                              span: 12,
                          },
                          {
                              label: "汇总表盖章附件：",
                              vNode: row.file
                                  ? h("div", { class: "u-flex" }, [
                                        h(
                                            "div",
                                            { class: "u-m-r-10" },
                                            split(row.file, "__")[1]
                                        ),
                                        createPreviewBtn(h, row.file),
                                        h(
                                            "el-button",
                                            {
                                                attrs: {
                                                    type: "text",
                                                },
                                                on: {
                                                    click: () => {
                                                        window.open(
                                                            sdk.buildFilePath(
                                                                row.file
                                                            ),
                                                            "_blank"
                                                        )
                                                    },
                                                },
                                            },
                                            "下载附件"
                                        ),
                                    ])
                                  : h("span", "未上传"),
                              span: 12,
                          },
                          {
                              label: "申请表盖章附件：",
                              vNode: row.request_table_file
                                  ? h("div", { class: "u-flex" }, [
                                        h(
                                            "div",
                                            { class: "u-m-r-10" },
                                            split(
                                                row.request_table_file,
                                                "__"
                                            )[1]
                                        ),
                                        createPreviewBtn(
                                            h,
                                            row.request_table_file
                                        ),
                                        h(
                                            "el-button",
                                            {
                                                attrs: {
                                                    type: "text",
                                                },
                                                on: {
                                                    click: () => {
                                                        window.open(
                                                            sdk.buildFilePath(
                                                                row.request_table_file
                                                            ),
                                                            "_blank"
                                                        )
                                                    },
                                                },
                                            },
                                            "下载附件"
                                        ),
                                    ])
                                  : h("span", "未上传"),
                              span: 12,
                              hide: type !== "qiyexinajiuye",
                          },
                          {
                              label: "审核备注：",
                              value: row.memo || "--",
                              span: 24,
                              hide: !row.memo,
                          },
                      ],
                  },
              ]
            : [
                  {
                      title: "审核信息",
                      list: [
                          {
                              label: "审核状态：",
                              // value: row.status_label || "--",
                              vNode: h(
                                  "span",
                                  {},
                                  row.personal_audit_status_label
                              ),
                              span: 12,
                          },
                          {
                              label: "审核备注：",
                              value: row.audit_memo || "--",
                              span: 24,
                          },
                      ],
                  },
                  {
                      title: "申报信息",
                      list: [
                          {
                              label: "政策名称：",
                              value: row.name || "--",
                              span: 12,
                          },
                          {
                              label: "申报时间：",
                              value:
                                  moment(row.apply_submit_time_label).format(
                                      "yyyy-MM-DD HH:mm"
                                  ) || "--",
                              span: 12,
                          },
                          {
                              label: "申报人：",
                              value: row.person_name || "--",
                              span: 12,
                          },
                          {
                              label: "联系方式：",
                              value: row.person_mobile || "--",
                              span: 12,
                          },
                          {
                              label: "身份证号：",
                              vNode: renDesensitizationView(h, {
                                  value: row.id_card,
                              }),
                              span: 12,
                          },
                          {
                              label: "户籍所在地：",
                              value: row.household_region_names || "--",
                              span: 12,
                          },
                          {
                              label: "文化程度：",
                              value: row.education_label || "--",
                              span: 12,
                          },
                          {
                              label: "毕业院校：",
                              value: row.graduation_school || "--",
                              span: 12,
                          },
                          {
                              label: "所学专业：",
                              value: row.major || "--",
                              span: 12,
                          },
                          {
                              label: "毕业时间：",
                              value:
                                  (row.graduation_date &&
                                      moment(row.graduation_date).format(
                                          "yyyy-MM-DD"
                                      )) ||
                                  "--",
                              span: 12,
                          },
                          {
                              label: "录用企业：",
                              vNode: h("div", { class: "u-flex" }, [
                                  h("div", {}, row.agent_name),
                                  //   h("div", {}, "未入驻"),
                                  //   h(
                                  //       "el-button",
                                  //       {
                                  //           attrs: {
                                  //               type: "text",
                                  //           },
                                  //           on: {
                                  //               click: () => {
                                  //                   // 邀请入驻
                                  //               },
                                  //           },
                                  //       },
                                  //       "邀请入驻"
                                  //   ),
                              ]),
                              span: 12,
                          },
                          {
                              label: "企业联系方式：",
                              value: row.agent_mobile || "--",
                              span: 12,
                          },
                          {
                              label: "合同起止时间：",
                              value:
                                  [
                                      moment(
                                          row.contract_start_date_label
                                      ).format("yyyy-MM-DD"),
                                      moment(
                                          row.contract_end_date_label
                                      ).format("yyyy-MM-DD"),
                                  ].join("至") || "--",
                              span: 12,
                          },
                          {
                              label: "参保地：",
                              value: row.social_region_names || "--",
                              span: 12,
                          },
                          //   {
                          //       label: "个人银行卡：",
                          //       vNode: row.bank_image
                          //           ? h(
                          //                 "el-button",
                          //                 {
                          //                     attrs: {
                          //                         type: "text",
                          //                     },
                          //                     on: {
                          //                         click: () => {
                          //                             window.open(
                          //                                 sdk.buildFilePath(
                          //                                     row.bank_image
                          //                                 ),
                          //                                 "_blank"
                          //                             )
                          //                         },
                          //                     },
                          //                 },
                          //                 "查看附件"
                          //             )
                          //           : h("span", "未上传"),
                          //       span: 12,
                          //   },
                          {
                              label: "银行账号：",
                              value: row.bank_card_no || "--",
                              span: 12,
                          },
                          {
                              label: "开户银行：",
                              value: row.bank_name || "--",
                              span: 12,
                          },
                          {
                              label: "开户行行号：",
                              value: row.bank_no || "--",
                              span: 12,
                          },
                      ],
                  },
              ]
    }
}

export function createAuditList(
    obj = {
        row: {} as any,
        type: "首次来鄂",
        h: (() => {}) as CreateElement,
    }
) {
    const { h, row, type } = obj
    console.log("jso", JSON.parse(JSON.stringify(row)))
    if (type === "qiyexinajiuye") {
        return [
            {
                title: "审核信息",
                list: [
                    {
                        label: "审核状态：",
                        value: row.audit_status_label || "--",
                        // vNode: h("span", {}, row.audit_status_label),
                        span: 12,
                    },
                    {
                        label: "审核备注：",
                        value: row.memo || "--",
                        span: 24,
                    },
                ],
            },
            {
                title: "申报信息",
                list: [
                    {
                        label: "姓名：",
                        value: row.name || "--",
                        span: 12,
                    },
                    {
                        label: "联系方式：",
                        value: row.mobile || "--",
                        span: 12,
                    },
                    {
                        label: "身份证号：",
                        vNode: renDesensitizationView(h, {
                            value: row.id_card,
                            dValue: row.id_card_hide,
                        }),
                        span: 12,
                    },
                    {
                        label: "民族：",
                        value: row.nation_label || "--",
                        span: 12,
                    },
                    {
                        label: "人员类别：",
                        value: row.personal_type_label || "--",
                        span: 12,
                    },
                    {
                        label: "户口性质：",
                        value: row.household_type_label || "--",
                        span: 12,
                    },
                    {
                        label: "常住地：",
                        value: row.permanent_area_names || "--",
                        span: 12,
                    },
                    {
                        label: "文化程度：",
                        value: row.education_label || "--",
                        span: 12,
                    },
                    {
                        label: "毕业院校：",
                        value: row.graduation_school || "--",
                        span: 12,
                    },
                    {
                        label: "所学专业：",
                        value: row.major || "--",
                        span: 12,
                    },
                    {
                        label: "毕业时间：",
                        value: row.graduation_date_label || "--",
                        span: 12,
                    },
                    {
                        label: "录用企业：",
                        vNode: h("div", { class: "u-flex" }, [
                            h("div", {}, row.company_name),
                            // h("div", {}, "未入驻"),
                            // h(
                            //     "el-button",
                            //     {
                            //         attrs: {
                            //             type: "text",
                            //         },
                            //         on: {
                            //             click: () => {
                            //                 // 邀请入驻
                            //             },
                            //         },
                            //     },
                            //     "邀请入驻"
                            // ),
                        ]),
                        // value: row.company_name,
                        span: 12,
                    },
                    {
                        label: "企业联系方式：",
                        value: row.company_contact_mobile || "--",
                        span: 12,
                    },
                    {
                        label: "合同起止时间：",
                        value:
                            [
                                row.contract_start_date_label,
                                row.contract_end_date_label,
                            ].join("至") || "--",
                        span: 12,
                    },
                ],
            },
        ]
    } else if (type === "jiuyeshehuobutie" || type === "jiuyeshehuobutie_bk") {
        return [
            {
                title: "审核信息",
                list: [
                    {
                        label: "审核状态：",
                        value: row.audit_status_label || "--",
                        // vNode: h("span", {}, row.audit_status_label),
                        span: 12,
                    },
                    {
                        label: "审核备注：",
                        value: row.memo || "--",
                        span: 24,
                    },
                ],
            },
            {
                title: "申报信息",
                list: [
                    {
                        label: "姓名：",
                        value: row.name || "--",
                        span: 12,
                    },
                    {
                        label: "联系方式：",
                        value: row.mobile || "--",
                        span: 12,
                    },
                    {
                        label: "身份证号：",
                        vNode: renDesensitizationView(h, {
                            value: row.id_card,
                            dValue: row.id_card_hide,
                        }),
                        span: 12,
                    },
                    {
                        label: "性别：",
                        value: row.sex_label || "--",
                        span: 12,
                    },
                    {
                        label: "身份证住址：",
                        value: row.household_region_names || "--",
                        span: 12,
                    },
                    {
                        label: "文化程度：",
                        value: row.education_label || "--",
                        span: 12,
                    },
                    {
                        label: "录用企业：",
                        value: row.company_name,
                        span: 12,
                    },
                    {
                        label: "企业联系人：",
                        value: row.contact_mobile || "--",
                        span: 12,
                    },
                    // {
                    //     label: "毕业院校：",
                    //     value: row.graduation_school || "--",
                    //     span: 12,
                    // },
                    // {
                    //     label: "所学专业：",
                    //     value: row.major || "--",
                    //     span: 12,
                    // },
                    // {
                    //     label: "毕业时间：",
                    //     value: row.graduation_date_label || "--",
                    //     span: 12,
                    // },
                    {
                        label: "合同起止时间：",
                        value:
                            [
                                row.contract_start_date_label,
                                row.contract_end_date_label,
                            ].join("至") || "--",
                        span: 12,
                    },
                    // {
                    //     label: "本人银行卡：",
                    //     vNode: row.bank_card_image
                    //         ? h("div", { class: "u-flex" }, [
                    //               h(
                    //                   "div",
                    //                   { class: "u-m-r-10" },
                    //                   row.bank_card_image.split("__")[1]
                    //               ),
                    //               h(
                    //                   "el-button",
                    //                   {
                    //                       attrs: {
                    //                           type: "text",
                    //                       },
                    //                       on: {
                    //                           click: () => {
                    //                               window.open(
                    //                                   sdk.buildFilePath(
                    //                                       row.bank_card_image
                    //                                   ),
                    //                                   "_blank"
                    //                               )
                    //                           },
                    //                       },
                    //                   },
                    //                   "查看附件"
                    //               ),
                    //           ])
                    //         : h("span", "未上传"),
                    //     span: 12,
                    // },
                    {
                        label: "银行账号：",
                        value: row.bank_card_no || "--",
                        span: 12,
                    },
                    {
                        label: "开户银行：",
                        value: row.bank_name || "--",
                        span: 12,
                    },
                    {
                        label: "开户行行号：",
                        value: row.bank_no || "--",
                        span: 12,
                    },
                ],
            },
        ]
    } else {
        // if(row) // 判断居民或企业
        return [
            {
                title: "审核信息",
                list: [
                    {
                        label: "审核状态：",
                        value: row.audit_status_label || "--",
                        // vNode: h("span", {}, row.audit_status_label),
                        span: 12,
                    },
                    {
                        label: "审核备注：",
                        value: row.memo || "--",
                        span: 24,
                    },
                ],
            },
            {
                title: "申报信息",
                list: [
                    {
                        label: "姓名：",
                        value: row.name || "--",
                        span: 12,
                    },
                    {
                        label: "联系方式：",
                        value: row.mobile || "--",
                        span: 12,
                    },
                    {
                        label: "身份证号：",
                        vNode: renDesensitizationView(h, {
                            value: row.id_card,
                            dValue: row.id_card_hide,
                        }),
                        span: 12,
                    },
                    {
                        label: "户籍所在地：",
                        value: row.household_region_names || "--",
                        span: 12,
                    },
                    {
                        label: "文化程度：",
                        value: row.education_label || "--",
                        span: 12,
                    },
                    {
                        label: "毕业院校：",
                        value: row.graduation_school || "--",
                        span: 12,
                    },
                    {
                        label: "所学专业：",
                        value: row.major || "--",
                        span: 12,
                    },
                    {
                        label: "毕业时间：",
                        value: row.graduation_date_label || "--",
                        span: 12,
                    },
                    {
                        label: "录用企业：",
                        // vNode: h("div", { class: "u-flex" }, [
                        //     h("div", {}, row.company_name),
                        //     h("div", {}, "未入驻"),
                        //     h(
                        //         "el-button",
                        //         {
                        //             attrs: {
                        //                 type: "text",
                        //             },
                        //             on: {
                        //                 click: () => {
                        //                     // 邀请入驻
                        //                 },
                        //             },
                        //         },
                        //         "邀请入驻"
                        //     ),
                        // ]),
                        value: row.company_name,
                        span: 12,
                    },
                    {
                        label: "合同起止时间：",
                        value:
                            [
                                row.contract_start_date_label,
                                row.contract_end_date_label,
                            ].join("至") || "--",
                        span: 12,
                    },
                    {
                        label: "本人银行卡：",
                        vNode: row.bank_card_image
                            ? h("div", { class: "u-flex" }, [
                                  h(
                                      "div",
                                      { class: "u-m-r-10" },
                                      row.bank_card_image.split("__")[1]
                                  ),
                                  h(
                                      "el-button",
                                      {
                                          attrs: {
                                              type: "text",
                                          },
                                          on: {
                                              click: () => {
                                                  window.open(
                                                      sdk.buildFilePath(
                                                          row.bank_card_image
                                                      ),
                                                      "_blank"
                                                  )
                                              },
                                          },
                                      },
                                      "查看附件"
                                  ),
                              ])
                            : h("span", "未上传"),
                        span: 12,
                    },
                    {
                        label: "银行账号：",
                        value: row.bank_card_no || "--",
                        span: 12,
                    },
                    {
                        label: "开户银行：",
                        value: row.bank_name || "--",
                        span: 12,
                    },
                    {
                        label: "开户行行号：",
                        value: row.bank_no || "--",
                        span: 12,
                    },
                ],
            },
        ]
    }
}

function getDate(row: any, key: string) {
    return [row[key + "_begin_date_label"], row[key + "_end_date_label"]]
        .filter(Boolean)
        .join("至")
}
