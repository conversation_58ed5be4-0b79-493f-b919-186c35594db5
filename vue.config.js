const build = process.env.NODE_ENV === "production"
const production = process.env.VUE_APP_ENV === "pro"
const webpack = require("webpack")
const CompressionPlugin = require("compression-webpack-plugin")
const TerserPlugin = require("terser-webpack-plugin")

// 配置基本大小
module.exports = {
    outputDir: process.env.CUSTOMER_OUTPUT_DIR,
    devServer: {
        port: 8085,
        historyApiFallback: true,
        allowedHosts: "all",
        // https: true,
    },
    transpileDependencies: true,
    productionSourceMap: !build,
    publicPath:
        process.env.NODE_ENV === "production"
            ? `/${process.env.BASE_URL || ""}`
            : "/",
    css: {
        extract: build,
        sourceMap: !build,
    },

    pluginOptions: {},

    chainWebpack: (config) => {
        config
            .plugin("ignoreMoment")
            .use(webpack.ContextReplacementPlugin, [
                /moment[/\\]locale$/,
                /zh-cn/,
            ])

        config.plugin("lodash").use(webpack.ProvidePlugin, [{ _: "lodash" }])

        config.optimization.splitChunks({
            cacheGroups: {
                vendors: {
                    name: "vendors",
                    test: /[\\/](sdk|lodash|vue|vue-router|moment|axios|qs|vant|uniplat-sdk|customer-service|core-js|babel)[\\/]/,
                    chunks: "all",
                    priority: 10,
                },
                assets: {
                    name: "assets",
                    test: /[\\/](assets)[\\/]/,
                    chunks: "all",
                    priority: 9,
                },
                // 'async-commons': {
                //     // 其余异步加载包
                //     chunks: 'async',
                //     minChunks: 2,
                //     name: 'async-commons',
                //     priority: 9,
                // },
                // commons: {
                //     // 其余同步加载包
                //     chunks: 'all',
                //     minChunks: 2,
                //     name: 'commons',
                //     priority: 8,
                // },
            },
        })

        config.plugin("html").tap((args) => {
            args[0].title =
                process.env.VUE_APP_HEADER_META_TITLE ||
                process.env.VUE_APP_HEADER_TITLE ||
                ""
            args[0].meta = {
                description: {
                    name: "description",
                    content: process.env.VUE_APP_HEADER_META_DESCRIPTION || "",
                },
                keywords: {
                    name: "keywords",
                    content: process.env.VUE_APP_HEADER_META_KEYWORDS || "",
                },
            }
            return args
        })
    },

    configureWebpack: (config) => {
        // 替换加密文件
        config.plugins.push(
            new webpack.NormalModuleReplacementPlugin(
                /uniplat-key-common/,
                function (resource) {
                    if (process.env.VUE_APP_AES_ENCRYPT_KEY_PATH) {
                        resource.request =
                            process.env.VUE_APP_AES_ENCRYPT_KEY_PATH
                    }
                }
            ),
            new webpack.NormalModuleReplacementPlugin(
                /bk-key-common/,
                function (resource) {
                    if (process.env.VUE_APP_AES_ENCRYPT_KEY_PATH_BK) {
                        resource.request =
                            process.env.VUE_APP_AES_ENCRYPT_KEY_PATH_BK
                    }
                }
            )
        )
        // 配置别名等放到这里

        // 用以解决在chrome sources中vue文件生成带hash而无法找准文件的问题
        if (!build) {
            config.output.devtoolModuleFilenameTemplate = (info) => {
                const resPath = info.resourcePath
                if (
                    (/\.vue$/.test(resPath) &&
                        !/type=script/.test(info.identifier)) ||
                    /node_modules/.test(resPath)
                ) {
                    return `webpack:///${resPath}?${info.hash}`
                }
                return `webpack:///${resPath.replace("./src", "my-code/src")}`
            }
        } else {
            config.plugins.push(
                new CompressionPlugin({
                    filename: "[path].gz[query]",
                    algorithm: "gzip",
                    test: /\.js$|\.html$|.\css/, // 匹配文件名
                    threshold: 10240, // 对超过10k的数据压缩
                    minRatio: 0.8, // 只有压缩好这个比率的资产才能被处理
                    deleteOriginalAssets: false,
                })
            )
            config.optimization.minimizer = [
                new TerserPlugin({
                    terserOptions: {
                        compress: {
                            // 暂时去掉
                            // drop_console: production,
                        },
                    },
                }),
            ]
        }
    },
}
