import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import {
    detailRowPredict,
    checkColumn as checkCommonColumn,
} from "@/views/zq-company/hr-info-manage/work-info-apply/index"
import { split } from "lodash"

const tableFilter: TableFilter[] = [
    {
        prop: "create_time",
        label: "提交时间",
        type: FormType.DatePicker,
    },
    {
        label: "公司名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_province_region_code",
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
]

export const rowPredict = {
    agent_name: "tg_enterprise#name",
    title: "",
    upload_date: "",
    update_time: "",
    yonggong_fill_count: "",
    yonggong_audit_pass_count: "",
    yonggong_audit_notpass_count: "",
    status: "label",
    description: "",
    file: "company_task_file_record#file_id",
}

export const detailPredict = {
    agent_name: "tg_enterprise#name",
    title: "",
    status: "label",
    type: "label",
    create_time: "",
    finish_time: "",
    version: "",
    description: "",
    file: "company_task_file_record#file_id",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("company_task_record")
            .list("list_for_plat_yonggong"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        column,
        oneTabFilter: true,
    }
}

export const enum AuthStatus {
    待认证 = 0,
    已认证 = 1,
    认证不通过 = 2,
}

export interface Row {
    /** 企业名称 */
    agent_name: string

    /** 统—社会信用代码 */
    company_code: string

    /** 法人 */
    legal_person: string

    /** 法人身份证号 */
    legal_card_open_id: string

    /** 区域名称 */
    region_name: string

    /** 企业所在地详细地址 */
    address_detail: string

    /** 联系人姓名 */
    contact_person: string

    /** 联系人姓名电话 */
    contact_mobile: string

    /** 所属行业 */
    industory_catalog: string

    /** 所属行业[文本] */
    industory_catalog_label: string

    /** 状态 */
    auth_status: AuthStatus

    /** 状态[文本] */
    auth_status_label: string
    status: Status
    id: number
    v: number
}

export const enum Status {
    已过期 = 5,
    审核通过 = 3,
    审核不通过 = 2,
    审核中 = 1,
    待填报 = 0,
}

export interface DetailRow {
    agent_name: string
    /** 任务名称 */
    title: string

    /** 任务状态 */
    status: Status

    /** 任务状态[文本] */
    status_label: string

    /** 任务类型 */
    type: number

    /** 任务类型[文本] */
    type_label: string

    /** 任务开始时间 */
    create_time: string

    /** 计划完成时间 */
    finish_time: string

    /** 填报次数 */
    version: string

    /** 任务描述 */
    description: string
    file: string
    id: number
    v: number
}

export const enum TwoCheckStatus {
    待核验 = 0,
    通过 = 1,
    不通过 = 2,
}

export interface CheckRow {
    /** 证件类型 */
    person_id_card_type: string

    /** 身份证号 */
    person_id_card_encode: string

    /** 姓名 */
    person_name: string

    /** 性别 */
    person_sex: string

    /** 出生日期 */
    person_birthday: string

    /** 民族 */
    person_nationality: string

    /** 文化程度 */
    education: string

    /** 户籍所在地 */
    household: string

    /** 户籍详细地址 */
    household_address: string

    /** 常住地址 */
    permanent: string

    /** 常住地详址 */
    permanent_address: string

    /** 手机号 */
    person_phone_encode: string

    /** 登记类别 */
    registration_category: string

    /** 就业前所属群体 */
    employment_group: string

    /** 就业形式 */
    employment_category: string

    /** 入职日期 */
    work_start_date: string

    /** 岗位名称 */
    job_name: string

    /** 岗位类别 */
    job_type: string

    /** 职业资格等级 */
    professional_qualification_level: string

    /** 职业资格工种（对应资格等级证书） */
    professional: string

    /** 最低薪资待遇 */
    min_salary: string

    /** 最高薪资待遇 */
    max_salary: string

    /** “妈妈岗”标识 */
    mama_flag: string

    /** 创业标识 */
    entrepreneurship_flag: string

    /** 备注 */
    remark: string

    /** 状态 */
    status: number

    /** 实名认证状态 */
    two_check_status: TwoCheckStatus

    /** 不通过原因 */
    error_message: string
    id: number
    v: number
}

export const column: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "调查标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "上报时间",
        prop: "upload_date",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.upload_date)
        },
    },
    {
        label: "已填报总用工数量",
        prop: "yonggong_fill_count",
        showOverflowTip: true,
    },
    {
        label: "文件",
        prop: "file",
        showOverflowTip: true,
        render: (h, row: any) => {
            return h(
                "a",
                {
                    class: "primary",
                    attrs: {
                        href: sdk.buildFilePath(row.file || ""),
                        target: "__blank",
                    },
                },
                split(row.file, "__")[1]
            )
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const column1: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "调查标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "审核时间",
        prop: "update_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.update_time)
        },
    },
    {
        label: "审核通过用工数量",
        prop: "yonggong_audit_pass_count",
        showOverflowTip: true,
    },
    {
        label: "文件",
        prop: "file",
        showOverflowTip: true,
        render: (h, row: any) => {
            return h(
                "a",
                {
                    class: "primary",
                    attrs: {
                        href: sdk.buildFilePath(row.file || ""),
                        target: "__blank",
                    },
                },
                split(row.file, "__")[1]
            )
        },
    },
    {
        label: "审核状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "120",
        showOverflowTip: true,
    },
]

export const column2: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "调查标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "审核时间",
        prop: "update_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.update_time)
        },
    },
    {
        label: "审核不通过用工数量",
        prop: "yonggong_audit_notpass_count",
        showOverflowTip: true,
    },
    {
        label: "文件",
        prop: "file",
        showOverflowTip: true,
        render: (h, row: any) => {
            return h(
                "a",
                {
                    class: "primary",
                    attrs: {
                        href: sdk.buildFilePath(row.file || ""),
                        target: "__blank",
                    },
                },
                split(row.file, "__")[1]
            )
        },
    },
    {
        label: "审核状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "220",
        showOverflowTip: true,
    },
]

export const column3: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "调查标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "描述",
        prop: "description",
        showOverflowTip: true,
    },
    {
        label: "文件",
        prop: "file",
        showOverflowTip: true,
        render: (h, row: any) => {
            return h(
                "a",
                {
                    class: "primary",
                    attrs: {
                        href: sdk.buildFilePath(row.file || ""),
                        target: "__blank",
                    },
                },
                split(row.file, "__")[1]
            )
        },
    },
    {
        label: "状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "220",
        showOverflowTip: true,
        fixed: "right",
    },
]

export function buildItems(rows: Row | {}) {
    const row = (rows || {}) as Row
    return [
        { label: "公司名称：", value: row.agent_name || "" },
        { label: "填报次数：", value: row.company_code || "" },
        { label: "当前任务状态：", value: row.company_code || "34342" },
        // { label: "服务区域：", value: row.company_code || "" },
        // {
        //     label: "人力资源许可证：",
        //     value: row.company_code || "",
        //     // vNode: handlePreImg(row.company_code, vue),
        // },
        // { label: "业务介绍：", value: row.company_code || "" },
    ].map((i) => {
        return { ...i, span: 8 }
    })
}

export const commonColumn: TableColumn[] = [...checkCommonColumn]

export const checkColumn: TableColumn[] = [
    { type: "selection", prop: "select" },
    ...commonColumn.filter((i) => {
        return i.prop !== "status_label" && i.prop !== "error_message"
    }),
    {
        label: "实名核验",
        prop: "two_check_status_label",
        showOverflowTip: true,
        fixed: "right",
    },
    {
        label: "审核状态",
        prop: "status_label",
        showOverflowTip: true,
        fixed: "right",
    },
    {
        label: "操作",
        prop: "h",
        width: "140",
        showOverflowTip: true,
        fixed: "right",
    },
]

export const checkColumn2: TableColumn[] = [
    ...commonColumn.filter((i) => {
        return i.prop !== "status_label" && i.prop !== "error_message"
    }),
    {
        label: "实名核验",
        prop: "two_check_status_label",
        showOverflowTip: true,
        fixed: "right",
    },
    {
        label: "审核状态",
        prop: "status_label",
        showOverflowTip: true,
        fixed: "right",
    },
    {
        label: "审核反馈",
        prop: "error_message",
        showOverflowTip: true,
        width: "120",
        fixed: "right",
        render: (h, row) => {
            return h(
                "span",
                {
                    class: "color-red",
                },
                row.error_message
            )
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "140",
        showOverflowTip: true,
        fixed: "right",
    },
]

export const checkRowPredict = {
    ...detailRowPredict,
}

export function checkTableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("company_task_emprequire_record")
            .list("list_for_review"),
        filter: [],
        defaultPageSize: 10,
        predict: checkRowPredict,
        column: checkColumn2,
        oneTabFilter: true,
    }
}
