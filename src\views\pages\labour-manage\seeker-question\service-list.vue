<template>
    <div class="check-list-container">
        <table-container
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
        >
            <div slot="title" class="d-flex-item-center bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20">
                <common-table
                    :data="data"
                    :tableConfig="tableConfig"
                    :columns="columns"
                >
                    <div
                        slot="contact_person"
                        slot-scope="scope"
                        class="d-flex flex-column align-items-start"
                    >
                        <div>{{ scope.row.contact_person }}</div>
                        <div>{{ scope.row.contact_mobile }}</div>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button
                            v-role="'model.xg_agent.action.back_detail'"
                            type="text"
                            @click="viewDetail(scope.row)"
                        >
                            查看详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { FormType } from "@/core-ui/component/form"
    import {
        TableColumn,
        TableColumn as TableColumnImpl,
        TableFilter,
    } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { formatTime } from "@/utils/tools"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { Component } from "vue-property-decorator"
    import { getCacheBreadcrumbsByRoutePath } from "../../single-page/components/tags-view"
    import { map } from "lodash"
    import { VNode } from "vue"
    import router from "@/router"

    const tableFilter: TableFilter[] = [
        {
            label: "用户信息",
            type: FormType.Text,
            prop: "name",
        },
        {
            label: "所在城市",
            type: FormType.MultipleCascader,
            prop: "permanent_province_code",
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
        },
        {
            label: "服务内容",
            type: FormType.Select,
            prop: "serve_type",
        },
        {
            label: "就业状态",
            type: FormType.Select,
            prop: "employment_status",
        },
        // {
        //     label: "服务时间",
        //     type: FormType.DatePicker,
        //     prop: "ddd",
        //     option: {
        //         type: "daterange",
        //     },
        // },
        {
            label: "标签",
            type: FormType.Cascader,
            prop: "tags",
            useTag: "*",
            option: {
                filterable: true,
                collapseTags: true,
                elProps: { multiple: true },
            },
        },
        {
            label: "出生日期",
            type: FormType.DatePicker,
            option: {
                type: "daterange",
            },
            prop: "birth_date",
        },
    ]

    const rowPredict = {
        title: "general_questionnaire#title",
        name: "user_profile#name",
        sex: "user_profile#sex_label",
        general_questionnaire_id: "general_questionnaire#id",
        birth_date: "user_profile#birth_date",
        id_card_hide: "user_profile#id_card_hide",
        mobile_hide: "user_profile#mobile_hide",
        served_tasks: "",
        region_name: "user_profile#basic_info#permanent_city#region_name",
        employment_status:
            "user_profile#user_profile_current_job_info#employment_status_label",
        update_time: "user_profile#update_time",
        served_content_list: "",
        _access_key: "",
    }

    const enum Sex {
        男 = 1,
        女 = 2,
        未设置 = 0,
    }

    const enum EmploymentStatus {
        已就业创业 = 1,
        未就业 = 2,
        务农 = 3,
        入伍 = 4,
        入学 = 5,
        退出农村劳动力范围 = 6,
        查无此人 = 7,
    }

    interface Row {
        /** 问卷名称 */
        title: string
        /** 姓名 */
        name: string
        /** 性别 */
        sex: Sex
        /** 性别[文本] */
        sex_label: string
        /** 出生日期 */
        birth_date: string
        /** 身份证号 */
        id_card_hide: string
        /** 手机号 */
        mobile_hide: string
        /** 已服务任务 */
        served_tasks: {
            id: number
            title: string
        }[]
        /** 区域名称 */
        region_name: string
        /** 就业状态 */
        employment_status: EmploymentStatus
        /** 就业状态[文本] */
        employment_status_label: string
        /** 更新时间 */
        update_time: string
        id: number
        v: number
        _access_key: string
    }

    export const columns: TableColumn[] = [
        {
            label: "问卷名称",
            prop: "title",
            showOverflowTip: true,
        },
        {
            label: "姓名",
            prop: "name",
            width: "70",
            showOverflowTip: true,
        },
        {
            label: "性别",
            width: "50",
            align: "center",
            prop: "sex_label",
            showOverflowTip: true,
        },
        {
            label: "出生日期",
            prop: "birth_date",
            showOverflowTip: true,
        },
        {
            label: "身份证号",
            prop: "id_card_hide",
            width: "150",
            showOverflowTip: true,
        },
        {
            label: "手机号",
            prop: "mobile_hide",
            width: "150",
            showOverflowTip: true,
        },
        {
            label: "已服务任务",
            prop: "served_tasks",
            width: "180",
            align: "center",
            render: (h, row: Row) => {
                return h(
                    "div",
                    map(row.served_tasks, (item, index) => {
                        const node: VNode[] = []
                        if (index) {
                            node.push(h("span", {}, "，"))
                        }
                        node.push(
                            h(
                                "span",
                                {
                                    class: "primary pointer",
                                    on: {
                                        click: () => {
                                            router.push({
                                                name: routesMap.groupService
                                                    .serviceManageDetail.task,
                                                query: {
                                                    id: item.id + "",
                                                    from: routesMap.groupService
                                                        .serviceManageDetail.result
                                                        .served_target_count,
                                                },
                                            })
                                        },
                                    },
                                },
                                `${item.title}`
                            )
                        )
                        return node
                    })
                )
            },
        },
        {
            label: "就业状态",
            prop: "employment_status_label",
            width: "150",
            showOverflowTip: true,
        },
        {
            label: "更新时间",
            prop: "update_time",
            width: "150",
            showOverflowTip: true,
            formatter: (row) => {
                return formatTime.day(row.update_time)
            },
        },
    ]

    export function tableConfig(id: string) {
        return {
            model: sdk.core
                .model("task_served_target_for_questionnaire")
                .list("list_for_questionnaire"),
            filter: tableFilter,
            defaultPageSize: 8,
            predict: rowPredict,
            preFilter: { questionnaire_id: id },
            // preFilter,
            columns,
        }
    }

    @Component({
        name: routesMap.labourManage.seekerQuestionServiceList,
        components: { TableContainer, CommonTable },
    })
    export default class PolicyBasis extends BaseTableController<Row> {
        breadcrumbs: BreadcrumbItem[] = [
            ...getCacheBreadcrumbsByRoutePath(
                routesMap.labourManage.seekerQuestionModel
            ),
            {
                label: "服务对象列表",
            },
        ]

        private tableConfig = tableConfig(this.$route.query.id as string)
        private readonly columns: TableColumnImpl[] = columns

        private get title() {
            return this.$route.meta?.title || ""
        }

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.labourManage.seekerQuestionServiceList,
        }

        viewDetail(row: Row) {
            this.$router.push({
                name: routesMap.employmentManage.companyManageDetail,
                query: { id: row._access_key || row.id + "" },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .check-list-container {
        width: 100%;
    }

    .header {
        padding-top: 15px;
        padding-bottom: 15px;

        .title {
            font-size: 22px;
            color: #000000;
        }
    }
</style>
