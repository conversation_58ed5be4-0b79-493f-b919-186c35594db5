import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import { TableColumn, TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getSalary } from "@/utils"
import { formatTime } from "@/utils/tools"
import moment from "moment"

export const predict = {
    device_name: "device_base_info#device_name",
    brand: "device_base_info#device_brand#brand",
    status: "device_base_info#status_label",
    status_now: "device_base_info#bind_info#status_now_label",
    update_time: "device_base_info#update_time",
    create_time: "device_base_info#create_time",
    full_region_name_display:
        "device_base_info#bind_info#full_region_name_display",
    access_key: "device_base_info#access_key",
}
export function tableConfig(id: string): TableConfig {
    return {
        model: sdk.core
            .model("device_information_device")
            .list("in_device_information_detail"),
        defaultPageSize: 10,
        predict: predict,
        preFilter: {
            information_id: id,
        },
    }
}

export const columns: TableColumn[] = [
    { label: "设备名称", prop: "device_name", showOverflowTip: true },
    { label: "设备品牌", prop: "brand", showOverflowTip: true },
    {
        label: "使用状态",
        prop: "status_label",
        showOverflowTip: true,
        width: "80px",
    },
    {
        label: "在线状态",
        prop: "status_now_label",
        showOverflowTip: true,
        width: "80px",
    },
    {
        label: "所在区域",
        prop: "full_region_name_display",
        showOverflowTip: true,
    },
    {
        label: "更新时间",
        prop: "update_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.seconds(row.update_time)
        },
    },
    {
        label: "创建时间",
        prop: "create_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.seconds(row.create_time)
        },
    },
]

export function createFormConfig(id?: number): BuildFormConfig {
    return {
        sdkModel: id ? "uniplat_model_remark@xg_project" : "user_account",
        sdkAction: id ? "updateContent" : "addCommit",
        id,
        forms: [
            {
                label: "备注内容",
                type: FormType.Text,
                prop: id ? "content" : "remark",
                required: true,
            },
        ],
    }
}

export const browsePredict = {
    name: "position#name",
    salary_min: "position#salary_min",
    salary_max: "position#salary_max",
    region_name: "position#area#region_name",
    create_time: "label",
    salary: "position#salary",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    address_detail: "position#agent#address_detail",
    agent_name: "position#agent#agent_name",
    salary_desc: "position#salary_desc_label",
}
export function browseTableConfig(user_id = ""): TableConfig {
    return {
        domainService: sdk.getDomainService(
            "getPositionList",
            "anonymous/client_api",
            "xg_project"
        ),
        domainType: "get",
        defaultPageSize: 6,
        predict: browsePredict,
        preFilter: {
            type: 4,
            user_id,
        },
    }
}

export const browseColumns: TableColumn[] = [
    { label: "浏览岗位", prop: "name", showOverflowTip: true },
    {
        label: "薪资",
        prop: "salary",
        showOverflowTip: true,
        formatter(row) {
            return row.salary_desc || getSalary(row.salary)
        },
    },
    {
        label: "工作地",
        prop: "address",
        showOverflowTip: true,
    },
    { label: "所属企业", prop: "enterprise_name", showOverflowTip: true },
    {
        label: "浏览时间",
        prop: "browse_time",
        showOverflowTip: true,
        formatter(row) {
            return moment(row.browse_time).format("YYYY-MM-DD hh:mm")
        },
    },
]
