<template>
    <div class="d-flex" v-if="data">
        <el-checkbox v-model="data.enable" @change="change" />
        <div class="u-m-l-8">
            <div class="color-2">
                {{ item.label }}
            </div>
            <div class="u-font-12 color-8 u-p-y-10">
                {{ item.tip }}
            </div>
            <div class="primary pointer" @click="expand = !expand">
                配置消息内容
                <span
                    class="el-icon-arrow-down bold arrow"
                    :class="{ rotate: expand }"
                ></span>
            </div>
            <div class="card u-p-20" v-show="expand">
                <div class="u-flex">
                    <div class="color-2 label">渠道</div>
                    <filed
                        :isEdit="true"
                        :formItem="formItemChannel"
                        v-bind="formItemChannel"
                        :value="data.msg_channel"
                        @input="changeMsgCodeChannel"
                    >
                    </filed>
                </div>
                <div class="u-flex u-m-t-10">
                    <div class="color-2 label">短信模板</div>
                    <filed
                        :isEdit="true"
                        :formItem="formItemMsgCode"
                        v-bind="formItemMsgCode"
                        :value="data.msg_code"
                        @input="changeMsgCode"
                    >
                    </filed>
                </div>
                <div>
                    <div
                        class="u-flex u-m-t-10"
                        v-for="(item, key) in msg_params"
                        :key="key"
                    >
                        <div class="label">参数{{ key }}</div>
                        <el-input
                            size="mini"
                            v-model="msg_params[key]"
                            @input="changeMsgParams"
                        />
                    </div>
                </div>
                <div class="d-flex u-m-t-20" v-if="data.sms_template_tip">
                    <div class="u-m-r-10 color-2 u-shrink-0">短信消息示例</div>
                    <div v-html="data.sms_template_tip || '--'"></div>
                </div>

                <div class="d-flex u-m-t-20">
                    <div class="u-m-r-10 color-2 u-m-t-5">通知时间选择</div>
                    <div>
                        <div class="u-flex">
                            <el-radio
                                class="u-m-r-10"
                                v-model="data.notify_time_type"
                                :label="0"
                            >
                                按固定时间发送
                            </el-radio>
                            <el-date-picker
                                v-if="data.notify_time_type === 0"
                                size="mini"
                                v-model="data.notify_time"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            />
                        </div>
                        <div class="u-m-t-10">
                            <el-radio
                                class="u-m-r-10"
                                v-model="data.notify_time_type"
                                :label="1"
                            >
                                即时发送
                            </el-radio>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import Filed from "@/core-ui/component/form/filed/index.vue"
    import moment from "moment"
    import { Component, Ref } from "vue-property-decorator"
    import { NotifyTimeType } from "../../../notify"
    import { BaseConfig } from "./base"
    import { UICore } from "@/core-ui/service/setup"
    import { find } from "lodash"

    @Component({ components: { CommonTable, Filed } })
    export default class SmsNotify extends BaseConfig {
        private mpLoading = false

        private msgCodeMapping = []

        @Ref()
        private commonTable!: CommonTable

        protected get formItemChannel() {
            const f = find(this.forms, { prop: "msg_channel" })
            return f || null
        }

        protected get formItemMsgCode() {
            const f = find(this.forms, { prop: "msg_code" })
            if (!f) return
            ;(f?.sourceInputsParameter as any)!.ext_properties.mapping.mapping_values =
                this.msgCodeMapping
            return f || null
        }

        mounted() {
            const d = {
                msg_code: "",
                msg_channel: "",
                tmp: "",
                notify_time_type: 0,
                notify_time: "",
                sms_template_tip: "",
                enable: !!this.item.data,
                msg_params: "",
                ...this.item.data,
            }
            this.data = d as any
            try {
                this.msg_params = JSON.parse(d.msg_params || "{}")
            } catch {}
            if (this.data?.enable) {
                this.expand = true
            }
            this.getFormConfig()
        }

        verify() {
            if (!this.data?.enable) {
                return true
            }
            if (!this.data?.msg_code) {
                this.$message.error(`请选择${this.item.label}短信模板`)
                return false
            }
            if (
                this.data?.notify_time_type === NotifyTimeType.按固定时间发送 &&
                !this.data.notify_time
            ) {
                this.$message.error(`请选择${this.item.label}通知时间`)
                return false
            }
            if (
                this.data?.notify_time_type === NotifyTimeType.按固定时间发送 &&
                this.data.notify_time &&
                moment(this.data.notify_time).isBefore(moment())
            ) {
                this.$message.error(`${this.item.label} 固定时间需要大于当前时间`)
                return false
            }
            return true
        }

        changeMsgCodeChannel(v: string) {
            this.data!.msg_channel = v
            this.data!.msg_code = ""
            this.msg_params = {}
            this.data!.sms_template_tip = ""
            this.buildSubscribeMsg(this.data!.sms_template_tip)
            this.getAction()
                .updateInitialParams({
                    selected_list: [{ id: this.taskId, v: 0 }],
                })
                .updateControlsProperties("msg_channel", {
                    inputs_parameters: UICore.buildActionParameter(
                        this.data as { [key: string]: any }
                    ),
                })
                .then((r) => {
                    const t = r?.masters?.find((i) => i.property === "msg_code")
                    this.msgCodeMapping = (
                        t?.ext_properties as any
                    )?.mapping?.mapping_values
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .arrow {
        transition: all 0.5s;
        &.rotate {
            transform: rotate(180deg);
        }
    }
    .label {
        width: 76px;
        color: #333;
        flex-shrink: 0;
    }
    .card {
        width: 514px;
        background: #f6f9fe;
        position: relative;
        margin-top: 12px;
        &::before {
            content: "";
            position: absolute;
            top: -10px;
            left: 50px;
            transform: translateX(-50%);
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 10px solid #f6f9fe;
        }
    }
</style>
