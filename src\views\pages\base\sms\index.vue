<template>
    <div>
        <table-container
            @tabName="tabName"
            showTableFilter
            filedWidth="250"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            class="container"
        >
            <div
                slot="title"
                class="d-flex-item-center justify-content-between bold"
            >
                <div>短信发送管理</div>
                <el-button
                    v-role="
                        'model.sms_send_history.action.insert_directional_sms'
                    "
                    slot="header-right"
                    type="primary"
                    @click="add"
                >
                    发送短信
                </el-button>
            </div>

            <div slot="table" class="u-p-x-20 u-p-t-20" slot-scope="{ data }">
                <common-table :data="data" :columns="tableConfig.column">
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="viewDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { cloneDeep } from "lodash"
    import { Component } from "vue-property-decorator"
    import {
        tableConfig,
        Row,
        cacheKey,
        column,
        column1,
        tableFilter,
        tableFilter1,
    } from "."

    @Component({
        name: routesMap.base.sms.list,
        components: { TableContainer, CommonTable },
    })
    export default class Index extends BaseTableController<Row> {
        private tableConfig = tableConfig()

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.base.sms.list,
        }

        tabName(name: string) {
            console.log(this.tableConfig)
            if (name === "验证码短信") {
                this.tableConfig.column = cloneDeep(column1)
                this.tableConfig.filter = cloneDeep(tableFilter1)
            } else {
                this.tableConfig.column = cloneDeep(column)
                this.tableConfig.filter = cloneDeep(tableFilter)
            }
            console.log(name)
        }

        add() {
            sessionStorage.removeItem(cacheKey)
            this.$router.push({
                name: routesMap.base.sms.add,
            })
        }

        viewDetail(row: Row) {
            this.$router.push({
                name: routesMap.base.sms.detail,
                query: {
                    id: row.id + "",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .container {
        background: #f6f7f9;

        ::v-deep.table-tabs {
            background: #f6f7f9;
        }
    }
</style>
