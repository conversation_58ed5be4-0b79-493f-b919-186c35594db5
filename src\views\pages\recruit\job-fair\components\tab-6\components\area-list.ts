import { TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"

export function tableConfig(id: string): TableConfig {
    return {
        model: sdk.core.model("job_fair_booth_area").list("for_operate"),
        defaultPageSize: 10,
        predict: {
            booth_name: "job_fair_booth#booth_name",
            booth_address: "job_fair_booth#booth_address",
            area_number: "",
            agent_name: "agent#agent_name",
        },
        preFilter: {
            job_fair_id: id,
            is_del: "0",
        },
        column: [
            {
                label: "展区名称",
                prop: "1",
                minWidth: "120",
                formatter: (row) => {
                    return `${row.booth_name}：${row.booth_address}`
                },
            },
            {
                label: "展位号",
                prop: "area_number",
                minWidth: "120",
            },
            {
                label: "企业名称",
                prop: "agent_name",
                minWidth: "180",
            },
            {
                label: "操作",
                prop: "h",
                minWidth: "120",
            },
        ],
    }
}
