<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="请先实名认证"
        width="660px"
    >
        <div class="u-flex justify-content-center">
            <div class="container">
                <el-form ref="form" :model="data" :rules="rules">
                    <el-form-item class="login-input" prop="name">
                        <el-input
                            v-model="data.name"
                            clearable
                            class="login-input"
                            placeholder="请输入"
                        >
                            <template slot="prefix">姓名</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="login-input" prop="citizen_no">
                        <el-input
                            v-model="data.citizen_no"
                            clearable
                            class="login-input"
                            placeholder="请输入"
                            @input="clearValidate"
                        >
                            <template slot="prefix">身份证号码</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="login-input" prop="mobile">
                        <el-input
                            id="username"
                            v-model="data.mobile"
                            clearable
                            placeholder="请输入手机号"
                            @input="clearValidate"
                        >
                            <template slot="prefix">手机号</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="login-input" prop="code">
                        <el-input
                            v-model="data.code"
                            placeholder="请输入验证码"
                            @input="clearValidate"
                        >
                            <template slot="prefix">验证码</template>
                            <template slot="suffix">
                                <el-button
                                    v-if="canSendSms"
                                    type="text"
                                    class="send-sms"
                                    :loading="codeLoading"
                                    @click="sendSms"
                                >
                                    获取验证码
                                </el-button>
                                <div v-else>{{ remainTime }} 后重新获取</div>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-form>
                <el-button
                    class="btn"
                    type="primary"
                    :loading="loading"
                    @click="confirm"
                >
                    提交
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
    import { FormRule } from "@/core-ui/component/form"
    import { rules } from "@/core-ui/component/form/rule"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { certificationService } from "@/core-ui/service/passport/certification"
    import { verifyService } from "@/core-ui/service/passport/verify"
    import { Form as ElForm, Message } from "element-ui"
    import { Component, Prop, Ref, Watch } from "vue-property-decorator"
    export const remainTimeCacheKey = "remainTimeCacheKey"

    @Component({ components: {} })
    export default class CertificationPop extends DialogController {
        private data = {
            mobile: "",
            code: "",
            name: "",
            citizen_no: "",
        }

        @Prop()
        private readonly mobile!: string

        @Watch("mobile", { immediate: true })
        private changeMobile() {
            this.data.mobile = this.mobile
        }

        @Watch("data.mobile")
        private changeDataMobile() {
            this.$emit("setMobile", this.data.mobile)
        }

        private rules: Record<string, FormRule[]> = {
            mobile: [
                { required: true, message: "手机号不能为空" },
                ...rules.mobile,
            ],
            code: [
                { required: true, message: "验证码不能为空", trigger: "blur" },
                {
                    validator: (_, __, callback) => {
                        if (!this.getIsSendSms()) {
                            return callback(new Error("请先获取验证码"))
                        }
                        callback()
                    },
                },
            ],
            citizen_no: [...rules.idCard],
            name: [{ required: true, message: "姓名不能为空" }],
        }

        private getCodeMessage() {
            return this.codeMessage
        }

        private getIsSendSms() {
            return this.isSendSms
        }

        @Ref()
        private readonly form?: ElForm

        private canSendSms = true
        private remainTime = 0
        private isSendSms = false
        private codeMessage = ""
        private codeLoading = false

        mounted() {
            const remainTime = +(
                window.localStorage.getItem(remainTimeCacheKey) || 0
            )
            if (remainTime) {
                this.canSendSms = false
                this.startTimer(remainTime)
            }
        }

        private confirm() {
            this.codeMessage = ""
            this.form?.validate().then((r) => {
                if (!r) {
                    return
                }
                this.loading = true
                certificationService
                    .threeElementCheck({
                        citizen_no: this.data.citizen_no,
                        name: this.data.name,
                        mobile: this.data.mobile,
                        verify_code: this.data.code,
                    })
                    .then(() => {
                        Message.success("实名认证成功")
                        this.$emit("success")
                    })
                    .catch((r) => {
                        this.codeMessage = r
                        this.form?.validate()
                    })
                    .finally(() => {
                        this.loading = false
                    })
            })
        }

        private sendSms() {
            this.form?.validateField("mobile", (r) => {
                if (!r && this.canSendSms) {
                    this.codeLoading = true
                    return verifyService
                        .sendSms(this.data.mobile)
                        .then(() => {
                            this.startTimer()
                        })
                        .finally(() => {
                            this.codeLoading = false
                        })
                }
            })
        }

        destroyed() {
            this.resetTimer()
        }

        resetTimer() {
            this.timer && clearInterval(this.timer)
        }

        private timer = 0
        private startTimer(start?: number) {
            this.remainTime = start || 59
            this.canSendSms = false
            this.isSendSms = true
            clearInterval(this.timer)
            this.timer = setInterval(() => {
                if (this.remainTime === 1) {
                    clearInterval(this.timer)
                    this.canSendSms = true
                }
                this.remainTime--
                window.localStorage.setItem(
                    remainTimeCacheKey,
                    this.remainTime.toString()
                )
            }, 1000)
            window.localStorage.setItem(
                remainTimeCacheKey,
                this.remainTime.toString()
            )
        }

        private clearValidate() {
            this.codeMessage = ""
            this.form?.clearValidate()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "login-form/login-form.less";
    .container {
        height: auto;
        .login-input {
            ::v-deep .el-input__inner {
                height: 48px;
                padding-left: 120px;
            }
        }
    }
</style>
