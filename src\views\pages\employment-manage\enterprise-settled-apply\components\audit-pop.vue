<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="审核"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 create-position">
            <detail-row-col
                :labelStyle="{ width: '130px' }"
                :list="items"
            ></detail-row-col>
            <div class="bg-gary">
                <form-builder
                    ref="formBuilder"
                    labelWidth="117px"
                    modelName="xg_agent_apply"
                    actionName="auth_data"
                ></form-builder>
            </div>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="confirm"
                    class="custom-btn btn u-m-0"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        BuildFormConfig,
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { ColItem } from "@/views/components/detail-row-col"
    import { Row, predict, buildItems } from "./detail"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"

    const createPositionFormSections: BuildFormConfig = {
        forms: [
            {
                label: "审核",
                type: FormType.Select,
                prop: "auth_status",
                required: true,
            },
            {
                label: "审核不通过原因",
                type: FormType.Select,
                prop: "auth_status_memo",
                needListen: true,
            },
            {
                label: "其他备注",
                type: FormType.Text,
                prop: "auth_status_memo2",
                needListen: true,
                option: {
                    type: "textarea",
                },
                handlerDisplay: (data) => {
                    return data.auth_status_memo === "其他"
                },
            },
        ],
        sdkModel: "xg_agent_apply",
        sdkAction: "auth_data",
        select_list: [],
    }

    @Component({ components: { FormBuilder, DetailRowCol } })
    export default class CreatePositionPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: "" })
        private readonly rowId!: number

        private items: ColItem[] = buildItems({}, this)
        private detail: Row | null = null

        private createPositionFormSections = createPositionFormSections

        onOpen() {
            this.init()
            pageLoading(() => {
                return this.getDetail()
            })
        }

        private show = false
        showMobile() {
            this.show = true
            this.items = buildItems(this.detail || {}, this)
        }

        private init() {
            this.show = false
            this.createPositionFormSections.select_list = [{ v: 0, id: this.rowId }]
            return buildFormSections(this.createPositionFormSections).then((r) => {
                this.buildForm(r.forms)
            })
        }

        private getDetail() {
            const id = this.rowId
            const model = sdk.core.model("xg_agent_apply").detail(id, "")
            return model.query().then((r) => {
                const row = sdk.buildRow<Row>(r.row, predict)
                this.detail = row
                this.items = buildItems(row, this)
                return row
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            if (data.auth_status_memo === "其他" && data.auth_status_memo2) {
                data.auth_status_memo = data.auth_status_memo2
            }
            pageLoading(() => {
                return sdk.core
                    .model("xg_agent_apply")
                    .action("auth_data")
                    .updateInitialParams({
                        selected_list: [
                            {
                                id: this.rowId,
                                v: 0,
                            },
                        ],
                    })
                    .addInputs_parameter(data)
                    .execute()
                    .then(() => {
                        this.$message.success("审核完成！")
                        this.$emit("refresh")
                    })
                    .finally(() => {
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .select {
        width: 320px;
    }
    .btn {
        width: 100px;
        height: 36px;
    }
    .create-position {
        ::v-deep .el-form-item__label {
            text-align: left;
            color: #555;
            font-size: 14px;
        }
    }

    .bg-gary {
        background-color: #fafafa;
        padding: 20px;
    }
</style>
