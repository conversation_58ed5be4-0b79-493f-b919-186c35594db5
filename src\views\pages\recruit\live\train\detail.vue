<template>
    <div
        class="core-ui-table-container container"
        v-if="row"
        :key="refreshQueryParams"
    >
        <div class="core-ui-custom-header w-100">
            <div class="title u-flex u-row-between w-100">
                <bread-crumb :items="breadcrumbs" />
                <div class="u-flex">
                    <template
                        v-if="
                            row.train_audit_status === liveAuditStatus.审核通过
                        "
                    >
                        <el-button
                            v-if="
                                row.train_shelf_status ===
                                liveShelfStatus.未发布
                            "
                            type="primary"
                            @click="toggleShell(row)"
                        >
                            上架
                        </el-button>
                        <el-button
                            v-else
                            type="primary"
                            @click="toggleShell(row)"
                        >
                            下架
                        </el-button>
                    </template>
                    <el-button
                        v-if="
                            row.live_audit_status !==
                                liveAuditStatus.审核通过 ||
                            row.live_shelf_status === liveShelfStatus.未发布
                        "
                        type="primary"
                        @click="edit(row)"
                        plain
                    >
                        编辑
                    </el-button>
                    <el-button
                        v-if="row.train_audit_status === liveAuditStatus.待审核"
                        type="primary"
                        @click="showAudit = true"
                    >
                        审核
                    </el-button>
                    <el-button
                        v-if="row.train_audit_status === liveAuditStatus.草稿"
                        type="primary"
                        @click="toAudit(row)"
                    >
                        提交审核
                    </el-button>
                </div>
            </div>
        </div>
        <div class="content bg-white u-p-30">
            <div class="title u-flex">
                <div>培训基本信息</div>
                <div class="u-m-l-10 status">
                    {{ row.train_audit_status_label }}
                </div>
                <div class="u-m-l-10 u-font-12 color-6">
                    {{ row.train_shelf_status_label }}
                </div>
            </div>
            <detail-row-col
                :list="computeList"
                :labelStyle="labelStyle"
            ></detail-row-col>
        </div>
        <div class="u-p-y-30 u-m-t-20">
            <el-tabs v-model="current">
                <el-tab-pane label="培训介绍" name="0" lazy>
                    <Tab1 :detail="row"></Tab1>
                </el-tab-pane>
                <el-tab-pane label="操作日志" name="1" lazy>
                    <Tab2 :detail="row"></Tab2>
                </el-tab-pane>
            </el-tabs>
        </div>
        <CommonPop
            labelWidth="140px"
            v-model="showAudit"
            title="审核"
            sdkModel="training_record"
            sdkAction="setAudit"
            @refresh="init"
            :id="row.id"
        />
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"
    import {
        LiveAuditStatus,
        LiveShelfStatus,
        predict,
        toAudit,
        toggleShell,
    } from "."
    import Tab1 from "./components/tab-1.vue"
    import Tab2 from "./components/tab-2.vue"

    @Component({
        name: routesMap.recruit.live.trainDetail,
        components: { DetailRowCol, Tab1, Tab2, CommonPop },
    })
    export default class Template extends Vue {
        private id = ""
        private row: any = null
        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.live.trainDetail,
        }

        private current = "0"
        private showAudit = false

        private liveAuditStatus = LiveAuditStatus
        private liveShelfStatus = LiveShelfStatus

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: "培训管理详情",
                    to: {
                        name: routesMap.recruit.live.trainDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.live.trainDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private labelStyle = {
            fontSize: "14px",
            marginRight: "10px",
            lineHeight: "34px",
            "word-break": "keep-all",
            width: "auto",
            color: "#555",
        }

        private get from() {
            return this.$route.query.from as string
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = (this.$route.query.id as string) || this.id || ""
            this.setBreadcrumbs()
            if (!this.id) return
            pageLoading(() => {
                return sdk.core
                    .model("training_record")
                    .detail(this.id, "details_operator")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                        console.log("r1", JSON.parse(JSON.stringify(this.row)))
                    })
            })
        }

        private toggleShell(row: any) {
            toggleShell(row).then(() => {
                this.init()
            })
        }

        private toAudit(row: any) {
            toAudit(row).then(() => {
                this.init()
            })
        }

        private edit(row: any) {
            this.$router.push({
                name: routesMap.recruit.live.trainAdd,
                query: {
                    id: row?.id || undefined,
                    from: this.$route.name,
                },
            })
        }

        private get computeList() {
            const h = this.$createElement
            return [
                {
                    label: "培训名称",
                    value: this.row.train_name || "",
                },
                {
                    label: "培训专业",
                    value: this.row.train_major || "",
                },
                {
                    label: "培训时间",
                    value: this.row.train_date || "",
                },
                {
                    label: "所属机构",
                    value: this.row.train_agency || "",
                },
                {
                    label: "培训天数",
                    value: this.row.train_days || "",
                },
                {
                    label: "计划人数",
                    value: this.row.train_students || "",
                },
                {
                    label: "收费标准",
                    value: this.row.train_cost || "",
                },
            ]
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";

    ::v-deep .detail-row .item {
        line-height: 34px;
        font-size: 14px;
        color: #333;
        margin: 0;
    }
    .content {
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
            .status {
                line-height: 16px;
                font-size: 12px;
                font-weight: 400;
                background-color: #d7f8ea;
                color: #22bd7a;
                padding: 0 8px;
            }
        }
    }
</style>
