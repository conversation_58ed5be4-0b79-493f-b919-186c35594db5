import { ExcelGenerator } from "@/core-ui/component/table/excel-generator"
import { find, sum } from "lodash"

export function exportExcel(list: any, list2: any) {
    console.log("l", JSON.parse(JSON.stringify(list2)))
    function getData(type: string, key: string) {
        const d = (find(list2, (e: any) => e.indicator_type === type)?.data ||
            [])[0]
        d.ratio = getRatio(d.complete, d.target)
        return d[key] || 0
    }
    function getData2(type: string, key: string, index = 0) {
        const d = (find(list, (e: any) => e.indicator_type === type)?.data ||
            [])[index]
        d.ratio = getRatio(d.complete_count, d.target_value)
        return d[key] || 0
    }
    function getRatio(v1: any, v2: any) {
        return ((+v1 * 100) / +v2 || 0).toFixed(2) + "%"
    }
    const sum1 = sum([
        +getData2("006", "complete_count_01"),
        +getData2("006", "complete_count_02"),
    ])
    const sum2 = sum([
        +getData2("006", "target_value_02"),
        +getData2("006", "target_value_01"),
    ])
    const sum3 = sum([
        +getData2("006", "complete_count_03"),
        +getData2("006", "complete_count_04"),
        +getData2("006", "complete_count_05"),
        +getData2("006", "complete_count_06"),
        +getData2("006", "complete_count_07"),
        +getData2("006", "complete_count_08"),
        +getData2("006", "complete_count_09"),
        +getData2("006", "complete_count_10"),
    ])
    const sum4 = sum([
        +getData2("006", "target_value_03"),
        +getData2("006", "target_value_04"),
        +getData2("006", "target_value_05"),
        +getData2("006", "target_value_06"),
        +getData2("006", "target_value_07"),
        +getData2("006", "target_value_08"),
        +getData2("006", "target_value_09"),
        +getData2("006", "target_value_10"),
    ])
    ExcelGenerator.execute({
        fileName: "就业综合指数进展情况",
        columns: [
            "项目",
            "子项目",
            "",
            "2024年完成数",
            "计划数",
            "完成计划占比",
        ] as any,
        rows: [
            [
                "（一）核心指标（25分）",
                "1、城镇新增就业 （25分）",
                "",
                getData("001", "complete"),
                getData("001", "target"),
                getData("001", "ratio"),
            ],
            [
                "（二）重点群体得分（45分）",
                "2、“才聚荆楚”工程新增高校毕业生就业创业人数（20分）",
                "",
                getData("002", "complete"),
                getData("002", "target"),
                getData("002", "ratio"),
            ],
            [
                "",
                "3、离校未就业高校毕业生帮扶就业率（5 分）",
                "",
                (getData("003", "complete") * 100).toFixed(2) + "%",
                (getData("003", "target") * 100).toFixed(2) + "%",
                getData("003", "ratio"),
            ],
            [
                "",
                "4、就业创业推进乡村振兴工作情况（10分）",
                "新增返乡创业人数",
                getData2("004", "return_complete_count"),
                getData2("004", "return_target_value"),
                getRatio(
                    getData2("004", "return_target_value"),
                    getData2("004", "return_complete_count")
                ),
            ],
            [
                "",
                "",
                "脱贫人口务工人数",
                getData2("004", "poverty_complete_value"),
                getData2("004", "poverty_target_value"),
                getRatio(
                    getData2("004", "poverty_complete_value"),
                    getData2("004", "poverty_target_value")
                ),
            ],
            [
                "（三）就业政策落实情况（40 分）",
                "5、中央就业补助资金执行率（20分）",
                "",
                getData("005", "complete") + "万元",
                getData("005", "target") + "万元",
                getData("005", "ratio"),
            ],
            [
                "",
                "6、就业创业政策落实情况（20分）",
                "覆盖率",
                (getData("006", "complete") * 100).toFixed(2) + "%",
                (getData("006", "target") * 100).toFixed(2) + "%",
                getData("006", "ratio"),
            ],
            ["", "", "保障性补贴人数", sum1, sum2, getRatio(sum1, sum2)],
            [
                "",
                "",
                "就业困难人员社保",
                getData2("006", "complete_count_01"),
                getData2("006", "target_value_01"),
                getRatio(
                    getData2("006", "complete_count_01"),
                    getData2("006", "target_value_01")
                ),
            ],
            [
                "",
                "",
                "公岗补贴",
                getData2("006", "complete_count_02"),
                getData2("006", "target_value_02"),
                getRatio(
                    getData2("006", "complete_count_02"),
                    getData2("006", "target_value_02")
                ),
            ],
            ["", "", "促进性补贴人数", sum3, sum4, getRatio(sum3, sum4)],
            [
                "",
                "",
                "高校毕业生社保补贴",
                getData2("006", "complete_count_03"),
                getData2("006", "target_value_03"),
                getRatio(
                    getData2("006", "complete_count_03"),
                    getData2("006", "target_value_03")
                ),
            ],
            [
                "",
                "",
                "求职补贴",
                getData2("006", "complete_count_04"),
                getData2("006", "target_value_04"),
                getRatio(
                    getData2("006", "complete_count_04"),
                    getData2("006", "target_value_04")
                ),
            ],
            [
                "",
                "",
                "创业补贴",
                getData2("006", "complete_count_05"),
                getData2("006", "target_value_05"),
                getRatio(
                    getData2("006", "complete_count_05"),
                    getData2("006", "target_value_05")
                ),
            ],
            [
                "",
                "",
                "吸纳就业补贴",
                getData2("006", "complete_count_06"),
                getData2("006", "target_value_06"),
                getRatio(
                    getData2("006", "complete_count_06"),
                    getData2("006", "target_value_06")
                ),
            ],
            [
                "",
                "",
                "首次来鄂",
                getData2("006", "complete_count_10"),
                getData2("006", "target_value_10"),
                getRatio(
                    getData2("006", "complete_count_10"),
                    getData2("006", "target_value_10")
                ),
            ],
            [
                "",
                "",
                "创业贷款（个人）",
                getData2("006", "complete_count_08"),
                getData2("006", "target_value_08"),
                getRatio(
                    getData2("006", "complete_count_08"),
                    getData2("006", "target_value_08")
                ),
            ],
            [
                "",
                "",
                "创贷（企业）",
                getData2("006", "complete_count_09"),
                getData2("006", "target_value_09"),
                getRatio(
                    getData2("006", "complete_count_09"),
                    getData2("006", "target_value_09")
                ),
            ],
            [
                "",
                "",
                "一次性扩岗补助",
                getData2("006", "complete_count_07"),
                getData2("006", "target_value_07"),
                getRatio(
                    getData2("006", "complete_count_07"),
                    getData2("006", "target_value_07")
                ),
            ],
        ].map((e) =>
            e.map((i) => ({
                v: i,
                s: {
                    alignment: {
                        vertical: "center",
                        horizontal: "center",
                    },
                },
            }))
        ) as any,
        mergeCells: [
            {
                s: { r: 0, c: 1 },
                e: { r: 0, c: 2 },
            },
            {
                s: { r: 2, c: 0 },
                e: { r: 5, c: 0 },
            },
            {
                s: { r: 6, c: 0 },
                e: { r: 19, c: 0 },
            },
            {
                s: { r: 4, c: 1 },
                e: { r: 5, c: 1 },
            },
            {
                s: { r: 7, c: 1 },
                e: { r: 19, c: 1 },
            },
            {
                s: { r: 1, c: 1 },
                e: { r: 1, c: 2 },
            },
            {
                s: { r: 2, c: 1 },
                e: { r: 2, c: 2 },
            },
            {
                s: { r: 3, c: 1 },
                e: { r: 3, c: 2 },
            },
            {
                s: { r: 6, c: 1 },
                e: { r: 6, c: 2 },
            },
        ],
    })
}
