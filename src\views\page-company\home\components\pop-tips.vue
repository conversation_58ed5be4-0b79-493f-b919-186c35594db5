<template>
    <div class="pre-view" v-if="value">
        <div class="custom-content">
            <div class="content-bg">
                <div class="header">
                    <div class="name">{{ name || "客服顾问" }}</div>
                    <div class="remark">为您提供一对一咨询</div>
                </div>
                <div class="qr-code">
                    <img
                        width="146px"
                        height="146px"
                        v-if="src"
                        class="qr-image"
                        :src="src"
                    />
                </div>
            </div>
        </div>

        <div class="mask" @click="close"></div>
    </div>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import { sdk } from "@/service"
    import { clientConfigService } from "@/service/client-config"
    import { userService } from "@/service/service-user"
    import { Component, Model, Prop, Vue, Watch } from "vue-property-decorator"

    const isXg = config.envProject === EnvProject.孝感项目

    @Component({ components: {} })
    export default class DialogAddedTips extends Vue {
        @Model("input")
        private value!: string

        @Prop({ default: "您已申请成功！等待审核~" })
        private msg!: string

        @Prop({ default: 1 })
        private msgCode!: 1 | 2 | 3 | 4

        private isXg = isXg

        @Watch("value", { immediate: true })
        protected onValueChange(cur: any, old: any) {
            if (cur !== old) {
                this.myValue = cur
            }
        }

        private myValue = ""

        private src = ""

        private name = ""

        private goLogin() {
            userService.logout()
        }

        mounted() {
            this.src = !isXg ? "/img/contact-jz.png" : "/img/contact.png"
            clientConfigService.setup().then((r) => {
                if (r?.advInfo?.qr_code) {
                    this.src = sdk.buildImage(r.advInfo.qr_code)
                    this.name = r.advInfo.name
                }
            })
        }

        private close() {
            if (this.msgCode === 4) {
                return
            }
            this.$emit("input", false)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .pre-view {
        position: fixed;
        top: 0px;
        bottom: 0px;
        left: 0px;
        right: 0px;
        z-index: 99999;

        .mask {
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: #000;
            opacity: 0.5;
        }

        .custom-content {
            width: 295px;
            height: 420px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-55%);
            box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            z-index: 30;
            padding-top: 30px;
            padding-left: 30px;
            padding-right: 30px;
            padding-bottom: 20px;
            background-image: url("../img/qr-code.png");
            background-size: 295px 420px;
        }

        .header {
            text-align: center;
            padding-top: 74px;
            color: #222222;

            .remark {
                padding-top: 35px;
            }
        }

        .qr-code {
            margin-top: 30px;
            border: 8px solid #e0e7ff;
            border-radius: 6px;
            margin-left: auto;
            margin-right: auto;

            width: 160px;
            height: 160px;

            .qr-image {
                width: 146px;
                height: 146px;
            }
        }
    }

    .is-apply {
        color: #22bd7a !important;
    }
</style>
