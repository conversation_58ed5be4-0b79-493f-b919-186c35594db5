export interface ListRow {
    name_hide: string
    sex: number
    getAge: string
    id_card_hide: string
    mobile_hide: string
    uniplat_uid_calc: number
    reg_residence_property: number
    education: number
    employment_status: number
    update_time: string
    resume_update_time: string
    profile_id: string
    birth_date: string
    create_by: string
    uniplat_uid: string
    is_job_willing: true
    id: string
    update_by: string
    job_willing_type_work: string
    create_time: string
    job_willing_industry: string
    uniplat_version: number
    household_province_name: string
    household_city_name: string
    household_area_name: string
    household_countryside_name: string
    household_village_name: string
    is_training_willingness_name: true
    nation: string
    political_outlook: number
    permanent_province_name: string
    permanent_city_name: string
    permanent_area_name: string
    study_speciality: string
    graduate_school: string
    graduate_date: string
    work_province_name: string
    work_city_name: string
    work_county_name: string
    job_willing_province_id: string
    job_willing_province_id_two: string
    job_willing_province_id_three: string
    black_operator: string
    black_operator_time: string
    education_name: string
    employment_status_name: string
    job_willing_type_work_display: string
    political_outlook_name: string
    employment_type_name: string
    job_industry_name: string
    job_type_work_name: string
    job_willing_province_id_name: string
    job_willing_province_id_two_name: string
    job_willing_province_id_three_name: string
    start_job_industry_name: string
    start_job_type_work_name: string
    training_willingness_work_type_name: string
    qualification_level_name: string
    _access_key: string
    is_start_job_name: string
    is_train_job_name: string
    tag_names?: string
    labels?: string
}
