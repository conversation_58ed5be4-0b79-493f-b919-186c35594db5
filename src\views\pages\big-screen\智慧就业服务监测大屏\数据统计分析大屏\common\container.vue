<template>
    <container :ts="false" :nav="false" class="container-box">
        <div class="d-flex flex-column flex-fill">
            <div class="banner d-flex justify-content-center">
                <div class="time-box">{{ curTime }}</div>
                <div class="header-title">{{ title }}</div>
                <img src="../header.webp" />
                <div class="update-time-box"></div>

                <div class="nav-box">
                    <slot name="nav"></slot>
                    <el-button
                        v-if="btnText"
                        type="primary"
                        class="custom-button"
                        @click="$emit('clickRightBtn')"
                        >{{ btnText }} <i class="el-icon-arrow-right"></i
                    ></el-button>
                </div>
            </div>
            <div class="">
                <slot></slot>
            </div>
        </div>
        <div class="router u-flex">
            <div v-if="routes[0]" @click="toHome">首页</div>
            <div v-if="routes[1]" class="u-flex" @click="up">
                <i class="el-icon-arrow-right"></i>
                上一级
            </div>
        </div>
    </container>
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import "../../../common/time-range.less"
    import "../../../common/style/style.less"
    import Container from "../../../common/container.vue"
    import moment from "moment"
    import { updateBigScreenType } from "../../../common/rem"
    import { loopTimer } from "../../../招聘会大屏/tools/looptimer"
    import { routesMap } from "@/router/direction"

    @Component({
        components: {
            Container,
        },
    })
    export default class Index extends Vue {
        @Prop({ default: "" })
        private btnText!: string

        @Prop({ default: "孝感市2025年春风行动数据统计分析大屏" })
        private title!: string

        private curTime = ""

        private monthValue: any = ""

        @Watch("monthValue")
        monthValueChanged() {
            this.$emit("monthValueChange", this.monthValue)
        }

        created() {
            updateBigScreenType("招聘会大屏")
            document.title = this.title
            this.getCurTime()
            this.setTime()
            this.getRoutes()
        }

        @Watch("$route.query", { deep: true, immediate: true })
        private timeChanged() {
            this.setTime()
        }

        private setTime() {
            if (this.$route?.query?.page_time) {
                this.monthValue = (
                    (this.$route?.query?.page_time as string) || ""
                ).split("-")
            } else {
                this.monthValue = [
                    moment().add(-11, "month").format("YYYY.MM"),
                    moment().format("YYYY.MM"),
                ]
            }
        }

        @loopTimer(2000)
        private getCurTime() {
            this.curTime = moment().format("YYYY年MM月DD日 HH:mm")
        }

        private pickerOptions = {
            disabledDate(time: any) {
                return (
                    time.getTime() <= moment("2022-12-31") ||
                    time.getTime() >= moment()
                )
            },
        }

        private routes: any[] = []

        private get routesName() {
            return this.$route?.name || ""
        }

        private getRoutes() {
            if (
                this.routesName === routesMap.bigScreen.dataStatisticsAnalysis.index
            ) {
                this.routes = []
                return
            }
            if (
                [
                    routesMap.bigScreen.dataStatisticsAnalysis.list1,
                ].includes(this.routesName)
            ) {
                this.routes = [{ title: "首页" }]
            }
            if (
                [routesMap.bigScreen.dataStatisticsAnalysis.personDetail].includes(
                    this.routesName
                )
            ) {
                this.routes = [
                    { title: "首页" },
                    {
                        name: routesMap.bigScreen.dataStatisticsAnalysis.list1,
                    },
                ]
            }
        }

        private toHome() {
            this.$router.push({
                name: routesMap.bigScreen.dataStatisticsAnalysis.index,
            })
        }

        private up() {
            const row = this.routes[1]
            this.$router.push({
                name: row.name,
                query: {
                    type: (this.$route.query?.type as string) || "",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .container {
        width: 100%;
        height: 100%;
        overflow-y: scroll;
        overflow-x: hidden;
        background-image: url("../assets/bg.webp");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        &::-webkit-scrollbar {
            display: none;
        }

        /deep/ .custom-button {
            &.el-button {
                background: #004fb8;
                border-radius: 6px;
                border: 1px solid #004fb8;
            }
        }

        .banner {
            position: relative;
            height: 94px;
            margin-bottom: 15px;

            img {
                width: 100%;
                height: 100px;
            }

            .header-title {
                position: absolute;
                font-size: 36px;
                font-weight: 600;
                color: #ffffff;
                line-height: 36px;
                text-shadow: 0px 3px 3px rgba(0, 0, 0, 0.5);
                top: 50%;
                transform: translateY(calc(-50%));
            }
        }
    }

    /deep/ .nav-box {
        position: absolute;
        right: 20px;
        bottom: 5px;
    }

    .time-box {
        position: absolute;
        bottom: 52px;
        left: 20px;
        font-size: 16px;
        color: #89b9ff;
    }

    .update-time-box {
        position: absolute;
        bottom: 25px;
        right: 20px;
        font-size: 16px;
        color: #89b9ff;
    }
    .container-box {
        position: relative;
        .router {
            color: #89b9ff;
            font-size: 14px;
            position: absolute;
            top: 28px;
            right: 30px;
            cursor: pointer;
        }
    }
</style>

<style lang="less">
    .app-container {
        min-width: 1060px !important;
    }

    .el-message {
        display: none !important;
    }

    .el-notification {
        display: none !important;
    }

    .big-screen-date-picker {
        width: 565px !important;

        .el-picker-panel__content {
            padding: 10px;
            padding-top: 5px;
        }

        .el-picker-panel__body {
            width: 565px !important;
            min-width: 565px !important;

            td {
                padding: 4px 0px;

                div {
                    padding: 0px;
                    height: 30px;
                }

                a {
                    width: 60px;
                    height: 30px;
                    line-height: 30px;
                }
            }
        }
    }
</style>
