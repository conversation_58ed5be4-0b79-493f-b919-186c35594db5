import { config, EnvProject } from "@/config"
import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"

function tableFilter(isPublic = false): TableFilter[] {
    return [
        {
            label: "姓名",
            type: FormType.Text,
            prop: "name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "电话",
            type: FormType.Text,
            prop: "mobile",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "学历",
            type: FormType.Select,
            prop: "education",
        },
        {
            label: "身份证",
            type: FormType.Text,
            prop: "id_card",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "岗位名称",
            type: FormType.Text,
            prop: "position.name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "来源",
            type: FormType.Select,
            prop: "created_from",
        },
        {
            label: "资格审核",
            type: FormType.Select,
            prop: "qualification_audit_status",
            hide: !isPublic,
        },
        {
            label: "投递时间",
            type: FormType.DatePicker,
            option: {
                type: "datetimerange",
            },
            prop: "create_time",
        },
        {
            label: "查阅简历",
            type: FormType.Select,
            prop: "company_view",
            hide:
                isPublic ||
                ![EnvProject.黄州项目, EnvProject.掇刀项目].includes(
                    config.envProject
                ),
        },
    ]
}

export const predict = {
    resume_file: "profile#basic_info#file_url",
    name: "",
    position_is_del: "position#is_del_label",
    position_name: "position#name",
    position_id: "position#id",
    position_access_key: "position#_access_key",
    agent_name: "position#agent#agent_name",
    region_name: "position#area#region_name",
    create_time: "label",
    status: "label",
    status_memo: "label",
    created_from: "label",
    contact_status: "label",
    qualification_audit_status: "label",
    client: "label",
    interview_time: "label",
    interview_address: "label",
    position_type: "position#position_type_label",
    tags: "tags",
    profile_access_key: "profile#_access_key",
    // 信息
    mobile_encode: "",
    mobile: "",
    id_card: "",
    id_card_encode: "",
    sex: "label",
    age: "",
    education: "label",
    employment_status: "label",
    household_province: "household_province#region_name",
    household_city: "household_city#region_name",
    household_area: "household_area#region_name",
    household_detail: "household_address_detail",
    permanent_province: "permanent_province#region_name",
    permanent_city: "permanent_city#region_name",
    permanent_area: "permanent_area#region_name",
    permanent_detail: "permanent_detail",
    // profile
    profile_id: "profile#id",
    p_mobile_encode: "profile#mobile_hide",
    p_mobile: "profile#mobile",
    p_id_card: "profile#id_card",
    p_id_card_encode: "profile#id_card_hide",
    p_sex: "profile#sex_label",
    p_age: "profile#getAge",
    p_education: "profile#basic_info#education_label",
    p_employment_status:
        "profile#user_profile_current_job_info#employment_status_label",
    job_willing_industry: "profile#job_willingness#job_willing_industry_label",
    profile_created_from: "profile#created_from_label",
    avatar: "profile#user#avatar",
    political_outlook: "profile#basic_info#political_outlook_label",
    p_household_province: "profile#user#household_province_name",
    p_household_city: "profile#user#household_city_name",
    p_household_area: "profile#user#household_area_name",
    p_household_detail: "profile#basic_info#household_detail",
    p_permanent_province: "profile#user#permanent_province_name",
    p_permanent_city: "profile#user#permanent_city_name",
    p_permanent_area: "profile#user#permanent_area_name",
    p_permanent_detail: "profile#basic_info#permanent_detail",
    job_years: "profile#user_profile_current_job_info#job_years",
    self_evaluation: "profile#basic_info#self_evaluation",
    company_view: "label",
}

export enum PositionType {
    普通岗位 = 1,
    公益岗位,
}
export enum Status {
    待处理 = 0,
    待面试 = 10,
    待入职 = 15,
    已入职 = 20,
    不合适 = 30,
    已过期 = 40,
}

export function tableConfig(list = "company_list"): TableConfig {
    return {
        model: sdk.core.model("xg_candidate_order").list(list),
        filter: tableFilter(list !== "company_list"),
        defaultPageSize: 10,
        predict: predict,
        column: columns(list !== "company_list"),
        oneTabFilter: true,
        tabPages:
            list === "company_list"
                ? [
                      "全部",
                      "待处理",
                      "待面试",
                      "通过初筛",
                      "已入职",
                      "不合适",
                      "已过期",
                  ]
                : [],
    }
}

export function columns(isPublic = false): TableColumn[] {
    return [
        {
            label: "用户信息",
            prop: "name",
            align: "left",
            showOverflowTip: true,
        },
        {
            label: "投递信息",
            prop: "position_name",
            align: "left",
            showOverflowTip: true,
        },
        {
            label: "求职意向",
            prop: "job_willing_industry_label",
            showOverflowTip: true,
        },
        {
            label: "投递来源",
            prop: "agent_name1",
            width: "200px",
            align: "left",
            render(h, row) {
                return h("div", {}, [
                    h("div", {}, row.created_from_label),
                    h("div", { class: "color-9" }, row.client_label),
                ])
            },
            showOverflowTip: true,
        },
        // { label: "投递方式", prop: "created_from_label", showOverflowTip: true },
        // { label: "投递企业", prop: "agent_name", showOverflowTip: true },
        {
            label: "投递状态",
            prop: "status_label",
            width: "140px",
            align: "left",
            render(h, row) {
                return h("div", {}, [
                    h("div", {}, row.status_label),
                    // h(
                    //     "div",
                    //     { class: "color-9" },
                    //     row.status_memo_label
                    // ),
                ])
            },
            showOverflowTip: true,
        },
        {
            label: "查阅简历",
            prop: "company_view_label",
            showOverflowTip: true,
            width: "140px",
            hide:
                isPublic ||
                ![EnvProject.黄州项目, EnvProject.掇刀项目].includes(
                    config.envProject
                ),
        },
        { label: "操作", prop: "h", width: "100px", showOverflowTip: true },
    ]
}
