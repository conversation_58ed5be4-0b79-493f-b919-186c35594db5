<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            @loaded="loaded"
            @tabName="onTabNameChange"
            @getTabPageDefaultTotal="getTabPageDefaultTotal"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div
                        slot="order"
                        slot-scope="scope"
                        @click="sss(scope.row)"
                    >
                        {{ Number(scope.index) + 1 }}
                    </div>
                    <div slot="extend_name" slot-scope="scope">
                        <el-button
                            v-if="scope.row.profile_v2_access_key"
                            type="text"
                            @click="toExtendPerson(scope.row)"
                        >
                            {{ scope.row.extend_name }}
                        </el-button>
                        <span v-else>{{ scope.row.extend_name }}</span>
                    </div>
                    <div slot="real_name" slot-scope="scope">
                        <span
                            :class="isPerson ? 'primary pointer' : ''"
                            @click="
                                toPersonDetail(scope.row.profile_access_key)
                            "
                            >{{ scope.row.real_name }}</span
                        >
                    </div>
                    <div slot="agent_name" slot-scope="scope">
                        <span
                            class="primary pointer"
                            @click="toAgentDetail(scope.row.agent_access_key)"
                            >{{ scope.row.agent_name }}</span
                        >
                    </div>
                    <div slot="contact_mobile" slot-scope="scope">
                        {{
                            scope.row.showMobile
                                ? scope.row.contact_mobile
                                : desensitization(scope.row.contact_mobile)
                        }}
                        <el-button
                            v-if="
                                !scope.row.showMobile &&
                                scope.row.contact_mobile
                            "
                            class="u-m-l-10"
                            type="text"
                            @click="scope.row.showMobile = true"
                            >查看</el-button
                        >
                        <span class="color-9" v-if="!scope.row.contact_mobile"
                            >-</span
                        >
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <div class="handler-btn" @click="toDetail(scope.row)">
                            申报内容
                        </div>
                    </div>
                    <div slot="h1" slot-scope="scope">
                        <el-button type="text" @click="del_1(scope.row)">
                            删除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入政策办理人员"
            placeholder="请点击「确定」上传"
            :importConfig="importConfig"
            @refresh="reloadList"
        >
        </excel-import>
        <excel-import
            v-if="importConfig2"
            v-model="showImportPop2"
            title="导入政策办理人员"
            placeholder="请点击「确定」上传"
            :importConfig="importConfig2"
            @refresh="reloadList"
        >
        </excel-import>
    </div>
</template>

<script lang='ts'>
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { sdk } from "@/service"
    import { ServeTargetType } from ".."
    import { desensitization } from "@/utils/tools"
    import { routesMap } from "@/router/direction"
    import { queryParamsKey } from "../../policy-apply-record"
    import { cloneDeep } from "lodash"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { buildSelectSource, FormType } from "@/core-ui/component/form"
    import { codeLazyLoad } from "../../recruit-company/index"
    import {
        createC,
        getSpolicyFilter,
        getSpPolicyTemplate,
        policyImportConfig,
        spPolicy,
    } from "./index"
    import { ListTypes } from "uniplat-sdk"
    import { MessageBox } from "element-ui"
    import moment from "moment"
    import { config, EnvProject } from "@/config"
    import { renDesensitizationView } from "@/views/components/common-comps"

    const enum Status {
        待提交 = -1,
        已提交审核 = 0,
        通过 = 1,
        不通过 = 2,
    }

    interface Row {
        /** 处理人 */
        real_name: number

        /** 申报时间 */
        create_time: string
        create_time_label: string

        /** 联系方式 */
        contact_mobile: string

        /** 处理时间 */
        dealer_time: number
        dealer_time_label: number

        /** 状态 */
        status: Status

        /** 状态[文本] */
        status_label: string
        _access_key: string
        profile_access_key: string
        agent_access_key: string
        id: number
        v: number
    }

    const tableConfig = (
        form_id: string,
        type: ServeTargetType,
        isNew = false,
        config: any = {}
    ): TableConfig => ({
        model: sdk.core
            .model("policy_form_apply")
            .list(
                type === ServeTargetType.居民
                    ? "apply_profile_for_policy"
                    : "apply_company_for_policy"
            ),
        preFilter: { form_id },
        defaultPageSize: 10,
        predict:
            type === ServeTargetType.居民
                ? {
                      real_name: "user#real_name",
                      create_time: "label",
                      contact_mobile: "",
                      dealer_name: "dealer#real_name",
                      dealer_time: "label",
                      status: "label",
                      profile_access_key: "profile#_access_key",
                      showMobile: false,
                      profile_v2_id_card: "profile_v2#id_card",
                  }
                : {
                      real_name: "creator#real_name",
                      agent_name: "agent#agent_name",
                      create_time: "label",
                      contact_mobile: "",
                      dealer_name: "dealer#real_name",
                      dealer_time: "label",
                      status: "label",
                      agent_access_key: "agent#_access_key",
                      showMobile: false,
                  },
        oneTabFilter: false,
        tabPages: ["离线申报", "线上申报"],
        ...config,
    })

    const column1: TableColumn[] = [
        {
            label: "申报人",
            prop: "real_name",
            showOverflowTip: true,
        },
        {
            label: "企业名称",
            prop: "agent_name",
            showOverflowTip: true,
        },
        {
            label: "申报时间",
            prop: "create_time_label",
            showOverflowTip: true,
        },
        {
            label: "联系方式",
            prop: "contact_mobile",
            showOverflowTip: true,
            minWidth: "140px",
        },
        {
            label: "处理人",
            prop: "dealer_name",
            showOverflowTip: true,
        },
        {
            label: "处理时间",
            prop: "dealer_time_label",
            showOverflowTip: true,
        },
        {
            label: "状态",
            prop: "status_label",
            showOverflowTip: true,
        },
        { label: "操作", prop: "h", showOverflowTip: true },
    ]

    export const column2: TableColumn[] = [
        {
            label: "申报人",
            prop: "real_name",
            showOverflowTip: true,
        },
        {
            label: "联系方式",
            prop: "contact_mobile",
            showOverflowTip: true,
            minWidth: "140px",
            render(h: any, row: any) {
                return renDesensitizationView(h, {
                    value: row.contact_mobile,
                })
            },
        },
        {
            label: "身份证号",
            prop: "profile_v2_id_card",
            width: "160px",
            formatter(row: any) {
                return desensitization(row.profile_v2_id_card, 5)
            },
        },
        {
            label: "办理单位",
            prop: "handle_org",
            showOverflowTip: true,
        },
        {
            label: "所属区域",
            prop: "handle_region",
            showOverflowTip: true,
        },
        {
            label: "办理日期",
            prop: "dealer_time_label",
            showOverflowTip: true,
        },
        // {
        //     label: "状态",
        //     prop: "status_label",
        //     showOverflowTip: true,
        // },
        { label: "操作", prop: "h1" },
    ]

    const predict1 = {
        real_name: "user#real_name",
        create_time: "label",
        contact_mobile: "",
        dealer_name: "dealer#real_name",
        dealer_time: "label",
        status: "label",
        profile_access_key: "profile#_access_key",
        showMobile: false,
    }

    export const predict2 = {
        real_name: "profile_v2#name",
        create_time: "label",
        contact_mobile: "",
        dealer_time: "label",
        status: "label",
        profile_access_key: "profile_v2#_access_key",
        handle_org: "",
        handle_region: "handle_region#region_name",
        showMobile: false,
        handle_region_region_name: "handle_region#region_name",
        extend_id_card: "extend#id_card",
        extend_name: "extend#name",
        extend_contact_mobile: "extend#contact_mobile",
        extend_nation: "extend#nation_label",
        extend_reg_residence_property: "extend#reg_residence_property_label",
        extend_education: "extend#education_label",
        extend_graduation_school: "extend#graduation_school_label",
        extend_graduation_time: "extend#graduation_time_label",
        extend_employment_time: "extend#employment_time_label",
        extend_is_disabled: "extend#is_disabled_label",
        extend_employment_category: "extend#employment_category_label",
        extend_employment_unit: "extend#employment_unit_label",
        extend_reg_region_code: "extend#reg_region_code_label",
        extend_permanent_region_code: "extend#permanent_region_code_label",
        employment_difficulties_category:
            "extend#employment_difficulties_category_label",
        apply_batch_id: "apply_batch_id",
        batch_status: "apply_batch#batch_status",
        bank_name: "extend#bank_name",
        bank_account: "extend#bank_account",
        first_enjoy_date: "extend#first_enjoy_date_label",
        subsidy_start_date: "extend#subsidy_start_date_label",
        subsidy_end_date: "extend#subsidy_end_date_label",
        subsidy_month: "extend#subsidy_month_label",
        amount: "extend#amount_label",
        remark: "extend#remark",
        sex: "profile_v2#sex_label",
        profile_v2_access_key: "profile_v2#_access_key",
        profile_v2_name: "profile_v2#name",
        profile_v2_id_card: "profile_v2#id_card",
        batch_name: "apply_batch#batch_name",
    }

    @Component({ components: { TableContainer, CommonTable, ExcelImport } })
    export default class ApplyList extends BaseTableController<{ id: number }> {
        @Prop()
        id!: string

        @Prop()
        type!: ServeTargetType

        @Prop()
        policyName!: string

        @Prop({ default: () => null })
        listConfig!: any

        private get isSpecial() {
            return spPolicy.includes(this.policyName)
        }

        private tableConfig: TableConfig | null = null

        private desensitization = desensitization

        private pageData: { item_index: number; item_size: number } | any = {}
        private queryParams = {}

        private activeName = "离线申报"
        private importConfig: any = null
        private importConfig2: any = null

        private columns: TableColumn[] = column1

        public showImportPop = false
        public showImportPop2 = false

        isQianJiang = config.envProject === EnvProject.潜江项目

        private get isPerson() {
            if (this.isQianJiang) {
                return false
            }
            return this.type && this.type === ServeTargetType.居民
        }

        mounted() {
            this.init()
        }

        private init() {
            if (this.listConfig?.tabPages?.length) {
                this.activeName = this.listConfig.tabPages[0]
            }
            this.tableConfig = tableConfig(
                this.id,
                this.type,
                this.isSpecial,
                this.listConfig
            )
            this.onTabNameChange(this.activeName) // wl-TODO

            if (this.isPerson) {
                this.columns = this.columns.filter((i) => i.prop !== "agent_name")
            }

            this.importConfig = {
                templateUrl:
                    window.location.origin + "/file/政策办理人员导入模板.xls",
                modelName: "policy_form_apply",
                actionName: "import_apply_person",
                prefilters: [
                    {
                        property: "form_id",
                        value: this.id,
                        prefilters: null,
                        relationalOperator: null,
                    },
                ],
                bigActionImportParams: {
                    inputs_parameters: [
                        {
                            property: "apply_type",
                            value: "1",
                        },
                    ],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                    prefilters: [
                        {
                            property: "form_id",
                            value: this.id,
                            prefilters: null,
                            relationalOperator: null,
                        },
                    ],
                },
            }
            this.importConfig2 = {
                templateUrl:
                    window.location.origin + "/file/城镇新增就业导入模板.xls",
                modelName: "policy_form_apply",
                actionName: "import_offline_apply_person",
                prefilters: [
                    {
                        property: "form_id",
                        value: this.id,
                        prefilters: null,
                        relationalOperator: null,
                    },
                ],
                bigActionImportParams: {
                    inputs_parameters: [
                        {
                            property: "apply_type",
                            value: "1",
                        },
                    ],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                    prefilters: [
                        {
                            property: "form_id",
                            value: this.id,
                            prefilters: null,
                            relationalOperator: null,
                        },
                    ],
                    ...this.policyImportConfig,
                },
            }
        }

        private get policyImportConfig() {
            return policyImportConfig(this.policyName)
        }

        private toDetail(row: Row) {
            let pageIndex = 1
            pageIndex = this.pageData.item_index / this.pageData.item_size + 1

            sessionStorage.setItem(
                queryParamsKey,
                JSON.stringify({
                    pageIndex,
                    queryParams: {},
                    policyName: this.policyName,
                })
            )
            this.$router.push({
                name: routesMap.policyApplyRecord.detail,
                query: {
                    id: row._access_key + "",
                    rowId: row.id + "",
                    serviceType: this.type + "",
                    from: this.$route.name,
                },
            })
        }

        loaded(data: { name: string; item_index: number }[]) {
            this.pageData = data
            console.log("d", JSON.parse(JSON.stringify(data)))
        }

        private toPersonDetail(key: string) {
            if (!this.isPerson) {
                return
            }
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: key,
                    from: this.$route.name,
                },
            })
        }

        toExtendPerson(row: any) {
            if (!row.profile_v2_access_key) {
                return
            }
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: row.profile_v2_access_key + "",
                    from: this.$route.name,
                },
            })
        }

        private toAgentDetail(key: string) {
            this.$router.push({
                name: routesMap.employmentManage.companyManageDetail,
                query: { id: key, from: this.$route.name },
            })
        }

        onTabNameChange(tabName: string) {
            this.activeName = tabName
            if (this.activeName === "线上申报") {
                this.columns = cloneDeep(column1)
                this.tableConfig!.predict = cloneDeep(predict1)
                this.tableConfig!.filter = []
                // this.init()
            } else {
                const c = createC(this.policyName)
                this.columns = [
                    {
                        label: "批次名称",
                        prop: "batch_name",
                        width: "120px",
                    },
                    ...(this.isSpecial ? c : cloneDeep(column2)),
                ]
                this.isPerson && (this.tableConfig!.predict = cloneDeep(predict2))
                if (getSpolicyFilter(this.policyName)) {
                    this.tableConfig!.filter = getSpolicyFilter(this.policyName)
                }
            }
            this.tableConfig = { ...this.tableConfig }
            this.$emit("onTabChange", this.activeName)
        }

        exportToExcel2() {
            if (this.activeName === "离线申报") {
                this.exportExcelUniplatV2({
                    template_name: getSpPolicyTemplate(this.policyName),
                })
            } else {
                this.exportToExcel()
            }
        }

        del_1(row: any) {
            MessageBox.confirm("是否确定删除", "删除").then(() => {
                return sdk.core
                    .model("policy_form_apply")
                    .action("delete_offline_apply_person")
                    .updateInitialParams({
                        selected_list: [
                            {
                                id: row.id,
                                v: 0,
                            },
                        ],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("删除成功")
                        this.reloadList()
                    })
            })
        }

        sss(r: any) {
            console.log("r", JSON.parse(JSON.stringify(r)))
        }

        getTabPageDefaultTotal(e: any) {
            this.$emit("setT", e?.page_datas[this.activeName]?.record_count || 0)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .list-view {
        background: #fff;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
    }
    /deep/.table {
        padding: 0;
        padding-bottom: 20px;
        .table-tabs {
            background: #f6f7f9;
        }
    }
</style>
