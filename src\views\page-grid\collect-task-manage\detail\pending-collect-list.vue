<template>
    <div :key="refreshQueryParams">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useDefaultSort2Tabs="true"
            :useTab="true"
            @getRows="getRows"
            @tabName="tabName"
            class="container"
        >
            <div slot="title">
                <div class="d-flex justify-content-end">
                    <el-button
                        type="primary"
                        v-if="canEdit"
                        @click="addCollectedInfo"
                        >新增采集</el-button
                    >
                </div>
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="h" slot-scope="scope">
                        <div class="u-flex u-row-center">
                            <el-button
                                type="text"
                                v-if="
                                    getShowBtn4List(
                                        scope.row.id,
                                        'insert_by_grid_intent'
                                    ) && canEdit
                                "
                                @click="toEdit(scope.row)"
                            >
                                采集
                            </el-button>

                            <el-button type="text" @click="toDetail(scope.row)">
                                详情
                            </el-button>
                        </div>

                        <el-button
                            type="text"
                            v-if="
                                getShowBtn4List(
                                    scope.row.id,
                                    'cancel_data_type_exception_type'
                                )
                            "
                            @click="
                                toOperate(
                                    scope.row,
                                    'cancel_data_type_exception_type'
                                )
                            "
                        >
                            {{
                                getShowBtn4List(
                                    scope.row.id,
                                    "cancel_data_type_exception_type"
                                ).label
                            }}
                        </el-button>

                        <el-button
                            type="text"
                            v-if="
                                getShowBtn4List(
                                    scope.row.id,
                                    'update_data_type_exception_type_1'
                                )
                            "
                            @click="
                                toOperate(
                                    scope.row,
                                    'update_data_type_exception_type_1'
                                )
                            "
                        >
                            <!-- 非本区户籍 -->
                            {{
                                getShowBtn4List(
                                    scope.row.id,
                                    "update_data_type_exception_type_1"
                                ).label
                            }}
                        </el-button>

                        <el-button
                            type="text"
                            v-if="
                                getShowBtn4List(
                                    scope.row.id,
                                    'update_data_type_exception_type'
                                )
                            "
                            @click="openDialogSetException(scope.row.id)"
                        >
                            {{
                                getShowBtn4List(
                                    scope.row.id,
                                    "update_data_type_exception_type"
                                ).label
                            }}
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>

        <DialogCheck
            v-model="displayDialogCheck"
            :detail="detail"
            @success="success"
        ></DialogCheck>

        <DialogFormCollect
            :preId="preId"
            :curId="curId"
            v-model="displayDialogFormCollect"
            intentModelName="collect_task_order_wait_detail"
            intentName="insert_by_grid_intent"
            :detail="detail"
            @refresh="refreshList"
            @reload="insertSuccess"
            :requiredConfig="requiredConfig"
            :schemaInfo="schemaInfo"
        >
        </DialogFormCollect>
        <DialogExceptionType
            :curId="curExceptionId"
            :curExceptionTitle="curExceptionTitle"
            v-model="displayDialogSetException"
            @refresh="reloadList"
        ></DialogExceptionType>
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { pageLoading } from "@/views/controller"
    import {
        buildConfig4RemoteMeta,
        getShowBtn4List,
        getShowBtn4Page,
    } from "@/views/pages/collect-task-manage/components/build-table"
    import DialogCheck from "../components/dialog-check.vue"
    import DialogFormCollect from "../components/dialog-form-collect.vue"
    import { Status } from "@/views/pages/collect-task-manage/task-info-manage/task-info/model"
    import { cloneDeep } from "lodash"
    import { MessageBox } from "element-ui"
    import { sdk } from "@/service"
    import DialogExceptionType from "../components/dialog-exception-type.vue"

    @Component({
        name: routesMap.collectTaskManage.taskInfoManage.manageCollect.list,
        components: {
            TableContainer,
            CommonTable,
            DialogCheck,
            DialogFormCollect,
            DialogExceptionType,
        },
    })
    export default class LaborInfoBase extends BaseTableController<any> {
        @Prop({ default: () => {} })
        private info!: any

        @Prop({ default: () => {} })
        private detail!: any

        @Prop({ default: () => [] })
        private requiredConfig!: string[]

        @Prop({ default: () => [] })
        private schemaInfo!: any[]

        tableConfig: TableConfig | null = null

        refreshConfig = {
            fun: this.init,
            name: routesMap.collectTaskManage.taskInfoManage.manageCollect.list,
        }

        private columns: TableColumn[] = []
        private showBtn = false

        private displayDialogFormCollect = false

        private displayDialogCheck = false

        private displayDialogSetException = false

        private showAllocationCollector = false

        private rows: any[] = []

        private checkIds: string[] = []

        private displayDialogAllocation = false

        private preId = ""
        private curId = ""

        private curExceptionId = ""

        private curExceptionTitle = ""

        private routesName =
            routesMap.collectTaskManage.taskInfoManage.manageCollect.list

        private get canEdit() {
            return this.detail?.collect_root_task_status === Status.进行中
        }

        private toEdit(row: any) {
            this.curId = row.id
            this.preId = ""
            this.displayDialogFormCollect = true
        }

        private openDialogSetException(id: string) {
            this.curExceptionTitle = getShowBtn4List(
                this.rows,
                id,
                "update_data_type_exception_type"
            )?.label

            this.curExceptionId = id
            this.displayDialogSetException = true
        }

        private insertSuccess() {
            this.$emit("changeTab")
        }

        private handleSelectionChange(d: { ids: string[]; rows: any[] }) {
            this.checkIds = d.rows.map((e) => e.id)
            console.log(this.checkIds)
            console.log(this.checkIds)
        }

        private toOperate(row: any, actionName: string) {
            const t = this.getShowBtn4List(row.id, actionName)
            MessageBox.confirm(`确认${t.label}？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model(this.info.name)
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private success(preId: string) {
            this.curId = ""
            this.preId = preId
            this.displayDialogCheck = false
            this.displayDialogFormCollect = true
        }

        private addCollectedInfo() {
            this.displayDialogCheck = true
            return ""
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private getShowBtn4List(id: string, key: string) {
            return getShowBtn4List(this.rows, id, key)
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "采集任务",
                    to: {
                        name: this.routesName,
                    },
                },
            ]
            updateTagItem({
                name: this.routesName,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        created() {
            this.init()
        }

        private config4Remote: any[] = []

        private init() {
            pageLoading(() => {
                const prefilters = {}
                this.info.prefilters.forEach((i: any) => {
                    Object.assign(prefilters, {
                        [i.property]: i.value,
                    })
                })

                return buildConfig4RemoteMeta(this.info.name, this.info.list_name, {
                    disabledOpt: false,
                    disabledFilter: true,
                    useLabelWidth: true,
                    prefilters: prefilters,
                    useTabs: true,
                    predicts: {
                        collect_user_profile__access_key:
                            "collect_user_profile#_access_key",
                    },
                    optColumn: {
                        label: "操作",
                        prop: "h",
                        fixed: "right",
                        minWidth: "120px",
                    },
                    customLabelWidths: {
                        任务名称: 270,
                        所属区域: 360,
                        采集模板: 170,
                        任务起止时间: 200,
                    },
                    useTabsV2: true,
                }).then((r: any) => {
                    console.log("console.log(r)")
                    console.log(r)
                    this.config4Remote = r
                    this.buildConfig(r[0])
                })
            })
        }

        private checkShowAllocationCollector(t: any) {
            this.showAllocationCollector = getShowBtn4Page(
                t,
                "allocation_collector"
            )
            const hasColumn = this.columns.find((i) => i.prop === "select")
            if (this.showAllocationCollector && !hasColumn) {
                this.columns.unshift({
                    prop: "select",
                    width: "58",
                    type: "selection",
                })
            }
            if (!this.showAllocationCollector && hasColumn) {
                this.columns.pop()
            }
        }

        private tabName(tabName?: string) {
            const t = this.config4Remote.find((i) => i.tabInfo.name === tabName)
            if (!t) {
                return
            }
            this.tableConfig!.preFilter = t.tableConfig.preFilter
            this.tableConfig!.predict = t.tableConfig.predict

            this.columns = t.columns
            this.checkShowAllocationCollector(t)
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            this.showBtn = getShowBtn4Page(r, "insert_by_village_back_intent")
            tableConfig.filter = r.filter
            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
                intents: "intents",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = r.columns
            this.checkShowAllocationCollector(r)
        }

        mounted() {
            this.setBreadcrumbs()
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.grid.collectTaskManage.recordListDetail,
                query: {
                    id: row.collect_user_profile__access_key + "",
                    type: "default",
                    detailName: "manage",
                    from: this.$route.name,
                    readonly: "1",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background-color: #fff;
        padding-left: 15px;
        padding-right: 15px;
        padding-top: 15px;

        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }

    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
