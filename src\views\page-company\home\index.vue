<template>
    <div class="container u-p-t-28">
        <div class="u-flex u-row-between title">
            <div class="u-flex">
                <div class="u-font-18 color-0 bold">{{ company }}</div>
                <div
                    class="u-m-l-20 primary pointer"
                    @click="
                        clickItem({
                            route: routesMap.company.companyManage.manage,
                        })
                    "
                >
                    查看企业基本信息
                </div>
                <div
                    class="u-m-l-20 primary pointer"
                    @click="showCustomPop = true"
                >
                    客服顾问
                </div>
            </div>
            <div class="u-flex-1"></div>
            <el-button
                class="u-m-r-10"
                type="primary"
                @click="showMessageConfig = true"
            >
                消息通知配置
            </el-button>
            <div class="tip">{{ time }}好！{{ name }}</div>
        </div>
        <div class="content u-p-b-20">
            <div class="color-0 u-font-16 l22 bold">
                待办事项
                <span class="tips" v-if="displayTips">
                    ( 以下为贵司当前需要尽快处理的事项，请点击对应数字后进行处理
                    <span class="dots"></span>
                    )
                </span>
            </div>
            <div class="todo-item-box u-m-t-20 d-flex flex-wrap">
                <div
                    class="todo-item"
                    v-for="(i, idx) in todoItems"
                    :key="idx"
                    @click="goDetail(i)"
                >
                    <div class="todo-count">{{ i.count }}</div>
                    <div class="todo-title">{{ i.title }}</div>
                </div>
            </div>
        </div>
        <div class="content u-p-b-40">
            <div class="u-flex u-row-between">
                <div>
                    <div class="color-0 u-font-16 l22 bold">招聘服务</div>
                    <div class="tip u-font-12 u-m-t-5">
                        共享海量{{
                            citySampleName
                        }}劳动资源，大数据赋能实现供需精准撮合，帮助企业降低招聘成本
                    </div>
                </div>
                <el-button
                    type="primary"
                    class="btn custom-btn"
                    @click="toAddPosition"
                >
                    发布新岗位
                </el-button>
            </div>
            <div class="recruit u-m-t-20">
                <div
                    class="item u-flex u-p-x-30"
                    v-for="(item, index) in displayRecruitList"
                    :key="index"
                    @click="clickItem(item)"
                >
                    <img :src="item.icon" class="icon" />
                    <div class="u-flex-1">
                        <div class="u-flex u-row-between">
                            <div class="color-0">{{ item.title }}</div>
                            <div class="tip">
                                查看详情 <i class="el-icon-arrow-right"></i>
                            </div>
                        </div>
                        <div class="tip u-m-t-5">{{ item.des }}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div
            class="content u-m-t-20 u-p-x-30 u-p-y-20 u-p-b-30"
            v-if="!hideConsult"
        >
            <div class="color-0 u-font-16 l22 bold">业务公告</div>
            <div class="consult u-m-t-15">
                <div
                    class="item u-flex pointer"
                    v-for="(item, idx) in consult"
                    :key="item.id"
                    @click="toSubsidyDetail(item)"
                >
                    <img class="icon" :src="computeImg(idx)" />
                    <div>
                        {{ item.title }}
                    </div>
                </div>
            </div>
            <empty v-if="!consult.length"></empty>
        </div> -->
        <div class="two-content u-m-t-20">
            <div class="content" v-if="!hideConsult">
                <div class="u-flex u-row-between u-m-b-15">
                    <div class="color-0 u-font-16 l22 bold">政策资讯</div>
                    <div
                        class="tip pointer"
                        @click="
                            clickItem({
                                route: routesMap.company.consult.index,
                                query: { tab: 1 },
                            })
                        "
                    >
                        更多<i class="el-icon-arrow-right" />
                    </div>
                </div>
                <div class="policy">
                    <div
                        class="item u-flex u-row-between u-m-b-10 pointer"
                        v-for="(item, index) in policy"
                        :key="index"
                        @click="toPolicyDetail(item)"
                    >
                        <div class="u-line-1 u-p-r-10">· {{ item.title }}</div>
                        <div class="tip">
                            {{ formatTime(item.create_time_label) }}
                        </div>
                    </div>
                </div>
                <empty v-if="!policy.length" />
            </div>
            <div class="content">
                <div class="u-flex u-row-between u-m-b-15">
                    <div class="color-0 u-font-16 l22 bold">消息</div>
                    <div
                        class="tip pointer"
                        @click="
                            clickItem({
                                route: routesMap.home.news,
                            })
                        "
                    >
                        更多
                        <i class="el-icon-arrow-right" />
                    </div>
                </div>
                <div class="policy">
                    <div
                        class="item u-flex u-row-between u-m-b-10 pointer"
                        v-for="(item, index) in news"
                        :key="index"
                        @click="toNews(item)"
                    >
                        <div class="u-line-1 u-p-r-10">· {{ item.title }}</div>
                        <div class="tip u-flex-none">
                            {{ formatTime(item.create_time_label) }}
                        </div>
                    </div>
                </div>
                <empty v-if="!news.length" />
            </div>
        </div>
        <create-position-pop
            v-model="showPositionPop"
            @refresh="refreshList"
        ></create-position-pop>
        <job-fair-pop
            v-model="showJobFairPop"
            :jobFairInfo="jobFairInfo"
        ></job-fair-pop>
        <submit-position-pop
            v-model="showSubmitPositionPop"
            :submitPositionInfo="submitPositionInfo"
        ></submit-position-pop>
        <enterprise-info-pop
            v-model="showEnterpriseInfoPop"
        ></enterprise-info-pop>
        <message-config-pop v-model="showMessageConfig" />

        <pop-tips v-model="showCustomPop"></pop-tips>
    </div>
</template>

<script lang="ts">
    import { userInfoService } from "@/core-ui/service/passport/user-info"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { userService } from "@/service/service-user"
    import { formatTime, getCurTime } from "@/utils/tools"
    import { Component, Vue } from "vue-property-decorator"
    import { getList, SubsidyDetail } from "../consult/components/detail"
    import Empty from "./components/empty.vue"
    import CreatePositionPop from "@/views/page-company/recruit/job/components/create-position-pop.vue"
    import JobFairPop from "@/views/page-company/home/<USER>/job-fair-pop.vue"
    import EnterpriseInfoPop from "@/views/page-company/home/<USER>/enterprise-info-pop.vue"
    import SubmitPositionPop from "@/views/page-company/home/<USER>/submit-position.vue"
    import { News, predict, toNewsDetail, Status, readMessage } from "."
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { config, EnvProject } from "@/config"
    import { head, map, sum } from "lodash"
    import { MessageBox } from "element-ui"
    import MessageConfigPop from "./components/message-config-pop.vue"
    import RichContent from "@/views/components/rich-content.vue"
    import PopTips from "./components/pop-tips.vue"
    import { PositionRecommendType, sysConfigService } from "@/service/sys-config"

    interface JobFairPopDetail {
        job_fair?: jobFair
        pop_job_fair: boolean
    }

    interface PopDetail {
        job_fair?: jobFair
        pop_position_commit_notifty: boolean
    }

    export interface jobFair {
        end_time: string
        id: string
        place_detail: string
        start_time: string
        title: string
    }

    export interface submitPositionInfo {
        id: string
        title: string
        start_time: string
        end_time: string
        place_detail: string
    }

    @Component({
        components: {
            Empty,
            CreatePositionPop,
            JobFairPop,
            SubmitPositionPop,
            EnterpriseInfoPop,
            MessageConfigPop,
            PopTips,
        },
    })
    export default class HomeIndex extends Vue {
        citySampleName = config.citySampleName
        showMessageConfig = false
        private recruitList = [
            {
                title: "人才推荐服务",
                icon: "/img/company/recruit/1.png",
                route: routesMap.company.recruit.person,
                des: "大数据赋能实现人、岗位精准推荐",
                key: PositionRecommendType.人才推荐服务,
                hidden: true,
            },
            {
                title: "人力资源机构撮合",
                icon: "/img/company/recruit/2.png",
                route: routesMap.company.recruit.cooperation,
                des: "优选人力资源机构，服务安全合规、业务精准撮合",
                key: PositionRecommendType.人力资源机构撮合服务,
                hidden: true,
            },
            {
                title: "社群推广服务",
                icon: "/img/company/recruit/3.png",
                route: routesMap.company.recruit.group,
                des: "依托微信社群生态，招聘需求高效触达辖区居民",
                key: PositionRecommendType.社群推广服务,
                hidden: true,
            },
            {
                title: "网格推广服务",
                icon: "/img/company/recruit/4.png",
                route: routesMap.company.recruit.grid,
                des: "依托政务网格服务体系，实现岗位精准推送",
                key: PositionRecommendType.网格推广服务,
                hidden: true,
            },
            {
                title: "招聘会",
                icon: "/img/company/recruit/5.png",
                route: routesMap.company.recruit.jobFair,
                des: "政府主导、高频次、主题明确",
                hidden: [EnvProject.宜都项目].includes(config.envProject),
            },
        ]

        private consult: SubsidyDetail[] = []
        private policy: any[] = []
        private news: News[] = []
        private lastNews: News | null = null
        private routesMap = routesMap
        private name = ""
        private company = ""
        private showPositionPop = false
        private showJobFairPop = false
        private showSubmitPositionPop = false
        private showEnterpriseInfoPop = false
        private showCustomPop = false
        private jobFairInfo: jobFair = {
            end_time: "",
            id: "",
            place_detail: "",
            start_time: "",
            title: "",
        }

        private submitPositionInfo: submitPositionInfo = {
            id: "",
            title: "",
            start_time: "",
            end_time: "",
            place_detail: "",
        }

        private get hideConsult() {
            return config.envProject === EnvProject.荆州项目
        }

        private dots = ""

        private todoItems: any[] = []

        private displayTips = false

        private get displayRecruitList() {
            return this.recruitList.filter((item) => !item.hidden)
        }

        private clickItem(item: any) {
            this.$router.push({ name: item.route })
        }

        private toSubsidyDetail(item: any) {
            this.$router.push({
                name: routesMap.company.consult.policyDetail,
                query: {
                    id: item.id,
                },
            })
        }

        private formatTime(v: string) {
            return formatTime.day(v)
        }

        private toNews(item: News) {
            if (item.content) {
                this.showContentMessage(item)
                return
            }
            toNewsDetail(item, routesMap.home.page)
        }

        private toPolicyDetail(row: any) {
            if (+row.type === 1) {
                this.$router.push({
                    name: routesMap.company.consult.policyCustomDetail,
                    query: { id: row._access_key + "" },
                })
            } else {
                window.open(row.url_path, "_blank")
            }
        }

        private computeImg(i: number) {
            return `/img/company/article/${(+i % 6) + 1}.png`
        }

        private get time() {
            return getCurTime()
        }

        private async setRecruitItemHidden() {
            const positionRecommendMapping = await sysConfigService.data
                .position_recommend_type_mapping

            this.recruitList.forEach((item) => {
                const key = item.key
                if (!key) {
                    return
                }
                item.hidden = !positionRecommendMapping.find((i) => i.key === key)
            })
        }

        async mounted() {
            await this.setRecruitItemHidden()
            userService.setup().then((r) => {
                if (r) {
                    this.company = r.label || "未知企业"
                    this.name = r?.data?.real_name
                    if (!this.name) {
                        userInfoService.get().then((r) => {
                            this.name = r.display_name
                        })
                    }
                    this.getList()
                } else {
                    this.company = "未知企业"
                }
            })
            updateTagItem({
                name: routesMap.home.page,
                breadcrumb: [
                    {
                        to: routesMap.home.page,
                        label: "首页",
                    },
                ],
            })
            this.getShowJobFairPopInfo()
            this.getShowSubmitPositionPopInfo()
            this.getTodeList()
        }

        private getList() {
            return Promise.all([
                this.hideConsult
                    ? Promise.resolve()
                    : getList().then((r) => {
                          this.consult = r.rowList
                      }),
                this.getPolicyList(),
                this.getNewsList(),
                this.getLastNew(),
            ])
        }

        private getNewsList() {
            return sdk.core
                .model("agent_msg")
                .list("for_agent")
                .query({
                    item_size: 3,
                    pageIndex: 1,
                })
                .then((r) => {
                    return sdk.buildRows<News>(r.pageData.rows, predict)
                })
                .then((r) => {
                    this.news = r
                })
        }

        showContentMessage(msg: News) {
            const h = this.$createElement
            MessageBox.confirm(
                msg.content || msg.title,
                msg.content ? msg.title : "提示",
                {
                    message: h(RichContent, {
                        props: {
                            content: msg.content || msg.title,
                        },
                    }),
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: "查看通知列表",
                    cancelButtonText: "关闭",
                    callback: (action) => {
                        if (action === "confirm") {
                            this.clickItem({
                                route: routesMap.home.news,
                            })
                        }
                    },
                }
            )
            readMessage(msg.id).then(() => {
                this.$root.$emit("refreshHeaderNews")
            })
        }

        private getLastNew() {
            return sdk.core
                .model("agent_msg")
                .list("for_agent")
                .addPrefilter({
                    status: Status.未读,
                })
                .query({
                    item_size: 1,
                    pageIndex: 1,
                })
                .then((r) => {
                    const msg =
                        head(sdk.buildRows<News>(r.pageData.rows, predict)) || null
                    if (msg) {
                        this.showContentMessage(msg)
                    }

                    // MessageBox.
                })
        }

        private async getPolicyList() {
            // if (
            //     [EnvProject.荆州项目, EnvProject.黄州项目].includes(
            //         config.envProject
            //     )
            // ) {
            //     return
            // }
            const model = sdk.core
                .model("g_policy_advice")
                .list("manage_for_company")
                .addPrefilter({
                    aprover_status: "1",
                    shelves_status: "1",
                })
            await model.query({
                pageIndex: 1,
                item_size: 0,
            })
            return model
                .query({
                    pageIndex: 1,
                    item_size: 3,
                    tagFilters: [],
                })
                .then((r) => {
                    this.policy = sdk.buildRows(r.pageData.rows, {
                        title: "",
                        create_time: "label",
                        url_path: "",
                        type: "",
                    })
                })
        }

        private toAddPosition() {
            this.$router.push({
                name: routesMap.company.recruit.createJob,
            })
        }

        private refreshList() {
            this.callRefresh("refreshCompanyJobList")
            this.$router.push({
                name: routesMap.company.recruit.job,
            })
        }

        private getShowJobFairPopInfo() {
            sdk.core
                .domainService("xg_project", "agent_api", "job_fair_apply_invite")
                .post<JobFairPopDetail>()
                .then((r) => {
                    if (r.pop_job_fair && r.job_fair) {
                        this.jobFairInfo = r.job_fair
                        this.showJobFairPop = true
                    }
                })
        }

        private getShowSubmitPositionPopInfo() {
            sdk.core
                .domainService(
                    "xg_project",
                    "agent_api",
                    "job_fair_positon_commit_notify"
                )
                .post<PopDetail>()
                .then((r) => {
                    if (r.pop_position_commit_notifty && r.job_fair) {
                        this.submitPositionInfo = r.job_fair
                        this.showSubmitPositionPop = true
                    }
                })
        }

        private getTodeList() {
            sdk.core
                .domainService("xg_project", "agent_api", "get_to_do_list")
                .post<any>()
                .then((r) => {
                    this.todoItems = [
                        // {
                        //     title: "人才推荐服务投递",
                        //     count: r.talent_count,
                        //     name: routesMap.company.recruit.person,
                        // },
                        // {
                        //     title: "网格推广服务投递",
                        //     count: r.grid_count,
                        //     name: routesMap.company.recruit.grid,
                        // },
                        // {
                        //     title: "社群推广服务投递",
                        //     count: r.socialize_group_count,
                        //     name: routesMap.company.recruit.group,
                        // },
                        // {
                        //     title: "人资撮合服务服务投递",
                        //     count: r.agent_bidding_count,
                        //     name: routesMap.company.recruit.cooperation,
                        // },
                        // {
                        //     title: "小程序居民岗位投递",
                        //     count: r.self_count,
                        //     name: routesMap.company.recruit.miniJob,
                        // },
                        // {
                        //     title: "招聘会投递",
                        //     count: r.job_fair_count,
                        //     name: routesMap.company.recruit.jobFairAgent,
                        // },
                        {
                            title: "待处理投递",
                            // 添加新的未读要改
                            count: sum(map(r, (e) => +e)) - +r.questionnaire_count,
                            name: routesMap.company.recruit.jobDeliver,
                        },
                        {
                            title: "企业信息调查",
                            count: r.questionnaire_count,
                            name: routesMap.company.research.index,
                            hide: [EnvProject.宜都项目].includes(config.envProject),
                        },
                    ].filter((i) => !i.hide)
                    this.displayTips = !!this.todoItems.some(
                        (i) => i.count && i.count !== "0"
                    )

                    const showPop =
                        localStorage.getItem("neverShowEnterprisePop") === "true"
                    if (r.questionnaire_count > 0 && !showPop) {
                        this.showEnterpriseInfoPop = true
                    }
                })
        }

        private goDetail(i: any) {
            this.$router.push({
                name: i.name,
                query: {
                    isHome: "1",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .l22 {
        line-height: 22px;
    }
    .container {
        .title {
            line-height: 23px;
        }
        .tip {
            color: #9098a6;
            font-weight: 500;
            font-size: 14px;
            flex: none;
        }
        .content {
            padding: 20px 30px;
            margin-top: 17px;
            background: #fefeff;
            border-radius: 5px;
            .btn {
                width: 136px;
                height: 40px;
            }
            .tips {
                color: @text-warn;
                font-size: 14px;

                .dots {
                    &::after {
                        content: ".";
                        animation: dots 1.5s infinite;
                    }
                }
            }
            .recruit {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 34px 20px;
                .item {
                    height: 92px;
                    background: #f4f7ff;
                    border-radius: 5px;
                    line-height: 20px;
                    cursor: pointer;
                    &:hover {
                        background: #f2f7ff;
                    }
                    .icon {
                        width: 60px;
                        height: 60px;
                        margin-top: 5px;
                    }
                }
            }
            .consult {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 10px;
                .item {
                    color: #4e5054;
                    .icon {
                        width: 28px;
                        height: 28px;
                        margin-right: 8px;
                    }
                }
            }
            .policy {
                width: 99.9%;
                .item {
                    line-height: 20px;
                }
            }
        }
        .two-content {
            display: grid;
            grid-template-columns: calc(50% - 10px) calc(50% - 10px);
            gap: 20px;
            min-height: 167px;
        }

        .todo-item {
            width: 170px;
            min-width: 170px;
            background: #f4f7ff;
            border-radius: 5px 5px 5px 5px;
            padding-top: 22px;
            margin-right: 16px;
            margin-bottom: 20px;
            padding-bottom: 20px;
            cursor: pointer;

            .todo-count {
                color: #598bff;
                font-size: 24px;
                font-weight: 500;
                text-align: center;
                margin-bottom: 12px;
            }

            .todo-title {
                color: #4e5054;
                font-size: 14px;
                font-weight: 500;
                text-align: center;
            }
        }
    }

    @keyframes dots {
        0% {
            content: ".";
        }
        33% {
            content: "..";
        }
        66% {
            content: "...";
        }
        100% {
            content: ".";
        }
    }
</style>
