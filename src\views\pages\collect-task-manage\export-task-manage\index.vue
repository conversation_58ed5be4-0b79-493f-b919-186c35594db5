<template>
    <div :key="refreshQueryParams">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            @getRows="getRows"
            class="container"
        >
            <div slot="title" class="u-flex u-row-between">
                <div class="d-flex-item-center bold">
                    <bread-crumb :backRoute="false" :items="breadcrumbs" />
                </div>
                <div>
                    <el-dropdown v-if="showBtn" @command="handleCommand">
                        <el-button type="primary">
                            申请导出
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                                v-for="(item, index) in databaseList"
                                :key="`d-${index}`"
                                :command="item.key"
                            >
                                {{ item.label }}
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        {{ scope.row.name }}
                        <div class="table-btns d-flex">
                            <el-button
                                type="text"
                                v-if="
                                    getShowBtn4List(
                                        scope.row.id,
                                        'audit_success'
                                    )
                                "
                                @click="toOperate(scope.row, 'audit_success')"
                            >
                                审核通过
                            </el-button>
                            <el-button
                                type="text"
                                v-if="
                                    getShowBtn4List(scope.row.id, 'audit_fail')
                                "
                                @click="toOperate(scope.row, 'audit_fail')"
                            >
                                审核未通过
                            </el-button>
                            <el-button
                                type="text"
                                v-if="
                                    getShowBtn4List(
                                        scope.row.id,
                                        'cancel_apply'
                                    )
                                "
                                @click="toOperate(scope.row, 'cancel_apply')"
                            >
                                撤销申请
                            </el-button>

                            <el-button
                                type="text"
                                v-if="
                                    getShowBtn4List(scope.row.id, 'make_file')
                                "
                                @click="toOperate(scope.row, 'make_file')"
                            >
                                生成文件
                            </el-button>
                            <el-button
                                type="text"
                                v-if="
                                    getShowBtn4List(
                                        scope.row.id,
                                        'download_file'
                                    )
                                "
                                @click="download(scope.row, 'download_file')"
                            >
                                下载文件
                            </el-button>
                        </div>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { MessageBox } from "element-ui"
    import { cloneDeep } from "lodash"
    import { Component } from "vue-property-decorator"
    import {
        buildConfig4Remote,
        getShowBtn4List,
        getShowBtn4Page,
    } from "../components/build-table"
    import { getExportMap4DCollect } from "@/views/pages/collect-task-manage/collect-task-map"

    @Component({
        name: routesMap.collectTaskManage.exportTaskManage,
        components: { TableContainer, CommonTable },
    })
    export default class LaborInfoBase extends BaseTableController<any> {
        tableConfig: TableConfig | null = null

        refreshConfig = {
            fun: this.init,
            name: routesMap.collectTaskManage.exportTaskManage,
        }

        private databaseList: { key: string; label: string }[] = [
            // {
            //     key: "manage",
            //     label: "从劳动力信息库导出",
            // },
            // {
            //     key: "manage_back_home",
            //     label: "从返乡人员信息库导出",
            // },
            // {
            //     key: "manage_student",
            //     label: "从高校毕业生信息库导出",
            // },
        ]

        private checkEdIds: number[] = []

        private columns: TableColumn[] = []
        private showBtn = false

        private rows: any[] = []

        private routesName = routesMap.collectTaskManage.exportTaskManage

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private getShowBtn4List(id: string, key: string) {
            return getShowBtn4List(this.rows, id, key)
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "导出任务管理",
                    to: {
                        name: this.routesName,
                    },
                },
            ]
            updateTagItem({
                name: this.routesName,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        created() {
            this.init()
            getExportMap4DCollect().then((r) => {
                this.databaseList = []
                Object.keys(r || {}).forEach((key) => {
                    this.databaseList.push({
                        key: key,
                        label: r[key],
                    })
                })
            })
        }

        private init() {
            pageLoading(() => {
                return buildConfig4Remote(
                    "collect_export_task",
                    "manage",
                    undefined,
                    false,
                    false,
                    {
                        predicts: {
                            file_path: "result.data#file_path",
                        },
                    }
                ).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            this.showBtn = getShowBtn4Page(r, "apply_export")

            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = r.columns
        }

        mounted() {
            this.setBreadcrumbs()
        }

        private download(row: any, actionName: string) {
            sdk.core
                .model("collect_export_task")
                .action(actionName)
                .updateInitialParams({
                    selected_list: [{ v: 0, id: row.id }],
                })
                .addInputs_parameter({})
                .execute()
                .then((r: any) => {
                    window.open(r.forward, "__blank")
                })
        }

        private toOperate(row: any, actionName: string) {
            const t = this.getShowBtn4List(row.id, actionName)
            MessageBox.confirm(`确认${t.label}？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("collect_export_task")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private goFilter(listName: string) {
            this.$router.push({
                name: routesMap.collectTaskManage.exportTaskManageFilter,
                query: {
                    region: "",
                    regionLabel: "",
                    listName: listName || "",
                },
            })
        }

        private handleCommand(key: string) {
            this.goFilter(key)
        }

        private findBtn(name: string, row: any) {
            const actions: any[] = row?.actions || []
            const t = actions.find((i) => i.action_name === name)
            return t
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }

    .table-btns {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        justify-content: center;

        /deep/ .el-button {
            margin-left: 0;
        }
    }
</style>
