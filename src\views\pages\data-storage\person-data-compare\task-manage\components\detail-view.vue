<template>
    <div v-if="detailRow">
        <div class="title u-flex u-row-between">
            <div class="u-flex">
                {{ detailRow.task_name }}
                <div
                    class="u-flex u-col-center status"
                    :class="'status-' + +detailRow.task_status"
                >
                    {{ detailRow.task_status_label }}
                </div>
            </div>
            <div class="u-flex progress" v-if="isProgress && finishTime">
                <div class="u-flex">
                    完成进度：{{ currentProcess }}
                    <div class="progress-box u-m-x-10">
                        <el-progress
                            :text-inside="true"
                            :stroke-width="10"
                            :percentage="+currentProcess.replace('%', '')"
                            define-back-color="#E5ECF2"
                            color="#5782EC"
                            :show-text="false"
                        ></el-progress>
                    </div>
                </div>
                <div class="u-m-l-8">
                    预计完成时间：<span class="finish-time">{{
                        finishTime
                    }}</span>
                </div>
            </div>
        </div>
        <div class="u-p-20">
            <detail-row-col :labelStyle="labelStyle" :list="items">
            </detail-row-col>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { sdk } from "@/service"
    import { routesMap } from "@/router/direction"
    import { Status } from "../index"

    @Component({
        components: { TableContainer, CommonTable, DetailRowCol },
    })
    export default class DetailView extends Vue {
        @Prop({ default: () => {} })
        private detailRow!: any

        @Watch("$route", { immediate: true, deep: true })
        onRouteChange(cur: any, old: any) {
            this.clearPolling()
            if (cur.name === routesMap.dataStorage.personDataCompareTaskDetail) {
                if (
                    this.isProgress &&
                    this.detailRow?.id === this.$route.query.taskId
                ) {
                    this.checkStatus()
                }
            }
        }

        private currentProcess = "0%"
        private pollTimer: any = null
        private finishTime = ""

        private get labelStyle() {
            return {
                minWidth: "100px",
                textAlign: "left",
                color: "#555",
            }
        }

        private get items() {
            const h = this.$createElement
            const item: ColItem[] = [
                {
                    label: "数据比对功能：",
                    value: this.detailRow?.func_name,
                    span: 8,
                },
                {
                    label: "需求描述：",
                    value: this.detailRow?.description || "--",
                    span: 8,
                },
                {
                    label: "创建人：",
                    value: this.detailRow?.real_name,
                    span: 8,
                },
                {
                    label: "创建时间：",
                    value: this.detailRow?.create_time_label,
                    span: 8,
                },
                {
                    label: "比对数据：",
                    vNode: this.detailRow.source_url
                        ? h(
                              "a",
                              {
                                  class: "primary",
                                  attrs: {
                                      href: sdk.buildFilePath(
                                          this.detailRow.source_url.split(
                                              "__"
                                          )[1] || ""
                                      ),
                                      target: "__blank",
                                  },
                              },
                              this.detailRow.source_url.split("__")[1] || ""
                          )
                        : h("span", undefined, "--"),
                    span: 24,
                },
            ]
            return item
        }

        private get isProgress() {
            return this.detailRow && this.detailRow.task_status === Status.处理中
        }

        private formatNumber(text: string) {
            if (!text) return ""
            return text.replace(/(\d+)/g, '<span style="color: red">$1</span>')
        }

        async checkStatus() {
            if (
                !this.isProgress ||
                this.$route.name !==
                    routesMap.dataStorage.personDataCompareTaskDetail
            ) {
                return
            }
            try {
                const { process_per_str, estimated_finish_date } =
                    await this.getProgress()

                if (process_per_str !== "100%") {
                    this.currentProcess = process_per_str
                    this.finishTime = estimated_finish_date || "计算中"
                    this.pollTimer = setTimeout(this.checkStatus, 6000)
                } else {
                    this.clearPolling()
                    this.callRefresh(
                        routesMap.dataStorage.personDataCompareTaskDetail
                    )
                    this.callRefresh(routesMap.dataStorage.personDataCompareTask)
                }
            } catch (error) {
                console.error("请求失败:", error)
                this.clearPolling()
            }
        }

        private getProgress() {
            return sdk
                .getDomainService("get_task_progress", "back_api", "xg_project")
                .post({
                    task_id: this.detailRow?.id || "",
                })
                .then((res: any) => {
                    return res
                })
        }

        clearPolling() {
            if (this.pollTimer) {
                clearTimeout(this.pollTimer)
                this.pollTimer = null
            }
        }

        beforeUnmount() {
            this.clearPolling()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    .title {
        width: 100%;
        height: 40px;
        background: #f8f8f8;
        color: #222;
        font-size: 18px;
        font-weight: 600;
        line-height: 40px;
        padding: 0 20px;
        .status {
            padding: 2px 6px;
            color: white;
            line-height: 16px;
            border-radius: 2px;
            margin-left: 10px;
            font-size: 14px;
            &.status-1 {
                background-color: #22bd7a;
            }
            &.status-0 {
                background-color: #f5820f;
            }
            &.status-2 {
                background-color: #6b99f6;
            }
            &.status-3 {
                background-color: #d0021b;
            }
        }
        .progress {
            font-size: 14px;
            font-weight: 600;
            white-space: nowrap;
            .progress-box {
                width: 100px;
            }
            .finish-time {
                color: #f5820f;
            }
        }
    }
</style>
