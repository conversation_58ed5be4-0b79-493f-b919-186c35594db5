<template>
    <div class="check-list-container">
        <table-container
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            class="container"
            @tabName="tabName"
        >
            <div slot="title" class="d-flex-item-center bold">
                企业用工信息填报
            </div>
            <div slot="header-right">
                <el-button type="primary" plain @click="toAdd"
                    >新建任务</el-button
                >
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table
                    :data="data"
                    :tableConfig="tableConfig"
                    :columns="tableConfig.column"
                >
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            查看详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { cloneDeep } from "lodash"
    import { Component } from "vue-property-decorator"
    import { tableConfig } from "."
    import {
        column,
        column1,
        column2,
        Row,
    } from "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/index"

    @Component({
        name: routesMap.employmentManage.hrInfoManage.workInfoApply,
        components: { TableContainer, CommonTable },
    })
    export default class WorkInfoApply extends BaseTableController<Row> {
        private tableConfig = tableConfig()

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.employmentManage.hrInfoManage.workInfoApply,
        }

        tabName(name: string) {
            if (name === "待发布") {
                this.tableConfig.column = cloneDeep(column1)
            } else if (name === "已结束") {
                this.tableConfig.column = cloneDeep(column2)
            } else {
                this.tableConfig.column = cloneDeep(column)
            }
        }

        private toAdd() {
            this.$router.push({
                name: routesMap.employmentManage.hrInfoManage.workInfoApplyAdd,
            })
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.employmentManage.hrInfoManage.workInfoApplyDetail,
                query: {
                    id: row.access_key,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .check-list-container {
        width: 100%;
    }

    .header {
        padding-top: 15px;
        padding-bottom: 15px;

        .title {
            font-size: 22px;
            color: #000000;
        }
    }

    .text-center {
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
</style>
