<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="预览"
        width="1000px"
        top="8vh"
    >
        <div class="u-p-x-20">
            <DetailTable
                v-if="tableColumns && tableColumns.length"
                :columns="tableColumns"
                :rows="rows"
            />
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { FormController } from "@/core-ui/component/form"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import DetailTable from "../../components/detail-table.vue"
    import { sdk } from "@/service"
    import { IndicatorColumn, ListDetailMeta } from "../../../report-manage/index"
    import { TableColumn } from "@/core-ui/component/table"

    interface RowItem {
        id: string
        name: string
    }

    @Component({ components: { DetailTable } })
    export default class PreviewPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: "" })
        private readonly id!: number

        private columns: IndicatorColumn[] = []
        private tableColumns: TableColumn[] | any[] = []
        private rows: RowItem[] = []

        onOpen() {
            this.init()
        }

        private init() {
            this.columns = []
            this.tableColumns = []
            Promise.all([this.getReportMeta(), this.getYMeta()]).then(
                (data: any[]) => {
                    this.columns = data[0].indicator_meta as IndicatorColumn[]
                    this.tableColumns = this.columns?.map((i) => {
                        return {
                            label: i.display_name,
                            prop: i.union_code,
                            description: i.description,
                            value_type: i.value_type,
                            showOverflowTip: false,
                            children:
                                i.children?.map((j) => {
                                    return {
                                        label: j.display_name,
                                        prop: j.union_code,
                                        showOverflowTip: false,
                                        description: j.description,
                                        children: j.children || [],
                                        value_type: j.value_type,
                                    }
                                }) || [],
                        }
                    })
                    this.rows =
                        sdk.buildRows<any>(data[1].pageData.rows, {
                            id: "",
                            name: "",
                        }) || []
                }
            )
        }

        private getReportMeta() {
            return sdk.core
                .model("xg_indicator_task")
                .getRequest<ListDetailMeta>("getReportMeta", { id: this.id })
        }

        private getYMeta() {
            return sdk.core
                .model("xg_indicator_name_y_dict")
                .list("for_operate_list")
                .addPrefilter({ group_ref_id: this.id })
                .query({ pageIndex: 1, item_size: 999 })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
