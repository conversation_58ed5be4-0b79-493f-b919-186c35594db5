import { config, EnvProject } from "@/config"
import { BuildFormConfig, FileType, FormType } from "@/core-ui/component/form"
import { rules } from "@/core-ui/component/form/rule"
import { buildBaiduFormItem } from "@/plugins/baidu-map-selector"
import { sdk } from "@/service"
import { desensitization } from "@/utils/tools"
import { get } from "lodash"

export function getCompanyInfo(id: string) {
    return sdk.core
        .model("xg_agent")
        .detail(id, "back_detail")
        .queryKeyCustom()
        .then((r) => {
            return sdk.buildRow<Row>(r.row, {
                agent_name: "",
                company_code: "",
                contact_person: "",
                contact_mobile: "",
                legal_person: "",
                legal_card_open_id: "",
                company_size: "label",
                industory_catalog: "label",
                legal_mobile: "",
                business_file: "",
                human_agent_id: "xg_human_agent#id",
                is_open_human_agent: "label",
                service_code_name: "xg_human_agent#service_code_name_label",
                remark: "xg_human_agent#remark",
                human_agent_logo: "xg_human_agent#logo_label",
                person_file: "xg_human_agent#person_file",
                audit_status: "xg_human_agent#audit_status_label",
                odd_job_id: "xg_human_agent#xg_odd_job#id",
                is_human_agent_manager: "",
                access_key: "",
            })
        })
}
export interface Row {
    id: number
    agent_name: string
    company_code: string
    contact_person: string
    contact_mobile: string
    legal_person: string
    legal_mobile: string
    business_file: string
    human_agent_id: string
    is_open_human_agent: string
    service_code_name: string
    remark: string
    human_agent_logo: string
    person_file: string
    audit_status: number
    audit_status_label: string
    odd_job_id: string
    is_human_agent_manager: boolean
    access_key: string
}

const forms: BuildFormConfig["forms"] = [
    {
        label: "服务区域：",
        type: FormType.MultipleCascader,
        prop: "region_code",
        option: { elProps: { checkStrictly: true, multiple: true } },
        required: true,
    },
    {
        label: "业务介绍：",
        type: FormType.Text,
        option: {
            type: "textarea",
            rows: 4,
            resize: "none",
        },
        prop: "remark",
    },
    {
        label: "人力资源许可证：",
        type: FormType.MyUpload,
        prop: "person_file",
        option: {
            fileType: [FileType.Image],
            listType: "picture-card",
            placeholder: "支持上传jpg、png等图片格式，不超过3M",
            limit: 1,
            limitSize: 3072,
        },
    },
    {
        prop: "audit_status",
        defaultValue: "0",
    },
]

export const createPositionFormSections = (id: string): BuildFormConfig => ({
    forms,
    sdkModel: "xg_agent",
    sdkAction: "open_human_agent",
    id: +id,
})

export const editCooperationFormConfig = (id: string): BuildFormConfig => ({
    forms,
    sdkModel: "xg_human_agent",
    sdkAction: "edit_human_agent",
    id: +id,
})

export function companyFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_agent",
        sdkAction: "edit_agent",
        id,
        forms: [
            {
                label: "企业名称",
                type: FormType.Text,
                prop: "agent_name",
                noEdit: true,
                col: {
                    span: 12,
                },
            },
            {
                label: "统一信用代码",
                type: FormType.Text,
                prop: "company_code",
                noEdit: true,
                col: {
                    span: 12,
                },
            },
            {
                label: "法人姓名",
                type: FormType.Text,
                prop: "legal_person",
                noEdit: true,
                col: {
                    span: 12,
                },
            },
            {
                label: "法人身份证",
                type: FormType.Text,
                prop: "legal_card_open_id",
                col: {
                    span: 12,
                },
                required: true,
            },
            {
                label: "企业联系人",
                type: FormType.Text,
                prop: "contact_person",
                col: {
                    span: 12,
                },
                required: true,
            },
            {
                label: "手机号",
                type: FormType.Text,
                prop: "contact_mobile",
                col: {
                    span: 12,
                },
                option: {
                    handlerDisplay(v) {
                        return desensitization(v)
                    },
                },
                required: true,
            },
            // {
            //     label: "固定电话",
            //     type: FormType.Text,
            //     prop: "tele_phone",
            //     col: {
            //         span: 12,
            //     },
            // },
            {
                label: "所属行业",
                type: FormType.Cascader,
                prop: "industory_catalog",
                option: {
                    elProps: { checkStrictly: true },
                },
                col: {
                    span: 12,
                },
                required: true,
            },
            // {
            //     label: "企业所在地",
            //     type: FormType.Cascader,
            //     prop: "region_code",
            //     emptyDisplay: "未选择",
            //     col: {
            //         span: 12,
            //     },
            //     required: true,
            // },
            // {
            //     label: "详细地址",
            //     type: FormType.Text,
            //     prop: "address_detail",
            //     col: {
            //         span: 12,
            //     },
            //     required: true,
            // },
            {
                prop: "address_detail",
                label: "",
                type: FormType.Text,
                hide: true,
            },
            {
                prop: "lat",
                label: "",
                type: FormType.Text,
                hide: true,
            },
            {
                prop: "lng",
                label: "",
                type: FormType.Text,
                hide: true,
            },
            {
                label: "",
                type: FormType.Cascader,
                prop: "region_code",
                option: { elProps: { checkStrictly: true } },
                required: true,
                col: { span: 0 },
                hide: true,
            },
            buildBaiduFormItem({
                label: "详细地址",
                prop: "addressEditFiled",
                col: { span: 12 },
                options: {
                    placeholder: "请选择企业所在地详细地址",
                },
                required: true,
                rules: [
                    {
                        validator: (_, value, callback) => {
                            if (!get(value, "address_detail")) {
                                callback(new Error("未设置企业详细地址"))
                                return
                            }
                            if (!get(value, "lng") || !get(value, "lat")) {
                                callback(
                                    new Error(
                                        "地址经纬度信息缺失，请重新选择地址"
                                    )
                                )
                                return
                            }
                            callback()
                        },
                    },
                ],
            }),
            {
                label: "注册资本",
                type: FormType.Text,
                prop: "reg_origin",
                col: {
                    span: 12,
                },
            },
            {
                label: "营业状态",
                type: FormType.Select,
                prop: "oper_status",
                col: {
                    span: 12,
                },
            },
            {
                label: "实缴资本",
                type: FormType.Text,
                prop: "pay_amount",
                option: {
                    append: "元人民币",
                    type: "number",
                    handlerDisplay(v, emptyDisplay) {
                        return v ? v + "元人民币" : emptyDisplay
                    },
                },
                col: {
                    span: 12,
                },
            },
            {
                label: "成立日期",
                type: FormType.DatePicker,
                prop: "found_time",
                option: { type: "date" },
                col: {
                    span: 12,
                },
            },
            {
                label: "登记机关",
                type: FormType.Text,
                prop: "reg_authority",
                col: {
                    span: 12,
                },
            },
            {
                label: "人员规模",
                type: FormType.Select,
                prop: "company_size",
                col: {
                    span: 12,
                },
                required: true,
            },
            {
                label: "附件资料",
                type: FormType.MyUpload,
                prop: "file",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式",
                },
                col: {
                    span: 12,
                },
            },
            {
                label: "营业执照",
                type: FormType.MyUpload,
                prop: "business_file",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式",
                    limit: 1,
                },
                col: {
                    span: 8,
                },
            },
            {
                label: "企业类型",
                type: FormType.Select,
                prop: "agent_type",
                col: {
                    span: 12,
                },
            },
        ],
    }
}

export function companyFormConfig2(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_agent",
        sdkAction: "edit_remark",
        id,
        forms: [
            {
                label: "是否重点企业",
                type: FormType.Select,
                prop: "is_focus",
                col: {
                    span: 8,
                },
                noEdit: true,
                required: false,
                disabledUniplatRule: true,
            },
            {
                label: "是否在荆州官网显示",
                type: FormType.Switch,
                prop: "web_show",
                col: {
                    span: 8,
                },
                noEdit: true,
                required: false,
                disabledUniplatRule: true,
                hide: config.envProject !== EnvProject.荆州项目,
            },
            {
                prop: "公司福利",
                useTag: "公司福利",
                label: "企业福利",
                type: FormType.Select2,
                option: {
                    multiple: true,
                },
                col: {
                    span: 24,
                },
                required: false,
            },
            {
                label: "企业官网",
                type: FormType.Text,
                prop: "webpage",
                col: {
                    span: 8,
                },
                rules: [...rules.httpUrl],
            },
            {
                label: "对外联系人",
                type: FormType.Text,
                prop: "external_contact_person",
                col: {
                    span: 8,
                },
            },
            {
                label: "对外联系电话",
                type: FormType.Text,
                prop: "external_contact_mobile",
                col: {
                    span: 8,
                },
            },
            {
                label: "企业logo",
                type: FormType.MyUpload,
                prop: "logo",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式",
                    limit: 1,
                },
                col: {
                    span: 24,
                },
            },
            {
                label: "企业风采图片",
                type: FormType.MyUpload,
                prop: "elegant_image",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式，最多支持20张",
                    limit: 20,
                },
                col: {
                    span: 24,
                },
            },
            {
                label: "企业风采视频",
                type: FormType.MyUpload,
                prop: "elegant_video",
                option: {
                    fileType: [FileType.Video],
                    placeholder:
                        "支持上传mp4、avi视频格式，最多支持5个，单个不超过20M。",
                    limit: 5,
                },
                col: {
                    span: 12,
                },
            },
            {
                label: "企业介绍",
                type: FormType.Text,
                prop: "remark",
                emptyDisplay: "暂无内容",
                option: {
                    type: "textarea",
                    autosize: { minRows: 3, maxRows: 6 },
                    maxlength: 1000,
                    showWordLimit: true,
                },
                col: {
                    span: 24,
                },
                required: true,
            },
        ],
    }
}
