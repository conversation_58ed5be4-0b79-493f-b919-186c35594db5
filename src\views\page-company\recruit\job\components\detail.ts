import { config, EnvProject } from "@/config"
import {
    BuildFormConfig,
    FormType,
    buildSelectSource,
} from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { buildBaiduFormItem } from "@/plugins/baidu-map-selector"
import { sdk } from "@/service"
import {
    expiateTimePickerOptions,
    expiateTimeRules,
} from "@/views/pages/recruit/job/components/detail"
import { get } from "lodash"
import { ListTypes } from "uniplat-sdk"
import SalaryEditField from "./salary-edit-field.vue"

const isDdDev =
    process.env.VUE_APP_ENV === "test" &&
    [EnvProject.掇刀项目].includes(config.envProject)

const tableFilter: TableFilter[] = [
    // {
    //     label: "申请时间",
    //     type: FormType.DatePicker,
    //     prop: "apply_time",
    //     option: {
    //         type: "datetimerange",
    //     },
    // },
]
export const tableConfig = (position_id: string): TableConfig => ({
    model: sdk.core
        .model("xg_company_position_recommend")
        .list("position_recommend_list"),
    filter: tableFilter,
    defaultPageSize: 10,
    preFilter: { position_id },
    predict: {
        recommend_type: "label",
        recommend_desc: "",
        recommend_duration: "label",
        audit_status: "label",
        apply_time: "label",
        start_time: "label",
        end_time: "label",
        status: "label",
        status_memo: "label",
    },
    oneTabFilter: true,
})
export const columns: TableColumn[] = [
    {
        label: "服务类型",
        prop: "recommend_type_label",
        showOverflowTip: true,
    },
    {
        label: "推广时间",
        prop: "recommend_duration_label",
        width: "180px",
        showOverflowTip: true,
    },
    {
        label: "状态",
        prop: "status_memo_label",
        showOverflowTip: true,
    },
    {
        label: "申请时间",
        prop: "apply_time_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
    },
]

export enum RecommendType {
    人才推荐服务 = 1,
    社群推广服务,
    网格推广服务,
    人力资源机构代招服务,
}

export interface Row {
    id: number
    recommend_type: RecommendType
    recommend_type_label: string
    _access_key: string
}

export const salary: any = {
    min: "",
    max: "",
}

export const age: any = {
    min: 0,
    max: 0,
}

export const experience = {
    min: 0,
    max: 0,
}

const itemStyle = {
    color: "#222",
    fontSize: "18px",
    marginLeft: "-50px",
    marginTop: "20px",
    fontWeight: "600",
}
function getCol(col = 0) {
    return {
        span: 11,
        offset: config.projectConfig.hideGender ? 2 - col : col,
    }
}
export const createPositionFormSections = (
    id?: string,
    isAudit?: number
): BuildFormConfig => ({
    forms: [
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位基本信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "岗位名称：",
            type: FormType.Text,
            prop: "name",
            required: true,
            col: { span: 11 },
        },
        {
            label: "薪资待遇：",
            type: SalaryEditField,
            prop: "salaryEditField",
            required: true,
            rules: [
                {
                    validator(rule, value, callback) {
                        if (!value.salary) {
                            return callback(new Error("请选择薪资待遇"))
                        }
                        if (value.salary === "自定义") {
                            if (!value.salary_min || !value.salary_max) {
                                return callback(new Error("请输入自定义薪资"))
                            }
                            if (+value.salary_min > +value.salary_max) {
                                return callback(new Error("需小于最高薪资"))
                            }
                        }
                        callback()
                    },
                },
            ],
            col: {
                span: 11,
                offset: 2,
            },
        },

        {
            type: FormType.Text,
            prop: "salary_min",
            hide: true,
        },
        {
            type: FormType.Text,
            prop: "salary_max",
            hide: true,
        },
        {
            type: FormType.Select,
            prop: "salary",
            hide: true,
        },
        {
            label: "结算方式：",
            prop: "salary_type",
            type: FormType.Select,
            col: { span: 11 },
        },
        {
            label: "工作性质：",
            type: FormType.Select,
            prop: "work_type",
            required: true,
            col: { span: 11, offset: 2 },
        },
        {
            label: "招聘人数：",
            type: FormType.Text,
            option: { type: "number" },
            prop: "recruit_count",
            col: { span: 11 },
            rules: [
                { required: true, message: "请输入招聘人数" },
                {
                    validator(rule, value, callback) {
                        if (+value < 1) {
                            callback(new Error("招聘人数不能小于1"))
                            return
                        }
                        if (+value > 9999) {
                            callback(new Error("招聘人数不能大于9999"))
                            return
                        }
                        if ((value + "").includes(".")) {
                            callback(new Error("请输入整数"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: "招聘过期时间：",
            type: FormType.DatePicker,
            prop: "expired_date",
            col: { span: 11, offset: 2 },
            option: {
                pickerOptions: expiateTimePickerOptions,
                disabled: !!isAudit,
            },
            rules: expiateTimeRules,
        },
        // {
        //     label: "工作地区：",
        //     type: FormType.Cascader,
        //     option: {
        //         filterable: true,
        //         elProps: { checkStrictly: true },
        //     },
        //     prop: "city",
        //     required: true,
        //     col: { span: 11, offset: 2 },
        // },
        {
            label: "职能：",
            type: FormType.Cascader,
            prop: "function_category",
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
                placeholder: "请输入或选择",
            },
            col: { span: 11 },
        },
        {
            prop: "职位福利",
            useTag: "职位福利",
            label: "岗位福利",
            type: FormType.Select2,
            option: {
                multiple: true,
            },
            col: {
                span: 24,
            },
        },
        {
            prop: "职位亮点",
            useTag: "职位亮点",
            label: "职位亮点",
            type: FormType.Select2,
            option: {
                multiple: true,
            },
            col: {
                span: 24,
            },
        },
        {
            label: "岗位描述：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 4,
                resize: "vertical",
                autosize: {
                    minRows: 4,
                    maxRows: 10,
                },
            },
            col: { span: 24 },
            prop: "description",
            rules: [{ required: true, message: "请输入岗位描述" }],
        },
        {
            prop: "lat",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        {
            prop: "lng",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        isDdDev
            ? {
                  label: "所属地：",
                  type: FormType.Cascader,
                  prop: "region_code",
                  option: { elProps: { checkStrictly: true } },
                  required: true,
                  col: { span: 10 },
                  rules: [{ required: true, message: "请选择所属地" }],
              }
            : {
                  label: "",
                  type: FormType.Cascader,
                  prop: "region_code",
                  option: { elProps: { checkStrictly: true } },
                  required: true,
                  col: { span: 0 },
                  hide: true,
              },
        isDdDev
            ? {
                  label: "详细地址：",
                  type: FormType.Text,
                  prop: "address_detail",
                  required: true,
                  col: { span: 12, offset: 2 },
                  rules: [{ required: true, message: "请输入详细地址" }],
              }
            : {
                  prop: "address_detail",
                  label: "",
                  type: FormType.Text,
                  hide: true,
              },
        isDdDev
            ? buildBaiduFormItem({
                  label: "工作地：",
                  prop: "addressEditFiled",
                  col: { span: 24 },
                  options: {
                      placeholder: "请选择地址",
                  },
                  extendData: {
                      from: "create-position",
                  },
                  required: true,
                  rules: [
                      {
                          validator: (_, value, callback) => {
                              if (!get(value, "lng") || !get(value, "lat")) {
                                  callback(
                                      new Error(
                                          "地址经纬度信息缺失，请重新选择地址"
                                      )
                                  )
                                  return
                              }
                              callback()
                          },
                      },
                  ],
              })
            : buildBaiduFormItem({
                  label: "详细地址：",
                  prop: "addressEditFiled",
                  col: { span: 24 },
                  options: {
                      placeholder: "请选择地址",
                  },
                  required: true,
                  rules: [
                      {
                          validator: (_, value, callback) => {
                              if (!get(value, "address_detail")) {
                                  callback(new Error("未设置工作地"))
                                  return
                              }
                              if (!get(value, "lng") || !get(value, "lat")) {
                                  callback(
                                      new Error(
                                          "地址经纬度信息缺失，请重新选择地址"
                                      )
                                  )
                                  return
                              }
                              callback()
                          },
                      },
                  ],
              }),
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "任职信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "岗位职责：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 4,
                resize: "vertical",
                autosize: {
                    minRows: 4,
                    maxRows: 10,
                },
            },
            col: { span: 24 },
            prop: "function_detail",
            rules: [{ message: "请输入岗位职责" }],
        },
        {
            label: "年龄限制：",
            type: FormType.Text,
            prop: "age_require_min",
            option: {
                type: "number",
                placeholder: "最低年龄",
            },
            col: {
                span: 6,
            },
            rules: [
                {
                    required: true,
                    message: "不能为空",
                },
                {
                    validator(rule, value, callback) {
                        age.min = +value
                        if (+age.min < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+age.min > +age.max && age.max !== "") {
                            callback(new Error("需小于最高年龄"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: "",
            type: FormType.Text,
            prop: "age_require_max",
            labelWidth: "25px",
            option: {
                type: "number",
                placeholder: "最高年龄",
            },
            col: {
                span: 5,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        age.max = +value
                        if (+age.max < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+age.min > +age.max) {
                            callback(new Error("需大于最低年龄"))
                            return
                        }
                        callback()
                    },
                    message: "需大于最低年龄",
                },
            ],
        },
        {
            label: "工作年限：",
            type: FormType.Text,
            prop: "experience_min",
            option: {
                type: "number",
                placeholder: "最低年限",
            },
            col: {
                span: 6,
                offset: 2,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        experience.min = +value
                        if (!experience.min || !experience.max) {
                            callback()
                            return
                        }
                        if (+experience.min < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+experience.min > +experience.max) {
                            callback(new Error("需小于最高年限"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: ``,
            type: FormType.Text,
            prop: "experience_max",
            labelWidth: "25px",
            option: {
                type: "number",
                placeholder: "最高年限",
            },
            col: {
                span: 5,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        experience.max = +value
                        if (!experience.min || !experience.max) {
                            callback()
                            return
                        }
                        if (+experience.min > +experience.max) {
                            callback(new Error("需大于最低年限"))
                            return
                        }
                        callback()
                    },
                    message: "需大于最低年限",
                },
            ],
        },
        {
            label: "性别要求：",
            prop: "gender_require",
            type: FormType.Select,
            col: { span: 11 },
            hide: config.projectConfig.hideGender,
        },
        {
            label: "学历要求：",
            prop: "education",
            type: FormType.Select,
            col: getCol(2),
        },

        {
            label: "语言种类：",
            type: FormType.Select,
            prop: "language",
            col: getCol(0),
        },
        {
            label: "外语水平：",
            type: FormType.Select,
            prop: "language_level",
            col: getCol(2),
        },
        {
            label: "户籍所在地：",
            type: FormType.Cascader,
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
            prop: "households",
            col: getCol(0),
        },
        {
            label: "专业要求：",
            type: FormType.Cascader,
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
            prop: "major",
            col: getCol(2),
        },
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位联系信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "联系人：",
            type: FormType.Text,
            prop: "contact_person",
            required: true,
            col: {
                span: 11,
            },
        },
        {
            label: "联系电话：",
            type: FormType.Text,
            prop: "contact_mobile",
            required: true,
            col: {
                span: 11,
                offset: 2,
            },
        },
        // {
        //     label: "岗位来源：",
        //     type: FormType.Select,
        //     prop: "source_from_type",
        //     required: true,
        // },
        // {
        //     type: FormType.Tip,
        //     prop: "t",
        //     label: "",
        //     defaultValue: "789",
        //     option: {
        //         placeholder: "如果此岗位有站外链接，请添加站外链接的来源与地址",
        //     },
        //     itemStyle: {
        //         color: "#7998B8",
        //         marginLeft: "-63px",
        //     },
        // },
        // {
        //     label: "岗位链接：",
        //     type: FormType.Text,
        //     prop: "page_url",
        //     col: { span: 11 },
        // },
        {
            label: "联系人邮箱：",
            type: FormType.Text,
            prop: "contact_email",
            col: { span: 11 },
        },
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位投递要求",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "附件简历是否必填：",
            type: FormType.Radio,
            prop: "require_profile",
            required: true,
            col: { span: 11 },
            option: {
                type: "boolean",
            },
            labelWidth: "140px",
            sourceInputsParameter: buildSelectSource([
                {
                    value: "是",
                    key: "1",
                },
                {
                    value: "否",
                    key: "0",
                },
            ]),
        },
        {
            label: "工作经历是否必填：",
            type: FormType.Radio,
            prop: "require_work",
            required: true,
            col: { span: 11, offset: 2 },
            option: {
                type: "boolean",
            },
            labelWidth: "140px",
            sourceInputsParameter: buildSelectSource([
                {
                    value: "是",
                    key: "1",
                },
                {
                    value: "否",
                    key: "0",
                },
            ]),
        },
        {
            label: "学历信息是否必填：",
            type: FormType.Radio,
            prop: "require_education",
            required: true,
            col: { span: 11 },
            option: {
                type: "boolean",
            },
            labelWidth: "140px",
            sourceInputsParameter: buildSelectSource([
                {
                    value: "是",
                    key: "1",
                },
                {
                    value: "否",
                    key: "0",
                },
            ]),
        },
        {
            prop: "create_type",
            type: FormType.Text,
            hide: true,
            defaultValue: "2",
        },
    ],
    sdkModel: "xg_company_position",
    sdkAction: id ? "company_update_position" : "create_company_position",
    id,
})

export const tableFilter2: TableConfig["filter"] = [
    {
        label: "姓名",
        prop: "name",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "性别",
        prop: "sex",
        type: FormType.Select,
    },
    {
        label: "当前城市",
        prop: "permanent_detail",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "年龄",
        prop: "age",
        type: FormType.TextRange,
        option: {
            type: "number",
        },
    },
    {
        label: "状态",
        prop: "status1",
        type: FormType.Select,
        sourceInputsParameter: buildSelectSource([
            {
                key: "0",
                value: "已推荐岗位给求职者/待反馈",
            },
            {
                key: "1",
                value: "联系待标记",
            },
            {
                key: "2",
                value: "不感兴趣",
            },
            {
                key: "10",
                value: "沟通中",
            },
            {
                key: "20",
                value: "已入职",
            },
            {
                key: "30",
                value: "未入职",
            },
        ]),
    },
]

export const tableConfig2 = (position_id: string): TableConfig => ({
    model: sdk.core
        .model("xg_candidate_order")
        .list("company_position_profile_list"),
    filter: tableFilter2,
    defaultPageSize: 10,
    preFilter: { position_id, created_from: "talent" },
    predict: {
        name: "",
        sex: "label",
        age: "",
        id_card: "",
        mobile: "",
        region_name: "permanent_city#region_name",
        update_time: "",
        status: "label",
        created_from: "label",
        agent_name: "xg_company_bidding#hm_agent#agent_name",
        education: "label",
        nation: "label",
        province: "permanent_province#region_name_label",
        city: "permanent_city#region_name_label",
        area: "permanent_area#region_name_label",
        status_memo: "label",
        profile_id: "profile#id",
        permanent_detail: "label",
    },
    oneTabFilter: true,
})

export const columns2: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "mobile",
        width: "110px",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "性别",
        prop: "sex_label",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "年龄",
        prop: "age",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "学历",
        minWidth: "80px",
        prop: "education_label",
        showOverflowTip: true,
    },
    // {
    //     label: "民族",
    //     prop: "nation",
    //     showOverflowTip: true,
    // },
    {
        label: "当前城市",
        prop: "permanent_detail_label",
        showOverflowTip: true,
    },
    {
        label: "当前状态",
        prop: "status_memo",
        showOverflowTip: true,
    },
    {
        label: "推荐业务",
        prop: "created_from_label",
        width: "140px",
        showOverflowTip: true,
    },
    {
        label: "机构名称",
        prop: "agent_name",
        width: "140px",
        showOverflowTip: true,
    },
    {
        label: "操作",
        width: "175px",
        prop: "h",
    },
]

export interface Row2 {
    id: number
    status_memo: string
    created_from: string
}

export const labelMap = {
    talent: { 10: "沟通中", 20: "成功入职", 30: "已淘汰/拒绝" },
    agent_bidding: { 20: "已入职", 30: "未入职" },
}

const tableFilter3: TableFilter[] = [
    {
        label: "姓名",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    {
        label: "年龄",
        type: FormType.TextRange,
        option: { type: "number" },
        prop: "age",
    },
    {
        label: "状态",
        type: FormType.Select,
        prop: "status",
    },
]

export const tableConfig3 = (position_id: string): TableConfig => ({
    model: sdk.core.model("xg_candidate_order").list("position_order_list"),
    defaultPageSize: 10,
    preFilter: { position_id },
    filter: tableFilter3,
    predict: {
        name: "",
        mobile_encode: "",
        mobile: "",
        sex: "label",
        age: "",
        education: "label",
        create_time: "label",
        created_from: "label",
        status: "label",
        uniplat_uid: "uniplat_uid",
        is_black: "is_agent_black",
    },
    oneTabFilter: true,
})

export const columns3: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "mobile",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "性别",
        prop: "sex_label",
        showOverflowTip: true,
    },
    {
        label: "年龄",
        prop: "age",
        showOverflowTip: true,
    },
    {
        label: "学历",
        prop: "education_label",
        showOverflowTip: true,
    },
    {
        label: "来源",
        prop: "created_from_label",
        showOverflowTip: true,
    },
    // {
    //     label: "投递方式",
    //     prop: "投递方式",
    //     showOverflowTip: true,
    // },
    {
        label: "投递时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "投递状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "280px",
        showOverflowTip: true,
    },
]

export interface Row3 {
    /** 姓名 */
    name: string

    /** 手机号 */
    mobile_encode: string

    /** 性别 */
    sex: string

    /** 年龄 */
    age: string

    /** 学历 */
    education: string

    /** 报名时间 */
    create_time: string

    /** 投递类别 */
    created_from: string

    /** 状态 */
    status: string
    id: number
    v: number
}

export function createOnlineFormConfig(id: string): BuildFormConfig {
    return {
        sdkModel: "xg_company_position",
        sdkAction: "online",
        id,
        forms: [
            {
                label: "招聘过期时间：",
                type: FormType.DatePicker,
                prop: "expired_date",
                option: {
                    pickerOptions: expiateTimePickerOptions,
                    disabled: true,
                },
                // rules: expiateTimeRules,
            },
        ],
    }
}
