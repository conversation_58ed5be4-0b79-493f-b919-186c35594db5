import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const job = [
    {
        path: "/job",
        name: routesMap.home.job,
        meta: {
            title: "求职招聘服务提升",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/job.svg"),
            hidden: isQj,
        },
        component: layout,
        children: [
            {
                path: "/job-deliver",
                redirect: "/job-deliver/job",
                name: routesMap.recruit.jobDeliverIndex,
                meta: {
                    title: "岗位投递管理",
                    // svgIcon: require("@/assets/icon/menu/job-deliver.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "statistics",
                        name: routesMap.recruit.statistics,
                        meta: {
                            title: "招聘数据统计",
                            role: "/redirect/xg_project/hr_statistic",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/statistics/index.vue"
                            ),
                    },
                    {
                        path: "grid-list",
                        name: routesMap.recruit.gridList,
                        meta: {
                            title: "网格推广统计详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.statistics,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/statistics/grid-list.vue"
                            ),
                    },
                    {
                        path: "order-list",
                        name: routesMap.recruit.orderList,
                        meta: {
                            title: "招聘数据统计",
                            hidden: true,
                            parentMenuName: routesMap.recruit.statistics,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/statistics/order-list.vue"
                            ),
                    },
                    {
                        path: "job",
                        name: routesMap.recruit.job,
                        meta: {
                            title: "招聘岗位列表",
                            role: "/tablelist/xg_company_position/manage",
                        },
                        component: () =>
                            import("@/views/pages/recruit/job/index.vue"),
                    },
                    {
                        path: "job-deliver-index",
                        name: routesMap.recruit.jobDeliver,
                        meta: {
                            title: "岗位投递列表",
                            role: "/tablelist/xg_candidate_order/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-deliver/index.vue"
                            ),
                    },
                    {
                        path: "job-deliver-detail",
                        name: routesMap.recruit.jobDeliverDetail,
                        meta: {
                            title: "岗位投递详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.jobDeliver,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-deliver/detail.vue"
                            ),
                    },
                    {
                        path: "job-detail",
                        name: routesMap.recruit.jobDetail,
                        meta: {
                            title: "招聘岗位详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.job,
                        },
                        component: () =>
                            import("@/views/pages/recruit/job/detail.vue"),
                    },
                    {
                        path: "job-edit",
                        name: routesMap.recruit.jobEdit,
                        meta: {
                            title: "岗位编辑",
                            hidden: true,
                            parentMenuName: routesMap.recruit.job,
                        },
                        component: () =>
                            import("@/views/pages/recruit/job/job-edit.vue"),
                    },

                    {
                        path: "/show-big-screen-statistic-job-fair",
                        name: routesMap.bigScreen4JobFair.statisticJobFair4out,
                        meta: {
                            hidden: ![EnvProject.荆州项目].includes(
                                config.envProject
                            ),
                            title: "招聘实时信息大屏",
                            role: "/redirect/xg_project/show-big-screen-statistic-job-fair",
                            newPage: true,
                            targetName:
                                routesMap.bigScreen4JobFair.statisticJobFair,
                        },
                    },
                    {
                        path: "job-contact-detail",
                        name: routesMap.recruit.jobContactDetail,
                        meta: {
                            title: "岗位被联系明细",
                            hidden: true,
                            parentMenuName: routesMap.recruit.jobDetail,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job/contact-detail-page.vue"
                            ),
                    },
                    {
                        path: "job-position-track-detail",
                        name: routesMap.recruit.jobTrackDetail,
                        meta: {
                            title: "岗位跟踪入职明细",
                            hidden: true,
                            parentMenuName: routesMap.recruit.jobDetail,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job/job-track-detail.vue"
                            ),
                    },
                ],
            },

            {
                path: "/public-job",
                redirect: "/public-job/index",
                name: routesMap.recruit.publicJobIndex,
                meta: {
                    title: "公益性岗位",
                    // svgIcon: require("@/assets/icon/menu/publicJob.svg"),
                    hidden:
                        [
                            EnvProject.荆州项目,
                            EnvProject.黄州项目,
                            EnvProject.宜都项目,
                            EnvProject.saas项目,
                        ].includes(config.envProject) && !isDev,
                },
                component: RouteView,
                children: [
                    {
                        path: "index",
                        name: routesMap.recruit.publicJob,
                        meta: {
                            title: "公益性岗位管理",
                            role: "/tablelist/xg_company_position/public_welfare_manage",
                            hidden:
                                [
                                    EnvProject.荆州项目,
                                    EnvProject.黄州项目,
                                ].includes(config.envProject) && !isDev,
                        },
                        component: () =>
                            import("@/views/pages/recruit/job/public-job.vue"),
                    },
                    {
                        path: "public-job-edit",
                        name: routesMap.recruit.publicJobEdit,
                        meta: {
                            title: "公益性岗位编辑",
                            hidden: true,
                            parentMenuName: routesMap.recruit.publicJob,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job/public-job-edit.vue"
                            ),
                    },
                    {
                        path: "public-job-deliver",
                        name: routesMap.recruit.publicJobDeliver,
                        meta: {
                            title: "公益性岗位投递列表",
                            role: "/tablelist/xg_candidate_order/public_welfare_manage",
                            hidden:
                                [
                                    EnvProject.荆州项目,
                                    EnvProject.黄州项目,
                                ].includes(config.envProject) && !isDev,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-deliver/public-job.vue"
                            ),
                    },
                    {
                        path: "public-job-deliver-detail",
                        name: routesMap.recruit.publicJobDeliverDetail,
                        meta: {
                            title: "岗位投递详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.publicJobDeliver,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-deliver/detail.vue"
                            ),
                    },
                    {
                        path: "public-job-detail",
                        name: routesMap.recruit.publicJobDetail,
                        meta: {
                            title: "公益性岗位详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.publicJob,
                        },
                        component: () =>
                            import("@/views/pages/recruit/job/detail.vue"),
                    },
                ],
            },

            {
                path: "/job",
                redirect: "/job/grid",
                name: routesMap.recruit.jobIndex,
                meta: {
                    title: "招聘服务管理",
                    // svgIcon: require("@/assets/icon/menu/job.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "grid",
                        name: routesMap.recruit.grid,
                        meta: {
                            title: "岗位网格推广",
                            role: "/tablelist/xg_company_position_recommend/manage_apply3_list",
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/recruit/grid/index.vue"),
                    },
                    {
                        path: "grid-detail",
                        name: routesMap.recruit.gridDetail,
                        meta: {
                            title: "网格推广申请详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.grid,
                        },
                        component: () =>
                            import("@/views/pages/recruit/grid/detail.vue"),
                    },
                    {
                        path: "group",
                        name: routesMap.recruit.group,
                        meta: {
                            title: "岗位社群推广",
                            role: "/tablelist/xg_company_position_recommend/manage_apply2_list",
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/recruit/group/index.vue"),
                    },
                    {
                        path: "group-detail",
                        name: routesMap.recruit.groupDetail,
                        meta: {
                            title: "社群推广申请详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.group,
                        },
                        component: () =>
                            import("@/views/pages/recruit/group/detail.vue"),
                    },
                    {
                        path: "cooperation",
                        name: routesMap.recruit.cooperation,
                        meta: {
                            title: "机构代招服务",
                            role: "/tablelist/xg_company_position_recommend/manage_apply4_list",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/cooperation/index.vue"
                            ),
                    },
                    {
                        path: "person",
                        name: routesMap.recruit.person,
                        meta: {
                            title: "智能人才推荐",
                            role: "/tablelist/xg_company_position_recommend/manage_apply1_list",
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/recruit/person/index.vue"),
                    },
                    {
                        path: "person-detail",
                        name: routesMap.recruit.personDetail,
                        meta: {
                            title: "人才推荐申请详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.person,
                        },
                        component: () =>
                            import("@/views/pages/recruit/person/detail.vue"),
                    },
                    {
                        path: "personIntelligentRecommendDetail",
                        name: routesMap.recruit
                            .personIntelligentRecommendDetail,
                        meta: {
                            title: "智能人才推荐",
                            hidden: true,
                            parentMenuName: routesMap.recruit.person,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/person/intelligent-recommend/index.vue"
                            ),
                    },
                    {
                        path: "personJobAutoMatch",
                        name: routesMap.recruit.personJobAutoMatch,
                        meta: {
                            title: "智能人岗匹配",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/person-job-auto-match/index.vue"
                            ),
                    },
                    {
                        path: "personJobAutoMatchDetail",
                        name: routesMap.recruit.personJobAutoMatchDetail,
                        meta: {
                            title: "人岗匹配详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.recruit.personJobAutoMatch,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/person-job-auto-match/company-detail.vue"
                            ),
                    },
                    {
                        path: "personJobAutoMatchPositionDetail",
                        name: routesMap.recruit
                            .personJobAutoMatchPositionDetail,
                        meta: {
                            title: "人岗匹配记录",
                            hidden: true,
                            parentMenuName:
                                routesMap.recruit.personJobAutoMatch,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/person-job-auto-match/position/index.vue"
                            ),
                    },
                    {
                        path: "hot-job",
                        name: routesMap.recruit.hotJob,
                        meta: {
                            title: "每日热招岗位",
                            role: "/tablelist/xg_activity",
                            hidden: true, // 0914 隐藏该模块
                        },
                        component: () =>
                            import("@/views/pages/recruit/hot-job/index.vue"),
                    },
                    {
                        path: "hot-job-detail",
                        name: routesMap.recruit.hotJobDetail,
                        meta: {
                            title: "热招岗位详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.hotJob,
                        },
                        component: () =>
                            import("@/views/pages/recruit/hot-job/detail.vue"),
                    },
                    {
                        path: "cooperation-detail",
                        name: routesMap.recruit.cooperationDetail,
                        meta: {
                            title: "人力资源撮合申请详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.cooperation,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/cooperation/detail.vue"
                            ),
                    },
                    {
                        path: "job-statistics-group",
                        name: routesMap.recruit.jobStatisticsGroup,
                        meta: {
                            title: "群内岗位点击统计",
                            hidden: [
                                EnvProject.黄州项目,
                                EnvProject.鄂州项目,
                            ].includes(config.envProject),
                            role: "/xg_project/back_api/fetch_position_share_info",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-statistics-group/index.vue"
                            ),
                    },
                    {
                        path: "DataResourceStatistics",
                        name: routesMap.recruit.DataResourceStatistics,
                        meta: {
                            title: "数据资源统计",
                            hidden: ![EnvProject.荆州项目].includes(
                                config.envProject
                            ),
                            role: "/xg_project/dashboard_api/fetch_business_report_data",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/data-resources-statistics/index.vue"
                            ),
                    },
                ],
            },

            {
                path: "/job-fair",
                redirect: "/job-fair/page",
                name: routesMap.recruit.jobFairIndex,
                meta: {
                    title: "招聘会",
                    // svgIcon: require("@/assets/icon/menu/jobFair.svg"),
                    hidden: [EnvProject.宜都项目].includes(config.envProject),
                },
                component: RouteView,
                children: [
                    {
                        path: "page",
                        name: routesMap.recruit.jobFair,
                        meta: {
                            title: "招聘会管理",
                            role: "/tablelist/job_fair/for_operate",
                        },
                        component: () =>
                            import("@/views/pages/recruit/job-fair/index.vue"),
                    },
                    {
                        path: "job-fair-statics",
                        name: routesMap.recruit.jobFairStatics,
                        meta: {
                            title: "招聘会统计",
                            role: "/xg_project/operate_api/get_job_fair_statistic",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-fair/statics.vue"
                            ),
                    },
                    {
                        path: "specialRecruitment",
                        name: routesMap.recruit.specialRecruitment,
                        meta: {
                            hidden: ![EnvProject.荆州项目].includes(
                                config.envProject
                            ),
                            title: "招聘活动汇总",
                            role: "/xg_project/back_api/get_job_fair_area",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-fair/specialRecruitment/index.vue"
                            ),
                    },
                    {
                        path: "recruitmentActivity",
                        name: routesMap.recruit.recruitmentActivity,
                        meta: {
                            hidden: ![EnvProject.荆州项目].includes(
                                config.envProject
                            ),
                            title: "专场招聘活动统计",
                            role: "/xg_project/back_api/get_job_fair_activity",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-fair/recruitmentActivity/index.vue"
                            ),
                    },

                    {
                        path: "/show-big-screen-job-fair-jz",
                        name: routesMap.bigScreen4JobFair.navJingZhou4out,
                        meta: {
                            hidden: ![EnvProject.荆州项目].includes(
                                config.envProject
                            ),
                            title: "荆州招聘会大屏",
                            role: "/redirect/xg_project/show-big-screen-job-fair-jz",
                            newPage: true,
                            targetName: routesMap.bigScreen4JobFair.navCommon,
                            targetQuery: {
                                type: "2025荆州春风行动",
                                title: "2025年荆州市春风行动启动仪式暨现场招聘会活动",
                            },
                        },
                    },

                    {
                        path: "/show-big-screen-job-fair-honghu",
                        name: routesMap.bigScreen4JobFair.navHonghu4out,
                        meta: {
                            hidden: ![EnvProject.荆州项目].includes(
                                config.envProject
                            ),
                            title: "洪湖招聘会大屏",
                            role: "/redirect/xg_project/show-big-screen-job-fair-honghu",
                            newPage: true,
                            targetName: routesMap.bigScreen4JobFair.navCommon,
                            targetQuery: {
                                type: "2025洪湖春风行动",
                                title: "2025年洪湖春风行动启动仪式暨现场招聘会活动",
                            },
                        },
                    },
                    {
                        path: "job-fair-detail",
                        name: routesMap.recruit.jobFairDetail,
                        meta: {
                            title: "招聘会管理详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.jobFair,
                        },
                        component: () =>
                            import("@/views/pages/recruit/job-fair/detail.vue"),
                    },
                    {
                        path: "add-job-fair",
                        name: routesMap.recruit.addJobFair,
                        meta: {
                            title: "创建招聘会",
                            hidden: true,
                            parentMenuName: routesMap.recruit.jobFair,
                        },
                        component: () =>
                            import("@/views/pages/recruit/job-fair/add.vue"),
                    },
                    {
                        path: "audit-detail",
                        name: routesMap.recruit.auditDetail,
                        meta: {
                            title: "招聘会审核详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.jobFair,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-fair/audit-detail.vue"
                            ),
                    },
                    {
                        path: "agent-audit-detail",
                        name: routesMap.recruit.agentAudit,
                        meta: {
                            title: "审核企业",
                            hidden: true,
                            parentMenuName: routesMap.recruit.agents,
                        },
                        component: () =>
                            import("@/views/pages/recruit/agents/audit.vue"),
                    },
                    {
                        path: "apply-detail",
                        name: routesMap.recruit.applyDetail,
                        meta: {
                            title: "投递详情",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/job-fair/apply-detail.vue"
                            ),
                    },
                    {
                        path: "agents",
                        name: routesMap.recruit.agents,
                        meta: {
                            title: "参会信息审核",
                            hidden: true,
                            role: "/tablelist/job_fair_agent_apply/for_operate",
                        },
                        component: () =>
                            import("@/views/pages/recruit/agents/index.vue"),
                    },
                    {
                        path: "agents-detail",
                        name: routesMap.recruit.agentsDetail,
                        meta: {
                            title: "企业详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.agents,
                        },
                        component: () =>
                            import("@/views/pages/recruit/agents/detail.vue"),
                    },
                    {
                        path: "agents-job-detail",
                        name: routesMap.recruit.agentsJobDetail,
                        meta: {
                            title: "岗位详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.agents,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/agents/job-detail.vue"
                            ),
                    },
                    {
                        path: "area",
                        name: routesMap.recruit.area,
                        meta: {
                            title: "场地管理",
                            role: "/tablelist/activity_area",
                        },
                        component: () =>
                            import("@/views/pages/recruit/area/index.vue"),
                    },
                    {
                        path: "area-detail",
                        name: routesMap.recruit.areaDetail,
                        meta: {
                            title: "场地详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.area,
                        },
                        component: () =>
                            import("@/views/pages/recruit/area/detail.vue"),
                    },
                    {
                        path: "hz-job-fair-bigscreen",
                        name: routesMap.jobStation.hzJobFairBigScreen,
                        meta: {
                            role: "/redirect/xg_project/job-fair-bigscreen",
                            title: "招聘会统计大屏",
                            newPage: true,
                            // 鄂州和黄州已经部署
                            targetPath: `/bigScreen/#/bigscreen/preview?code=bigScreen_tyoVII4KbX`,
                        },
                    },
                ],
            },

            {
                path: "/work-index",
                redirect: "/work-index/work",
                name: routesMap.recruit.workIndex,
                meta: {
                    title: "共享用工",
                    // svgIcon: require("@/assets/icon/menu/work.svg"),
                    hidden: [EnvProject.宜都项目].includes(config.envProject),
                },
                component: RouteView,
                children: [
                    {
                        path: "work",
                        name: routesMap.recruit.work,
                        meta: {
                            title: "共享用工",
                            role: "/tablelist/share_employee_apply/list_for_workbench",
                        },
                        // 荆州使用孝感一样的共享用工逻辑
                        // import("@/views/pages/recruit/work/manage.vue")
                        component: () =>
                            import("@/views/pages/recruit/work/index.vue"),
                    },
                    {
                        path: "work-detail",
                        name: routesMap.recruit.workDetail,
                        meta: {
                            title: "共享用工信息详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.work,
                        },
                        component: () =>
                            import("@/views/pages/recruit/work/detail.vue"),
                    },
                    {
                        path: "work-add",
                        name: routesMap.recruit.workAdd,
                        meta: {
                            title: "发布共享申请审核",
                            hidden: true,
                            parentMenuName: routesMap.recruit.work,
                        },
                        component: () =>
                            import("@/views/pages/recruit/work/add.vue"),
                    },
                ],
            },
            {
                path: "/complaint",
                redirect: "/complaint/manage",
                name: routesMap.recruit.complaintIndex,
                meta: {
                    title: "投诉管理",
                    // svgIcon: require("@/assets/icon/menu/complaint.svg"),
                    hidden: [EnvProject.宜都项目].includes(config.envProject),
                },
                component: RouteView,
                children: [
                    {
                        path: "manage",
                        name: routesMap.recruit.complaint,
                        meta: {
                            title: [EnvProject.荆州项目].includes(
                                config.envProject
                            )
                                ? "有问必答"
                                : "投诉专区",
                            role: "/tablelist/user_complaint/manage",
                        },
                        component: () =>
                            import("@/views/pages/recruit/complaint/index.vue"),
                    },
                    {
                        path: "job-complaint-detail",
                        name: routesMap.recruit.complaintDetail,
                        meta: {
                            title: "投诉详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.complaint,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/complaint/detail.vue"
                            ),
                    },
                    {
                        path: "labour-service-complaint",
                        name: routesMap.recruit.labourServiceComplaint,
                        meta: {
                            title: "劳务用工投诉管理",
                            role: "/tablelist/complaints/manage",
                            hidden: !isXg,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/labour-service-complaint/index.vue"
                            ),
                    },
                    {
                        path: "labour-service-complaint-detail",
                        name: routesMap.recruit.labourServiceComplaintDetail,
                        meta: {
                            title: "投诉详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.recruit.labourServiceComplaint,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/labour-service-complaint/detail.vue"
                            ),
                    },
                ],
            },
            {
                path: "/position-collected",
                redirect: "/position-collected/index",
                name: routesMap.recruit.positionCollectedIndex,
                meta: {
                    title: "岗位筹集",
                    hidden: [EnvProject.咸丰项目].includes(config.envProject),
                    // svgIcon: require("@/assets/icon/menu/positionCollected.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "index",
                        name: routesMap.recruit.positionCollected,
                        meta: {
                            title: "岗位筹集管理",
                            role: "/tablelist/xg_position_collected/manage",
                            hidden: !isHz && !isDev,
                        },
                        component: () =>
                            import("@/views/pages/recruit/collected/index.vue"),
                    },
                    {
                        path: "position-collected-add",
                        name: routesMap.recruit.positionCollectedAdd,
                        meta: {
                            title: "编辑岗位筹集",
                            parentMenuName: routesMap.recruit.positionCollected,
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/recruit/collected/add.vue"),
                    },
                    {
                        path: "position-collected-detail",
                        name: routesMap.recruit.positionCollectedDetail,
                        meta: {
                            title: "岗位筹集详情",
                            parentMenuName: routesMap.recruit.positionCollected,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/collected/detail.vue"
                            ),
                    },
                ],
            },
            // 直播管理
            {
                path: "/live",
                redirect: "/live/index",
                name: routesMap.recruit.live.root,
                meta: {
                    title: "直播管理",
                    // svgIcon: require("@/assets/icon/menu/positionCollected.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "index",
                        name: routesMap.recruit.live.index,
                        meta: {
                            title: "直播管理",
                            role: "/tablelist/live_broadcast/list_operator",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/live/manage/index.vue"
                            ),
                    },
                    {
                        path: "add",
                        name: routesMap.recruit.live.add,
                        meta: {
                            title: "添加直播",
                            hidden: true,
                            parentMenuName: routesMap.recruit.live.index,
                        },
                        component: () =>
                            import("@/views/pages/recruit/live/manage/add.vue"),
                    },
                    {
                        path: "detail",
                        name: routesMap.recruit.live.detail,
                        meta: {
                            title: "直播管理详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.live.index,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/live/manage/detail.vue"
                            ),
                    },
                    {
                        path: "video-index",
                        name: routesMap.recruit.live.video,
                        meta: {
                            title: "短视频管理",
                            role: "/tablelist/video_broadcast/list_operator",
                            hidden: [EnvProject.咸丰项目].includes(
                                config.envProject
                            ),
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/live/video/index.vue"
                            ),
                    },
                    {
                        path: "video-add",
                        name: routesMap.recruit.live.videoAdd,
                        meta: {
                            title: "添加短视频",
                            hidden: true,
                            parentMenuName: routesMap.recruit.live.video,
                        },
                        component: () =>
                            import("@/views/pages/recruit/live/video/add.vue"),
                    },
                    {
                        path: "video-detail",
                        name: routesMap.recruit.live.videoDetail,
                        meta: {
                            title: "短视频管理详情",
                            hidden: true,
                            parentMenuName: routesMap.recruit.live.video,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit/live/video/detail.vue"
                            ),
                    },
                ],
            },
            {
                path: "/recruit-company",
                redirect: "recruit-company/index",
                name: routesMap.recruitCompany.root,
                meta: {
                    title: "区域企业招聘需求管理",
                    // svgIcon: require("@/assets/icon/menu/recruit-company.svg"),
                    hidden: ![
                        EnvProject.荆州项目,
                        EnvProject.saas项目,
                        EnvProject.黄州项目,
                    ].includes(config.envProject),
                },
                component: RouteView,
                children: [
                    {
                        path: "index",
                        name: routesMap.recruitCompany.index,
                        meta: {
                            title: "企业招聘需求监测",
                            role: "/tablelist/tg_enterprise_task/manage_list",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/index/index.vue"
                            ),
                    },
                    {
                        path: "company-list",
                        name: routesMap.recruitCompany.companyList,
                        meta: {
                            title: "企业列表",
                            parentMenuName: routesMap.recruitCompany.index,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/index/company-list.vue"
                            ),
                    },
                    {
                        path: "company-detail",
                        name: routesMap.recruitCompany.companyDetail,
                        meta: {
                            title: "企业详情",
                            parentMenuName:
                                routesMap.recruitCompany.companyIndex,
                            hidden: true,
                            // role: "/tablelist/collect_user_profile/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/index/company-detail.vue"
                            ),
                    },
                    {
                        path: "job-list",
                        name: routesMap.recruitCompany.jobList,
                        meta: {
                            title: "岗位列表",
                            parentMenuName: routesMap.recruitCompany.index,
                            hidden: true,
                            // role: "/tablelist/collect_user_profile/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/index/job-list.vue"
                            ),
                    },
                    {
                        path: "company-index",
                        name: routesMap.recruitCompany.companyIndex,
                        meta: {
                            title: "企业信息管理",
                            role: "/tablelist/tg_enterprise_follow/follow_back_list",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/company/index.vue"
                            ),
                    },
                    {
                        path: "company-search",
                        name: routesMap.recruitCompany.companySearch,
                        meta: {
                            title: "企业信息查询",
                            parentMenuName:
                                routesMap.recruitCompany.companyIndex,
                            hidden: true,
                            // role: "/tablelist/collect_user_profile/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/company/search.vue"
                            ),
                    },
                    {
                        path: "manage",
                        name: routesMap.recruitCompany.manage,
                        meta: {
                            title: "企业开发项目管理",
                            role: "/tablelist/tg_enterprise_task/back_list",
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/manage/index.vue"
                            ),
                    },
                    {
                        path: "manage-detail",
                        name: routesMap.recruitCompany.manageDetail,
                        meta: {
                            title: "企业开发详情",
                            parentMenuName: routesMap.recruitCompany.manage,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/manage/detail.vue"
                            ),
                    },
                    {
                        path: "manage-add",
                        name: routesMap.recruitCompany.manageAdd,
                        meta: {
                            title: "创建项目",
                            parentMenuName: routesMap.recruitCompany.manage,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/recruit-company/manage/add.vue"
                            ),
                    },
                ],
            },
        ],
    },
]
