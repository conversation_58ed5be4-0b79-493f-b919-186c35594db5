<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :showExpand="true"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-x-20 bg-white">
                <div slot="header-right">
                    <div class="u-flex u-row-right u-m-b-15 u-p-t-15">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="showAddCompany = true"
                            >新增企业</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            @click="downloadPack"
                            >打包下载宣传码</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            plain
                            @click="createCode()"
                            >生成宣传码</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            plain
                            @click="
                                exportExcelUniplatV2({
                                    template_name: '参会企业导出',
                                })
                            "
                            >导出</el-button
                        >
                    </div>
                </div>
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="position_count_label" slot-scope="scope">
                        <span v-if="!scope.row.position_count_label"></span>
                        <el-button
                            v-else
                            type="text"
                            @click="toTab3(scope.row)"
                        >
                            {{ scope.row.position_count_label }}
                        </el-button>
                    </div>
                    <div slot="wx_qr_share_image" slot-scope="scope">
                        <div
                            class="u-flex justify-content-center"
                            v-if="scope.row.wx_qr_share_image"
                        >
                            <el-image
                                v-if="scope.row.wx_qr_share_image"
                                :src="handleImg(scope.row.wx_qr_share_image)"
                                :preview-src-list="[
                                    handleImg(scope.row.wx_qr_share_image),
                                ]"
                                class="img logo"
                            ></el-image>
                            <el-button
                                type="text"
                                class="u-m-l-10"
                                @click="
                                    onDownload(
                                        scope.row.wx_qr_share_image,
                                        scope.row.agent_name
                                    )
                                "
                                >下载
                            </el-button>
                        </div>
                        <div
                            v-else
                            class="primary pointer"
                            @click="createCode(scope.row.id)"
                        >
                            生成宣传码
                        </div>
                    </div>
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <!-- <el-button
                            v-if="
                                scope.row.audit_status == auditStatus.审核中 ||
                                (scope.row.audit_status ==
                                    auditStatus.审核通过 &&
                                    scope.row.position_wait_count)
                            "
                            type="text"
                            @click="toAudit(scope.row.id)"
                        >
                            审核
                        </el-button> -->
                    </div>
                </common-table>
            </div>
        </table-container>
        <AddCompany v-model="showAddCompany" @refresh="reloadList"></AddCompany>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { Component, Prop } from "vue-property-decorator"
    import { Row } from ".."
    import { columns2, tableConfig2 } from "./index"
    import { sdk } from "@/service"
    import { downLoadFile } from "@/utils/tools"
    import { map } from "lodash"
    import { pageLoading } from "@/views/controller"
    import { routesMap } from "@/router/direction"
    import AddCompany from "../components/tab-2/add-company.vue"

    export enum AuditStatus {
        审核中 = 0,
        审核通过 = 1,
        审核不通过 = 2,
    }

    @Component({ components: { TableContainer, CommonTable, AddCompany } })
    export default class Tab2 extends BaseTableController<{ id: number }> {
        @Prop()
        row2!: Row

        private tableConfig: TableConfig | null = null
        private columns = columns2
        private auditStatus = AuditStatus

        private showAddCompany = false

        mounted() {
            this.tableConfig = tableConfig2(this.row2.id + "")
        }

        private toDetail(row?: any) {
            this.$router.push({
                name: routesMap.recruit.agentsDetail,
                query: {
                    id: row._access_key || row.id,
                    from: routesMap.recruit.jobFairDetail,
                    jobFairId: this.$route.query.id,
                },
            })
        }

        private toTab3(row: any) {
            this.$emit("changeTab3Filter", row.agent_name)
        }

        private handleImg(wx_qr: string) {
            return sdk.buildImage(wx_qr || "")
        }

        private onDownload(wx_qr: string, agent_name: string) {
            downLoadFile(
                sdk.buildImage(wx_qr || ""),
                `${agent_name}-企业宣传码.png`
            )
        }

        private downloadPack() {
            pageLoading(() => {
                return sdk
                    .getDomainService(
                        "job_fair_qr_code_zip",
                        "anonymous/agent_api",
                        "xg_project"
                    )
                    .post({
                        job_fair_id: this.row2.id,
                    })
                    .then((r: any) => {
                        window.open(r.file_url, "_blank")
                    })
            })
        }

        private ids: string[] = []

        private createCode(id?: string) {
            let arr: string[] = []
            if (id) {
                arr = [id]
            } else {
                arr = [...this.ids]
            }
            if (!arr.length) {
                return this.$message.warning("请选择要生成二维码的企业")
            }
            pageLoading(() => {
                const model = sdk.core
                    .model("job_fair_agent_apply")
                    .action("batch_update_qr_code_playbill")
                const selected_list = map(arr, (e) => ({ v: 0, id: e }))
                return model
                    .updateInitialParams({
                        selected_list,
                        prefilters: [
                            {
                                property: "job_fair_id",
                                value: this.row2.id,
                            },
                        ],
                    })
                    .addInputs_parameter({})
                    .query()
                    .then(() => {
                        model
                            .executeEach({
                                inputs_parameters: [],
                                selected_list,
                            } as any)(
                                () => {},
                                (errorRow) => {
                                    this.$message.error(
                                        `${errorRow.map(
                                            (i) => `id:${i.id}: ${i.error}`
                                        )}`
                                    )
                                }
                            )
                            .awaiting.then(() => {
                                this.$message.success("提交成功")
                                this.refreshList()
                            })
                    })
            })
        }

        private handleSelectionChange(e: { ids: string[] }) {
            this.ids = e.ids
        }

        private toAudit(id: string) {
            this.$router.push({
                name: routesMap.recruit.agentAudit,
                query: {
                    id,
                    from: routesMap.recruit.jobFairDetail,
                    rowId: this.row2!.id + "",
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    ::v-deep .table-tabs {
        padding-top: 10px;
    }

    .logo {
        height: 25px;
    }
    ::v-deep .table {
        background: transparent !important;
    }
</style>
