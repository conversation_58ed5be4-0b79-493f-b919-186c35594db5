<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="1400px"
        custom-class="dialog-body"
    >
        <div class="u-p-x-20" v-if="row">
            <RecommendList
                :key="row.id"
                :policyRow="row"
            />
            <div class="u-flex u-row-center u-m-t-20 u-p-b-20">
                <el-button
                    @click="close"
                    class="btn custom-btn u-m-r-30"
                    type="primary"
                    plain
                >
                    关闭
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Component, Prop } from "vue-property-decorator"
    import { Row } from ".."
    import RecommendList from "./list.vue"

    @Component({ components: { RecommendList } })
    export default class RecommendPop extends DialogController {
        private title = "政策找人"

        @Prop({ default: null })
        private row!: Row
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .btn {
        width: 100px;
        height: 36px;
    }
</style>
