<template>
    <div>
        <baidu-map
            v-if="showMap"
            class="map"
            :center="config.center"
            :zoom="config.zoom"
            :ak="key"
            @ready="mapReady"
        >
            <bm-control style="width: 400px" class="bm-control">
                <container class="container-title" :title="title"> </container>
                <div class="u-flex search-input">
                    <el-autocomplete
                        class="u-flex-1"
                        v-model="keyword"
                        :fetch-suggestions="querySearch"
                        @select="handleSelect"
                        placeholder="请输入搜索关键字"
                    ></el-autocomplete>
                    <img :src="baseImgUrl + '/img/xiaogan/map/search.png'" />
                </div>
                <el-scrollbar
                    v-if="keyword && searchList.length"
                    class="scrollbar overflow-x-hidden"
                >
                    <div class="u-p-10 u-p-r-15">
                        <div
                            class="address-item"
                            @click="clickListItem(item)"
                            v-for="item in searchList"
                            :key="item.uid"
                            :class="{
                                active:
                                    item.point.lat === myValue.lat &&
                                    item.point.lng === myValue.lng,
                            }"
                        >
                            {{ item.title }}
                            <div class="detail">
                                {{ item.address }}
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
            </bm-control>
            <!-- <bm-marker
                v-if="myValue.lng && myValue.lat"
                :position="{ lng: myValue.lng, lat: myValue.lat }"
                :dragging="false"
                animation="BMAP_ANIMATION_BOUNCE"
            >
            </bm-marker> -->
            <!-- 地图点 -->
            <bm-marker
                v-for="e in curTypePoints"
                :key="e.id"
                :position="{ lng: e.lng, lat: e.lat }"
                class="custom-marker"
                :icon="getIcon(e.id, e)"
                :offset="{ width: 0, height: curInfoIndex === e.id ? -55 : 0 }"
                @click="infoWindowOpen(e.id)"
                :zIndex="+e.id"
            >
            </bm-marker>
            <!-- 地图控件位置 -->
            <!-- <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT" /> -->
            <!-- 地图容器 -->
            <bm-view
                v-loading="loading"
                :style="{
                    width: '100%',
                    height: config.clientHeight + 'px',
                    flex: 1,
                }"
            />
        </baidu-map>
        <!-- <el-dialog
            :visible.sync="showInfoWindow"
            @close="infoWindowClose"
            :modal="false"
            :show-close="false"
            top="-100vh"
        > -->
        <div
            v-show="showInfoWindow"
            class="info-window u-flex u-col-top"
            v-if="curInfo"
            @click="clickInfoWindow"
        >
            <div class="close" @click.stop="infoWindowClose">
                <i class="el-icon-close color-8 pointer"></i>
            </div>
            <div class="u-m-r-30">
                <img width="100px" height="100px" :src="iconSrc" />
            </div>
            <div class="u-flex-1">
                <div class="u-flex u-row-between mb17 u-col-top">
                    <div class="title">{{ curInfo.name }}</div>
                    <div
                        class="tag u-flex-none"
                        :class="[`tag${curInfo.type}`]"
                    >
                        {{ curInfo.type_label }}
                    </div>
                </div>
                <div class="u-flex mb17">
                    <img
                        width="20px"
                        height="20px"
                        :src="baseImgUrl + '/img/xiaogan/map/mobile.png'"
                        class="mr20"
                    />
                    <div>
                        {{ curInfo.contact_person }}
                        {{ curInfo.mobile }}
                    </div>
                </div>
                <div class="u-flex mb17">
                    <img
                        width="20px"
                        height="20px"
                        :src="baseImgUrl + '/img/xiaogan/map/address.png'"
                        class="mr20"
                    />
                    <div>{{ curInfo.address || "--" }}</div>
                </div>
                <div class="text">
                    {{ curInfo.remark }}
                </div>
            </div>
        </div>
        <!-- </el-dialog> -->
        <div class="toggle">
            <div
                class="toggle-button u-flex u-row-center"
                @click="$emit('changeCur', 1)"
            >
                <img
                    :src="baseImgUrl + '/img/xiaogan/map/list.png'"
                    width="28px"
                    height="28px"
                />
                <div class="text">列表</div>
            </div>
            <div class="map-list color-8">
                <div class="top">网点分类</div>
                <div class="content" :class="{ show: showToggle }">
                    <div
                        v-for="item in mapList"
                        :key="item.label"
                        @click="curTypes[item.type] = !curTypes[item.type]"
                        class="item"
                    >
                        <img
                            :src="
                                baseImgUrl +
                                `/img/xiaogan/map/${item.icon}-${
                                    curTypes[item.type] ? 1 : 2
                                }.png`
                            "
                            class="img"
                        />
                        <div>{{ item.label }}</div>
                    </div>
                </div>
                <div class="bottom" @click="showToggle = !showToggle">
                    <i
                        class="el-icon-arrow-down"
                        :class="{ rotated: showToggle }"
                    ></i>
                </div>
            </div>
        </div>
        <div class="init-button" @click="resetMap">
            <img :src="baseImgUrl + '/img/xiaogan/map/init.png'" />
        </div>
    </div>
</template>

<script lang='ts'>
    import { Component, Vue, Watch } from "vue-property-decorator"
    import {
        BaiduMap,
        BmAutoComplete,
        BmControl,
        BmMarker,
        BmView,
        BmNavigation,
        BmLocalSearch,
        BmPolygon,
        BmOverlay,
        BmInfoWindow,
    } from "vue-baidu-map"
    import { debounce, find, get, isNull } from "lodash"
    import { get_area_info, MapValue } from "@/plugins/baidu-map-selector/index"
    import { sdk } from "@/service"
    import { getName, getPoints, MapType, showCode } from "."
    import { sleep } from "@/utils"
    import Container from "./common/container.vue"
    import { config } from "@/config"

    interface searchData {
        address: string
        city: string
        province: string
        title: string
        detailUrl: string
        isAccurate: boolean
        phoneNumber: string
        uid: searchData
        point: {
            lat: number
            lng: number
        }
    }

    @Component({
        components: {
            BaiduMap,
            BmView,
            BmControl,
            BmAutoComplete,
            BmMarker,
            BmNavigation,
            BmLocalSearch,
            BmPolygon,
            BmOverlay,
            BmInfoWindow,
            Container,
        },
    })
    export default class Template extends Vue {
        readonly key = process.env.VUE_APP_BAIDU_KEY
        location: string = process.env.VUE_APP_BM_AUTO_COMPLETE_LOCATION || "孝感市"
        private propsValue: any = {}
        config = {
            clientHeight: 600,
            // 天安门经纬度
            center: { lng: "116.403765", lat: "39.91485" } as {
                lng: string | number
                lat: string | number
            },
            zoom: 17,
        }

        curType = MapType.零工驿站
        private curTypes = {
            [MapType.培训机构]: true,
            [MapType.零工驿站]: true,
            [MapType.孵化基地]: true,
            [MapType.社保服务]: true,
        }

        private showToggle = true
        private baseImgUrl =
            process.env.BASE_URL === "/" ? "" : process.env.BASE_URL

        BMap: any
        map: any
        geocoder: any
        local: any
        searchList: searchData[] = []
        keyword = ""

        private showMap = false
        loading = false
        myValue: MapValue = {
            address_detail: "",
            lng: "",
            lat: "",
        }

        private get title() {
            return `${config.citySampleName}公共就业服务地图`
        }

        private get mapList() {
            return [
                {
                    label: "培训机构",
                    type: MapType.培训机构,
                },
                {
                    label: "零工驿站",
                    type: MapType.零工驿站,
                },
                {
                    label: "孵化基地",
                    type: MapType.孵化基地,
                },
                {
                    label: "社保服务",
                    type: MapType.社保服务,
                },
            ].map((e) => {
                return { ...e, icon: getName(e.type) }
            })
        }

        mounted() {
            this.init()
        }

        @Watch("curType", { immediate: true })
        onCurTypeChange(v: MapType) {
            // this.loading = true
            getPoints({}).then((res) => {
                this.points = res
                this.loading = false
            })
        }

        private get iconSrc() {
            return `${process.env.BASE_URL || ""}/img/xiaogan/map/${getName(
                this.curInfo.type
            )}-icon.png`.replace("//", "/")
        }

        private async init() {
            this.showMap = false
            this.$nextTick(() => {
                this.showMap = true
            })
            if (!this.key) {
                throw new Error("请先配置百度key")
            }
            this.config.clientHeight = document.body.clientHeight
            this.myValue.address_detail = this.propsValue.address_detail
            // this.inputText
            if (this.propsValue.lng) {
                this.myValue.lng = this.propsValue.lng
                this.config.center.lng = this.propsValue.lng
            }
            if (this.propsValue.lat) {
                this.myValue.lat = this.propsValue.lat
                this.config.center.lat = this.propsValue.lat
            }

            // this.region_code = get(this.formCompData, this.regionKey, "")
            if (
                !this.myValue.lat &&
                !this.myValue.lng &&
                (this.myValue.address_detail || this.location)
            ) {
                await this.setPointToMapWithAddress(
                    this.myValue.address_detail || this.location
                )
            }

            if (!this.region_code && this.myValue.lat && this.myValue.lng) {
                await this.setRegionCode()
                // assign(this.formCompData, { region_code: this.region_code })
            }
        }

        search() {
            // this.local?.search(this.keyword)
        }

        searchDebounce = debounce(this.search, 800)

        private locationData: any = {}

        private resetMap() {
            this.setCurrentPoint(this.locationData, true)
            this.config.zoom = 17
            this.init()
        }

        setPointToMapWithAddress(address: string) {
            return sdk.core
                .domainService(
                    "xg_project",
                    "anonymous/client_api",
                    "get_lbs_by_word"
                )
                .get<{
                    result: {
                        location: {
                            lat: number
                            lng: number
                        }
                    }
                }>({ address })
                .then(async (res) => {
                    console.log("res", JSON.parse(JSON.stringify(res)))
                    const location = res.result.location
                    this.locationData = location
                    this.myValue.lat = location.lat + ""
                    this.myValue.lng = location.lng + ""
                    this.config.center.lat = location.lat + ""
                    this.config.center.lng = location.lng + ""
                })
        }

        mapReady({ BMap, map }: any) {
            this.BMap = BMap
            this.map = map
            this.local = new BMap.LocalSearch(map, {
                onSearchComplete: this.onSearchComplete,
            })
            this.geocoder = new BMap.Geocoder()

            map.addEventListener("click", ({ point }: any) => {
                this.geocoder.getLocation(point, (res: any) => {
                    this.keyword = ""
                    const v = res.addressComponents
                    const surroundingPois = get(res, "surroundingPois[0]")
                    if (surroundingPois) {
                        this.setCurrentPoint(
                            {
                                address_detail: surroundingPois.title,
                                lng: point.lng,
                                lat: point.lat,
                            },
                            false
                        )
                    } else {
                        this.setCurrentPoint(
                            {
                                address_detail: `${v.district}${v.street}${v.streetNumber}`,
                                lng: point.lng,
                                lat: point.lat,
                            },
                            false
                        )
                    }
                })
            })
        }

        private drawPoint({ el, BMap, map }: any, e: any) {
            const pixel = map.pointToOverlayPixel(new BMap.Point(e.lng, e.lat))
            el.style.left = pixel.x - 60 + "px"
            el.style.top = pixel.y - 20 + "px"
        }

        private points: any = []

        private get curTypePoints() {
            return this.points.filter((e: any) => (this.curTypes as any)[e.type])
        }

        onSearchComplete(results: { as: searchData[] }) {
            if (results?.as) {
                this.searchList = results.as
            } else {
                this.searchList = []
            }
            if (this.searchList.length) {
                this.clickListItem(this.searchList[0])
            }
        }

        clickListItem(e: searchData) {
            this.setCurrentPoint({
                address_detail: e.title,
                lng: e.point.lng,
                lat: e.point.lat,
            })
        }

        setCurrentPoint(
            e: {
                address_detail: string
                lng: string | number
                lat: string | number
            },
            changeCenter = true
        ) {
            this.infoWindowClose()
            this.myValue = e
            // this.setRegionCode()
            if (changeCenter) {
                this.config.center.lat = e.lat + ""
                this.config.center.lng = e.lng + ""
            }
        }

        private clickInfoWindow() {
            this.$createElement()
            showCode(this.curInfo, this)
        }

        region_code = ""

        setRegionCode() {
            return get_area_info({
                lng: this.myValue.lng + "",
                lat: this.myValue.lat + "",
            }).then((res) => {
                const address = res.address
                this.region_code = [
                    address.province.id,
                    address.city.id,
                    address.area.id,
                ]
                    .filter(Boolean)
                    .join(",")
            })
        }

        private getIcon(index: number, e: any) {
            const isCur = this.curInfoIndex === index
            const size = isCur
                ? new this.BMap.Size(100, 115)
                : new this.BMap.Size(60, 60)
            const icon = {
                url: isCur ? e.icon2 : e.icon,
                opts: {
                    imageSize: size,
                },
            }
            return { ...icon, size }
        }

        private showInfoWindow = false
        private curInfoIndex = null
        private lock = false

        private get curInfo() {
            return find(this.points, (e) => e.id === this.curInfoIndex) || null
        }

        private async infoWindowOpen(e: any) {
            await sleep(10)
            this.curInfoIndex = null
            this.lock = true
            this.$nextTick(() => {
                this.curInfoIndex = e
                this.showInfoWindow = true
            })
            sleep(200).then(() => {
                this.lock = false
            })
        }

        private infoWindowClose() {
            if (this.lock) return
            this.curInfoIndex = null
            this.showInfoWindow = false
        }

        querySearch(v: string, cb: any) {
            cb(
                this.points
                    .filter((e: any) => e.name.includes(v))
                    .map((e: any) => {
                        return {
                            value: e.name,
                            id: e.id,
                            ...e,
                        }
                    })
            )
        }

        handleSelect(point: any) {
            this.changeCurType(point.type)
            this.setCurrentPoint(point, true)
            this.curInfoIndex = point.id
            this.showInfoWindow = true
        }

        changeCurType(v: number) {
            this.curType = v
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .map {
        position: relative;
        z-index: 9;
    }
    .container-title {
        position: fixed;
        z-index: 9;
    }
    .bm-control {
        .search-input {
            top: 40px;
            position: fixed;
            z-index: 9;
            margin-left: 20px;
            width: 360px;
            padding: 5px;
            font-size: 20px;
            height: 60px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);
            /deep/.el-input__inner {
                border: none;
            }
            img {
                width: 50px;
                height: 50px;
                cursor: pointer;
            }
        }
    }
    /deep/ .el-dialog__header {
        display: none;
    }
    /deep/ .el-dialog__body {
        background: transparent;
    }
    .toggle {
        position: absolute;
        top: 40px;
        right: 30px;
        width: 100px;
        z-index: 9;
        .toggle-button {
            width: 100px;
            line-height: 60px;
            background: #1770e5;
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);
            color: #fff;
            font-size: 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            .text {
                margin-left: 8px;
            }
        }
        .map-list {
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            background-color: #fff;
            padding: 0 8px;
            text-align: center;
            user-select: none;
            .top {
                line-height: 60px;
                border-bottom: 1px solid #e6e6e6;
            }
            .content {
                height: 0;
                transition: all 0.3s;
                background-color: #fff;
                font-size: 20px;
                line-height: 20px;
                overflow: hidden;
                .item {
                    padding-bottom: 25px;
                    border-bottom: 1px solid #e6e6e6;
                }
                &.show {
                    height: 450px;
                }
                .img {
                    height: 40px;
                    width: 40px;
                    margin-top: 19px;
                    margin-bottom: 9px;
                }
            }
            .bottom {
                height: 30px;
                color: #1770e5;
                font-size: 30px;
                cursor: pointer;
                i {
                    transition: all 0.3s;
                }
                .rotated {
                    transform: rotate(-180deg);
                }
            }
        }
    }
    .info-window {
        width: 820px;
        min-height: 250px;
        position: fixed;
        left: 0;
        right: 0;
        margin: auto;
        bottom: 30px;
        background: #ffffff;
        box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        z-index: 99999999;
        padding: 30px;
        padding-bottom: 24px;
        font-size: 16px;
        color: #666666;
        line-height: 1;
        .close {
            font-size: 20px;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .mb17 {
            margin-bottom: 17px;
        }
        .mr20 {
            margin-right: 20px;
        }
        .title {
            color: #053b91;
            font-size: 26px;
            font-weight: 600;
        }
        .tag {
            padding: 0 8px;
            line-height: 26px;
            font-size: 16px;
            &.tag1 {
                background: #ffeed4;
                border-radius: 4px;
                color: #e17000;
            }
            &.tag2 {
                background: #e4f0ff;
                border-radius: 4px;
                color: #1770e5;
            }
            &.tag3 {
                background: #e0f8ee;
                border-radius: 4px;
                color: #00a25c;
            }
        }
        .text {
            line-height: 26px;
        }
    }
    .scrollbar {
        background-color: #fff;
        height: 400px;
        border-radius: 8px;
        .address-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            line-height: 1.5;
            .detail {
                font-size: 12px;
                margin-top: 4px;
                border-top: 1px solid #eee;
                padding-top: 4px;
            }
            &.active {
                background-color: var(--primary);
                color: #fff;
            }
        }
    }
    .init-button {
        position: fixed;
        z-index: 999999;
        bottom: 30px;
        right: 30px;
        background-color: #fff;
        cursor: pointer;
        width: 60px;
        height: 60px;
        background: #ffffff;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        text-align: center;
        padding: 15px 0;
        img {
            width: 30px;
            height: 30px;
        }
    }
</style>
<style>
    .tangram-suggestion-main {
        z-index: 99999999999999999999999;
    }
</style>
