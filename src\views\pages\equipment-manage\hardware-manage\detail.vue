<template>
    <div class="core-ui-table-container" :key="refreshQueryParams" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    type="primary"
                    @click="changeStatus"
                    v-if="!hideAction"
                >
                    {{ actionLabel() }}
                </el-button>
                <el-button type="primary" plain @click="toEdit">
                    编辑
                </el-button>
                <el-button
                    type="primary"
                    plain
                    @click="toDelete"
                    v-if="!hideAction"
                >
                    删除
                </el-button>
            </div>
        </div>
        <!-- <div class="bg-white u-p-x-40 u-p-y-10 content" v-if="row">
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
            ></detail-row-col>
            <div class="u-flex">
                <div class="label bold">
                    {{ row.p_name }}
                </div>
                <div
                    class="primary pointer u-m-l-10"
                    @click="open"
                    v-if="row.task_type === 2"
                >
                    问卷详情
                </div>
            </div>
        </div> -->
        <div class="bg-white u-p-20 content" v-if="row">
            <div class="title u-m-b-10">基本信息</div>
            <div class="u-p-x-20">
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="items2"
                ></detail-row-col>
            </div>
            <!-- <div class="title u-m-b-10 u-m-t-10">服务操作记录</div>
            <list-view :id="id"></list-view> -->
        </div>
        <div class="u-m-t-24 common-table">
            <div class="core-ui-table-container">
                <el-tabs>
                    <el-tab-pane label="信息发布" v-if="!hideAction">
                        <list-view :detailId="rowId"></list-view>
                    </el-tab-pane>
                    <el-tab-pane lazy label="业务信息" v-if="isJyxz">
                        <list-view2
                            :detailId="rowId"
                            :detailRow="row"
                        ></list-view2>
                    </el-tab-pane>
                    <el-tab-pane lazy label="设备日志" v-if="!hideAction">
                        <list-view3 :device_sn="row.device_sn"></list-view3>
                    </el-tab-pane>
                    <el-tab-pane lazy label="在线时间段" v-else>
                        <list-view4 :detailRow="row"></list-view4>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <EditPop v-model="showEditPop" :id="rowId" @refresh="init" />
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { formatTime } from "@/utils/tools"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import {
        updateTagItem,
        getCacheBreadcrumbsByRoutePath,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"
    import { DeviceType, predict, Row } from "./detail"
    import ListView from "./components/list-view.vue"
    import ListView2 from "./components/list-view-2.vue"
    import ListView3 from "./components/list-view-3.vue"
    import ListView4 from "./components/list-view-4.vue"
    import { MessageBox } from "element-ui"
    import EditPop from "./components/edit-pop.vue"
    import { Status } from "./index"

    @Component({
        name: routesMap.equipmentManage.hardwareManageDetail,
        components: {
            DetailRowCol,
            ListView,
            ListView2,
            ListView3,
            EditPop,
            ListView4,
        },
    })
    export default class Detail extends Vue {
        private row: Row | null = null
        private id = ""
        private get rowId() {
            return this.row?.id || ""
        }

        private get isJyxz() {
            return this.row?.device_type === DeviceType.就业小知
        }

        private get hideAction() {
            return (
                this.isJyxz ||
                this.row?.device_type === DeviceType.智能移动公共服务工作站
            )
        }

        private showEditPop = false

        refreshConfig = {
            fun: this.init,
            name: routesMap.equipmentManage.hardwareManageDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            // let d: BreadcrumbItem[] = [
            //     {
            //         label: `硬件设备管理`,
            //         to: {
            //             name: routesMap.equipmentManage.hardwareManage,
            //         },
            //     },
            // ]
            // d = [
            //     ...d,
            //     {
            //         label: "设备详情",
            //         to: {
            //             name: routesMap.equipmentManage.hardwareManageDetail,
            //             query: {
            //                 id: this.id,
            //             },
            //         },
            //     },
            // ]
            const d = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: "设备详情",
                    to: {
                        name: routesMap.equipmentManage.hardwareManageDetail,
                        query: {
                            id: this.id,
                            from: this.from,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.equipmentManage.hardwareManageDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string
        }

        mounted() {
            return this.init()
        }

        private actionLabel() {
            return this.row?.status === Status.启用 ? "停用" : "启用"
        }

        private init() {
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("device_base_info")
                    .detail(this.id, "for_operate")
                    .query()
                    .then((r) => (this.row = sdk.buildRow(r.row, predict)))
            })
        }

        private labelStyle = {
            width: "110px",
            marginRight: "10px",
            lineHeight: "34px",
        }

        private get items(): ColItem[] {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "任务名称：",
                    vNode: h("div", {}, [h("span", this.row.job_fair_title)]),
                    span: 24,
                },
                {
                    label: "服务内容：",
                    value: this.row.content,
                    span: 24,
                },
            ]
        }

        private get items2(): ColItem[] {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "设备名称",
                    value: this.row.device_name,
                },
                {
                    label: "设备激活码",
                    value: this.row.device_sn,
                },
                {
                    label: "设备品牌",
                    value: this.row.brand,
                },
                {
                    label: "设备类型",
                    value: this.row.device_type_label,
                },
                {
                    label: "设备型号",
                    value: this.row.version,
                },
                {
                    label: "使用状态",
                    vNode: h(
                        "span",
                        { class: `status status-${this.row.status}` },
                        this.row.status_label
                    ),
                },
                {
                    label: "所在区域",
                    value: this.row.region_name,
                },
                {
                    label: "系统版本号",
                    value: this.row.device_open_id,
                    hide: !this.hideAction,
                },
                {
                    label: "在线天数",
                    value: this.row.onlineDay || 0,
                },
                {
                    label: "更新时间",
                    vNode: h("span", {}, formatTime.seconds(this.row.update_time)),
                },
                {
                    label: "在线状态",
                    vNode: h(
                        "span",
                        { class: `status status-${this.row.status_now}` },
                        this.row.status_now_label
                    ),
                },
                {
                    label: "最后在线时间",
                    vNode: h("span", {}, formatTime.seconds(this.row.online_time)),
                },
                {
                    label: "激活时间",
                    vNode: h("span", {}, formatTime.seconds(this.row.active_time)),
                },
                {
                    label: "设备备注",
                    value: this.row.device_remark || "--",
                    span: 24,
                },
            ].map((e) => ({ ...e, span: e.span ?? 8, label: e.label + "：" }))
        }

        private toEdit() {
            this.showEditPop = true
        }

        changeStatus() {
            MessageBox.confirm(`请确认${this.actionLabel()}？`, "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        const actionName =
                            this.row?.status === Status.启用 ? "disable" : "enable"
                        instance.confirmButtonLoading = true
                        sdk.core
                            .model("device_base_info")
                            .action(actionName)
                            .addInputs_parameter({
                                status: this.row?.status === 0 ? "1" : "0",
                            })
                            .updateInitialParams({
                                selected_list: [{ id: this.rowId, v: 0 }],
                            })
                            .execute()
                            .then(() => {
                                done()
                                this.init()
                            })
                            .finally(() => {
                                instance.confirmButtonLoading = false
                            })
                    }
                    done()
                },
            })
        }

        toDelete() {
            MessageBox.confirm(`请确认删除${this.row?.device_name}？`, "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        instance.confirmButtonLoading = true
                        sdk.core
                            .model("device_base_info")
                            .action("delete_device")
                            .updateInitialParams({
                                selected_list: [{ id: this.rowId, v: 0 }],
                            })
                            .execute()
                            .then(() => {
                                done()
                                this.init()
                            })
                            .finally(() => {
                                instance.confirmButtonLoading = false
                            })
                    } else {
                        done()
                    }
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
    .label {
        line-height: 34px;
    }
    .content {
        ::v-deep .item {
            line-height: 34px;
            font-size: 14px;
            color: #222;
        }
    }
    .title {
        width: 100%;
        height: 40px;
        background: #f8f8f8;
        color: #222;
        font-size: 18px;
        font-weight: 600;
        line-height: 40px;
        padding: 0 20px;
    }
    .status {
        color: #e87005;
        &.status-0 {
            color: #d0021b;
        }
        &.status-1 {
            color: #22bd7a;
        }
    }
</style>
