<template>
    <div class="u-p-30 bg-white">
        <div>
            <detail-row-col
                :list="computeList"
                :labelStyle="labelStyle"
            ></detail-row-col>
        </div>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: { TableContainer, CommonTable, DetailRowCol } })
    export default class Tab1 extends BaseTableController<any> {
        @Prop()
        detail!: any

        private labelStyle = {
            fontSize: "14px",
            marginRight: "10px",
            lineHeight: "34px",
            "word-break": "keep-all",
            width: "auto",
            color: "#555",
        }

        private get computeList() {
            const h = this.$createElement
            return [
                {
                    label: "培训地点",
                    value:
                        [
                            this.detail.province,
                            this.detail.city,
                            this.detail.area,
                        ].join("") +
                        (this.detail.address_detail
                            ? `-${this.detail.address_detail}`
                            : ""),
                    hide: false,
                    span: 24,
                },
                {
                    label: "培训内容及课程安排",
                    value: this.detail.train_content || "",
                    hide: false,
                    span: 24,
                },
                {
                    label: "培训方式",
                    value: this.detail.train_manner || "",
                    hide: false,
                    span: 24,
                },
                {
                    label: "就业创业服务内容",
                    value: this.detail.train_founder || "",
                    hide: false,
                    span: 24,
                },
                {
                    label: "可获得证书",
                    value: this.detail.train_certificate || "",
                    hide: false,
                    span: 24,
                },
                {
                    label: "报名联系人",
                    value: this.detail.train_contact || "",
                    hide: false,
                    span: 12,
                },
                {
                    label: "报名联系电话",
                    value: this.detail.train_phone || "",
                    hide: false,
                    span: 12,
                },
                {
                    label: "报名联系QQ",
                    value: this.detail.train_QQ || "",
                    hide: false,
                    span: 12,
                },
                {
                    label: "报名联系微信",
                    value: this.detail.train_wechat || "",
                    hide: false,
                    span: 12,
                },
            ]
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
