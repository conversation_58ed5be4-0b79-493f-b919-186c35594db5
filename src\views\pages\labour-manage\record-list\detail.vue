<template>
    <div class="core-ui-table-container" :key="refreshQueryParams" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="bg-white u-p-x-40 u-p-y-10 content" v-if="row">
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
            ></detail-row-col>
            <div class="u-flex">
                <div class="label bold">
                    {{ row.p_name }}
                </div>
                <!-- 类型是问卷才显示 -->
                <div
                    class="primary pointer u-m-l-10"
                    @click="open"
                    v-if="row.task_type === 2"
                >
                    问卷详情
                </div>
            </div>
        </div>
        <div class="u-m-t-24 bg-white u-p-20 content" v-if="row">
            <div class="title u-m-b-10">用户基础信息</div>
            <div class="u-p-x-20">
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="items2"
                ></detail-row-col>
            </div>
            <!-- <div class="title u-m-b-10 u-m-t-10">服务操作记录</div>
            <list-view :id="id"></list-view> -->
        </div>
        <div class="u-m-t-24 common-table">
            <div class="core-ui-table-container">
                <el-tabs>
                    <el-tab-pane label="服务操作记录">
                        <list-view :id="rowId"></list-view>
                    </el-tab-pane>
                    <el-tab-pane lazy label="跟进处理记录">
                        <list-view2
                            :id="rowId"
                            :v="v"
                            :addId="row.task_serve_call_back_id"
                        ></list-view2>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { config } from "@/config"
    import { DrawerBox } from "@/core-ui/component/drawer-box"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { getAddress } from "@/utils"
    import { object2UrlParams } from "@/utils/tools"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"
    import { predict, Row } from "."
    import { RecordType } from "../seeker-info/components/detail"
    import ListView from "./components/list-view.vue"
    import ListView2 from "./components/list-view2.vue"

    @Component({
        name: routesMap.labourManage.recordDetail,
        components: { DetailRowCol, ListView, ListView2 },
    })
    export default class Detail extends Vue {
        private row: Row | null = null
        private id = ""
        private get rowId() {
            return this.row?.id || ""
        }

        private v = ""
        refreshConfig = {
            fun: this.init,
            name: "refreshRecordDetail",
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `服务记录列表`,
                    to: {
                        name: routesMap.labourManage.recordList,
                    },
                },
            ]
            if (this.from) {
                const routes: Record<string, string> = {
                    "person-detail": routesMap.recruit.personDetail,
                }
                d = [...getCacheBreadcrumbsByRoutePath(routes[this.from])]
            }
            d = [
                ...d,
                {
                    label: "档案详情",
                    to: {
                        name: routesMap.labourManage.recordDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.labourManage.recordDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string | undefined
        }

        mounted() {
            return this.init()
        }

        private init() {
            this.id = this.$route.query.id as string
            this.v = this.$route.query.v as string
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("task_serve_record")
                    .detail(this.id, "for_operate")
                    .query()
                    .then((r) => (this.row = sdk.buildRow(r.row, predict)))
            })
        }

        private labelStyle = {
            width: "90px",
            marginRight: "10px",
            lineHeight: "34px",
        }

        private toDetail() {
            this.$router.push({
                name: this.isJobFair
                    ? routesMap.recruit.jobFairDetail
                    : routesMap.groupService.serviceManageDetail.task,
                query: {
                    id:
                        (this.isJobFair
                            ? this.row!.job_fair_id
                            : this.row!.task_id) + "",
                    from: routesMap.labourManage.recordDetail,
                },
            })
        }

        private open() {
            DrawerBox.open({
                title: "问卷模版",
                url: `${
                    config.h5
                }/pages/sub/questionnaire/preview?${object2UrlParams({
                    questionnaire_id: this.row!.related_id,
                    verify: false,
                })}`,
            })
        }

        private get isJobFair() {
            if (!this.row) return false
            return this.row.record_type === RecordType.招聘会
        }

        private get items(): ColItem[] {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "任务名称：",
                    vNode: h("div", {}, [
                        h(
                            "span",
                            this.isJobFair
                                ? this.row.job_fair_title
                                : this.row.p_name
                        ),
                        h(
                            "span",
                            {
                                class: "primary pointer u-m-l-10",
                                on: {
                                    click: this.toDetail,
                                },
                            },
                            this.isJobFair ? "招聘会详情" : "任务详情"
                        ),
                    ]),
                    span: 24,
                },
                {
                    label: "服务内容：",
                    value: this.row.content,
                    span: 24,
                },
            ]
        }

        private get items2(): ColItem[] {
            if (!this.row) return []
            console.log(JSON.parse(JSON.stringify(this.row)))
            const h = this.$createElement
            return [
                {
                    label: "姓名",
                    vNode: h("span", { class: "primary" }, this.row.name),
                },
                {
                    label: "性别",
                    value: this.row.sex_label,
                },
                {
                    label: "身份证号",
                    value: this.row.id_card_hide,
                },
                {
                    label: "联系电话",
                    vNode: renDesensitizationView(h, {
                        value: this.row.mobile,
                    }),
                },
                {
                    label: "注册状态",
                    value: this.row.uniplat_uid_calc ? "已注册" : "未注册",
                },
                {
                    label: "户籍地址",
                    value: getAddress(this.row, ["province2", "city2", "area2"]),
                },
            ].map((e) => ({ ...e, span: 8, label: e.label + "：" }))
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
    .label {
        line-height: 34px;
    }
    .content {
        ::v-deep .item {
            line-height: 34px;
            font-size: 14px;
            color: #222;
        }
    }
    .title {
        width: 100%;
        height: 40px;
        background: #f8f8f8;
        color: #222;
        font-size: 18px;
        font-weight: 600;
        line-height: 40px;
        padding: 0 20px;
    }
</style>
