<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 content">
            <div class="placeholder" v-show="loading" v-loading="true"></div>
            <form-builder
                ref="formBuilder"
                labelWidth="100px"
                v-show="!loading"
                :onValueChange="onValueChange"
                @changeRow="changeRow"
            ></form-builder>
            <div v-if="showData">
                共{{ totalCount }}条数据，
                <el-button type="text" @click="checkTask"> 点击查看 </el-button>
            </div>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="submitValidate"
                    class="custom-btn btn u-m-0"
                >
                    提交
                </el-button>
            </div>
        </div>
        <CheckTaskPop
            :policyId="curPolicyId"
            :date="date"
            v-model="showCheckTaskPop"
        />
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FileType,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { Action } from "uniplat-sdk"
    import { routesMap } from "@/router/direction"
    import { intentColumn, intentPredict } from ".."
    import CheckTaskPop from "./check-task-pop.vue"

    @Component({ components: { FormBuilder, CheckTaskPop } })
    export default class AddTaskPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: "" })
        private readonly modelName!: string

        @Prop({ default: "" })
        private readonly id!: number

        @Prop({ default: "" })
        private readonly policyId!: string

        private action?: Action

        private showCheckTaskPop = false
        private showData = false
        private totalCount = 0
        private date = ""
        private curPolicyId = ""

        private get title() {
            return "新建核查任务"
        }

        private get model() {
            return this.modelName || "common_check_tool"
        }

        onOpen() {
            this.init()
        }

        onClosing() {
            this.resetFormFields()
            this.clearForm()
        }

        private getAction() {
            return (this.action = sdk.core
                .model(this.model)
                .action("add_check_task")
                .updateInitialParams({
                    selected_list: this.id ? [{ v: 0, id: this.id }] : [],
                }))
        }

        private init() {
            this.curPolicyId = this.policyId
            this.loading = true
            return buildFormSections({
                action: this.getAction(),
                forms: [
                    {
                        label: "核查任务名称",
                        type: FormType.Text,
                        prop: "name",
                        required: true,
                    },
                    {
                        label: "核查模版",
                        type: FormType.IntentSearch,
                        prop: "tool_id",
                        required: true,
                        option: {
                            dialogProp: {
                                width: "1200px",
                            },
                            intentSearchConfig: {
                                tableConfig: () => ({
                                    model: sdk.core
                                        .model("common_check_tool")
                                        .list("back_list"),
                                    preFilter: {
                                        is_del: "0",
                                    },
                                    predict: intentPredict,
                                    column: intentColumn,
                                    filter: [],
                                }),
                                template: "{name}",
                                valueKey: "id",
                            },
                        },
                    },
                    {
                        label: "核查需求简述",
                        type: FormType.Text,
                        prop: "require_note",
                        option: {
                            type: "textarea",
                            rows: 3,
                        },
                        required: true,
                    },
                    {
                        label: "核查时间段",
                        type: FormType.DatePicker,
                        prop: "start",
                        option: {
                            type: "date",
                        },
                        required: true,
                        col: {
                            span: 14,
                        },
                    },
                    {
                        label: "",
                        labelWidth: "0",
                        type: FormType.DatePicker,
                        prop: "end",
                        option: {
                            type: "date",
                        },
                        required: true,
                        col: {
                            span: 10,
                        },
                    },
                ],
            }).then((r) => {
                this.buildForm(r.forms)
                this.loading = false
            })
        }

        private submitValidate() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                    })
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model(this.model)
                    .action("add_check_task")
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: this.id ? [{ v: 0, id: +this.id }] : [],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("新建成功")
                        this.callRefresh(routesMap.dataCheck.generalToolIndex)
                        this.callRefresh(routesMap.dataCheck.generalToolDetail)
                        this.callRefresh(routesMap.dataCheck.generalIndex)
                        this.close()
                    })
            })
        }

        private changeRow(intent: any) {
            this.curPolicyId = intent.data.policy_id || ""
            const data = this.getFormValues()
            if (data.tool_id && data.start && data.end && this.curPolicyId) {
                this.getTotalCount()
            } else {
                this.showData = false
            }
        }

        private checkTask() {
            this.showCheckTaskPop = true
        }

        private onValueChange(key: string, value: string) {
            const data = this.getFormValues()
            if (data.tool_id && data.start && data.end && this.curPolicyId) {
                this.getTotalCount()
            } else {
                this.showData = false
            }
        }

        private getTotalCount() {
            const data = this.getFormValues()
            this.date = `${data.start},${data.end}`
            sdk.getDomainService("get_policy", "back_api", "warning_platform")
                .post<{}>({
                    page_index: 1,
                    page_size: 1,
                    policy_id: this.curPolicyId,
                    filter: [
                        {
                            date_type: "date",
                            filter_type: 1,
                            info_key: "handle_date",
                            info_type: "date",
                            info_value: this.date,
                        },
                    ],
                })
                .then((r: any) => {
                    this.totalCount = r.total_count || 0
                })
                .finally(() => {
                    this.showData = true
                })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
