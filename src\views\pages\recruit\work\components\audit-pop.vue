<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="审核发布共享申请"
        width="600px"
        top="5vh"
    >
        <div v-if="row">
            <div class="view-item u-flex">
                <div class="label">所属企业</div>
                <div class="value">{{ row.agent_name || "-" }}</div>
            </div>
            <div class="view-item u-flex">
                <div class="label">类型</div>
                <div class="value">{{ row.type_label || "-" }}</div>
            </div>
            <div class="view-item u-flex">
                <div class="label">涉及人数</div>
                <div class="value">{{ row.person_num || "-" }}</div>
            </div>
            <div class="view-item u-flex">
                <div class="label">描述</div>
                <div class="value">
                    <div v-for="(d, index) in des" :key="index">
                        {{ d }}
                    </div>
                </div>
            </div>
            <div class="view-item u-flex">
                <div class="label">联系人</div>
                <div class="value">{{ row.contact_person || "-" }}</div>
            </div>
            <div class="view-item u-flex">
                <div class="label">联系电话</div>
                <div class="value">{{ row.contact_number || "-" }}</div>
            </div>
            <div class="view-item u-flex">
                <div class="label">图片</div>
                <div class="value u-flex">
                    <el-image
                        :src="img"
                        v-for="img in images"
                        :key="img"
                        class="img"
                        :preview-src-list="images"
                    />
                    <span v-if="!images.length" style="lineheight: 34px"
                        >-</span
                    >
                </div>
            </div>
            <div class="view-item u-flex">
                <div class="label">申请时间</div>
                <div class="value">
                    {{ row.final_change_time_label || "-" }}
                </div>
            </div>
        </div>
        <form-builder ref="formBuilder" labelWidth="120px"></form-builder>
        <div class="u-flex u-row-center u-m-t-40">
            <el-button
                @click="close"
                class="btn custom-btn u-m-r-30"
                type="primary"
                plain
            >
                取消
            </el-button>
            <el-button class="btn custom-btn" type="primary" @click="confirm">
                确定
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Row } from ".."
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { sdk } from "@/service"
    import { auditFormConfig } from "./detail"
    import { getImages } from "@/utils"
    import { routesMap } from "@/router/direction"
    import { omit } from "lodash"

    @Component({ components: { FormBuilder } })
    export default class AuditPop extends Mixins(DialogController, FormController) {
        @Prop()
        private row!: Row

        onOpen() {
            this.init()
        }

        private init() {
            return buildFormSections(auditFormConfig(this.row.id)).then((r) => {
                this.buildForm(r.forms)
            })
        }

        private get des() {
            const des = (this.row?.apply_description || "")
                .split("\n")
                .filter(Boolean)
            return des.length ? des : ["-"]
        }

        private get images() {
            if (!this.row) return []
            return getImages(this.row.images)
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                        apply_type: 0,
                    })
                }
            })
        }

        private submit(data: any) {
            if (+data.status !== 1) {
                data.review_comment = data.review_comment_require
            }

            return sdk.core
                .model("share_employee_apply")
                .action("review")
                .updateInitialParams({
                    selected_list: [
                        {
                            v: 0,
                            id: this.row.id,
                        },
                    ],
                } as any)
                .addInputs_parameter(omit(data, "review_comment_require"))
                .execute()
                .then(() => {
                    this.close()
                    this.callRefresh(routesMap.recruit.work)
                    this.callRefresh(routesMap.recruit.workDetail)
                })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .view-item {
        margin-bottom: 10px;
        line-height: 22px;
        .label {
            margin-right: 10px;
            color: #555;
            font-size: 14px;
            width: 120px;
            text-align: right;
            padding-right: 12px;
            flex: none;
        }
    }
    .btn {
        width: 100px;
        height: 36px;
    }
    .u-flex {
        align-items: flex-start;
    }
    .value {
        .img {
            width: 80px;
            height: 80px;
            margin-right: 10px;
        }
    }
</style>
