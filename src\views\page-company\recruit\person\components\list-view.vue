<template>
    <div class="u-p-x-20 bg-white">
        <!-- <div class="title">人才推荐服务</div> -->
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            @getData="getData"
        >
            <div slot="pages-tabs-right"></div>
            <div slot="table" slot-scope="{ data }" class="t bg-white">
                <common-table :data="data" :columns="columns">
                    <div
                        slot="h"
                        class="u-flex u-flex-wrap u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <template
                            v-if="
                                [
                                    '联系待标记',
                                    '沟通中',
                                    '已推荐岗位给求职者',
                                ].includes(scope.row.status1_label)
                            "
                        >
                            <el-button
                                type="text"
                                v-if="
                                    [
                                        '已推荐岗位给求职者',
                                        '联系待标记',
                                    ].includes(scope.row.status1_label)
                                "
                                @click="changeStatus(scope.row, 10)"
                            >
                                沟通中
                            </el-button>
                            <div class="u-flex">
                                <el-button
                                    type="text"
                                    @click="changeStatus(scope.row, 20)"
                                >
                                    成功入职
                                </el-button>
                                <el-button
                                    type="text"
                                    @click="changeStatus(scope.row, 30)"
                                >
                                    已淘汰/拒绝
                                </el-button>
                            </div>
                        </template>
                        <div v-else>-</div>
                    </div>
                </common-table>
            </div>
        </table-container>
        <empty v-show="!show"></empty>
    </div>
</template>

<script lang='ts'>
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "../../person/components/detail"
    import { TableConfig } from "@/core-ui/component/table"
    import { MessageBox } from "element-ui"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import Empty from "@/views/page-company/home/<USER>/empty.vue"
    import { routesMap } from "@/router/direction"
    import { config } from "@/config"

    const companyListDIalogSession = "COMPANY_LIST_DIALOG_SESSION"

    @Component({ components: { TableContainer, CommonTable, Empty } })
    export default class ListView extends BaseTableController<{ id: number }> {
        private tableConfig: TableConfig | null = null
        private columns = columns
        private ids: string[] = []
        private config = config

        private tipsOpenStatus = false

        @Prop()
        id!: string

        mounted() {
            this.tableConfig = tableConfig(this.id)
        }

        private show = false
        private getData(data: any) {
            this.show = !!data[0].rows.length
            if (data[1].record_count) {
                this.openTips()
            }
        }

        private openTips() {
            if (
                this.tipsOpenStatus ||
                sessionStorage.getItem(companyListDIalogSession)
            ) {
                return
            }
            this.tipsOpenStatus = true
            const box = `<div style="width: 420px">#</div>`
            const msgList = [
                "我们通过对填写了就业意愿的居民进行筛选，将与当前岗位招聘要求相匹配并且长在求职的居民进行了推荐。<span style='color: #f56c6c '>需要注意的是，这些居民并没有直接投递岗位。</span><br/>",
                "在联系推荐的求职者时，请明确告知他们:",
                `1. 您是通过<span style='color: #ff8d59 '>就业局的“${config.miniProgramName}”小程序</span>获取到的信息。`,
                "2. 同时，请向他们详细介绍当前岗位，并与他们确认是否对该岗位感兴趣。",
            ]

            const box2 = box.replace("#", msgList.join("<br/>"))
            MessageBox.confirm("是否将该岗位提交审核？", {
                title: "电话联系时需注意事项（请仔细阅读）",
                message: box2,
                dangerouslyUseHTMLString: true,
                cancelButtonText: "我已认真阅读完，当前页面不再提示",
                confirmButtonText: "我已知晓",
            })
                .then(() => {})
                .catch(() => {
                    sessionStorage.setItem(companyListDIalogSession, "1")
                })
        }

        private changeStatus(row: Row, status = 0) {
            MessageBox.confirm("确认改变状态？", "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_candidate_order")
                        .action("update_status1")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .addInputs_parameter({ status })
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.company.recruit.jobDeliverDetail,
                query: { id: row._access_key || row.id, from: this.$route.name },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .title {
        height: 40px;
        background: #f8f8f8;
        color: #222;
        font-size: 18px;
        font-weight: 600;
        line-height: 40px;
        padding: 0 20px;
    }
    ::v-deep .table {
        padding: 0;
        background: transparent;
    }
    ::v-deep .el-tabs__item {
        color: #333;
        user-select: none;
        font-size: 16px;
        width: auto;
        background: transparent !important;
        &.is-active {
            background: transparent !important;
            font-weight: 600;
            color: @main-color;
        }
    }
    ::v-deep .el-tabs__active-bar {
        display: block;
    }
    ::v-deep .table-tabs {
        justify-content: space-between;
        background: transparent;
    }
    ::v-deep .common-table {
        margin-top: 0 !important;
    }
    .remove-btn {
        width: 120px;
        height: 40px;
    }
    .t {
        padding: 20px;
    }
</style>
