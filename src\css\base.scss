@for $i from 0 through 9 {
    $c: $i * 16 + $i;

    .color-#{$i} {
        color: rgb($c, $c, $c);
    }
}

.u-relative,
.u-rela {
    position: relative;
}

.u-absolute,
.u-abso {
    position: absolute;
}

// nvue不能用标签命名样式，不能放在微信组件中，否则微信开发工具会报警告，无法使用标签名当做选择器
/* #ifndef APP-NVUE */
image {
    display: inline-block;
}

// 在weex，也即nvue中，所有元素默认为border-box
view,
text {
    box-sizing: border-box;
}

/* #endif */

.u-font-xs {
    font-size: 22px;
}

.u-font-sm {
    font-size: 26px;
}

.u-font-md {
    font-size: 28px;
}

.u-font-lg {
    font-size: 30px;
}

.u-font-xl {
    font-size: 34px;
}

.u-flex {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    align-items: center;
}

.u-flex-center {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.u-flex-default {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
}

.u-shrink-0 {
    flex-shrink: 0;
}

.u-flex-wrap {
    flex-wrap: wrap;
}

.u-flex-nowrap {
    flex-wrap: nowrap;
}

.u-flex-none {
    flex: none;
}

.u-col-center {
    align-items: center;
}

.u-col-stretch {
    align-items: stretch;
}

.u-col-top {
    align-items: flex-start;
}

.u-col-bottom {
    align-items: flex-end;
}

.u-row-center {
    justify-content: center;
}

.u-row-left {
    justify-content: flex-start;
}

.u-row-right {
    justify-content: flex-end;
}

.u-row-between {
    justify-content: space-between;
}

.u-row-around {
    justify-content: space-around;
}

.u-row-evenly {
    justify-content: space-evenly;
}

.u-text-left {
    text-align: left;
}

.u-text-center {
    text-align: center;
}

.u-text-right {
    text-align: right;
}

.u-flex-col {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
}

// 定义flex等分
@for $i from 0 through 12 {
    .u-flex-#{$i} {
        flex: $i;
    }
}

// 定义字体(px)单位，小于20都为px单位字体
@for $i from 9 to 20 {
    .u-font-#{$i} {
        font-size: $i + px;
    }
}

// 定义字体(px)单位，大于或等于20的都为px单位字体
@for $i from 20 through 40 {
    .u-font-#{$i} {
        font-size: $i + px;
    }
}

// 定义内外边距，历遍1-80
@for $i from 0 through 80 {
    .u-p-x-#{$i} {
        padding-left: $i + px !important;
        padding-right: $i + px !important;
    }

    .u-p-y-#{$i} {
        padding-top: $i + px !important;
        padding-bottom: $i + px !important;
    }

    .u-m-x-#{$i} {
        margin-left: $i + px !important;
        margin-right: $i + px !important;
    }

    .u-m-y-#{$i} {
        margin-top: $i + px !important;
        margin-bottom: $i + px !important;
    }

    // 只要双数和能被5除尽的数
    @if $i % 2==0 or $i % 5==0 {

        // 得出：u-margin-30或者u-m-30
        .u-margin-#{$i},
        .u-m-#{$i} {
            margin: $i + px !important;
        }

        // 得出：u-padding-30或者u-p-30
        .u-padding-#{$i},
        .u-p-#{$i} {
            padding: $i + px !important;
        }

        @each $short, $long in l left, t top, r right, b bottom {

            // 缩写版，结果如： u-m-l-30
            // 定义外边距
            .u-m-#{$short}-#{$i} {
                margin-#{$long}: $i + px !important;
            }

            // 定义内边距
            .u-p-#{$short}-#{$i} {
                padding-#{$long}: $i + px !important;
            }

            // 完整版，结果如：u-margin-left-30
            // 定义外边距
            .u-margin-#{$long}-#{$i} {
                margin-#{$long}: $i + px !important;
            }

            // 定义内边距
            .u-padding-#{$long}-#{$i} {
                padding-#{$long}: $i + px !important;
            }
        }
    }
}

// 重置nvue的默认关于flex的样式
.u-reset-nvue {
    flex-direction: row;
    align-items: center;
}

// 重置drop-down标题样式
.u-dropdown__menu__item.u-dropdown__menu__item__gap {
    border: none !important;
    position: relative;
}

.u-dropdown__menu__item:not(:last-child) .after {
    width: 1px;
    height: 40px;
    position: absolute;
    right: 0;
}

// 下拉框背景色
.u-dropdown-item {
    background-color: #fff;
}

.font-weight-4 {
    font-weight: 400;
}

.font-weight-5 {
    font-weight: 500;
}

.bold {
    font-weight: bold;
}

.font-weight-6 {
    font-weight: 600;
}

.u-line-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.u-line-2 {
    -webkit-line-clamp: 2;
}

.u-line-3 {
    -webkit-line-clamp: 3;
}

.u-line-4 {
    -webkit-line-clamp: 4;
}

.u-line-5 {
    -webkit-line-clamp: 5;
}

.u-line-2,
.u-line-3,
.u-line-4,
.u-line-5 {
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box; // 弹性伸缩盒
    -webkit-box-orient: vertical; // 设置伸缩盒子元素排列方式
}

.custom-btn {
    min-width: 0 !important;
    padding: 0 !important;
}

.bg-white {
    background: #fff;
}

.lh1 {
    line-height: 1;
}

.lh13 {
    line-height: 1.3;
}

.lh15 {
    line-height: 1.5;
}

.lh18 {
    line-height: 1.8;
}

.lh2 {
    line-height: 2;
}

.pointer {
    cursor: pointer;
}

.pre-line {
    white-space: pre-line;
}

p {
    margin-top: 0;
    margin-bottom: 0;
}

@font-face {
    font-family: "dotsfont";
    src: url("@/core-ui/component/form/filed/v-input/font/dotsfont.eot");
    src: url("@/core-ui/component/form/filed/v-input/font/dotsfont.eot?#iefix") format("embedded-opentype"),
        url("@/core-ui/component/form/filed/v-input/font/dotsfont.woff") format("woff"),
        url("@/core-ui/component/form/filed/v-input/font/dotsfont.ttf") format("truetype"),
        url("@/core-ui/component/form/filed/v-input/font/dotsfont.svg#dotsfontregular") format("svg");
}

.password-input {
    &.el-input .el-input__inner {
        font-family: "dotsfont";
        ime-mode: "inactive";
    }
}

.rich-content {
    img {
        max-width: 100%;
    }
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    --color: var(--primary);

    .tag {
        flex-shrink: 0;
        padding: 0 6px;
        border-radius: 2px;
        color: var(--color);
        border: 1px solid var(--color);
        line-height: 22px;
        margin-right: 10px;
        text-align: center;
    }

    .primary {
        line-height: 22px;
    }
}