import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import router from "@/router"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import { serviceTaskRowPredict } from "../service-manage/service-detail/task"
import { find, get } from "lodash"
import { buildSelectSource } from "../../../../core-ui/component/form/builder"

const tableFilter: TableFilter[] = [
    {
        label: "任务编号",
        type: FormType.Text,
        prop: "task_no",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "任务名称",
        type: FormType.Text,
        prop: "p_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "所属城市",
        type: FormType.MultipleCascader,
        prop: "area_1",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "所属服务",
        type: FormType.Text,
        prop: "serve_project.p_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "所属服务内容项",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "任务状态",
        type: FormType.Select,
        prop: "status",
        option: {
            filterable: true,
            multiple: true,
        },
    },
    {
        label: "创建时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
]

export const columns: TableColumn[] = [
    {
        label: "任务编号",
        prop: "task_no",
        minWidth: "80",
        showOverflowTip: true,
    },
    {
        label: "任务名称",
        prop: "p_name",
        minWidth: "120",
        showOverflowTip: true,
    },
    {
        label: "任务工具",
        prop: "task_type_display",
        showOverflowTip: true,
    },
    {
        label: "所属服务",
        prop: "sp_name",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "所属服务内容项",
        prop: "title",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "所属城市",
        prop: "address",
        width: "150",
        showOverflowTip: true,
        formatter: (row) => {
            return [
                row.area_1_name,
                row.area_2_name,
                row.area_3_name,
                row.area_4_name,
            ]
                .filter(Boolean)
                .join("")
        },
    },
    {
        label: "创建时间",
        prop: "create_time",
        width: "180",
        render: (h, row) => {
            return [
                h("div", formatTime.day(row.create_time)),
                h("div", formatTime.default(row.create_time, "HH:mm:ss")),
            ]
        },
    },
    {
        label: "状态",
        prop: "status_display",
        showOverflowTip: true,
    },
    {
        label: "已服务人数",
        prop: "id",
        minWidth: "100",
        showOverflowTip: true,
        render: (h, row) => {
            return h(
                "span",
                {
                    class: "primary pointer",
                    on: {
                        click: () => {
                            router.push({
                                name: routesMap.groupService.serviceManageDetail
                                    .task,
                                query: {
                                    id: row.id,
                                    from: routesMap.groupService.taskManage,
                                },
                            })
                        },
                    },
                },
                row.served_target_count || 0
            )
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export async function tableConfig(): Promise<TableConfig> {
    const filters = await sdk.core
        .model("serve_task")
        .list("manage_for_operate")
        .query({
            pageIndex: 1,
            item_size: 0,
        })
        .then((r) => r.pageData.meta.filters)
    return {
        domainService: sdk.getDomainService(
            "get_serve_task_list",
            "operate_api",
            "xg_project"
        ),
        filter: tableFilter.map((e) => {
            return {
                ...e,
                sourceInputsParameter: buildSelectSource(
                    get(
                        find(filters, {
                            property: e.prop,
                        }),
                        "ext_properties.mapping.mapping_values"
                    )
                ),
            }
        }),
        defaultPageSize: 8,
        predict: serviceTaskRowPredict,
        handleFilterData(params) {
            if (!params.area_1?.length) {
                params.area_1 = ""
            }
            return params
        },
    }
}
