import { buildSelectSource, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { BatchPop } from "@/views/components/batch-pop"
import { Message, MessageBox } from "element-ui"
import { flatMap } from "lodash"
import { TagManagerTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    {
        label: "用户信息",
        type: FormType.Text,
        prop: "name",
    },
    {
        label: "所在城市",
        type: FormType.MultipleCascader,
        prop: "permanent_province_code",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "服务内容",
        type: FormType.Select,
        prop: "serve_type",
    },
    {
        label: "就业状态",
        type: FormType.Select,
        prop: "employment_status",
        option: {
            multiple: true,
        },
    },
    {
        label: "标签",
        type: FormType.MultipleCascader,
        prop: "label_nos",
        option: {
            filterable: true,
            collapseTags: true,
            elProps: {
                // multiple: true,
                checkStrictly: true,
            },
        },
    },
    {
        label: "是否注册",
        prop: "uniplat_uid",
        type: FormType.Select,
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "已注册",
            },
            {
                key: "0",
                value: "未注册",
            },
        ]),
    },
]

const rowPredict = {
    userId: "user_profile#id",
    profile_access_key: "user_profile#_access_key",
    name: "user_profile#name",
    sex: "user_profile#sex_label",
    birth_date: "user_profile#birth_date",
    mobile_hide: "user_profile#mobile_hide",
    idcard_hide: "user_profile#id_card_hide",
    region_name: "user_profile#basic_info#permanent_city#region_name",
    employment_status:
        "user_profile#user_profile_current_job_info#employment_status_label",
    p_name: "serve_task#p_name",
    province_name: "user_profile#basic_info#permanent_province#region_name",
    city_name: "user_profile#basic_info#permanent_city#region_name",
    update_time: "user_profile#update_time",
    tags: "tags",
    is_register: "user_profile#uniplat_uid_calc_label",
}

export function tableConfig(preFilter: {
    project_id?: string
    task_id?: string
}): TableConfig {
    const model = preFilter.project_id
        ? "task_serve_target_for_project"
        : "task_serve_target"
    const list = preFilter.project_id ? "list_for_project" : "list_for_task"
    return {
        model: sdk.core.model(model).list(list),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        preFilter,
    }
}

export interface Row {
    userId: string
    name: string
    sex_label: string
    birth_date: string
    mobile_hide: string
    idcard_hide: string
    region_name: number
    employment_status_label: string
    p_name: string
    province_name: string
    city_name: string
    update_time: string
    tags: { [key: string]: TagManagerTypes.TagInfo[] }
    id: number
    v: number
    profile_access_key: string
}

export const columns: TableColumn[] = [
    {
        prop: "select",
        width: "58",
        type: "selection",
    },
    {
        label: "姓名",
        prop: "name",
        width: "70",
        showOverflowTip: true,
    },
    {
        label: "性别",
        width: "50",
        align: "center",
        prop: "sex_label",
        showOverflowTip: true,
    },
    {
        label: "身份证号",
        prop: "idcard_hide",
        width: "180",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile_hide",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "是否注册",
        prop: "is_register_label",
        width: "100",
        showOverflowTip: true,
    },
    {
        label: "当前城市",
        prop: "address",
        width: "150",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.province_name, row.city_name].filter(Boolean).join("")
        },
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "更新时间",
        prop: "update_time",
        width: "180",
        align: "center",
        render: (h, row) => {
            return h("div", [
                h("div", formatTime.day(row.update_time)),
                h("div", formatTime.default(row.update_time, "HH:mm")),
            ])
        },
    },
    {
        label: "标签",
        prop: "id",
        minWidth: "100",
        showOverflowTip: true,
        formatter: (row: Row) => {
            return flatMap(row.tags, (item) =>
                flatMap(item, (i) => i.tagName)
            ).join("，")
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "120",
        fixed: "right",
        showOverflowTip: true,
    },
]

export function batchDelete(ids: number[]) {
    if (!ids.length) {
        Message.error("请选择数据")
        throw new Error()
    }
    let errorList: any = []
    const selected_list = ids.map((i) => ({ id: i, v: 0 }))
    const model = sdk.core.model("task_serve_target").action("batch_delete")
    return new Promise((resolve, reject) => {
        MessageBox.confirm("请确认是否删除？", "提示", {
            beforeClose: (action, instance, done) => {
                if (action === "confirm") {
                    instance.confirmButtonLoading = true
                    model
                        .updateInitialParams({
                            selected_list,
                        })
                        .query()
                        .then(() => {
                            return model
                                .executeEach({
                                    selected_list,
                                    inputs_parameters: [],
                                } as any)(
                                    () => {},
                                    (errorRow) => {
                                        errorList = errorRow
                                    }
                                )
                                .awaiting.then(() => {
                                    BatchPop.use({
                                        list: selected_list,
                                        errorList,
                                        columns: [
                                            {
                                                label: "id",
                                                prop: "id",
                                            },

                                            {
                                                label: "错误原因",
                                                prop: "error",
                                                width: "120px",
                                                showOverflowTip: true,
                                            },
                                        ],
                                    })
                                    if (!errorList.length) {
                                        done()
                                        resolve(0)
                                    } else {
                                        reject()
                                    }
                                })
                                .catch((e) => {
                                    reject()
                                    Message.error(e)
                                    return Promise.reject(e)
                                })
                        })
                        .finally(() => {
                            instance.confirmButtonLoading = false
                        })
                } else {
                    done()
                    return Promise.reject()
                }
            },
        })
    })
}
