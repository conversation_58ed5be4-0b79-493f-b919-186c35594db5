<template>
    <div>
        <div class="d-flex justify-content-end u-m-b-10" v-if="enterpriseId">
            <el-button
                v-role="'model.xg_agent_manager.action.add_agent_manager'"
                type="primary"
                @click="displayAddManagerPop = true"
            >
                新增管理员
            </el-button>
        </div>
        <table-container
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
        >
            <div slot="table" slot-scope="{ data }">
                <common-table
                    :data="data"
                    :tableConfig="tableConfig"
                    :columns="columns"
                >
                    <div slot="mobile_hide" slot-scope="scope">
                        {{
                            scope.row.showMobile
                                ? scope.row.mobile
                                : scope.row.mobile_hide
                        }}
                        <el-button
                            v-if="!scope.row.showMobile"
                            class="u-m-l-10"
                            type="text"
                            @click="scope.row.showMobile = true"
                            >查看</el-button
                        >
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button
                            v-role="'model.xg_agent_manager.action.update'"
                            type="text"
                            @click="openEdit(scope.row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-role="
                                'model.xg_agent_manager.action.disable_agent'
                            "
                            type="text"
                            @click="operate(scope.row, scope.row.status)"
                        >
                            删除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <add-manager-pop
            @refresh="refresh"
            :enterpriseId="enterpriseId"
            v-model="displayAddManagerPop"
        ></add-manager-pop>

        <edit-manager-pop
            @refresh="refreshList"
            :enterpriseId="enterpriseId"
            :v="curEditVersion"
            :rowId="curEditId"
            v-model="displayEditManagerPop"
        ></edit-manager-pop>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import {
        TableColumn as TableColumnImpl,
        TableConfig,
    } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { sdk } from "@/service"
    import { Component, Prop, Watch } from "vue-property-decorator"
    import AddManagerPop from "@/views/pages/employment-manage/company-manage/components/add-manager-pop.vue"
    import EditManagerPop from "@/views/pages/employment-manage/company-manage/components/edit-manager-pop.vue"
    import { formatTime } from "@/utils/tools"
    import { userInfoService } from "@/core-ui/service/passport/user-info"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"

    const rowPredict = {
        name: "",
        name_hide: "",
        mobile: "",
        mobile_hide: "",
        type: "label",
        status: "label",
        create_time: "",
        create_type: "label",
        activation_status: "label",
        last_login_time: "",
        uniplat_uid: "",
        showMobile: false,
    }

    const enum Type {
        超级管理员 = 0,
        一般管理员 = 1,
    }

    const enum Status {
        正常 = 0,
        禁用 = 1,
    }

    const enum CreateType {
        自行注册 = 1,
        后台添加 = 2,
    }

    const enum ActivationStatus {
        已激活 = 1,
        未激活 = 0,
    }

    interface Row {
        /** 姓名 */
        name: string
        /** 手机号 */
        mobile: string
        /** 角色 */
        type: Type
        /** 角色[文本] */
        type_label: string
        /** 状态 */
        status: Status
        /** 状态[文本] */
        status_label: string
        /** 创建时间 */
        create_time: string
        last_login_time: string
        /** 添加方式 */
        create_type: CreateType
        /** 添加方式[文本] */
        create_type_label: string
        /** 激活状态 */
        activation_status: ActivationStatus
        /** 激活状态[文本] */
        activation_status_label: string
        id: number
        v: number
    }

    function tableConfig(): TableConfig {
        return {
            model: sdk.core.model("xg_agent_manager").list("company_back_list_v2"),
            defaultPageSize: 8,
            predict: rowPredict,
        }
    }

    const columns: TableColumnImpl[] = [
        {
            label: "姓名",
            prop: "name_hide",
            showOverflowTip: true,
        },
        {
            label: "手机号/账号",
            prop: "mobile_hide",
            showOverflowTip: true,
        },
        // {
        //     label: "状态",
        //     prop: "status_label",
        //     showOverflowTip: true,
        // },
        {
            label: "角色",
            prop: "type_label",
            showOverflowTip: true,
        },
        {
            label: "添加方式",
            prop: "create_type_label",
            showOverflowTip: true,
        },
        {
            label: "创建时间",
            prop: "create_time",
            showOverflowTip: true,
            formatter: (row) => {
                return formatTime.seconds(row.create_time)
            },
        },
        {
            label: "上次登录时间",
            prop: "last_login_time",
            showOverflowTip: true,
            formatter: (row) => {
                return formatTime.seconds(row.last_login_time)
            },
        },
        {
            label: "操作",
            prop: "h",
            width: "100",
            showOverflowTip: true,
        },
    ]

    @Component({
        components: { TableContainer, CommonTable, AddManagerPop, EditManagerPop },
    })
    export default class PolicyBasis extends BaseTableController<Row> {
        @Prop()
        private enterpriseId!: string

        private tableConfig = tableConfig()

        private uid = ""

        created() {
            const config = tableConfig()
            config.preFilter = {
                tg_enterprise_id: this.enterpriseId,
            }
            this.tableConfig = config
        }

        mounted() {
            userInfoService.get().then((r) => {
                this.uid = r.uid
            })
        }

        @Watch("enterpriseId", { immediate: true })
        private onEnterpriseIdChange() {
            if (this.enterpriseId) {
                this.tableConfig = Object.assign(tableConfig(), {
                    preFilter: {
                        tg_enterprise_id: this.enterpriseId,
                    },
                })
            }
        }

        private readonly columns: TableColumnImpl[] = columns

        private displayAddManagerPop = false
        private displayEditManagerPop = false

        private curEditVersion = 0
        private curEditId = 0

        private openEdit(row: Row) {
            this.curEditVersion = row.v
            this.curEditId = row.id
            this.displayEditManagerPop = true
        }

        refresh() {
            this.reloadList()
        }

        private operate(row: Row) {
            MessageBox.confirm(`确认删除？`, "删除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_agent_manager")
                        .action("disable_agent")
                        .updateInitialParams({
                            selected_list: [{ id: row.id, v: 0 }],
                            prefilters: [
                                {
                                    property: "tg_enterprise_id",
                                    value: this.enterpriseId,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("删除成功")
                            this.refresh()
                        })
                })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .check-list-container {
        width: 100%;
    }

    .header {
        padding-top: 15px;
        padding-bottom: 15px;

        .title {
            font-size: 22px;
            color: #000000;
        }
    }
</style>
