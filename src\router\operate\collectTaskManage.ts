import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const collectTaskManage = [
    {
        path: "/collectTaskManage",
        redirect: "collectTaskManage/employmentManage/company-mange",
        name: routesMap.home.collectTaskManage,
        meta: {
            title: "劳动力信息库",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/labourManage.svg"),
            hidden: [EnvProject.掇刀项目].includes(config.envProject),
        },
        component: layout,
        children: [
            // 数采任务管理
            {
                path: "/collect-task-manage",
                redirect: "collect-task-manage/data-statistics",
                name: routesMap.collectTaskManage.root,
                meta: {
                    title: "数采任务管理",
                    // svgIcon: require("@/assets/icon/menu/collectTaskManage.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "data-statistics",
                        name: routesMap.collectTaskManage.dataStatistics,
                        meta: {
                            title: "工作台",
                            role: "/workbench",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/data-statistics/index.vue"
                            ),
                    },
                    {
                        path: "business-manage",
                        name: routesMap.collectTaskManage.businessManage,
                        meta: {
                            title: "项目列表",
                            role: "/tablelist/collect_project/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/business-manage/index.vue"
                            ),
                    },
                    {
                        path: "business-manage-create",
                        name: routesMap.collectTaskManage.businessManageCreate,
                        meta: {
                            title: "项目创建",
                            parentMenuName:
                                routesMap.collectTaskManage.businessManage,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/business-manage/create.vue"
                            ),
                    },
                    {
                        path: "business-manage-detail",
                        name: routesMap.collectTaskManage.businessManageDetail,
                        meta: {
                            title: "项目详情",
                            parentMenuName:
                                routesMap.collectTaskManage.businessManage,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/business-manage/detail.vue"
                            ),
                    },
                    {
                        path: "task-info-list",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .taskInfo.list,
                        meta: {
                            title: "任务列表",
                            role: "/tablelist/collect_task/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/task-info/index.vue"
                            ),
                    },
                    {
                        path: "create-task",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .taskInfo.create,
                        meta: {
                            title: "项目创建",
                            hidden: true,
                            parentMenuName:
                                routesMap.collectTaskManage.taskInfoManage
                                    .taskInfo.list,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/task-info/create-task.vue"
                            ),
                    },

                    {
                        path: "record-list-detail",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .taskInfo.recordListDetail,
                        meta: {
                            parentMenuName:
                                routesMap.collectTaskManage.businessManage,
                            title: "采集记录详情",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/task-info/record-list-detail.vue"
                            ),
                    },
                    {
                        path: "record-list-detail-import",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .taskInfo.detailImport,
                        meta: {
                            parentMenuName:
                                routesMap.collectTaskManage.businessManage,
                            title: "导入中心",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/task-info/import-center/index.vue"
                            ),
                    },
                    // {
                    //     path: "collect-info-manage",
                    //     name: routesMap.collectTaskManage.taskInfoManage
                    //         .collectInfoManage.list,
                    //     meta: {
                    //         title: "采集信息管理",
                    //     },
                    //     component: () =>
                    //         import(
                    //             "@/views/pages/collect-task-manage/task-info-manage/collect-info-manage/index.vue"
                    //         ),
                    // },
                    {
                        path: "collect-info-manage-detail",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .collectInfoManage.detail,
                        meta: {
                            parentMenuName:
                                routesMap.collectTaskManage.businessManage,
                            title: "采集信息管理详情",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/collect-info-manage/detail.vue"
                            ),
                    },

                    {
                        path: "manage-collect-list",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .manageCollect.list,
                        meta: {
                            title: "采集任务",
                            role: "/tablelist/collect_task/manage_collect",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/manage-collect/index.vue"
                            ),
                    },

                    {
                        path: "manage-collect-detail",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .manageCollect.detail,
                        meta: {
                            title: "采集任务详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.collectTaskManage.taskInfoManage
                                    .manageCollect.list,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/manage-collect/detail.vue"
                            ),
                    },
                    {
                        path: "collect-detail-import",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .manageCollect.detailImport,
                        meta: {
                            parentMenuName:
                                routesMap.collectTaskManage.taskInfoManage
                                    .manageCollect.list,
                            title: "导入中心",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/manage-collect/import-center/index.vue"
                            ),
                    },
                    {
                        path: "task-info-detail",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .taskInfo.detail,
                        meta: {
                            parentMenuName:
                                routesMap.collectTaskManage.taskInfoManage
                                    .taskInfo.list,
                            title: "任务信息",
                            role: "/tablelist/collect_task/manage",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/task-info/detail/index.vue"
                            ),
                    },
                    {
                        path: "allocation",
                        name: routesMap.collectTaskManage.taskInfoManage
                            .taskInfo.allocation,
                        meta: {
                            parentMenuName:
                                routesMap.collectTaskManage.businessManage,
                            title: "分配任务",
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/task-info-manage/task-info/detail/allocation.vue"
                            ),
                    },

                    {
                        path: "export-task-manage",
                        name: routesMap.collectTaskManage.exportTaskManage,
                        meta: {
                            title: "导出任务管理",
                            role: "/tablelist/collect_export_task/manage",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/export-task-manage/index.vue"
                            ),
                    },

                    {
                        path: "export-task-manage-filter",
                        name: routesMap.collectTaskManage
                            .exportTaskManageFilter,
                        meta: {
                            title: "筛选条件",
                            parentMenuName:
                                routesMap.collectTaskManage.exportTaskManage,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/export-task-manage/filter.vue"
                            ),
                    },
                ],
            },
            // 劳动力信息库管理
            {
                path: "/labor-info-base-manage",
                redirect: "labor-info-base-manage/labor-info-base",
                name: routesMap.laborInfoBaseManage.root,
                meta: {
                    title: "劳动力信息库管理",
                    // svgIcon: require("@/assets/icon/menu2/laborInfoBase.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "labor-info-base",
                        name: routesMap.laborInfoBaseManage.laborInfoBase,
                        meta: {
                            title: "劳动力信息库",
                            role: "/tablelist/collect_user_profile/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/labor-info-base/index.vue"
                            ),
                    },
                    {
                        path: "labor-info-base-filter",
                        name: routesMap.laborInfoBaseManage.laborInfoBaseFilter,
                        meta: {
                            title: "劳动力信息库详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage.laborInfoBase,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/labor-info-base/filter.vue"
                            ),
                    },
                    {
                        path: "labor-info-base-detail",
                        name: routesMap.laborInfoBaseManage.laborInfoBaseDetail,
                        meta: {
                            title: "劳动力信息库详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage.laborInfoBase,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/labor-info-base/detail.vue"
                            ),
                    },

                    {
                        path: "manage-region-incomplete",
                        name: routesMap.laborInfoBaseManage
                            .manageRegionIncomplete,
                        meta: {
                            title: "区划信息不完整",
                            role: "/tablelist/collect_user_profile/manage_region_incomplete",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-region-incomplete/index.vue"
                            ),
                    },

                    {
                        path: "manage-back-home",
                        name: routesMap.laborInfoBaseManage.manageBackHome,
                        meta: {
                            title: "返乡人员信息库",
                            role: "/tablelist/collect_user_profile/manage_back_home",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-back-home/index.vue"
                            ),
                    },
                    {
                        path: "manage-back-home-filter",
                        name: routesMap.laborInfoBaseManage
                            .manageBackHomeFilter,
                        meta: {
                            title: "返乡人员信息查询",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage.manageBackHome,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-back-home/filter.vue"
                            ),
                    },
                    {
                        path: "manage-back-home-detail",
                        name: routesMap.laborInfoBaseManage
                            .manageBackHomeDetail,
                        meta: {
                            title: "返乡人员信息库详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage.manageBackHome,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-back-home/detail.vue"
                            ),
                    },
                    {
                        path: "manage-student",
                        name: routesMap.laborInfoBaseManage.manageStudent,
                        meta: {
                            title: "高校毕业生信息库",
                            role: "/tablelist/collect_user_profile/manage_student",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-student/index.vue"
                            ),
                    },
                    {
                        path: "manage-student-filter",
                        name: routesMap.laborInfoBaseManage.manageStudentFilter,
                        meta: {
                            title: "高校毕业生信息库查询",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage.manageStudent,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-student/filter.vue"
                            ),
                    },
                    {
                        path: "manage-student-detail",
                        name: routesMap.laborInfoBaseManage.manageStudentDetail,
                        meta: {
                            title: "高校毕业生信息库详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage.manageStudent,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-student/detail.vue"
                            ),
                    },
                    {
                        path: "manage-push-province",
                        name: routesMap.laborInfoBaseManage.managePushProvince,
                        meta: {
                            title: "上传数据情况",
                            role: "/tablelist/collect_wait_push_province/manage_push_province",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-push-province/index.vue"
                            ),
                    },
                    {
                        path: "manage-push-provincet-detail",
                        name: routesMap.laborInfoBaseManage
                            .managePushProvinceDetail,
                        meta: {
                            title: "上传数据情况详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage
                                    .managePushProvince,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/manage-push-province/detail.vue"
                            ),
                    },
                ],
            },
            // 劳动力数据分析大屏
            {
                path: "/show-big-screen-report",
                name: routesMap.dataAcquisition.labour,
                meta: {
                    title: "劳动力数据分析大屏",
                    role: "/redirect/xg_project/data_show",
                    // svgIcon: require("@/assets/icon/menu/screen.svg"),

                    newPage: true,
                    targetName: [EnvProject.武汉数采项目].includes(
                        config.envProject
                    )
                        ? "_DataRoomBigScreenPage"
                        : routesMap.bigScreen.report1,
                },
            },
            {
                path: "/labor-upload-manage",
                name: routesMap.laborInfoBaseManage.uploadManage,
                meta: {
                    title: "文件管理",
                    role: "/tablelist/collect_source_data_file/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/upload-manage/index.vue"
                    ),
            },
        ],
    },
]
