<template>
    <Card label="">
        <div class="left-card">
            <div class="title">参会人员统计</div>
            <div class="u-flex num-items">
                <div class="bigger-item static-info">
                    <div class="info-label">总参会人数</div>
                    <scroll-number
                        :value="info['总参会人数']"
                        class="text cus-scale"
                        :dot="false"
                    />
                </div>

                <div class="static-info">
                    <div class="info-label">到场人数</div>
                    <scroll-number
                        :value="info['到场人数']"
                        class="text smaller"
                        :dot="false"
                    />
                </div>

                <div class="static-info">
                    <div class="info-label">男性</div>
                    <scroll-number
                        :value="sexObj.man"
                        class="text smaller"
                        :dot="false"
                    />
                </div>

                <div class="static-info">
                    <div class="info-label">女性</div>
                    <scroll-number
                        :value="sexObj.woman"
                        class="text smaller"
                        :dot="false"
                    />
                </div>
            </div>
        </div>
        <div class="right-card">
            <div class="title">岗位浏览统计</div>
            <div class="u-flex num-items">
                <div class="static-info">
                    <div class="info-label">浏览岗位总数</div>
                    <scroll-number
                        :value="info['浏览岗位总数']"
                        class="text smaller"
                        :dot="false"
                    />
                </div>
                <div class="static-info">
                    <div class="info-label">投递总人次</div>
                    <scroll-number
                        :value="info['投递总人次']"
                        class="text smaller"
                        :dot="false"
                    />
                </div>
            </div>
        </div>
    </Card>
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import Card from "../../common/card.vue"
    import ScrollNumber from "@/views/pages/big-screen/common/number-scroll/scroll-number.vue"

    @Component({ components: { Card, ScrollNumber } })
    export default class Template extends Vue {
        @Prop({ default: {} })
        private info!: any

        private sexObj = {
            man: "50%",
            woman: "50%",
        }

        @Watch("info", { immediate: true })
        private onInfoChange() {
            if (!this.info) {
                return
            }
            const man = (this.info["男性"] || 0) * 1
            const woman = (this.info["女性"] || 0) * 1
            const sum = man + woman || 0
            const rate2Man = Math.round((man / (sum || 1)) * 100)
            const rate2Woman = Math.round((woman / (sum || 1)) * 100)
            this.sexObj = {
                man: rate2Man + "%",
                woman: (rate2Woman ? 100 - rate2Man : 0) + "%",
            }
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 1838px;
        height: 99px;
        display: flex;
        flex-direction: row;

        .left-card {
            width: 50%;
            display: flex;
        }

        .right-card {
            width: 50%;
            display: flex;

            padding-left: 88px;
        }

        .title {
            font-weight: 500;
            font-size: 22px;
            color: #ffffff;
            line-height: 26px;
            margin-right: 136px;
        }
    }

    .text {
        color: #fdc850;
        font-weight: bold;
        line-height: 38px;
        text-align: center;
        height: 38px;
    }

    .card-box {
        display: flex;
        gap: 20px;
        padding-left: 20px;
        padding-right: 20px;
    }

    .cus-scale {
        transform: scale(1);
    }

    .smaller {
        transform: scale(0.7);
    }

    .bigger-item {
        font-size: 18px;
    }

    .static-info {
        display: flex;
        flex-direction: column;
        justify-content: center;

        margin-right: 85px;

        &:first-child {
            margin-right: 100px;
        }

        &:last-child {
            margin-right: 0;
        }

        /deep/ .digit {
            font-size: 20px;
            position: relative;
            top: -3px;
            left: 1px;
        }
    }

    .info-label {
        word-break: keep-all;
        margin-bottom: 2px;
        font-size: 18px;
        color: #89b9ff;
        line-height: 21px;
    }

    .mr20 {
        margin-right: 20px;
    }
</style>
