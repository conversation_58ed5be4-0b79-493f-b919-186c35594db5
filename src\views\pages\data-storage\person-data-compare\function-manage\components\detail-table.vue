<template>
    <div>
        <div class="fill-box">
            <div class="core-ui-table-container u-flex u-row-between">
                <div class="d-flex flex-column table-tabs">
                    <el-tabs v-model="currentPageName">
                        <el-tab-pane
                            v-for="item in tabs"
                            :key="item.label"
                            :label="item.label"
                            :name="item.name"
                        >
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <el-button type="primary" @click="toAdd" v-if="showCompare"
                    >增加比对数据源</el-button
                >
            </div>
            <div v-if="detailId" class="bg-white u-p-b-20" v-loading="loading">
                <table-container
                    v-if="tableConfig"
                    filedWidth="200"
                    ref="table"
                    v-model="tableConfig"
                    class="container"
                    :showExpand="false"
                    :hideFilter="true"
                    @getRows="getRows"
                >
                    <div slot="table" slot-scope="{ data }" class="u-p-20">
                        <common-table :data="data" :columns="columns">
                            <div
                                slot="h"
                                class="u-flex u-row-center"
                                slot-scope="scope"
                            >
                                <el-button
                                    type="text"
                                    v-if="
                                        getShowBtn4List(
                                            scope.row.id,
                                            'update_export_data'
                                        )
                                    "
                                    @click="toEdit(scope.row)"
                                >
                                    编辑
                                </el-button>
                                <el-button
                                    type="text"
                                    v-if="
                                        getShowBtn4List(
                                            scope.row.id,
                                            'delete_data_comparison_export_data'
                                        )
                                    "
                                    @click="deleteRow(scope.row)"
                                >
                                    删除
                                </el-button>
                            </div>
                        </common-table>
                    </div>
                </table-container>
            </div>
        </div>
        <AddDataSourcePop
            :id="detailId"
            :rowId="rowId"
            :compareId="data_comparison_id"
            v-model="showAddPop"
        />
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import {
        buildConfig4RemoteMeta,
        getShowBtn4List,
    } from "@/views/pages/collect-task-manage/components/build-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { DetailListRow } from "../index"
    import { cloneDeep } from "lodash"
    import AddDataSourcePop from "./add-data-source-pop.vue"
    import { MessageBox } from "element-ui"
    import { sdk } from "@/service"

    @Component({
        components: { TableContainer, CommonTable, AddDataSourcePop },
    })
    export default class DetailTable extends BaseTableController<DetailListRow> {
        tableConfig: TableConfig | null = null

        @Prop({ default: "" })
        private readonly detailId!: string

        @Prop({ default: false })
        private showCompare!: boolean

        private columns: TableColumn[] = []

        protected rowId: string | any = ""
        protected data_comparison_id: string | any = ""

        private currentPageName = "common_check_task"
        private listName = "tool_back_list"
        private actionName = ""
        private prefilters: any = {}
        private loading = false
        private showDetailPop = false
        private dataDetailModel = ""
        private dataDetailTitle = ""
        private rows: any[] = []
        private showAddPop = false

        private get tabs() {
            return [
                {
                    label: "比对数据源",
                    name: "common_check_task",
                },
            ]
        }

        created() {
            this.init()
        }

        refresh() {
            this.reloadList()
        }

        private init() {
            return buildConfig4RemoteMeta(
                "data_comparison_export_data",
                "export_data_list",
                {
                    prefilters: { data_comparison_id: this.detailId, is_del: 0 },
                    useLabelWidth: true,
                    optColumn: {
                        label: "操作",
                        prop: "h",
                        fixed: "right",
                        minWidth: "100px",
                    },
                }
            )
                .then((r) => {
                    this.buildConfig(r)
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            tableConfig.predict = {
                ...r.tableConfig.predict,
                func_name: "data_comparison#func_name",
                data_comparison_id: "",
                actions: "actions",
                intents: "intents",
            }
            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            this.columns = r.columns
        }

        private formatNumber(text: string) {
            if (!text) return ""
            return text.replace(/(\d+)/g, '<span style="color: red">$1</span>')
        }

        private getShowBtn4List(id: string, key: string) {
            return getShowBtn4List(this.rows, id, key)
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private toAdd() {
            this.rowId = ""
            this.data_comparison_id = ""
            this.showAddPop = true
        }

        private toEdit(row: any) {
            this.rowId = row.id
            this.data_comparison_id = row.data_comparison_id
            this.showAddPop = true
        }

        deleteRow(row: any) {
            MessageBox.confirm("是否确认删除？", "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        instance.confirmButtonLoading = true
                        sdk.core
                            .model("data_comparison_export_data")
                            .action("delete_data_comparison_export_data")
                            .updateInitialParams({
                                selected_list: [{ id: row.id, v: 0 }],
                                prefilters: [
                                    {
                                        property: "data_comparison_id",
                                        value: row.data_comparison_id,
                                    },
                                    {
                                        property: "is_del",
                                        value: 0,
                                    },
                                ],
                            })
                            .execute()
                            .then(() => {
                                done()
                                this.refreshList()
                            })
                            .finally(() => {
                                instance.confirmButtonLoading = false
                            })
                    }
                    done()
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
</style>
