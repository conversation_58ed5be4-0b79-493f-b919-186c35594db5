<template>
    <Card label="春风行动服务数据统计">
        <div class="top-item">
            <Item label="服务总人次" :value="value" unit="次"></Item>
        </div>
        <div class="stats-container">
            <div class="stats-grid">
                <div
                    class="stat-card"
                    v-for="(stat, index) in items"
                    :key="index"
                >
                    <div class="stat-title">{{ stat.title }}</div>
                    <div class="stat-value">
                        <number-scroll
                            :value="stat.value"
                            :digit-height="38"
                            :digitWidth="18"
                            :duration="1500"
                            :size="36"
                        />
                    </div>
                </div>
            </div>
        </div>
    </Card>
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import Card from "../../common/card.vue"
    import Item from "../../common/item.vue"
    import NumberScroll from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/number-scroll.vue"

    @Component({ components: { Card, Item, NumberScroll } })
    export default class Template extends Vue {
        @Prop({ default: {} })
        private info!: any

        @Watch("info", { immediate: true })
        private onInfoChange() {
            if (!this.info) {
                return
            }
            this.value = this.info["服务总人次"]
            this.items = this.buildInfo()
        }

        private value = 0
        private items = this.buildInfo()

        private buildInfo() {
            return [
                { title: "返乡慰问", value: this.info["返乡慰问"] || 0 },
                { title: "节日祝福", value: this.info["节日祝福"] || 0 },
                {
                    title: "邀请注册居民数",
                    value: this.info["邀请注册居民数"] || 0,
                },
                { title: "岗位推荐", value: this.info["岗位推荐"] || 0 },
                { title: "政策推荐次数", value: this.info["政策推荐次数"] || 0 },
                { title: "培训推荐次数", value: this.info["培训推荐次数"] || 0 },
            ]
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 429px;
        height: 362px;

        /deep/ .number-scroll-container {
            font-weight: 400;
        }

        .top-item {
            padding-left: 30px;
            padding-right: 30px;
        }

        .stats-container {
            padding: 20px;
            padding-left: 0px;
            padding-right: 0px;
            border-radius: 12px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            // gap: 20px;
        }

        .stat-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            padding-left: 5px;
            padding-right: 5px;
            border-radius: 8px;
        }

        .stat-title {
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 8px;
            opacity: 0.9;
            word-break: keep-all;
            white-space: nowrap;
        }

        .stat-value {
            color: #fdc850;
            font-size: 24px;
            font-weight: bold;
        }
    }
</style>
