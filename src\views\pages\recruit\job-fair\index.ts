import { config, EnvProject } from "@/config"
import {
    BuildFormConfig,
    FileType,
    FormType,
    buildSelectSource,
    defaultTimePickerOptions,
} from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
    getStatusLabel,
} from "@/core-ui/component/table"
import { formatDate } from "@/core-ui/helpers/tools"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { formatTime } from "@/utils/tools"
import { pageLoading } from "@/views/controller"
import { MessageBox } from "element-ui"
import moment from "moment"
import { ListTypes } from "uniplat-sdk"
const isJz = [EnvProject.荆州项目].includes(config.envProject)
const tableFilter: TableFilter[] = [
    {
        label: "招聘会名称",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "招聘会类型",
        type: FormType.Select,
        prop: "type",
    },
    {
        label: "所属区域",
        type: FormType.Cascader,
        prop: "place_area_1",
        option: { elProps: { checkStrictly: true } },
    },
    {
        label: "开始时间",
        type: FormType.DatePicker,
        option: {
            type: "daterange",
        },
        prop: "start_time",
    },
    {
        label: "结束时间",
        type: FormType.DatePicker,
        option: {
            type: "daterange",
        },
        prop: "end_time",
    },
    {
        label: "发布状态",
        type: FormType.Select,
        prop: "status",
    },
    {
        label: "主办单位",
        type: FormType.Text,
        prop: "organizer",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "召开状态",
        type: FormType.Select,
        prop: "is_start",
    },
    {
        label: "主题类型",
        type: FormType.Cascader,
        prop: "theme_type",
        option: {
            filterable: true,
        },
    },
    {
        label: "专场类型",
        type: FormType.Select,
        prop: "activity_type",
        option: {
            filterable: true,
        },
        hide: !isJz,
    },
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_province_region_code",
        hide: config.envProject === EnvProject.黄州项目,
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
    {
        label: "是否本级",
        type: FormType.Radio,
        prop: "is_current_mgt_region",
        sourceInputsParameter: buildSelectSource([
            {
                key: "1",
                value: "是",
            },
            {
                key: "",
                value: "否",
            },
        ]),
        hide: !isJz,
    },
    {
        label: "是否夜市招聘",
        type: FormType.Radio,
        prop: "is_evening",
        sourceInputsParameter: buildSelectSource([
            {
                key: "1",
                value: "是",
            },
            {
                key: "",
                value: "否",
            },
        ]),
    },
    {
        label: "招聘会规模",
        type: FormType.Select,
        prop: "job_fair_size",
    },
]

export interface Row {
    id: number
    title: string
    type: string
    theme: string
    theme_type: string
    activity_type: number
    activity_type_label: string
    status: number
    start_time: string
    end_time: string
    sign_qr: string
    site_id: string
    place_detail: string
    place_lat: string
    place_lng: string
    contact_email: string
    contact_mobile: string
    audit_time: string
    audit_status: string
    audit_memo: string
    apply_time: string
    hosted_by: string // 承办
    organizer: string // 主办
    co_organizer: string // 协办
    region_name: string
    update_time: string
    contact_person: string
    contact_office_mobile: string
    description: string
    image_mobile: string
    image_pc: string
    apply_agent_count: string
    booth_image: string
    un_set_booth_company_count: number
    is_booth: boolean
    job_will_count: number
    pub_file_count: number
    live_person_count: number
    make_work_count: number
    is_recommend: boolean
    live_page_url: string
    theme_type_remark: string
    activity_type_remark: string
    go_online_count: string
    [key: string]: any
}

export const predict = {
    title: "",
    type: "label",
    theme: "label",
    theme_type: "label",
    activity_type: "label",
    status: "label",
    start_time: "label",
    end_time: "label",
    sign_qr: "label",
    site_id: "label",
    place_detail: "",
    place_lat: "",
    place_lng: "",
    contact_email: "label",
    contact_mobile: "label",
    contact_person: "label",
    co_organizer: "label",
    audit_time: "label",
    audit_status: "label",
    audit_memo: "label",
    apply_time: "label",
    hosted_by: "label",
    organizer: "label",
    region_name: "last_place#region_name",
    update_time: "label",
    contact_office_mobile: "label",
    description: "",
    image_mobile: "label",
    image_pc: "label",
    apply_agent_count: "",
    place: "last_place#alias",
    booth_image: "",
    un_set_booth_company_count: 0,
    apply_user_count: "label",
    is_booth: false,
    position_online_status: "position#online_status_label",
    agent_sign_qr: "",
    is_booth_apply: "label",
    cal_address_detail: "cal_address_detail",
    company_count: "",
    is_regular: "label",
    activity_area_id: "",
    activity_area_name: "",
    job_fair_id: "id",
    show_status: "label",
    position_count: "label",
    activity_area_address: "activity_area_address",
    job_will_count: "",
    pub_file_count: "",
    live_person_count: "",
    make_work_count: "",
    mgt_province_region_name: "mgt_province#region_name",
    mgt_city_region_name: "mgt_city#region_name",
    mgt_area_region_name: "mgt_area#region_name",
    is_recommend: "",
    live_page_url: "",
    theme_type_remark: "",
    activity_type_remark: "",
    go_online_count: "",
    is_evening: "label",
    job_fair_size: "label",
    place_area_remark3: ""
}
export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("job_fair").list("for_operate"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
        column: columns,
        oneTabFilter: true,
    }
}

export enum AuditStatus {
    草稿 = 0,
    待审核 = 1,
    审核通过 = 2,
    审核未通过 = 3,
}

export enum Status {
    未发布 = 0,
    已发布 = 1,
}

export enum JobFairType {
    网络招聘会 = 1,
    O2O招聘会 = 2,
}

export const columns: TableColumn[] = [
    {
        label: "招聘会名称",
        prop: "title",
        align: "left",
        width: "202px",
        showOverflowTip: true,
    },
    {
        label: "招聘会类型",
        prop: "type_label",
        width: "220px",
        showOverflowTip: true,
    },
    {
        label: "主题类型",
        prop: "theme_type_label",
        align: "left",
        width: "150px",
        showOverflowTip: true,
    },
    {
        label: "专场类型",
        prop: "activity_type_label",
        align: "left",
        width: "150px",
        showOverflowTip: true,
        hide: !isJz,
    },
    {
        label: "招聘会规模",
        prop: "job_fair_size_label",
        align: "left",
        width: "150px",
        showOverflowTip: true,
    },
    {
        label: "召开时间",
        prop: "time",
        width: "140px",
        render(h, row) {
            return h("div", {}, [
                h("div", formatTime.default(row.start_time_label)),
                h("div", formatTime.default(row.end_time_label)),
            ])
        },
        showOverflowTip: true,
    },
    {
        label: "报名截止时间",
        prop: "apply_time_label",
        formatter: (row) => {
            return formatTime.default(row.apply_time_label)
        },
        width: "140px",
        showOverflowTip: true,
    },
    {
        label: "所属区域",
        prop: "place_area_remark3",
        width: "100px",
        showOverflowTip: true,
    },
    {
        label: "管理区域",
        prop: "mgt_region",
        showOverflowTip: true,
        hide: config.envProject === EnvProject.黄州项目,
        formatter(row) {
            return getAddress(row, [
                "mgt_province_region_name",
                "mgt_city_region_name",
                "mgt_area_region_name",
            ])
        },
    },
    {
        label: "主办单位",
        prop: "organizer_label",
        width: "130px",
        showOverflowTip: true,
    },
    { label: "审核状态", prop: "audit_status_label", showOverflowTip: true },
    { label: "发布状态", prop: "status_label", showOverflowTip: true },
    {
        label: "报名企业",
        prop: "apply_agent_count",
        formatter(row) {
            return row.apply_agent_count || "0"
        },
        showOverflowTip: true,
    },
    {
        label: "报名岗位",
        prop: "position_count",
        formatter(row) {
            return row.position_count || "0"
        },
        showOverflowTip: true,
    },
    // {
    //     label: "参会居民",
    //     prop: "apply_user_count",
    //     formatter(row) {
    //         return row.apply_user_count || "0"
    //     },
    //     showOverflowTip: true,
    // },
    // { label: "展位开关", prop: "is_booth", minWidth: "100" },
    {
        label: "召开状态",
        prop: "show_status_label",
        fixed: "right",
        render(h, row) {
            return getStatusLabel(
                h,
                {
                    label: row.show_status_label,
                    value: row.show_status,
                },
                ["#FF8B16", "#65D2A3", "#aaa"]
            )
        },
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "160px",
        align: "left",
        fixed: "right",
        showOverflowTip: true,
    },
]
export function drop(row: Row, reloadList: Function) {
    const isDrop = row.status === Status.已发布
    const label = isDrop ? "取消发布" : "发布"
    const msg = [
        `招聘会名称：${row.title}`,
        `召开时间：${row.start_time_label} - ${row.end_time_label}`,
        isDrop
            ? "取消发布会导致用户和企业看不到该招聘会，确认取消发布吗？"
            : "确认发布？",
    ]
    MessageBox.confirm(msg.join("\n"), label).then(() => {
        pageLoading(() => {
            return sdk.core
                .model("job_fair")
                .action(isDrop ? "off_shelf" : "on_shelf")
                .updateInitialParams({
                    selected_list: [{ v: 0, id: row.id }],
                })
                .execute()
                .then(() => {
                    reloadList()
                })
        })
    })
}

export function submit2Apply(id: string, reloadList: Function) {
    MessageBox.confirm("确认提交审核吗？", "提示").then(() => {
        pageLoading(() => {
            return sdk.core
                .model("job_fair")
                .action("audit_submit")
                .updateInitialParams({
                    selected_list: [{ v: 0, id }],
                })
                .execute()
                .then(() => {
                    reloadList()
                })
        })
    })
}

export function tableConfig2(): TableConfig {
    return {
        model: sdk.core.model("xg_odd_job").list("manage"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
    }
}

export const columns2: TableColumn[] = [
    {
        label: "投递企业名称",
        prop: "sad",
        align: "left",
        width: "202px",
        showOverflowTip: true,
    },
    { label: "投递岗位名称", prop: "da", showOverflowTip: true },
    {
        label: "工作地址",
        prop: "dwa",
        align: "left",
        width: "100px",
        showOverflowTip: true,
    },
    { label: "薪资待遇", prop: "q", width: "120px", showOverflowTip: true },
    { label: "报名时间", prop: "e", showOverflowTip: true },
    { label: "投递状态", prop: "r", width: "130px", showOverflowTip: true },
]
export const itemStyle = {
    color: "#222",
    fontSize: "18px",
    marginLeft: "-70px",
    fontWeight: "600",
}

export const filter = (areas: any = []) => {
    return [
        {
            label: "招聘会名称",
            prop: "name",
            type: FormType.Text,
        },
        {
            label: "统计时间",
            prop: "time",
            type: FormType.DatePicker,
            option: {
                type: "daterange",
                pickerOptions: defaultTimePickerOptions,
            },
            defaultValue: [
                formatDate(+moment().startOf("month")),
                formatDate(+moment().endOf("month")),
            ],
        },
        {
            label: "召开区县",
            prop: "region_code",
            type: FormType.Cascader,
            option: { elProps: { checkStrictly: true } },
            sourceInputsParameter: buildSelectSource(areas),
        },
        [EnvProject.孝感项目].includes(config.envProject) ||
        process.env.VUE_APP_ENV === "test"
            ? {
                  label: "招聘会类型",
                  prop: "type",
                  type: FormType.Select,
                  option: { elProps: { checkStrictly: true } },
                  sourceInputsParameter: buildSelectSource([
                      {
                          key: "2",
                          value: "O2O招聘会",
                      },
                      {
                          key: "1",
                          value: "网络招聘会",
                      },
                      {
                          key: "3",
                          value: "直播招聘会",
                      },
                  ]),
              }
            : {},
    ]
}
