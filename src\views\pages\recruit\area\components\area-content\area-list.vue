<template>
    <div class="bg-white u-m-t-20">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :alwaysShowPageIndex="false"
            :showExpand="false"
            @setTotal="setTotal"
        >
            <div slot="table" slot-scope="{ data }">
                <common-table :data="data" :columns="tableConfig.column">
                    <template slot="h" slot-scope="{ row }">
                        <el-button type="text" @click="edit(row)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="remove(row)">
                            删除
                        </el-button>
                    </template>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { sdk } from "@/service"
    import { MessageBox } from "element-ui"
    import { Component, Prop } from "vue-property-decorator"
    import { Row } from "../.."
    import { tableConfig2 } from "./area-detail"

    @Component({ components: { TableContainer, CommonTable } })
    export default class AreaList extends BaseTableController<{ id: number }> {
        @Prop()
        private area!: Row

        @Prop()
        private currentAreaId!: number

        private tableConfig: TableConfig | null = null

        mounted() {
            this.tableConfig = tableConfig2(this.area.id + "")
        }

        private setTotal(num: number) {
            if (!this.currentAreaId) {
                this.$emit("setTotal", num)
            }
        }

        private edit(row: any) {
            this.$emit("edit", row.id)
        }

        remove(e: any) {
            MessageBox.confirm(
                `是否删除${e.booth_name}：${e.booth_address} ${
                    e.agent_name || ""
                }?`,
                "提示",
                {
                    beforeClose: (action, instance, done) => {
                        if (action === "confirm") {
                            instance.confirmButtonLoading = true
                            sdk.core
                                .model("activity_area_booth_area")
                                .action("delete")
                                .updateInitialParams({
                                    selected_list: [{ id: e.id, v: 0 }],
                                })
                                .execute()
                                .then(() => {
                                    done()
                                    this.$message.success("删除成功")
                                    this.refreshList()
                                })
                                .finally(() => {
                                    instance.confirmButtonLoading = false
                                })
                        } else {
                            done()
                        }
                    },
                }
            )
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
