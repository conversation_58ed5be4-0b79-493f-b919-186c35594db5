<template>
    <div class="detail-container" v-if="row">
        <div class="content u-p-20">
            <div class="title u-m-b-10">注册居民基本信息</div>
            <detail-row-col
                :list="baseList"
                :labelStyle="labelStyle"
            ></detail-row-col>
            <div class="info" v-if="row.is_verified">
                <div class="u-flex u-col-top">
                    <div :style="labelStyle" class="u-m-t-10">关联档案：</div>
                    <div class="profile u-flex-1 u-flex u-col-top">
                        <div class="u-flex-1">
                            <div class="u-flex">
                                <div class="name">
                                    {{ row.real_name }}
                                </div>
                                <div class="u-flex-1 u-flex">
                                    <div>{{ info || "-" }}</div>
                                    <DesensitizationView
                                        v-if="row.profile_mobile"
                                        :value="row.profile_mobile"
                                    ></DesensitizationView>
                                </div>
                            </div>
                            <div>
                                户籍地：{{
                                    row
                                        | city(
                                            [
                                                "household_province_name",
                                                "household_city_name",
                                                "household_area_name",
                                                "household_countryside_name",
                                                "household_village_name",
                                            ],
                                            "--"
                                        )
                                }}
                            </div>
                            <div>
                                常住地：{{
                                    row
                                        | city(
                                            [
                                                "permanent_province_name",
                                                "permanent_city_name",
                                                "permanent_area_name",
                                                "permanent_countryside_name",
                                                "permanent_village_name",
                                            ],
                                            "--"
                                        )
                                }}
                            </div>
                        </div>
                        <div class="u-text-right">
                            <div
                                class="emp"
                                :class="'emp-' + row.employment_status"
                            >
                                {{ row.employment_status_label || "-" }}
                            </div>
                            <div
                                v-if="row.profile_access_key || row.profile_id"
                            >
                                <el-button
                                    type="primary"
                                    size="mini"
                                    plain
                                    @click="
                                        toProfile(
                                            row.profile_access_key ||
                                                row.profile_id
                                        )
                                    "
                                >
                                    查看档案详情
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tags-list u-m-t-10">
                <div class="u-flex u-col-top">
                    <div :style="labelStyle">标签：</div>
                    <div class="tags-list u-flex">
                        <div class="tag" v-for="tag in tags" :key="tag">
                            {{ tag }}
                        </div>
                        <template v-if="row.profile_id">
                            <div class="color-9 u-m-r-10" v-if="!tags.length">
                                暂无标签
                            </div>
                            <div
                                class="primary pointer"
                                @click="editTag()"
                                v-if="!isYD"
                            >
                                编辑标签
                            </div>
                            <div
                                v-if="row.id_card_openid"
                                class="primary pointer u-m-l-10"
                                @click="tagHistory = true"
                            >
                                历史标签
                            </div>
                            <TagHistory
                                v-if="row.id_card_openid"
                                v-model="tagHistory"
                                :key="row.id_card_openid"
                                :id_card_openid="row.id_card_openid"
                            />
                        </template>
                        <div class="color-9 u-m-t-10" v-else>
                            未实名，暂无标签
                        </div>
                    </div>
                </div>
            </div>
            <div class="remark u-m-t-10">
                <div class="u-flex u-col-top">
                    <div :style="labelStyle">备注：</div>
                    <div class="u-flex-1">
                        <el-button
                            type="text"
                            @click="show = true"
                            v-role="['model.user_account.action.addCommit']"
                        >
                            添加备注
                        </el-button>
                        <remark-list
                            ref="list"
                            :id="row.uniplat_uid"
                        ></remark-list>
                    </div>
                </div>
            </div>
        </div>
        <div class="content u-p-20 u-m-t-24" v-show="row.is_verified">
            <div class="title u-m-b-10">居民注册/登录信息</div>
            <detail-row-col
                :list="infoList"
                :labelStyle="labelStyle"
            ></detail-row-col>
        </div>
        <remark-pop
            v-model="show"
            @refresh="refreshList"
            :associate_id="row.uniplat_uid"
        ></remark-pop>
        <set-tag-dialog
            model="user_account"
            action="set_labels"
            v-model="showSetTag"
            :ids="checkEdIds"
            @refresh="refreshDetail"
            :isBatch="false"
        ></set-tag-dialog>
    </div>
</template>

<script lang='ts'>
    import { config, EnvProject } from "@/config"
    import { routesMap } from "@/router/direction"
    import { desensitization } from "@/utils/tools"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import DesensitizationView from "@/views/components/common-comps/desensitization-view.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { forEach } from "lodash"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { Row } from ".."
    import SetTagDialog from "../../seeker-info/components/set-tag.vue"
    import TagHistory from "../../seeker-info/components/tag-history.vue"
    import RemarkList from "./remark-list.vue"
    import RemarkPop from "./remark-pop.vue"

    @Component({
        components: {
            DetailRowCol,
            RemarkList,
            RemarkPop,
            SetTagDialog,
            DesensitizationView,
            TagHistory,
        },
    })
    export default class DetailView extends Vue {
        @Prop()
        private row!: Row

        private show = false
        private showSetTag = false

        private get checkEdIds() {
            return [this.row!.uniplat_uid]
        }

        get tags() {
            const tags = JSON.parse(this.row.labels || "{}")
            const tagArr: string[] = []
            forEach(tags, (v) => {
                tagArr.push(...v)
            })
            return tagArr.filter(Boolean)
        }

        tagHistory = false
        private isYD = config.envProject === EnvProject.宜都项目

        private labelStyle = {
            fontSize: "14px",
            marginRight: "10px",
            lineHeight: "34px",
            color: "#555",
            width: "100px",
            flexShrink: "0",
        }

        private editTag() {
            this.showSetTag = true
        }

        private refreshList() {
            const fn = (this.$refs.list as any).refreshList
            fn && fn()
        }

        private get baseList(): ColItem[] {
            const h = this.$createElement
            return [
                {
                    label: "注册手机号：",
                    // value: this.row.mobile,
                    vNode: renDesensitizationView(h, {
                        value: this.row.mobile,
                    }),
                    span: 8,
                },
                {
                    label: "实名信息：",
                    value: this.row.is_verified ? "已实名" : "未实名",
                    span: 8,
                },
                {
                    label: "实名时间：",
                    value: this.row.verify_time
                        ? this.row.verify_time.replace("T", " ")
                        : "",
                    span: 8,
                    hide: !this.row.is_verified,
                },
            ].filter((e) => !e.hide)
        }

        private get infoList(): ColItem[] {
            return [
                {
                    label: "注册时间：",
                    value: this.row.reg_time_label,
                    span: 8,
                },
                {
                    label: "最近登录时间：",
                    value: this.row.last_login_time_label,
                    span: 8,
                },
                {
                    label: "累计登录次数：",
                    value: this.row.login_times || "0",
                    span: 8,
                },
                {
                    label: "注册渠道：",
                    value: this.row.pc_label,
                    span: 8,
                },
                // {
                //     label: "来源页面：",
                //     value: this.row.pc_ref_id_label,
                //     span: 8,
                // },
                // {
                //     label: "推荐注册人：",
                //     value: this.row.reg_time_label,
                //     span: 8,
                // },
            ]
        }

        private get info() {
            const age = this.row.age ? this.row.age + "岁" : ""
            return [
                this.row.sex_label,
                age,
                desensitization(this.row.id_card, 4),
                this.row.profile_mobile && "档案手机号：",
            ]
                .filter(Boolean)
                .join(" 丨 ")
        }

        private toProfile(id: string) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: { id, from: routesMap.labourManage.userAccountDetail },
            })
        }

        private refreshDetail() {
            console.log("r")
            this.callRefresh(routesMap.labourManage.userAccountDetail)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        .content {
            background: #fff;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
        }
        ::v-deep .detail-row .item {
            line-height: 34px;
            font-size: 14px;
            color: #333;
            margin: 0;
        }
    }
    .info {
        .profile {
            line-height: 34px;
            width: 720px;
            background: #f6f9fe;
            padding: 10px 20px;
            color: #222;
        }
        .name {
            max-width: 120px;
            min-width: 100px;
        }
        .emp {
            color: #3bbc6b;
            &.emp-2 {
                color: #ff8b16;
            }
        }
    }
</style>
