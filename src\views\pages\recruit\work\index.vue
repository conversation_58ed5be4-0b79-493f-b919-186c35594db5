<template>
    <div class="">
        <div class="core-ui-custom-header">
            <div class="title u-flex u-row-between">
                <div>发布共享申请审核</div>
            </div>
            <div class="u-flex u-row-between">
                <el-button type="primary" @click="toAdd()" plain>
                    创建共享信息
                </el-button>
            </div>
        </div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <el-button
                            v-if="scope.row"
                            type="text"
                            @click="toDetail(scope.row)"
                        >
                            详情
                        </el-button>
                        <template v-if="scope.row.status === status.审核通过">
                            <el-button
                                type="text"
                                @click="toggleOnlineStatus(scope.row)"
                            >
                                {{
                                    scope.row.shelf_status ===
                                    shelf_status.已上架
                                        ? "下"
                                        : "上"
                                }}架
                            </el-button>
                        </template>
                        <el-button
                            v-if="
                                scope.row.status === status.审核通过 &&
                                scope.row.shelf_status === shelf_status.未上架
                            "
                            type="text"
                            @click="toAdd(scope.row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-if="scope.row.status === status.待审核"
                            type="text"
                            @click="toAudit(scope.row)"
                        >
                            审核
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <audit-pop
            v-model="showAudit"
            :row="row"
            @refresh="reloadList"
        ></audit-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { Component } from "vue-property-decorator"
    import { updateTagItem } from "../../single-page/components/tags-view"
    import { tableConfig, columns, Row, AuditStatus, ShelfStatus } from "."
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import { sdk } from "@/service"
    import AuditPop from "./components/audit-pop.vue"

    @Component({
        name: routesMap.recruit.work,
        components: { TableContainer, CommonTable, AuditPop },
    })
    export default class Template extends BaseTableController<{ id: number }> {
        tableConfig: TableConfig | null = tableConfig()
        private readonly columns: TableColumn[] = columns
        private showAudit = false
        row: Row | null = null
        private status = AuditStatus
        private shelf_status = ShelfStatus

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.recruit.work,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "发布共享申请审核",
                    to: routesMap.recruit.work,
                },
            ]
            updateTagItem({
                name: routesMap.recruit.work,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.recruit.workDetail,
                query: { id: row.id + "" },
            })
        }

        private toAdd(row?: Row) {
            this.$router.push({
                name: routesMap.recruit.workAdd,
                query: {
                    id: row ? row?.id : (undefined as any),
                    from: routesMap.recruit.work,
                },
            })
        }

        private toggleOnlineStatus(row: Row) {
            const isOnline = row.shelf_status === ShelfStatus.未上架
            const label = isOnline ? "上架" : "下架"
            const action = isOnline ? "shelf" : "unShelf"
            return MessageBox.confirm(
                `${label}后企业${
                    isOnline ? "可" : "无法"
                }看到此信息，确定${label}吗？`,
                label
            )
                .then(() => {
                    pageLoading(() => {
                        return sdk.core
                            .model("share_employee_apply")
                            .action(action)
                            .updateInitialParams({
                                selected_list: [{ v: 0, id: row.id }],
                            })
                            .execute()
                            .then(() => {
                                this.reloadList()
                            })
                    })
                })
                .catch(() => {})
        }

        private toAudit(row: Row) {
            this.row = row
            this.$nextTick(() => {
                this.showAudit = true
            })
        }

        mounted() {
            this.setBreadcrumbs()
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .logo {
        height: 25px;
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
