import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const baseService = [
    {
        path: "/baseService",
        name: routesMap.home.baseService,
        meta: {
            title: "基层服务",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/laborInfoBase.svg"),
            hidden: ![
                EnvProject.黄州项目,
                EnvProject.孝感项目,
                EnvProject.掇刀项目,
            ].includes(config.envProject),
        },
        component: layout,
        children: [
            {
                path: "policy",
                name: routesMap.baseService.policy,
                meta: {
                    title: "政策推广服务",
                    role: "/tablelist/xg_company_position_recommend/manage_apply6_list",
                },
                component: () =>
                    import("@/views/pages/baseService/policy/index.vue"),
            },
            {
                path: "policyDetail",
                name: routesMap.baseService.policyDetail,
                meta: {
                    title: "政策推广详情",
                    parentMenuName: routesMap.baseService.policy,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/baseService/policy/detail.vue"),
            },
            {
                path: "jobFair",
                name: routesMap.baseService.jobFair,
                meta: {
                    title: "招聘会推广服务",
                    role: "/tablelist/xg_company_position_recommend/manage_apply7_list",
                },
                component: () =>
                    import("@/views/pages/baseService/jobFair/index.vue"),
            },
            {
                path: "jobFairDetail",
                name: routesMap.baseService.jobFairDetail,
                meta: {
                    title: "招聘会推广详情",
                    parentMenuName: routesMap.baseService.jobFair,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/baseService/jobFair/detail.vue"),
            },
            {
                path: "grid",
                name: routesMap.baseService.grid,
                meta: {
                    title: "岗位网格推广",
                    role: "/tablelist/xg_company_position_recommend/manage_apply3_list",
                    hidden: [EnvProject.孝感项目].includes(config.envProject),
                },
                component: () => import("@/views/pages/recruit/grid/hz.vue"),
            },
            {
                path: "grid-detail",
                name: routesMap.baseService.gridDetail,
                meta: {
                    title: "网格推广申请详情",
                    hidden: true,
                    parentMenuName: routesMap.baseService.grid,
                },
                component: () =>
                    import("@/views/pages/recruit/grid/hz-detail.vue"),
            },
            {
                path: "service",
                name: routesMap.branch.service.index,
                meta: {
                    title: "服务网点管理",
                    role: "/tablelist/server_stage",
                },
                component: () =>
                    import("@/views/pages/branch/service-branch/index.vue"),
            },
            {
                path: "service-add",
                name: routesMap.branch.service.add,
                meta: {
                    title: "新增网点",
                    hidden: true,
                    parentMenuName: routesMap.branch.service.index,
                },
                component: () =>
                    import("@/views/pages/branch/service-branch/create.vue"),
            },
            {
                path: "service-detail",
                name: routesMap.branch.service.detail,
                meta: {
                    title: "网点详情",
                    hidden: true,
                    parentMenuName: routesMap.branch.service.index,
                },
                component: () =>
                    import("@/views/pages/branch/service-branch/detail.vue"),
            },
            {
                path: "ledger",
                name: routesMap.baseService.ledger,
                meta: {
                    title: "基层服务台账",
                    // role: "/tablelist/xg_company_position_recommend/manage_apply3_list",
                    // hidden: [EnvProject.孝感项目].includes(config.envProject),
                },
                component: () =>
                    import("@/views/pages/baseService/ledger/index.vue"),
            },
            {
                path: "ledger-detail",
                name: routesMap.baseService.ledgerDetail,
                meta: {
                    title: "人社专员详情",
                    hidden: true,
                    parentMenuName: routesMap.baseService.ledger,
                },
                component: () =>
                    import("@/views/pages/baseService/ledger/detail.vue"),
            },
            {
                path: "chart-detail",
                name: routesMap.baseService.ledgerChartDetail,
                meta: {
                    title: "基层服务台账详情",
                    hidden: true,
                    parentMenuName: routesMap.baseService.ledger,
                },
                component: () =>
                    import("@/views/pages/baseService/ledger/chart-detail.vue"),
            },
        ],
    },
]
