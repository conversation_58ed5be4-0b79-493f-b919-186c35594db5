<template>
    <div>
        <div class="fill-box">
            <div class="core-ui-table-container u-flex u-row-between">
                <div class="d-flex flex-column table-tabs">
                    <el-tabs v-model="currentPageName" @tab-click="handleClick">
                        <el-tab-pane
                            v-for="item in tabs"
                            :key="item.label"
                            :label="item.label"
                            :name="item.name"
                        >
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <div
                    class="d-flex"
                    v-if="currentPageName === 'common_check_result'"
                >
                    <el-button
                        type="primary"
                        plain
                        @click="showExportPop = true"
                    >
                        导出结果
                    </el-button>
                </div>
            </div>
            <div v-if="detailId" class="bg-white u-p-b-20" v-loading="loading">
                <table-container
                    v-if="tableConfig"
                    filedWidth="200"
                    ref="table"
                    v-model="tableConfig"
                    class="container"
                    :showExpand="false"
                >
                    <div slot="table" slot-scope="{ data }" class="u-p-20">
                        <common-table :data="data" :columns="columns">
                            <div slot="real_name" slot-scope="scope">
                                <div>{{ scope.row.real_name }}</div>
                                <div>{{ scope.row.mobile_hide }}</div>
                            </div>
                            <div slot="check_result_label" slot-scope="scope">
                                <span
                                    :class="'status-' + scope.row.check_result"
                                    >{{ scope.row.check_result_label }}</span
                                >
                            </div>
                        </common-table>
                    </div>
                </table-container>
            </div>
        </div>
        <ExportPop :id="detailId" v-model="showExportPop" @export="toExport" />
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { buildConfig4RemoteMeta } from "@/views/pages/collect-task-manage/components/build-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { DetailListRow, columns1, columns2 } from "../index"
    import ExportPop from "./export-pop.vue"
    import { sdk } from "@/service"

    @Component({
        components: { TableContainer, CommonTable, ExportPop },
    })
    export default class DetailTable extends BaseTableController<DetailListRow> {
        tableConfig: TableConfig | null = null

        @Prop({ default: "" })
        private readonly detailId!: string

        private columns: TableColumn[] = columns1

        protected rowId: string | any = ""

        private currentPageName = "common_check_result"
        private listName = "back_list"
        private actionName = ""
        private prefilters: any = {}
        private loading = false
        private showExportPop = false

        private get tabs() {
            return [
                {
                    label: "数据核查结果",
                    name: "common_check_result",
                },
                {
                    label: "结果导出记录",
                    name: "common_check_download",
                },
            ]
        }

        created() {
            this.init()
        }

        refresh() {
            this.reloadList()
        }

        private init() {
            return buildConfig4RemoteMeta(this.currentPageName, this.listName, {
                prefilters: { task_id: this.detailId },
                useLabelWidth: true,
                disabledFilter: true,
            })
                .then((r) => {
                    this.buildConfig(r)
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            tableConfig.filter = r.filter.map((item: any) => ({
                ...item,
                label: item.label.replace(/下载/g, "导出"),
            }))
            tableConfig.predict = {
                ...r.tableConfig.predict,
                name: "",
                id_card_hide: "",
                create_time: "label",
                real_name: "user_account#real_name",
                mobile_hide: "user_account#mobile_hide",
                org_name: "xg_login_user#xg_organization#name",
                download_status: "label",
                download_note: "",
                actions: "actions",
            }
            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
        }

        private handleClick() {
            this.loading = true
            if (this.currentPageName === "common_check_download") {
                this.columns = columns2
            } else {
                this.columns = columns1
            }
            return this.init()
        }

        private toExport(data: { download_note: string }) {
            this.exportLoading = true
            this.table
                ?.exportToExcel()
                .then(() => {
                    this.recordDownload(0, data.download_note)
                })
                .catch(() => {
                    this.recordDownload(1, data.download_note)
                })
                .finally(() => {
                    this.exportLoading = false
                })
        }

        private recordDownload(status: number, download_note: string) {
            sdk.getDomainService(
                "common_check_download",
                "back_api",
                "warning_platform"
            )
                .post({
                    task_id: this.detailId,
                    status: status,
                    note: download_note || "",
                })
                .then(() => {
                    this.init()
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    .status-1 {
        color: #d0021b;
    }
</style>
