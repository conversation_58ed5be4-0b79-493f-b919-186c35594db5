<template>
    <div class="u-m-t-10 u-p-l-20">
        <detail-row-col :labelStyle="{ width: '130px' }" :list="detailRow" />
    </div>
</template>

<script lang="ts">
    import { sdk } from "@/service"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import Tag from "@/views/components/tag.vue"
    import { map } from "lodash"
    import { TagManagerTypes } from "uniplat-sdk"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { ServiceTaskRow } from "../../task"
    import JobItem from "./job-item.vue"
    import NotifyDetail from "./notify-detail/index.vue"

    const predict = {
        call_count: "",
        apply_count: "",
        name: "xg_company_position#name",
        address_detail: "xg_company_position#address_detail",
        salary: "xg_company_position#salary",
        company: "xg_company_position#agent#enterprise#name",
        source: "xg_company_position#source_from_type",
        time: "xg_company_position#final_change_time",
        agent_name: "xg_company_position#agent#agent_name",
        age_require: "xg_company_position#age_require",
        education: "xg_company_position#education",
        online_status: "xg_company_position#online_status_label",
        position_id: "xg_company_position#id",
        tags: "tags",
    }

    export interface TaskJobRow {
        call_count: number
        apply_count: number
        name: string
        address_detail: string
        salary: string
        company: string
        source: string
        time: string
        agent_name: string
        age_require: string
        education: string
        position_id: string
        online_status: number
        online_status_label: string
        tags: { [key: string]: TagManagerTypes.TagInfo[] }
    }

    @Component({ components: { DetailRowCol } })
    export default class TaskJob extends Vue {
        @Prop()
        item!: ServiceTaskRow

        private tag = ["已注册", "已实名", "显示咨询按钮"]

        private jobItems: TaskJobRow[] = []

        private get detailRow(): ColItem[] {
            const h = this.$createElement
            const item = this.item
            const d: ColItem[] = [
                {
                    label: "岗位推荐标题",
                    value: item.serve_task_job_recommend_title,
                },
                {
                    label: "岗位推荐描述",
                    value: item.serve_task_job_recommend_content,
                },
                {
                    label: "已推荐岗位",
                    vNode: h(
                        "div",
                        {},
                        this.jobItems.map((i) => {
                            return h(JobItem, {
                                props: { inTaskDetail: true, detail: i },
                            })
                        })
                    ),
                },
                {
                    label: "填写要求",
                    vNode: map(this.tag, (i, index) =>
                        h(Tag, {
                            props: {
                                left: index ? 20 : 0,
                                name: i,
                            },
                        })
                    ),
                },
                {
                    label: "通知配置",
                    vNode: [h(NotifyDetail, { props: { taskId: this.item.id } })],
                },
            ].map((i) => ({ ...i, span: 24 }))
            return d
        }

        created() {
            this.getList()
        }

        private getList() {
            sdk.core
                .model("serve_task_job_recommend_detail")
                .list("for_operate")
                .addPrefilter({
                    recommend_id: this.item.serve_task_job_recommend_id,
                })
                .query({ pageIndex: 1, item_size: 999 })
                .then((res) => {
                    this.jobItems = sdk.buildRows<TaskJobRow>(
                        res.pageData.rows,
                        predict
                    )
                    this.jobItems = this.jobItems.map((i) => {
                        return {
                            ...i,
                            summary: [
                                {
                                    label: "已联系：",
                                    content: (i.call_count || 0) + "人",
                                },
                                {
                                    label: "申请点击：",
                                    content: (i.apply_count || 0) + "人",
                                },
                            ],
                        }
                    })
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
</style>
