<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex" v-if="row">
                <!-- <el-button type="primary" plain @click="confirm()">
                    不通过
                </el-button> -->
                <el-button
                    type="primary"
                    v-if="row.audit_status === 2"
                    @click="showPop = true"
                >
                    审核
                </el-button>
            </div>
        </div>
        <div class="detail-container" v-if="row">
            <div class="content u-flex-1 u-p-x-20 u-p-t-20">
                <template v-for="item in formList">
                    <div class="title" :key="item.title">{{ item.title }}</div>
                    <div class="u-p-20" :key="item.title + 'detail'">
                        <detail-row-col
                            :labelStyle="labelStyle"
                            :list="item.list"
                        ></detail-row-col>
                    </div>
                </template>
                <div class="title u-flex u-row-between">
                    <div>附件信息</div>
                    <el-button @click="download" type="text">下载附件</el-button>
                </div>
                <FileList :id="row.id"></FileList>
            </div>
        </div>
        <common-pop
            v-model="showPop"
            title="审核"
            sdkModel="zc_apply_personal"
            sdkAction="audit_first"
            :id="row && row.id"
            @refresh="close"
        ></common-pop>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../single-page/components/tags-view"
    import { createAuditList } from "./detail"
    import FileList from "./components/file-list.vue"
    import { sleep } from "@/utils"
    import { batchDownload } from "."

    @Component({
        name: routesMap.policyApplyRecordDD.audit,
        components: { DetailRowCol, CommonPop, FileList },
    })
    export default class Template extends Vue {
        private id = ""
        private row: any = null

        breadcrumbs: BreadcrumbItem[] = []

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from as string),
                {
                    label: "去审核",
                },
            ]

            updateTagItem({
                name: routesMap.policyApplyRecordDD.audit,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private get labelStyle() {
            return { width: "126px" }
            // if (+this.$route.query.serviceType === ServeTargetType.居民) {
            //     return { width: "86px" }
            // } else {
            //     return { width: "106px" }
            // }
        }

        refreshConfig = {
            name: routesMap.policyApplyRecordDD.audit,
            fun: this.init,
        }

        mounted() {
            this.init()
        }

        async init(id?: string) {
            await sleep(30)
            this.id = id || (this.$route.query.id as string)
            this.row = null
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("zc_apply_personal")
                    .detail(this.id, "manage_detail")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, {
                            audit_status: "label",
                            memo: "",
                            name: "",
                            mobile: "",
                            id_card: "",
                            id_card_hide: "",
                            town: "town#region_name",
                            province: "province#region_name",
                            city: "city#region_name",
                            area: "area#region_name",
                            education: "label",
                            graduation_school: "",
                            major: "",
                            graduation_date: "label",
                            company_name: "company#company_name",
                            company_access_key: "company#_access_key",
                            company_contact_mobile: "company#contact_mobile",
                            contract_start_date: "label",
                            contract_end_date: "label",
                            bank_card_image: "",
                            bank_card_no: "",
                            bank_name: "",
                            bank_no: "",
                            status: "label",
                            no: "zc_no",
                            nation: "label",
                            personal_type: "label",
                            household_type: "label",
                            sex: "label",
                            household_region_names: "",
                            contact_mobile: "company#contact_mobile",
                        })
                        console.log("row", JSON.parse(JSON.stringify(this.row)))
                    })
            })
        }

        private get formList() {
            if (!this.row) return []
            return createAuditList({
                row: this.row,
                h: this.$createElement,
                type: this.row.no,
            })
        }

        private showPop = false

        close() {
            this.callRefresh(routesMap.policyApplyRecordDD.detail, "true")
            closeCurrentTap()
        }

        download() {
            batchDownload(this.row!.id)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        background: #fff;
        .content {
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
            .label {
                width: 86px;
                color: #555;
            }
        }
        .content-row {
            color: #333;
        }
        /deep/.attachment-template {
            width: 50%;
        }
    }
</style>
