import { TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"

export function detailTableConfig(id: string): TableConfig {
    return {
        model: sdk.core
            .model("xg_indicator_task_instance_ref")
            .list("for_task"),
        defaultPageSize: 99,
        predict: {
            indicator_data: "",
            name: "",
            isEdit: false,
        },
        preFilter: {
            task_id: id,
        },
    }
}
