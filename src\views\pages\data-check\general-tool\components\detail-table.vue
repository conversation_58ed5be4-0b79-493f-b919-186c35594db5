<template>
    <div>
        <div class="fill-box">
            <div class="core-ui-table-container">
                <div class="d-flex flex-column table-tabs">
                    <el-tabs v-model="currentPageName" @tab-click="handleClick">
                        <el-tab-pane
                            v-for="item in tabs"
                            :key="item.label"
                            :label="item.label"
                            :name="item.name"
                        >
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <div v-if="detailId" class="bg-white u-p-b-20" v-loading="loading">
                <table-container
                    v-if="tableConfig"
                    filedWidth="200"
                    ref="table"
                    v-model="tableConfig"
                    class="container"
                    :showExpand="false"
                    @getRows="getRows"
                >
                    <div slot="table" slot-scope="{ data }" class="u-p-20">
                        <common-table :data="data" :columns="columns">
                            <div slot="real_name" slot-scope="scope">
                                <div>{{ scope.row.real_name }}</div>
                                <div>{{ scope.row.mobile_hide }}</div>
                            </div>
                            <div slot="result_note" slot-scope="scope">
                                <span
                                    v-html="formatNumber(scope.row.result_note)"
                                ></span>
                            </div>
                            <div
                                slot="h"
                                class="u-flex u-row-center"
                                slot-scope="scope"
                            >
                                <el-button
                                    type="text"
                                    @click="toDetail(scope.row)"
                                >
                                    详情
                                </el-button>
                            </div>
                        </common-table>
                    </div>
                </table-container>
            </div>
        </div>
        <DataSetDetailPop
            :title="dataDetailTitle"
            :modelName="dataDetailModel"
            v-model="showDetailPop"
        />
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { buildConfig4RemoteMeta } from "@/views/pages/collect-task-manage/components/build-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { DetailListRow, columns, columns2 } from "../index"
    import { routesMap } from "@/router/direction"
    import DataSetDetailPop from "./data-set-detail-pop.vue"

    @Component({
        components: { TableContainer, CommonTable, DataSetDetailPop },
    })
    export default class DetailTable extends BaseTableController<DetailListRow> {
        tableConfig: TableConfig | null = null

        @Prop({ default: "" })
        private readonly detailId!: string

        private columns: TableColumn[] = columns

        protected rowId: string | any = ""

        private currentPageName = "common_check_task"
        private listName = "tool_back_list"
        private actionName = ""
        private prefilters: any = {}
        private loading = false
        private showDetailPop = false
        private dataDetailModel = ""
        private dataDetailTitle = ""
        private originRows: any[] = []

        private get tabs() {
            return [
                {
                    label: "使用记录",
                    name: "common_check_task",
                },
                {
                    label: "关联数据资源",
                    name: "common_check_task_associate",
                },
            ]
        }

        created() {
            this.init()
        }

        refresh() {
            this.reloadList()
        }

        private init() {
            return buildConfig4RemoteMeta(this.currentPageName, this.listName, {
                prefilters: { tool_id: this.detailId },
                useLabelWidth: true,
                optColumn: {
                    label: "操作",
                    prop: "h",
                    fixed: "right",
                    minWidth: "150px",
                },
            })
                .then((r) => {
                    this.buildConfig(r)
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            tableConfig.predict = {
                ...r.tableConfig.predict,
                org_name: "xg_login_user#xg_organization#name",
                real_name: "user_account#real_name",
                mobile_hide: "user_account#mobile_hide",
                table_display: "common_source#table_display",
                record_count: "",
                note: "common_source#note",
                access_key: "_access_key",
                actions: "actions",
                intents: "intents",
            }
            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            this.originRows = rows
        }

        private toDetail(row: any) {
            if (this.currentPageName === "common_check_task_associate") {
                const currentRow = this.originRows.find(
                    (i) => +i.id.value === +row.id
                )
                const t = currentRow.intents.find(
                    (i: any) => i.action === "showList"
                )
                this.dataDetailModel = t?.model
                this.dataDetailTitle = row.table_display
                return (this.showDetailPop = true)
            }
            this.$router.push({
                name: routesMap.dataCheck.generalDetail,
                query: { id: row.access_key, from: this.$route.name },
            })
        }

        private handleClick() {
            this.loading = true
            if (this.currentPageName === "common_check_task_associate") {
                this.listName = "back_list"
                this.columns = columns2
            } else {
                this.listName = "tool_back_list"
                this.columns = columns
            }
            return this.init()
        }

        private formatNumber(text: string) {
            if (!text) return ""
            return text.replace(/(\d+)/g, '<span style="color: red">$1</span>')
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
</style>
