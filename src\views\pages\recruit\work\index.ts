import { BuildFormConfig, FileType, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import { expiateTimePickerOptions } from "../job/components/detail"

const tableFilter: TableFilter[] = [
    {
        label: "所属企业",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "类型",
        type: FormType.Select,
        prop: "type",
    },
    {
        label: "上架状态",
        type: FormType.Select,
        prop: "shelf_status",
    },
    {
        label: "申请时间",
        type: FormType.DatePicker,
        option: { type: "datetimerange" },
        prop: "update_time",
    },
]
export enum AuditStatus {
    待审核 = 0,
    审核通过 = 1,
    审核不通过 = 2,
}

export enum ShelfStatus {
    未上架 = 0,
    已上架 = 1,
}
export interface Row {
    id: number
    create_time: string
    agent_name: string
    type: string
    person_num: string
    contact_person: string
    contact_number: string
    apply_description: string
    status: AuditStatus
    shelf_status: ShelfStatus
    final_change_time: string
    address_detail: string
    region: string
    images: string
    shelf_user: string
    shelf_time: string
    audit_user: string
    review_comment: string
    review_time: string
    expired_date: string
    [key: string]: any
}

export const predict = {
    create_time: "label",
    agent_name: "agentRef#agent_name",
    type: "label",
    person_num: "label",
    contact_person: "label",
    contact_number: "label",
    apply_description: "label",
    status: "label",
    shelf_status: "label",
    final_change_time: "label",
    address_detail: "agentRef#address_detail_label",
    region: "label",
    images: "label",
    shelf_user: "shelf_by#systemUser.username",
    shelf_time: "label",
    audit_user: "review_by#systemUser.username",
    review_comment: "label",
    review_time: "label",
    mobile_hide: "",
    expired_date: "label",
}
export function tableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("share_employee_apply")
            .list("list_for_workbench"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
        oneTabFilter: true,
    }
}

export const columns: TableColumn[] = [
    {
        label: "所属企业",
        minWidth: "220",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "类型",
        minWidth: "100",
        prop: "type_label",
        showOverflowTip: true,
    },
    {
        label: "涉及人数",
        minWidth: "100",
        prop: "person_num_label",
        showOverflowTip: true,
    },
    {
        label: "描述",
        minWidth: "120",
        prop: "apply_description",
        showOverflowTip: true,
    },
    {
        label: "所在地",
        prop: "address_detail_label",
        minWidth: "150",
        showOverflowTip: true,
    },
    {
        label: "联系人",
        minWidth: "100",
        prop: "contact_person",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        minWidth: "120",
        prop: "mobile_hide",
        showOverflowTip: true,
    },
    {
        label: "有效期",
        minWidth: "140",
        prop: "expired_date_label",
        formatter: (row) => formatTime.day(row.expired_date),
        showOverflowTip: true,
    },
    {
        label: "审核状态",
        minWidth: "100",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "上架状态",
        minWidth: "100",
        prop: "shelf_status_label",
        showOverflowTip: true,
    },
    {
        label: "申请时间",
        minWidth: "140",
        prop: "final_change_time_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        minWidth: "100",
        fixed: "right",
        prop: "h",
        showOverflowTip: true,
    },
]

const intentPredict = {
    agent_name: "",
    legal_person: "",
    province: "province#region_name",
    city: "city#region_name",
    area: "area#region_name",
    address_detail: "label",
    contact_person: "",
    contact_mobile: "",
    company_code: "",
}
const intentColumn: TableColumn[] = [
    { label: "企业名称", prop: "agent_name", showOverflowTip: true },
    { label: "统一社会信用代码", prop: "company_code", showOverflowTip: true },
    { label: "法人", prop: "legal_person", showOverflowTip: true },
    { label: "联系人", prop: "a", showOverflowTip: true },
    {
        label: "地址",
        prop: "city",
        render(h, row) {
            return h("span", {}, getAddress(row))
        },
        showOverflowTip: true,
    },
]

export function createFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "share_employee_apply",
        sdkAction: id ? "workbenchEdit" : "addNewApply",
        id,
        forms: [
            {
                label: "所属企业",
                type: FormType.IntentSearch,
                prop: "agent_id",
                required: true,
                option: {
                    disabled: !!id,
                    dialogProp: { width: "1000px" },
                    intentSearchConfig: {
                        tableConfig: {
                            model: sdk.core
                                .model("xg_agent")
                                .list("intent_search_list"),
                            preFilter: {},
                            predict: intentPredict,
                            column: intentColumn,
                            filter: [
                                {
                                    label: "企业名称",
                                    prop: "agent_name",
                                    type: FormType.Text,
                                    keyValueFilter: {
                                        match: ListTypes.filterMatchType.fuzzy,
                                    },
                                },
                            ],
                        },
                        template: "{agent_name}",
                        valueKey: "id",
                    },
                },
            },
            {
                label: "类型",
                type: FormType.Select,
                prop: "type",
                required: true,
            },
            {
                label: "涉及人数",
                type: FormType.Text,
                option: { type: "number" },
                prop: "person_num",
                required: true,
            },
            {
                label: "所在地",
                type: FormType.Cascader,
                option: { elProps: { checkStrictly: true } },
                prop: "region",
                required: true,
                col: {
                    span: 13,
                },
            },
            {
                labelWidth: "10px",
                type: FormType.Text,
                prop: "address",
                col: {
                    span: 11,
                },
            },
            {
                label: "联系人",
                type: FormType.Text,
                prop: "contact_person",
                required: true,
            },
            {
                label: "联系方式",
                type: FormType.Text,
                prop: "contact_number",
                required: true,
            },
            {
                label: "有效期",
                type: FormType.DatePicker,
                option: {
                    type: "date",
                    pickerOptions: expiateTimePickerOptions,
                },
                prop: "expired_date",
                required: true,
            },
            {
                label: "描述",
                type: FormType.Text,
                option: {
                    type: "textarea",
                    rows: 4,
                    resize: "none",
                },
                prop: "apply_description",
                required: true,
            },
            {
                label: "上传图片",
                type: FormType.MyUpload,
                prop: "images",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式,最多支持三张",
                    limit: 3,
                    limitSize: 3072,
                },
            },
        ],
    }
}
