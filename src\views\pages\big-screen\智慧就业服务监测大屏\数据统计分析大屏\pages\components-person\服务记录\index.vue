<template>
    <div class="content">
        <Header title="服务记录"></Header>
        <Tables
            :defaultDisableCanClick="true"
            :titles="titles"
            :data="tableData"
            :loading="loading"
        ></Tables>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Watch } from "vue-property-decorator"
    import Header from "../../../common/header.vue"
    import Tables from "../../../common/tables.vue"
    import BaseItem from "../../../common/base-item"
    import { sdk } from "@/service"
    import { RecordType } from "@/views/pages/labour-manage/seeker-info/components/detail"
    import { exportPdfService } from "@/views/common/exportPdf"

    @Component({ components: { Header, Tables } })
    export default class Index extends BaseItem {
        @Watch("row", { immediate: true, deep: true })
        private onRowChanged() {
            if (!this.row) {
                return
            }
            this.getList()
        }

        private titles = ["服务类型", "服务名称", "服务状态", "服务时间"]

        private loading = false

        private tableData: string[][] = []

        private getList() {
            if (!this.row) {
                return
            }
            const id_card_openid = this.row.id_card_openid
            this.loading = true
            sdk.core
                .model("task_serve_record")
                .list("for_object")
                .addPrefilter({
                    "task_serve_target.id_card_openid": id_card_openid,
                })
                .query({
                    pageIndex: 1,
                    item_size: 99,
                })
                .then((r) => {
                    const rows: any[] = sdk.buildRows(r.pageData.rows, {
                        serve_type: "serve_project#serve_type_label",
                        p_name: "serve_task#p_name",
                        status: "label",
                        update_time: "label",
                        create_time: "label",
                        from_id: "xg_candidate_order#created_from_id",
                        record_type: "record_type",
                        record_title: "record_title",
                        related_id: "related_id",
                        job_fair_title: "job_fair#title",
                    })
                    this.tableData = []
                    rows.forEach((i) => {
                        let serve_type_label = i.serve_type_label
                        let p_name = i.p_name
                        if (i.record_type === RecordType.人才推荐记录) {
                            serve_type_label = "人才推荐详情"
                            p_name = i.record_title
                        }
                        if (i.record_type === RecordType.招聘会) {
                            serve_type_label = "招聘会"
                            p_name = i.job_fair_title
                        }
                        this.tableData.push([
                            serve_type_label || "--",
                            p_name,
                            i.status_label,
                            i.update_time_label,
                        ])
                    })
                    exportPdfService.pushItem({
                        title: "服务记录",
                        order: 2.5,
                        list: this.tableData,
                        listHeader: this.titles,
                        type: "list",
                    })
                })
                .finally(() => {
                    this.loading = false
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .content {
        width: 580px;
        height: 266px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;

        /deep/ .header-content {
            height: 40px;
        }

        /deep/ .table-box {
            margin-top: 6px;

            .line-box {
                height: 170px;
            }

            .line {
                .row {
                    width: 50%;
                }
            }

            .empty-img {
                display: none;
            }
        }
    }
</style>
