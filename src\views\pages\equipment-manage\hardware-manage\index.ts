import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"
import { BatchPop } from "@/views/components/batch-pop"
import { Message, MessageBox } from "element-ui"
import { formatTime } from "@/utils/tools"

const rowPredict = {
    device_name: "",
    brand: "device_brand#brand",
    status: "label",
    status_now: "bind_info#status_now_label",
    update_time: "label",
    create_time: "label",
    access_key: "_access_key",
    region_name: "bind_info#full_region_name_display",
    device_type: "",
    device_sn: "",
}

export const enum Status {
    停用 = 0,
    启用 = 1,
}

export const enum StatusNow {
    未知 = -1,
    离线 = 0,
    在线 = 1,
}
export interface Row {
    device_name: string
    brand: string
    status: Status
    status_label: string
    status_now: StatusNow
    status_now_label: string
    update_time: string
    create_time: string
    access_key: string
    region_name: string
    device_type: number
    device_sn: string
    id: number
    v: number
}

const tableFilter: TableFilter[] = [
    {
        prop: "device_name",
        label: "设备名称",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "brand",
        label: "设备品牌",
        type: FormType.Select,
    },
    {
        prop: "device_sn",
        label: "设备激活码",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "status",
        label: "使用状态",
        type: FormType.Radio,
    },
    {
        prop: "status_now",
        label: "在线状态",
        type: FormType.Radio,
    },
]
const column: TableColumn[] = [
    {
        type: "selection",
        prop: "select",
    },
    {
        label: "设备名称",
        prop: "device_name",
        minWidth: "120px",
        showOverflowTip: true
    },
    {
        label: "设备激活码",
        prop: "device_sn",
        minWidth: "160px",
        showOverflowTip: true
    },
    {
        label: "设备品牌",
        prop: "brand",
        minWidth: "120px",
    },
    {
        label: "使用状态",
        prop: "status_label",
        minWidth: "90px",
    },
    {
        label: "在线状态",
        prop: "status_now_label",
        minWidth: "90px",
    },
    {
        label: "所在区域",
        prop: "region_name",
        minWidth: "120px",
        showOverflowTip: true
    },
    {
        label: "更新时间",
        prop: "update_time",
        minWidth: "120px",
        formatter: (row) => {
            return formatTime.seconds(row.update_time)
        },
        showOverflowTip: true
    },
    {
        label: "创建时间",
        prop: "create_time",
        minWidth: "120px",
        formatter: (row) => {
            return formatTime.seconds(row.create_time)
        },
        showOverflowTip: true
    },
    {
        label: "操作",
        prop: "h",
        minWidth: "130px",
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("device_base_info").list("for_operate"),
        filter: tableFilter,
        predict: rowPredict,
        column,
    }
}

export function batchDelete(ids: any[], rows: any[]) {
    if (!ids.length) {
        Message.error("请选择数据")
        throw new Error()
    }
    let errorList: any = []
    const selected_list = ids.map((i) => ({ id: i, v: 0 }))
    const model = sdk.core
        .model("device_base_info")
        .action("batch_delete_device")
    return new Promise((resolve, reject) => {
        MessageBox.confirm("请确认是否删除？", "提示", {
            beforeClose: (action, instance, done) => {
                if (action === "confirm") {
                    instance.confirmButtonLoading = true
                    model
                        .updateInitialParams({
                            selected_list,
                            prefilters: [
                                {
                                    property: "device_type",
                                    value: rows[0].device_type + "",
                                },
                            ],
                        })
                        .query()
                        .then(() => {
                            return model
                                .executeEach({
                                    selected_list,
                                    inputs_parameters: [],
                                } as any)(
                                    () => {},
                                    (errorRow) => {
                                        errorList = errorRow
                                    }
                                )
                                .awaiting.then(() => {
                                    BatchPop.use({
                                        list: selected_list,
                                        errorList,
                                        columns: [
                                            {
                                                label: "id",
                                                prop: "id",
                                            },

                                            {
                                                label: "错误原因",
                                                prop: "error",
                                                width: "120px",
                                                showOverflowTip: true,
                                            },
                                        ],
                                    })
                                    if (!errorList.length) {
                                        done()
                                        resolve(0)
                                    } else {
                                        reject()
                                    }
                                })
                                .catch((e) => {
                                    reject()
                                    Message.error(e)
                                    return Promise.reject(e)
                                })
                        })
                        .finally(() => {
                            instance.confirmButtonLoading = false
                        })
                } else {
                    done()
                    return Promise.reject()
                }
            },
        })
    })
}
