<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="list-container"
            :showExpand="false"
            :defaultFilterIsExpand="true"
            @getData="loadingChange"
        >
            <div slot="table" slot-scope="{ data }">
                <common-table
                    :emptyText="emptyText"
                    :data="data"
                    :columns="columns"
                >
                    <div
                        slot="h"
                        class="u-flex u-row-center handler-btn"
                        slot-scope="scope"
                    >
                        <div @click="toDetail(scope.row)">详情</div>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { FormType } from "@/core-ui/component/form"
    import { TableConfig, TableFilter } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Mixins } from "vue-property-decorator"
    import { getAddress } from "@/utils"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/数据统计分析大屏/common/base-item"
    import { config } from "@/config"
    import {
        maskId,
        maskMobile,
        maskName,
    } from "../../../智慧就业检测大屏/common/tools"

    @Component({ components: { TableContainer, CommonTable } })
    export default class Template extends Mixins(BaseTableController, BaseItem) {
        tableConfig: TableConfig | null = null

        defaultCode = process.env.VUE_APP_DEFAULT_REGION_CODE

        private loading = true

        private loadingChange() {
            this.loading = false
        }

        private get emptyText() {
            return this.loading ? "加载中..." : "暂无数据"
        }

        private get tableFilter(): any[] {
            return [
                {
                    label: "用户信息：",
                    type: FormType.Text,
                    prop: "name",
                },
                {
                    label: "户籍地址：",
                    type: FormType.Cascader,
                    prop: "household_province",
                    option: {
                        filterable: true,
                        elProps: { checkStrictly: true },
                    },
                },
                {
                    label: "常住地：",
                    type: FormType.Cascader,
                    prop: "permanent_province",
                    option: {
                        filterable: true,
                        elProps: { checkStrictly: true },
                    },
                },
            ]
        }

        mounted() {
            this.tableConfig = {
                model: sdk.core
                    .model("person_return_exanchange")
                    .list("big_screen_list"),
                filter: this.tableFilter,
                defaultPageSize: 10,
                predict: {
                    name: "name",
                    mobile: "mobile",
                    id_card: "id_card",
                    region_name_household_province:
                        "household_province#region_name",
                    region_name_household_city: "household_city#region_name",
                    region_name_household_area: "household_area#region_name",
                    region_name_household_countryside:
                        "household_countryside#region_name",
                    region_name_household_village: "household_village#region_name",
                    region_name_permanent_province:
                        "permanent_province#region_name",
                    region_name_permanent_city: "permanent_city#region_name",
                    region_name_permanent_area: "permanent_area#region_name",
                    region_name_permanent_countryside:
                        "permanent_countryside#region_name",
                    region_name_permanent_village: "permanent_village#region_name",
                    education: "education",
                    profile_access_key: "profile#_access_key",
                    access_key: "_access_key",
                    broker_name: "broker_name",
                },
            }
        }

        private readonly columns: any[] = [
            {
                prop: "name",
                label: "姓名",
                formatter(row: any) {
                    return maskName(row.name)
                },
            },
            {
                label: "手机号",
                prop: "mobile",
                formatter: (row: any) => {
                    return maskMobile(row.mobile)
                },
                showOverflowTip: true,
            },
            {
                label: "身份证号",
                prop: "id_card",
                formatter: (row: any) => {
                    return maskId(row.id_card)
                },
            },
            {
                label: "户籍地",
                prop: "household",
                showOverflowTip: true,
                render(h: any, row: any) {
                    return h(
                        "div",
                        {
                            class: "pre-line u-text-center u-line-1",
                        },
                        [
                            h(
                                "span",
                                { class: "u-line-1" },
                                getAddress(row, [
                                    "region_name_household_province",
                                    "region_name_household_city",
                                    "region_name_household_area",
                                    "region_name_household_countryside",
                                    "region_name_household_village",
                                ]) || "-"
                            ),
                        ]
                    )
                },
            },
            {
                label: "常住地",
                prop: "permanent",
                showOverflowTip: true,
                render(h: any, row: any) {
                    return h(
                        "div",
                        {
                            class: "pre-line u-text-center u-line-1",
                        },
                        [
                            h(
                                "span",
                                { class: "u-line-1" },
                                getAddress(row, [
                                    "region_name_permanent_province",
                                    "region_name_permanent_city",
                                    "region_name_permanent_area",
                                    "region_name_permanent_countryside",
                                    "region_name_permanent_village",
                                ]) || "-"
                            ),
                        ]
                    )
                },
            },
            {
                label: "学历",
                prop: "education",
            },
            {
                label: "包保人",
                prop: "broker_name",
            },
            {
                label: "包保负责人",
                defaultValue: "--",
            },
            {
                label: "乡镇分管领导",
                defaultValue: "--",
            },
            {
                label: "对口就业专员",
                defaultValue: "--",
            },
            {
                label: "操作",
                prop: "h",
            },
        ]

        toDetail(row: any) {
            this.$router.push({
                name: routesMap.bigScreen.dataStatisticsAnalysis.personDetail,
                query: {
                    id: row.access_key || "",
                    profile_id: row.profile_access_key || "",
                    type: (this.$route.query?.type as string) || "",
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "./table.less";
    /deep/ .table {
        background: transparent !important;
    }
</style>
