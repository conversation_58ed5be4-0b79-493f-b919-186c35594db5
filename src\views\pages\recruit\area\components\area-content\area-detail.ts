import { TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"
export interface AreaRow {
    booth_name: string
    booth_address: string
    id: number
    v: number
}

export function tableConfig(activity_area_id: string): TableConfig {
    return {
        model: sdk.core.model("activity_area_booth").list("for_operate"),
        defaultPageSize: 10,
        predict: { booth_name: "", booth_address: "" },
        preFilter: {
            activity_area_id,
        },
        column: [
            { label: "序号", prop: "id" },
            { label: "展区编号", prop: "booth_name" },
            { label: "展区名称", prop: "booth_address" },
            { label: "操作", prop: "h" },
        ],
    }
}

export function tableConfig2(activity_area_id: string): TableConfig {
    return {
        model: sdk.core.model("activity_area_booth_area").list("for_operate"),
        defaultPageSize: 10,
        predict: {
            booth_name: "activity_area_booth#booth_name",
            booth_address: "activity_area_booth#booth_address",
            area_number: "",
        },
        preFilter: {
            activity_area_id,
        },
        column: [
            {
                label: "展区名称",
                prop: "1",
                minWidth: "120",
                formatter: (row) => {
                    return `${row.booth_name}：${row.booth_address}`
                },
            },
            {
                label: "展位号",
                prop: "area_number",
                minWidth: "120",
            },
            {
                label: "操作",
                prop: "h",
                minWidth: "120",
            },
        ],
    }
}
