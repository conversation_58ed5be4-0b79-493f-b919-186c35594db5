<template>
    <div class="content" v-if="row">
        <div class="title u-m-b-20">任务完成情况</div>
        <div class="list">
            <div
                class="item line1"
                :class="{ pointer: isRoute(item.value) }"
                v-for="item in list"
                :key="item.title"
                @click="toRoute(item.value)"
            >
                <div class="u-font-32 num">
                    {{ row[item.value] || 0 }}
                </div>
                <div class="u-font-16 label u-m-t-12">{{ item.label }}</div>
                <el-button
                    type="primary"
                    plain
                    size="mini"
                    v-if="item.value === 'task_count' && !isEnd"
                    class="add-company custom-btn u-m-t-4"
                    @click="addCompanyIntent = true"
                    >添加企业</el-button
                >
            </div>
        </div>
        <add-company
            v-model="addCompanyIntent"
            @refresh="refreshList"
            :selected="[{ id: row.id, v: row.v }]"
            v-if="row"
        />
    </div>
</template>

<script lang='ts'>
    import { Component, Prop, Vue } from "vue-property-decorator"
    import AddCompany from "./add-company.vue"
    import { DetailRow, Status } from ".."

    @Component({ components: { AddCompany } })
    export default class WorkInfoApplyStatics extends Vue {
        @Prop()
        private row!: DetailRow

        private get isEnd() {
            return this.row.status === Status.已结束
        }

        private get list() {
            return [
                {
                    label: "目标填报企业",
                    value: "task_count",
                },
                {
                    label: "已填报的企业",
                    value: "task_fill_count",
                },
                {
                    label: "未填报的企业数量",
                    value: "task_notfill_count",
                },
                {
                    label: "未通过的企业数量",
                    value: "task_notpass_count",
                },
                {
                    label: "已填报的用工数量",
                    value: "yonggong_fill_count",
                },
                {
                    label: "待核查的用工数量",
                    value: "yonggong_audit_count",
                },
                {
                    label: "核查通过的用工数量",
                    value: "yonggong_audit_pass_count",
                },
                {
                    label: "核查不通过的用工数量",
                    value: "yonggong_audit_notpass_count",
                },
            ]
        }

        private addCompanyIntent = false

        private isRoute(value: string) {
            return (
                value === "task_fill_count" ||
                value === "task_notfill_count" ||
                value === "task_notpass_count" ||
                value === "yonggong_fill_count" ||
                value === "yonggong_audit_count" ||
                value === "yonggong_audit_pass_count" ||
                value === "yonggong_audit_notpass_count"
            )
        }

        private toRoute(value: string) {
            if (
                value === "task_fill_count" ||
                value === "task_notpass_count" ||
                value === "task_notfill_count"
            ) {
                let status = [""]
                if (value === "task_fill_count") {
                    status = ["1", "2", "3"]
                } else if (value === "task_notpass_count") {
                    status = ["2"]
                } else if (value === "task_notfill_count") {
                    status = ["0"]
                }
                return this.$emit("toCompany", status)
            }
            if (
                value === "yonggong_fill_count" ||
                value === "yonggong_audit_count" ||
                value === "yonggong_audit_pass_count" ||
                value === "yonggong_audit_notpass_count"
            ) {
                let status = [""]
                if (value === "yonggong_fill_count") {
                    status = ["0", "1", "2"]
                } else if (value === "yonggong_audit_pass_count") {
                    status = ["1"]
                } else if (value === "yonggong_audit_notpass_count") {
                    status = ["2"]
                } else if (value === "yonggong_audit_count") {
                    status = ["0"]
                }
                return this.$emit("toRecord", status)
            }
        }

        private refreshList() {
            this.callRefresh("refreshWorkInfoApplyDetail")
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        background-color: #fff;
        padding: 20px;
        margin-top: 24px;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
    }

    .item {
        height: 120px;
        background: #f4f4ff;
        border-radius: 8px;
        text-align: center;
        padding-top: 24px;
        &.pointer {
            cursor: pointer;
        }
        &:nth-child(n + 5) {
            background: #ebf9f9;
            .num {
                color: #32b2b2;
            }
        }
        .num {
            height: 32px;
            font-size: 32px;
            font-weight: 600;
            color: #6068e0;
        }
        .label {
            font-size: 16px;
            color: #333333;
        }
        .add-company {
            width: 60px;
            height: 20px;
        }
    }
    .list {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 19px;
    }
</style>
