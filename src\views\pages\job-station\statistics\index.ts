import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { PassportTokenController } from "@/core-ui/service/passport/token"
import { getBkKey } from "@/encrypt"
import { app } from "@/main"
import { sdk } from "@/service"
import { decryptAesString } from "@/utils/tools"
import Axios from "axios"
import { debounce, get } from "lodash"
import { ListTypes } from "uniplat-sdk"
export const list = {
    jobStation: [
        { title: "零工驿站总数", prop: "position_create_num" },
        { title: "累计注册零工用户数", prop: "LG" },
        { title: "累计注册雇主用户数", prop: "GZ" },
        { title: "累计零工发布求职数", prop: "QZ" },
        { title: "累计雇主发布岗位数", prop: "GG" },
        {
            title: "空闲求职者数",
            prop: "KXQZ",
            info: "统计当前零工接单状态为空闲中的人数",
        },
        { title: "有效招工岗位数", prop: "YXZG" },
        {
            title: "达成意向总数",
            prop: "DC",
            info: "统计零工/雇主确认合适达成雇佣关系总数",
        },
        {
            title: "确认完工总数",
            prop: "WG",
            info: "统计雇主确认完工的总数",
        },
    ],
    recommend: [
        {
            title: "使用用工推荐驿站数",
            prop: "tjyz",
            info: "统计驿站进行了用工推荐的总数",
        },
        { title: "累计用工推荐次数", prop: "tjcs" },
        { title: "累计推送零工人数", prop: "tjlg" },
        { title: "用工推荐达成意向数", prop: "tjdc" },
        { title: "用工推荐确认完工数", prop: "tjwg" },
    ],
    share: [
        {
            title: "使用共享调度驿站数",
            prop: "gxyz",
            info: "统计进行发起共享、接收/拒绝共享的驿站数",
        },
        { title: "发起共享岗位总数", prop: "fqgw" },
        { title: "发起共享零工总数", prop: "fqlg" },
        { title: "接受共享岗位总数", prop: "jsgw" },
        { title: "接受共享零工总数", prop: "jslg" },
    ],
}

export const tableFilter1: TableFilter[] = [
    // {
    //     label: "驿站名称",
    //     prop: "UCE385REMARK",
    //     type: FormType.Text,
    //     keyValueFilter: {
    //         match: ListTypes.filterMatchType.fuzzy,
    //     },
    //     col: { span: 8 },
    // },
    {
        label: "地区",
        type: FormType.Select,
        prop: "AAB301",
        defaultValue: "",
    },
    {
        label: "统计开始时间",
        prop: "UCK012_S",
        type: FormType.DatePicker,
        option: {
            type: "date",
        },
    },
    {
        label: "统计结束时间",
        prop: "UCK012_E",
        type: FormType.DatePicker,
        option: {
            type: "date",
        },
    },
]

export const predict1 = {}
export const columns1: TableColumn[] = [
    { label: "驿站名称", prop: "YZ", align: "center", showOverflowTip: true },
    {
        label: "零工发布求职数",
        prop: "QZS",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "雇主发布岗位数",
        prop: "GWS",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "有效岗位数",
        prop: "YXGW",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "达成意向数",
        prop: "DCYX",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "确认完工数",
        prop: "QRWG",
        align: "center",
        showOverflowTip: true,
    },
]
export function tableConfig1(): TableConfig {
    return {
        model: sdk.core.model("xg_odd_job").list("manage"),
        filter: tableFilter1,
        defaultPageSize: 10,
        predict: predict1,
        column: columns1,
    }
}

export const predict2 = {}
export const columns2: TableColumn[] = [
    { label: "驿站名称", prop: "YZ", align: "center", showOverflowTip: true },
    {
        label: "用工推荐次数",
        prop: "TJCS",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "推荐零工人数",
        prop: "TJLG",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "达成意向数",
        prop: "DCYX",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "确认完工数",
        prop: "QRWG",
        align: "center",
        showOverflowTip: true,
    },
]
export function tableConfig2(): TableConfig {
    return {
        model: sdk.core.model("xg_odd_job").list("manage"),
        filter: tableFilter1,
        defaultPageSize: 10,
        predict: predict2,
        column: columns2,
    }
}

export const predict3 = {}
export const columns3: TableColumn[] = [
    {
        label: "驿站名称",
        prop: "YZ",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "发起共享岗位",
        prop: "id3",
        align: "center",
        showOverflowTip: true,
        children: [
            {
                label: "发起共享岗位数",
                prop: "gxgw",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "被接受数",
                prop: "jsgw",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "被拒回数",
                prop: "jhgw",
                align: "center",
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "发起共享零工",
        prop: "lgscGxlg",
        align: "center",
        showOverflowTip: true,
        children: [
            {
                label: "发起共享零工数",
                prop: "gxlg",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "被接受数",
                prop: "jslg",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "被拒回数",
                prop: "jhlg",
                align: "center",
                showOverflowTip: true,
            },
        ],
    },
]
export const columns4: TableColumn[] = [
    {
        label: "驿站名称",
        prop: "YZ",
        align: "center",
        showOverflowTip: true,
    },
    {
        label: "收到共享岗位",
        prop: "lgscGxgw",
        align: "center",
        showOverflowTip: true,
        children: [
            {
                label: "收到共享岗位数",
                prop: "gxgw",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "已接受数",
                prop: "jsgw",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "已拒回数",
                prop: "jhgw",
                align: "center",
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "收到共享零工",
        prop: "lgscGxlg",
        align: "center",
        showOverflowTip: true,
        children: [
            {
                label: "收到共享零工数",
                prop: "gxlg",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "已接受数",
                prop: "jslg",
                align: "center",
                showOverflowTip: true,
            },
            {
                label: "已拒回数",
                prop: "jhlg",
                align: "center",
                showOverflowTip: true,
            },
        ],
    },
]
export function tableConfig3(cur = 0): TableConfig {
    return {
        model: sdk.core.model("xg_odd_job").list("manage"),
        filter: tableFilter1,
        defaultPageSize: 10,
        predict: predict3,
        column: +cur === 0 ? columns3 : columns4,
    }
}

export let BKToken: Promise<string> | null
export async function getBkToken() {
    const baseUrl = process.env.VUE_APP_BK_LINGGONG_ADMIN
    if (!BKToken) {
        return (BKToken = Axios.post(
            baseUrl + "/ORG/U/AC/API/V1/LGSC/ACCESS_TOKEN",
            {
                token: PassportTokenController.hasToken() || "",
            }
        ).then((r) => {
            let d: any = {}
            try {
                if (getBkKey()) {
                    d = JSON.parse(decryptAesString(r.data, getBkKey()))
                } else {
                    d = r.data || {}
                }
            } catch (e: any) {
                d = r.data || {}
            }
            if (d.IsOK) {
                const token = get(d, "Result.token.token")
                token && (BKToken = token)
                return token
            } else {
                const err = d.Error || "系统错误"
                showMessage(err)
                return Promise.reject(err)
            }
        }))
    } else {
        return BKToken
    }
}

const showMessage = debounce((message: string) => {
    app.$message({
        message,
        type: "error",
        offset: 20,
        onClose: () => {
            console.log("close")
        },
    })
}, 10)

export async function bkApi(api = "", params?: any) {
    const baseUrl2 =
        process.env.VUE_APP_ENV === "test"
            ? "https://admin.beikesmart.com"
            : process.env.VUE_APP_BK_LINGGONG_ADMIN

    return Axios.get(baseUrl2 + api, {
        params: params,
        headers: {
            "Content-Type": "application/json",
            TOKEN: await getBkToken(),
        },
    }).then((r) => {
        let d: any = {}
        try {
            if (getBkKey()) {
                d = JSON.parse(decryptAesString(r.data, getBkKey()))
            } else {
                d = r.data || {}
            }
        } catch (e: any) {
            d = r.data || {}
        }
        if (d.IsOK) {
            return d.Result || {}
        } else {
            const err = d.Error || "系统错误"
            showMessage(err)
            return Promise.reject(err)
        }
    })
}

export function getAddressOptions() {
    return sdk.core
        .domainService(
            "xg_project",
            "anonymous/client_api",
            "fetch_list_from_countycode"
        )
        .get<{ key: string; value: string }[]>({
            countycode: process.env.VUE_APP_DEFAULT_REGION_CODE || "420900",
        })
}
