<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 content">
            <div class="placeholder" v-show="loading" v-loading="true"></div>
            <form-builder
                ref="formBuilder"
                labelWidth="100px"
                v-show="!loading"
            ></form-builder>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="submitValidate"
                    class="custom-btn btn u-m-0"
                    :loading="btnLoading"
                >
                    提交
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { Action } from "uniplat-sdk"
    import { cloneDeep } from "lodash"
    import { uuid } from "uniplat-sdk/build/main/helpers/uuid"

    @Component({ components: { FormBuilder } })
    export default class AddTitlePop extends Mixins(
        DialogController,
        FormController
    ) {
        private action?: Action

        @Prop({ default: "" })
        private readonly id!: string

        @Prop({ default: "" })
        private readonly groupId!: string

        @Prop({ default: "" })
        private readonly modelName!: string

        @Prop({ default: "" })
        private readonly actionName!: string

        private btnLoading = false

        private get title() {
            return this.id ? "编辑标题" : "新建标题"
        }

        private get prefilterProperty() {
            return this.modelName === "indicator_name_dict"
                ? "group_id"
                : "group_ref_id"
        }

        onOpen() {
            this.init()
        }

        protected close() {
            this.$emit("update", false)
            this.$emit("close", false)
            setTimeout(() => {
                this.onClosing()
            }, 300)
        }

        onClosing() {
            this.resetFormFields()
        }

        private getAction() {
            return (this.action = sdk.core
                .model(this.modelName)
                .action(this.actionName)
                .updateInitialParams({
                    selected_list: this.id ? [{ v: 0, id: this.id }] : [],
                    prefilters: [
                        {
                            property: this.prefilterProperty,
                            value: this.groupId,
                        },
                    ],
                }))
        }

        private init() {
            this.loading = true
            return buildFormSections({
                needSourceData: true,
                action: this.getAction(),
                forms:
                    this.modelName === "indicator_name_dict"
                        ? [
                              ...[
                                  this.id
                                      ? {
                                            label: "指标code",
                                            type: FormType.Text,
                                            prop: "indicator_name",
                                            hide: true,
                                        }
                                      : {
                                            label: "指标code",
                                            type: FormType.Text,
                                            prop: "indicator_name",
                                            defaultValue: uuid(),
                                            hide: true,
                                        },
                              ],
                              {
                                  label: "标题名称",
                                  type: FormType.Text,
                                  prop: "display_name",
                                  required: true,
                              },
                              {
                                  label: "上一级标题",
                                  type: FormType.Select,
                                  prop: "pid",
                              },
                              {
                                  label: "数据类型",
                                  type: FormType.Select,
                                  prop: "value_type",
                                  required: true,
                              },
                              ...[
                                  this.id
                                      ? {
                                            label: "收集方式",
                                            type: FormType.Select,
                                            prop: "type",
                                            hide: true,
                                        }
                                      : {
                                            label: "收集方式",
                                            type: FormType.Select,
                                            prop: "type",
                                            defaultValue: "1",
                                            hide: true,
                                        },
                              ],
                              ...[
                                  this.id
                                      ? {
                                            label: "指标类型",
                                            type: FormType.Select,
                                            prop: "category",
                                            hide: true,
                                        }
                                      : {
                                            label: "指标类型",
                                            type: FormType.Select,
                                            prop: "category",
                                            defaultValue: "2",
                                            hide: true,
                                        },
                              ],

                              {
                                  label: "填写说明",
                                  type: FormType.Text,
                                  prop: "description",
                              },
                              {
                                  label: "排序值-降序",
                                  type: FormType.InputNumber,
                                  prop: "sort_index",
                                  option: {
                                      useNumber: true,
                                  },
                              },
                              {
                                  label: "",
                                  type: FormType.Select,
                                  prop: "kind_name",
                                  defaultValue: "",
                                  hide: true,
                              },
                          ]
                        : [
                              {
                                  label: "标题名称",
                                  type: FormType.Text,
                                  prop: "name",
                                  required: true,
                              },
                              {
                                  label: "排序值-降序",
                                  type: FormType.InputNumber,
                                  prop: "seq_number",
                                  option: {
                                      useNumber: true,
                                  },
                              },
                          ],
            }).then((r) => {
                this.buildForm(r.forms)
                this.loading = false
            })
        }

        private submitValidate() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                    })
                }
            })
        }

        private submit(data: any) {
            this.btnLoading = true
            const d = cloneDeep(data)
            if (this.modelName === "xg_indicator_name_y_dict") {
                if (!d.seq_number) {
                    d.seq_number = 0
                }
            }

            pageLoading(() => {
                return sdk.core
                    .model(this.modelName)
                    .action(this.actionName)
                    .addInputs_parameter(d)
                    .updateInitialParams({
                        selected_list: this.id ? [{ v: 0, id: +this.id }] : [],
                        prefilters: [
                            {
                                property: this.prefilterProperty,
                                value: this.groupId,
                            },
                        ],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success(this.id ? "编辑成功" : "新建成功")
                        this.$emit("refresh")
                        this.close()
                    })
                    .finally(() => (this.btnLoading = false))
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
