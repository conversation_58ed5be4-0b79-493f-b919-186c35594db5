import { TableColumn, TableConfig } from "@/core-ui/component/table"

export interface Row {
    name: string
    note: string
    function_note: string
    source_count: string
    task_count: string
    policy_id: string
    is_create_task: boolean
    id: number
    v: number
    _access_key: string
}

export interface DetailRow {
    /** 核查工具名称 */
    name: string

    /** 核查工具描述 */
    note: string

    /** 功能逻辑详述 */
    function_note: string
    policy_id: string
    is_create_task: boolean
    id: number
    v: number
}

export const columns: TableColumn[] = [
    {
        label: "核查任务名称",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "关联单位",
        prop: "org_name",
        showOverflowTip: true,
    },
    {
        label: "创建人",
        prop: "real_name",
        showOverflowTip: true,
    },
    {
        label: "创建时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "任务状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "结果简述",
        prop: "result_note",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        showOverflowTip: true,
    },
]

export const columns2: TableColumn[] = [
    {
        label: "数据集名称",
        prop: "table_display",
        showOverflowTip: true,
    },
    {
        label: "数据量",
        prop: "record_count",
        showOverflowTip: true,
    },
    {
        label: "使用说明",
        prop: "note",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        showOverflowTip: true,
    },
]

const enum Status {
    启用 = 0,
    禁用 = 1,
}

export interface DetailListRow {
    /** 关联单位 */
    name: string

    /** 核查需求简述 */
    require_note: string

    /** 结果描述 */
    result_note: number

    /** 是否有异常数据 */
    is_exception: number

    /** 创建人 */
    real_name: string

    /** 创建时间 */
    create_time: string

    /** 任务状态 */
    status: Status

    /** 任务状态[文本] */
    status_label: string
    id: number
    v: number
}

export const intentPredict = {
    id: "",
    name: "",
    note: "",
    source_count: "",
    task_count: "",
    policy_id: "",
}

export const intentColumn: TableConfig["column"] = [
    {
        label: "核查工具名称",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "描述",
        prop: "note",
        showOverflowTip: true,
    },
    {
        label: "关联数据库",
        prop: "source_count",
        showOverflowTip: true,
    },
    {
        label: "下属核查任务",
        prop: "task_count",
        showOverflowTip: true,
    },
]

export const checkTaskPredict = {
    name: "name",
    address_detail: "address_detail",
    salary: "",
    tags: "tags",
    company: "agent#agent_name",
    source: "source_from_type",
    time: "final_change_time",
    agent_name: "agent#agent_name",
    age_require: "",
    education: "",
    online_status: "label",
    position_id: "id",
}
