import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress } from "@/utils"

const tableFilter: TableFilter[] = [
    // {
    //     label: "使用记录名称",
    //     type: FormType.Text,
    //     prop: "name",
    // },
]

export const predict = {
    salary_desc: "position#salary",
    name: "position#name",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    recruit_count: "position#recruit_count",
    gender_require: "position#gender_require_label",
    age_require: "position#age_require_label",
    industry: "position#industry_label",
    online_status: "position#online_status_label",
    create_time: "position#create_time_label",
    source_from_type: "position#source_from_type_label",
    status: "position#status_label",
    position_id: "position#id",
    position_access_key: "position#_access_key",
    qr_code: "",
    audit_status: "label",
    wx_qr: "label",
}
export function tableConfig(
    agent_id: string,
    job_fair_id: string
): TableConfig {
    return {
        model: sdk.core
            .model("job_fair_agent_position")
            .list("in_job_fair_detail_for_operate"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
        preFilter: {
            agent_id,
            job_fair_id,
        },
    }
}

export const columns: TableColumn[] = [
    { label: "岗位名称", prop: "name", align: "left", showOverflowTip: true },
    {
        label: "薪资待遇",
        prop: "salary",
        render(h, row) {
            return h("span", {}, row.salary_desc)
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "工作地区",
        prop: "city",
        render: (h, row) => {
            return h("span", {}, getAddress(row))
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "招聘人数",
        prop: "recruit_count",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "来源",
        prop: "source_from_type_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "年龄要求",
        prop: "age_require_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "岗位状态",
        prop: "audit_status_label",
        align: "left",
        showOverflowTip: true,
    },
    // {
    //     label: "上架状态",
    //     prop: "online_status_label",
    //     align: "left",
    //     showOverflowTip: true,
    // },
    {
        label: "发布时间",
        prop: "create_time_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "岗位二维码",
        prop: "sign_qr",
        align: "center",
        render(h, row) {
            return row.qr_code
                ? h("el-image", {
                      class: "img",
                      style: { width: "40px" },
                      props: {
                          "preview-src-list": [
                              sdk.buildImage(row.qr_code || ""),
                          ],
                          fit: "contain",
                          src: sdk.buildImage(row?.qr_code || ""),
                      },
                  })
                : h("span", "-")
        },
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "180px",
        align: "left",
        showOverflowTip: true,
    },
]

const tableFilter2: TableFilter[] = [
    {
        label: "求职者名称",
        type: FormType.Text,
        prop: "name",
    },
]

export const predict2 = {
    name: "",
    mobile_encode: "",
    mobile: "",
    age: "",
    contact_status: "label",
    position_id: "position#id",
    position_name: "position#name",
    sex: "profile#sex_label",
    education: "label",
    job_kind1: "position#job_kind_1",
    job_kind2: "position#job_kind_2",
    job_kind3: "position#job_kind_3",
    create_time: "label",
    salary: "position#salary_label",
    status: "label",
    status1: "label",
    status2: "label",
    status3: "label",
    status4: "label",
    household_address_full_name: "",
    address_detail: "position#address_detail",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    function_categories: "position#function_categories",
}
export function tableConfig2(
    agent_id: string,
    created_from_id: string
): TableConfig {
    return {
        model: sdk.core.model("xg_candidate_order").list("job_fair_agent_list"),
        filter: tableFilter2,
        defaultPageSize: 10,
        predict: predict2,
        preFilter: {
            "position.agent_id": agent_id,
            created_from: "job_fair",
            created_from_id,
        },
    }
}

export enum Status {
    已报名 = 0,
    沟通中 = 10,
    已入职 = 20,
    已淘汰 = 30,
}

export const columns2: TableColumn[] = [
    { label: "姓名", prop: "name", align: "left", showOverflowTip: true },
    {
        label: "电话",
        prop: "mobile",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "性别/学历",
        prop: "sex",
        formatter(row) {
            return [row.sex_label, row.education_label]
                .filter(Boolean)
                .join("/")
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "户籍",
        prop: "household_address_full_name",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "岗位名称",
        prop: "position_name",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "岗位职能",
        prop: "function_categories",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "工作地",
        prop: "id23",
        formatter(row) {
            return getAddress(row)
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "薪资待遇",
        prop: "salary_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "报名时间",
        prop: "create_time_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "投递状态",
        prop: "status_label",
        align: "left",
        formatter(row) {
            return Status[row.status]
        },
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "180px",
        align: "left",
        showOverflowTip: true,
    },
]
