import { VNode } from "vue"
import { Direction } from "@/views/common-module/router"
import { Dictionary } from "vue-router/types/router"
import { Direction, RouteConfig } from "./router"

declare module "vue/types/vue" {
    interface NewDirectionOption {
        openInNewTab?: boolean
        params?: Dictionary<string | number>
        query?: Dictionary<string | number>
    }

    interface Vue {
        beforeCreate?(): void
        created?(): void
        beforeMount?(): void
        mounted?(): void
        beforeDestroy?(): void
        destroyed?(): void
        beforeUpdate?(): void
        updated?(): void
        activated?(): void
        deactivated?(): void
        render?(createElement: CreateElement): VNode

        $route: RouteConfig

        $once(event: "hook:beforeDestroy", callback: () => void): this
        /** 监听一个自定义事件，但是只触发一次。一旦触发之后，监听器就会被移除。 */
        $once(event: string | string[], callback: () => void): this

        go(
            direction: Direction,
            params?: { [key: string]: any },
            query?: { [key: string]: any }
        ): void

        openDirection(
            direction: Direction,
            params?: { [key: string]: any },
            query?: { [key: string]: any }
        ): void

        getCurrentDirection(): Direction

        registerPostMessageEvent(
            callBack: (e: { data: any }) => void | Promise<void>
        ): void

        attachDocumentEvent<K extends keyof HTMLElementEventMap>(
            target: HTMLElement,
            type: K,
            listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any,
            options?: boolean | AddEventListenerOptions
        ): this
        attachDocumentEvent(
            target: HTMLElement,
            type: string,
            listener: EventListenerOrEventListenerObject,
            options?: boolean | AddEventListenerOptions
        ): this
        attachDocumentEvent<K extends keyof ElementEventMap>(
            target: Element,
            type: K,
            listener: (this: Element, ev: ElementEventMap[K]) => any,
            options?: boolean | AddEventListenerOptions
        ): this
        attachDocumentEvent(
            target: Element,
            type: string,
            listener: EventListenerOrEventListenerObject,
            options?: boolean | AddEventListenerOptions
        ): this
        attachDocumentEvent<K extends keyof DocumentEventMap>(
            target: Document,
            type: K,
            listener: (this: Document, ev: DocumentEventMap[K]) => any,
            options?: boolean | AddEventListenerOptions
        ): this
        attachDocumentEvent(
            target: Document,
            type: string,
            listener: EventListenerOrEventListenerObject,
            options?: boolean | AddEventListenerOptions
        ): this
        attachDocumentEvent(
            target: HTMLElement | Element | Document,
            type: string,
            listener: EventListenerOrEventListenerObject,
            options?: boolean | AddEventListenerOptions
        ): this
        getCellvalue(
            row: Record<string, string>,
            column: TableColumn,
            emptyValue?: string | number | Array<string | number>
        ): string | number
        getCellDispaly(
            row: Record<string, string>,
            column: TableColumn,
            emptyText: string,
            emptyValue?: string | number | Array<string | number>
        ): string | number
        refreshConfig?: {
            fun: Function
            name: string | string[]
        }
        callRefresh(name: string, ...args: any[]): void
    }
}

declare module "element-ui/types/message" {
    interface ElMessage {
        closeAll(): void
    }
}

declare module "element-ui/types/message-box" {
    interface ElMessageBoxOptions {
        modal?: boolean
    }
}

declare module "element-ui/types/popover" {
    interface ElPopover {
        doClose(): void
    }
}

declare module "element-ui/types/table" {
    interface ElTable {
        selection: any[]
    }
}
