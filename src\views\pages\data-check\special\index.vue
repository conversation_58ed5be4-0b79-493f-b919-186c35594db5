<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            class="container"
        >
            <div slot="title" class="u-flex u-row-between">
                <div class="d-flex-item-center bold">
                    <bread-crumb :backRoute="false" :items="breadcrumbs" />
                </div>
                <div>
                    <el-button type="primary" @click="toAdd" v-if="showBtn"
                        >新建核查任务</el-button
                    >
                </div>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div slot="创建人" slot-scope="scope">
                        <div>{{ scope.row.real_name }}</div>
                        <div>{{ scope.row.mobile_hide }}</div>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <AddPop v-model="showAddPop" />
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { pageLoading } from "@/views/controller"
    import {
        buildConfig4RemoteMeta,
        getShowBtn4Page,
    } from "../../collect-task-manage/components/build-table"
    import { Row } from "./index"
    import AddPop from "./components/add-pop.vue"

    @Component({
        name: routesMap.dataCheck.specialIndex,
        components: { TableContainer, CommonTable, AddPop },
    })
    export default class SpecialIndex extends BaseTableController<Row> {
        tableConfig: TableConfig | null = null

        refreshConfig = {
            fun: this.init,
            name: routesMap.dataCheck.specialIndex,
        }

        private checkEdIds: number[] = []

        private columns: TableColumn[] = []

        private rows: Row[] = []

        private showBtn = false

        private routesName = routesMap.dataCheck.specialIndex

        private showAddPop = false

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "专项数据核查任务",
                    to: {
                        name: this.routesName,
                    },
                },
            ]
            updateTagItem({
                name: this.routesName,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        created() {
            this.init()
        }

        private init() {
            pageLoading(() => {
                return buildConfig4RemoteMeta("special_check_task", "back_list", {
                    predicts: {
                        name: "xg_login_user#xg_organization#name",
                        check_type: "label",
                        real_name: "user_account#real_name",
                        create_time: "",
                        status: "label",
                        mobile_hide: "user_account#mobile_hide",
                    },
                    useLabelWidth: true,
                    useRowFieldGroups: true,
                }).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            this.showBtn = getShowBtn4Page(r, "add_check_task")

            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
            }
            console.log(333333, r)
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            this.columns = r.columns
        }

        mounted() {
            this.setBreadcrumbs()
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.dataCheck.specialDetail,
                query: { id: row._access_key },
            })
        }

        private toAdd() {
            this.showAddPop = true
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
</style>
