<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="下载"
        width="600px"
    >
        <form-builder ref="formBuilder" labelWidth="140px"></form-builder>
        <div class="u-flex u-row-center">
            <el-button type="primary" plain @click="close">取消</el-button>
            <el-button
                type="primary"
                class="u-m-l-30"
                @click="confirm"
                :loading="btnLoading"
                >确定</el-button
            >
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { routesMap } from "@/router/direction"
    import { Action } from "uniplat-sdk"
    import { DetailRow } from "../detail"

    @Component({ components: { FormBuilder } })
    export default class DownloadPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: () => {} })
        private detailRow!: DetailRow | any

        @Prop({ default: false })
        private isResult!: boolean

        @Prop({ default: "" })
        private filePath!: string

        private action?: Action

        private btnLoading = false

        onOpen() {
            this.init()
        }

        onClosing() {
            this.resetFormFields()
        }

        private getAction() {
            return (this.action = sdk.core
                .model("special_check_task")
                .action(this.isResult ? "download_result" : "download_data")
                .updateInitialParams({
                    selected_list: [{ v: 0, id: this.detailRow.id }],
                }))
        }

        private init() {
            return buildFormSections({
                action: this.getAction(),
                forms: [],
            }).then((r) => {
                this.buildFormFull(r)
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            this.btnLoading = true
            return pageLoading(() => {
                window.open(this.filePath, "_blank")
                return sdk
                    .getDomainService(
                        "special_check_download",
                        "back_api",
                        "warning_platform"
                    )
                    .post({
                        task_id: this.detailRow.id,
                        status: 0,
                        note: data.download_note || "",
                        type: this.isResult ? 1 : 0,
                    })
                    .then(() => {
                        this.callRefresh(routesMap.dataCheck.specialDetail)
                        this.close()
                    })
                    .finally(() => (this.btnLoading = false))
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
