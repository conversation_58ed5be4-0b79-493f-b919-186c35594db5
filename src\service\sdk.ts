import { config } from "@/config"
import { SdkController } from "@/core-ui/controller/sdk-controller"
import { LoginRegisterResult } from "@/core-ui/service/passport/index"
import { instanceService } from "@/core-ui/service/passport/instance"
import { UICore } from "@/core-ui/service/setup"
import { Notification } from "element-ui"
import { assign, find, get, includes } from "lodash"
import {
    metaRow,
    PrevertXssConfig,
    SdkListRowPredict,
    SdkListRowPredictObject,
    UniplatSdk,
    UniplatSdkExtender,
} from "uniplat-sdk"
import { userService } from "./service-user"
class Sdk {
    private uniplatSdk!: UniplatSdk
    private readonly handler = new UniplatSdkExtender()
    public orgId!: number
    private xidToken = ""
    public getXidToken() {
        return this.xidToken
    }

    constructor() {
        // 从config获取目标服务器
        const baseUrl = config.uniplatApi
        // 生成uniplat实例
        this.uniplatSdk = new UniplatSdk({
            sse: false,
        })
        this.uniplatSdk.setInitData({})
        UICore.setupSdk(this.uniplatSdk, { client: config.clientId })
        // 传入目标服务器以链接服务器
        this.uniplatSdk.connect({
            baseUrl: baseUrl,
            axiosTimeout: 1e3 * 60,
            // 30分钟刷新token
            refreshInterval: 1e3 * 60 * 20,
        })
        // 设置实例根入口程序
        this.uniplatSdk.global.rootEntrance = config.uniplatEntrance
        // 添加Token过期时的回调
        this.uniplatSdk.events.addTokenExpiring(() => {
            userService.showLogout()
        })
        this.uniplatSdk.events.addUniversalErrorResponseCallback((r) => {
            let data: any = {}
            try {
                data = JSON.parse(get(r, "config.data", "{}")).inputs_parameters
            } catch {
                data = {}
            }

            if (find(data, (i) => i.property === "customeClientCatch")) {
                return
            }

            if (get(r, "config.params", {})._customeClientCatch) {
                return
            }

            let message = get(r, "data.msg") || get(r, "msg") || "网络错误"
            if (includes(r.toString(), "Error: timeout of")) {
                message = "网络请求超时，请重试"
            }

            if (r.data?.msg === "对不起，您没有权限访问该数据") {
                return userService.showContact()
            }
            const status = get(r, "response.status") || get(r, "status")

            if (get(r, "status") === 429) {
                message = "您的访问过于频繁，请稍后再试。"
            }

            // 401授权过期，下面处理
            if (
                status === 401 ||
                (r.data?.rescode === 999 &&
                    ["授权失败", "无效token", "用户已注销"].includes(
                        r.data?.msg
                    ))
            ) {
                sdk.core.logout(false)
                return userService.showLogout()
            }
            Notification.error({ title: "", message, duration: 2000 })
        })
    }

    public getDomainService(
        apiName: string,
        serviceName = "agent_api",
        subProjectName = "welfare_bean"
    ) {
        return sdk.core.domainService(subProjectName, serviceName, apiName)
    }

    public getOrdId() {
        return this.orgId
    }

    public setup(r: LoginRegisterResult) {
        this.uniplatSdk.loginByToken({
            token: r.jwt,
            isSuperUser: r.is_super,
        })
        return this.loadOrg()
    }

    public loadOrg() {
        return instanceService
            .getOrgList(sdk.core, config.uniplatEntrance as any)
            .then(async (org) => {
                if (org.list.length) {
                    sdk.orgId = org.list[0].id
                    sdk.core.setInitData(
                        assign(sdk.core.global.initData, {
                            orgId: org.list[0].id,
                            xid: org.list[0].id,
                        })
                    )
                    return sdk.core.configurationApi
                        .changeTokenWithXid(org.list[0].id + "")
                        .then((r) => {
                            this.xidToken = r
                        })
                } else {
                    userService.showContact()
                    return Promise.reject("无此系统权限")
                }
            })
    }

    public get core() {
        return this.uniplatSdk
    }

    public buildRows<T>(
        rows: metaRow[],
        predicts: SdkListRowPredict[] | SdkListRowPredictObject,
        prevertXss?: PrevertXssConfig
    ) {
        return this.handler.buildRows<T>(rows, predicts, prevertXss)
    }

    public buildRow<T>(
        item: metaRow,
        predicts: SdkListRowPredict[] | SdkListRowPredictObject,
        prevertXss?: PrevertXssConfig
    ) {
        return this.handler.buildRow<T>(item, predicts, prevertXss)
    }

    public buildActionParameter(parameter: { [key: string]: any }) {
        return this.handler.buildActionParameter(parameter)
    }

    public buildImage(url: string, w?: number, h?: number) {
        if (url.includes("yz.hbggzp.cn") && !url.includes("http")) {
            url = "http://" + url
        }
        return SdkController.buildImage(this.core, url, w, h)
    }

    public buildFilePath(url: string, notForceDownload?: boolean) {
        return SdkController.buildFilePath(this.core, url, notForceDownload)
    }
}

export const sdk = new Sdk()
