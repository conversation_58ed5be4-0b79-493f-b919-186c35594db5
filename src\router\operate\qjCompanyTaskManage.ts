import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const qjCompanyTaskManage = [
    // 企业用工信息库（潜江）
    {
        path: "/company-task-manage",
        redirect: "company-task-manage/index",
        name: routesMap.home.qjCompanyTaskManage,
        meta: {
            title: "企业信息库",
            svgIcon: require("@/assets/icon/menu2/market.svg"),
            hidden: ![EnvProject.潜江项目].includes(config.envProject),
            homeIcon: "/img/xiaogan/home/<USER>",
        },
        component: layout,
        children: [
            {
                path: "index",
                name: routesMap.companyTaskManage.index,
                meta: {
                    title: "企业用工信息库",
                    role: "model.company_task_emprequire_record.list.list_for_operation_v2",
                },
                component: () =>
                    import(
                        "@/views/pages/company-task-manage/company-database/index.vue"
                    ),
            },
            {
                path: "list",
                name: routesMap.companyTaskManage.search,
                meta: {
                    title: "信息查询",
                    hidden: true,
                    parentMenuName: routesMap.companyTaskManage.index,
                },
                component: () =>
                    import(
                        "@/views/pages/company-task-manage/company-database/filter.vue"
                    ),
            },
            {
                path: "enterprise-database",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .enterpriseDatabase.list,
                meta: {
                    title: "企业信息库",
                    role: [
                        "/tablelist/tg_enterprise/back_list",
                        "/tablelist/tg_enterprise/back_list2",
                    ],
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/enterprise-database/index.vue"
                    ),
            },
            {
                path: "enterprise-database-filter",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .enterpriseDatabase.filter,
                meta: {
                    title: "企业信息库筛选",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.marketDatabaseManage
                            .enterpriseDatabase.list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/enterprise-database/filter.vue"
                    ),
            },
            {
                path: "enterprise-database-detail",
                name: routesMap.collectTaskManage.marketDatabaseManage
                    .enterpriseDatabase.detail,
                meta: {
                    title: "企业信息库详情",
                    hidden: true,
                    parentMenuName:
                        routesMap.collectTaskManage.marketDatabaseManage
                            .enterpriseDatabase.list,
                },
                component: () =>
                    import(
                        "@/views/pages/collect-task-manage/enterprise-database/detail.vue"
                    ),
            },
        ],
    },
]
