const defaultDomainAllInOne = {
    VUE_APP_UNIPLAT: "/api",
    VUE_APP_UNIPLAT_WEB: "/uniplat",
    VUE_APP_H5: "/h5",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
}

const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "红安",
    VUE_APP_CITY_SAMPLE_NAME2: "红安县",
    VUE_APP_DEFAULT_REGION_CODE: "421122000000",
    VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
    VUE_APP_DEFAULT_REGION_NAME: "红安县",
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "红安县",
    VUE_APP_BAIDU_KEY: "Xa338L0G79uVCEmkg7tIXOZtCLEPFfMY",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "bM8swUjSRSGrxlzf",
    VUE_APP_requestEncoder: "", // 入参加密 aes
    VUE_APP_responseEncoder: "", // 返回参加密 aes
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-ha",
}
const { config } = require("./hongan_p.js")
module.exports = {
    name: "红安数采项目",
    env: {
        test: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_UNIPLAT: "http://jzpes-h5-test.teammix.com:8800/api",
        },
        pro: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_requestEncoder: "aes", // 入参加密 aes
            VUE_APP_responseEncoder: "aes", // 返回参加密 aes
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "就业管理工作台",
                VUE_APP_APP_TITLE: "智慧就业服务工作台",
                BASE_URL: "platform/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                pro: {
                    path: "/mnt/hongan/front/platform",
                    host: "wuhan",
                },
            },
        },
        // 企业端
        {
            name: "网格员端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "居民就业服务客户端",
                VUE_APP_HEADER_TITLE: "劳动力资源信息采集平台",
                VUE_APP_APP_TITLE: "劳动力资源信息采集平台",
                BASE_URL: "grid/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["网格员端"],
                VUE_APP_APP_NAME: "xg_project_grid",
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/grid",
                    host: "88",
                    env: {},
                },
                pro: {
                    path: "/mnt/hongan/front/grid",
                    host: "wuhan",
                },
            },
        },
    ],
}
