<template>
    <div class="bg-white u-m-t-20">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :alwaysShowPageIndex="false"
            :showExpand="false"
            @setTotal="setTotal"
        >
            <div slot="table" slot-scope="{ data }">
                <common-table :data="data" :columns="tableConfig.column">
                    <template slot="h" slot-scope="{ row }">
                        <el-button
                            type="text"
                            @click="viewDetail(row)"
                        >
                            关联企业
                        </el-button>
                        <el-button type="text" @click="edit(row)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="remove(row)">
                            删除
                        </el-button>
                    </template>
                </common-table>
            </div>
        </table-container>
        <RelateCompany
            :job_fair_id="job_fair.id"
            v-model="showDetail"
            :id="row && row.id"
            @refresh="refreshList"
        />
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { sdk } from "@/service"
    import { MessageBox } from "element-ui"
    import { Component, Prop, Watch } from "vue-property-decorator"
    import { Row } from "../../.."
    import { tableConfig } from "./area-list"
    import RelateCompany from "./relate-company.vue"

    @Component({ components: { TableContainer, CommonTable, RelateCompany } })
    export default class AreaList extends BaseTableController<{ id: number }> {
        @Prop()
        private job_fair!: Row

        @Prop()
        private currentAreaId!: number

        private tableConfig: TableConfig | null = null

        mounted() {
            this.tableConfig = tableConfig(this.job_fair.id + "")
        }

        private setTotal(num: number) {
            if (!this.currentAreaId) {
                this.$emit("setTotal", num)
            }
        }

        private edit(row: any) {
            this.$emit("edit", row.id)
        }

        @Watch("currentAreaId")
        private onCurrentAreaIdChange() {
            let preFilter: any = {
                ...this.tableConfig!.preFilter,
                booth_id: this.currentAreaId,
            }
            if (!preFilter.booth_id) {
                preFilter = _.omit(preFilter, "booth_id")
            }
            this.tableConfig!.preFilter = preFilter
            this.$nextTick(() => {
                this.reloadList()
            })
        }

        remove(e: any) {
            MessageBox.confirm(
                `是否删除${e.booth_name}：${e.booth_address} ${
                    e.agent_name || ""
                }?`,
                "提示",
                {
                    beforeClose: (action, instance, done) => {
                        if (action === "confirm") {
                            instance.confirmButtonLoading = true
                            sdk.core
                                .model("job_fair_booth_area")
                                .action("delete")
                                .updateInitialParams({
                                    selected_list: [{ id: e.id, v: 0 }],
                                })
                                .execute()
                                .then(() => {
                                    done()
                                    this.$message.success("删除成功")
                                    this.refreshList()
                                })
                                .finally(() => {
                                    instance.confirmButtonLoading = false
                                })
                        } else {
                            done()
                        }
                    },
                }
            )
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
