<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="500px"
        top="8vh"
    >
        <div class="u-p-x-20 content">
            <!-- <div class="placeholder" v-show="loading" v-loading="true"></div> -->
            <el-form ref="form" :model="data" :rules="rules">
                <el-form-item
                    class="login-input"
                    label="跳转页面"
                    prop="pageName"
                >
                    <el-select
                        v-model="data.pageName"
                        placeholder="请选择"
                        @change="onPageChange"
                    >
                        <el-option
                            v-for="item in pagesList"
                            :key="item.path"
                            :label="item.meta.title || item.path"
                            :value="item.path"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="入口名称" prop="name">
                    <el-input
                        v-model="data.name"
                        clearable
                        placeholder="请输入入口名称"
                        @input="clearValidate"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="排序值" prop="sort">
                    <el-input-number
                        v-model="data.sort"
                        controls-position="right"
                        :min="0"
                        :max="999999999"
                        @input="clearValidate"
                    ></el-input-number>
                </el-form-item>
            </el-form>
            <div class="u-flex u-m-t-26 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="submit"
                    :loading="loading"
                    class="custom-btn btn u-m-0"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { FormRule } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { userInfoService } from "@/core-ui/service/passport/user-info"
    import { ElForm } from "element-ui/types/form"
    import { uuid } from "uniplat-sdk/build/main/helpers/uuid"
    import { Component, Mixins, Prop, Ref } from "vue-property-decorator"
    import { RouteConfig } from "vue-router"
    import {
        entranceMenuController,
        EntranceMenuItem,
    } from "../../entrance-menu-controller-remote"
    import { getRoutes } from "../../routes"

    @Component({ components: { FormBuilder } })
    export default class AddEntrancePagePop extends DialogController {
        @Prop()
        private readonly row!: EntranceMenuItem

        @Ref()
        private readonly form?: ElForm

        private data = {
            id: "",
            pageName: "",
            name: "",
            sort: 0,
        }

        private pageMeta: RouteConfig | null = null

        private rules: Record<string, FormRule[]> = {
            pageName: [{ required: true, message: "跳转页面不能为空" }],
            name: [
                { required: true, message: "入口名称不能为空", trigger: "blur" },
                // {
                //     validator: (_, __, callback) => {
                //         if (!this.getIsSendSms()) {
                //             return callback(new Error("请先获取验证码"))
                //         }
                //         callback()
                //     },
                // },
            ],
        }

        private pagesList: RouteConfig[] = getRoutes()

        private get title() {
            return ""
        }

        onOpen() {
            this.reset()
            if (this.row) {
                this.data = {
                    id: this.row.id,
                    pageName: this.row.pageName,
                    name: this.row.name,
                    sort: this.row.sort,
                }
                this.pageMeta = this.row.meta
            }
        }

        private reset() {
            this.data = {
                id: "",
                pageName: "",
                name: "",
                sort: 0,
            }
        }

        private submit() {
            this.form?.validate().then((r) => {
                if (!r) {
                    return
                }
                let query = null
                if (this.row) {
                    query = entranceMenuController.updateEntry(this.data.id, {
                        ...this.data,
                        ...(this.pageMeta ? { meta: this.pageMeta } : {}),
                    })
                } else {
                    query = entranceMenuController.addEntry({
                        ...this.data,
                        meta: this.pageMeta,
                        id: uuid(),
                    })
                }

                this.loading = true
                query
                    .then(() => {
                        this.close()
                        this.$emit("onSuccess")
                    })
                    .finally(() => {
                        this.loading = false
                    })
            })
        }

        private clearValidate() {
            this.form?.clearValidate()
        }

        private onPageChange(v: string) {
            this.pageMeta = this.pagesList.find((i) => i.path === v) as any
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    /deep/.el-form .el-form-item {
        display: flex;
        .el-form-item__label {
            min-width: 80px;
        }
        .el-form-item__content {
            flex: 1;
        }
        .el-select,
        .el-input-number {
            width: 100%;
        }
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
