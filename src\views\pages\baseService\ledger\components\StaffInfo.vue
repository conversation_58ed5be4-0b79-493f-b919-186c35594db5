<template>
    <div style="padding-block: 16px">
        <div class="text-18 font-600">人社专员信息</div>

        <div
            v-loading="loading"
            class="bg-white round-3"
            style="margin-top: 24px"
        >
            <div v-if="loading" style="height: 118px"></div>
            <div
                v-else
                class="flex items-center"
                style="padding-block: 20px; padding-inline: 36px; gap: 40px"
            >
                <!-- 头像，昵称 -->
                <div class="flex items-center" style="gap: 10px">
                    <img
                        :src="staffInfo.gender === 0 ? girlIcon : boyIcon"
                        alt="头像"
                        srcset=""
                        style="width: 42px; height: 42px; border-radius: 50%"
                    />
                    <div class="text-18 font-600">
                        {{ staffInfo.real_name }}
                    </div>
                </div>
                <!-- line -->
                <div
                    class="line"
                    style="
                        width: 1px;
                        height: 78px;
                        background-color: #6666664d;
                    "
                ></div>
                <!-- info -->
                <div
                    class="flex-1 grid-cols-3 grid info-container"
                    style="gap: 16px"
                >
                    <div class="flex items-center item">
                        <div class="label">联系电话</div>
                        <div class="value">
                            <div>
                                {{
                                    showMobile
                                        ? staffInfo.mobile
                                        : staffInfo.mobile_hide
                                }}
                            </div>
                            <div
                                class="text-14"
                                style="color: #5782ec; cursor: pointer"
                                @click="showMobile = !showMobile"
                            >
                                {{ showMobile ? "隐藏" : "查看" }}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center item">
                        <div class="label">性别</div>
                        <div class="value">
                            <span>
                                {{ staffInfo.gender === 1 ? "男" : "女" }}
                            </span>

                            <div class="flex items-center item">
                                <div class="label">年龄</div>
                                <div>{{ staffInfo.age }}岁</div>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center item">
                        <div class="label">最近登录时间</div>
                        <div>{{ staffInfo.last_login_time || "--" }}</div>
                    </div>
                    <div class="flex items-center item">
                        <div class="label">身份证号</div>
                        <div class="value">
                            <div>
                                {{
                                    showIdCard
                                        ? staffInfo.id_card
                                        : staffInfo.id_card_hide
                                }}
                            </div>
                            <div
                                class="text-14"
                                style="color: #5782ec; cursor: pointer"
                                @click="showIdCard = !showIdCard"
                            >
                                {{ showIdCard ? "隐藏" : "查看" }}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center item">
                        <div class="label">管理区域</div>
                        <div>{{ staffInfo.region_name }}</div>
                    </div>
                    <div class="flex items-center item">
                        <div class="label">设备编号</div>
                        <div>--</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- statistics -->
        <div
            v-loading="loading"
            class="flex items-center justify-between gap-20"
            style="margin-top: 20px"
        >
            <div v-if="loading" style="height: 90px"></div>
            <template v-else>
                <div
                    class="flex-1 bg-white round-3 flex flex-col items-center gap-4"
                    style="padding-block: 20px"
                >
                    <div class="text-30 font-600" style="color: #5b86ee">
                        {{ statistics.update_labor_force_count }}
                    </div>
                    <div class="text-16" style="color: #777e8e">
                        劳动力更新数量
                    </div>
                </div>
                <div
                    class="flex-1 bg-white round-3 flex flex-col items-center gap-4"
                    style="padding-block: 20px"
                >
                    <div class="text-30 font-600" style="color: #a1c452">
                        {{ statistics.policy_form_count }}
                    </div>
                    <div class="text-16" style="color: #858d72">
                        政策推广数量
                    </div>
                </div>
                <div
                    class="flex-1 bg-white round-3 flex flex-col items-center gap-4"
                    style="padding-block: 20px"
                >
                    <div class="text-30 font-600" style="color: #e6b81e">
                        {{ statistics.xg_company_position_count }}
                    </div>
                    <div class="text-16" style="color: #807658">
                        岗位推广数量
                    </div>
                </div>
                <div
                    class="flex-1 bg-white round-3 flex flex-col items-center gap-4"
                    style="padding-block: 20px"
                >
                    <div class="text-30 font-600" style="color: #f3a162">
                        {{ statistics.job_fair_count }}
                    </div>
                    <div class="text-16" style="color: #807658">
                        招聘会推广数量
                    </div>
                </div>
                <div
                    class="flex-1 bg-white round-3 flex flex-col items-center gap-4"
                    style="padding-block: 20px"
                >
                    <div class="text-30 font-600" style="color: #57bdec">
                        {{ statistics.hb_training_count }}
                    </div>
                    <div class="text-16" style="color: #738587">
                        培训推广数量
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts">
    import { sdk } from "@/service"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import girlIcon from "../girl.svg"
    import boyIcon from "../boy.svg"

    @Component({ components: {} })
    export default class StaffInfo extends Vue {
        @Prop({ type: String, default: "" })
        private region_code!: string

        @Prop({ type: String, default: "" })
        private uniplat_uid!: string

        private girlIcon = girlIcon
        private boyIcon = boyIcon

        private loading = false

        private staffInfo: any = {}
        private statistics: any = {}
        private showMobile = false

        private showIdCard = false

        /**
         * 获取人社专员信息
         */
        private fetchGetGridStaffInfo() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "g_service_register",
                    "get_grid_staff_info"
                )
                .post({
                    region_code: this.region_code,
                    uniplat_uid: this.uniplat_uid,
                })
        }

        /**
         * 获取人社专员统计信息
         */
        private getGridStaffStatistics() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "g_service_register",
                    "get_grid_staff_statistics"
                )
                .post({
                    region_code: this.region_code,
                    uniplat_uid: this.uniplat_uid,
                })
        }

        mounted() {
            this.loading = true
            Promise.all([
                this.fetchGetGridStaffInfo(),
                this.getGridStaffStatistics(),
            ])
                .then(([staffInfo, statistics]) => {
                    this.staffInfo = staffInfo
                    this.statistics = statistics
                })
                .finally(() => {
                    this.loading = false
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "../style/tail.less";
    .info-container {
        .item {
            gap: 20px;
            .label {
                width: 90px;
                text-align: right;
                color: #666666;
            }
            .value {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        }
    }
</style>
