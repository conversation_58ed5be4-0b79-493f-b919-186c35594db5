<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button
                    @click="actionTask('start')"
                    type="primary"
                    v-if="noStart && showStartBtn"
                    >开始填报</el-button
                >
                <el-button
                    @click="actionTask('finish')"
                    type="primary"
                    v-else-if="isGoing && showFinishBtn"
                    >结束填报</el-button
                >
                <el-button @click="edit" type="primary" v-if="showEditBtn" plain
                    >编辑</el-button
                >
                <el-button
                    @click="deleteDetail"
                    type="primary"
                    v-if="showDeleteBtn"
                    plain
                    >删除</el-button
                >
                <el-button
                    @click="toExport"
                    type="primary"
                    plain
                    v-if="isFinish"
                    >导出</el-button
                >
            </div>
        </div>

        <div class="detail-top-box" v-if="details && items">
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
                class="u-p-x-20"
            >
            </detail-row-col>
        </div>

        <div
            class="u-p-20 bg-white"
            v-if="details && tableColumns && tableColumns.length"
        >
            <DetailTableMultiple
                :columns="tableColumns"
                :id="reportId"
                ref="detailTable"
                @getRows="getRows"
            />
        </div>
        <PublishPop
            v-model="showPublishPop"
            :id="reportId"
            :isStart="!noStart"
        />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailTableMultiple from "../components/detail-table-multiple.vue"
    import PublishPop from "./components/publish-pop.vue"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import {
        DetailItemType,
        DetailRow,
        IndicatorColumn,
        IndicatorDataRows,
        primaryRows,
        Status,
    } from "../../report-manage/index"
    import { getShowBtn4Page } from "../../collect-task-manage/components/build-table"
    import { ExcelColumnsGenerator } from "../../report-manage/excel-columns-generator"
    import { TableColumn } from "@/core-ui/component/table"
    import { cloneDeep } from "lodash"

    @Component({
        name: routesMap.reportManage.creator.detail,
        components: { DetailRowCol, DetailTableMultiple, PublishPop },
    })
    export default class ReportManageDetail extends Vue {
        private items: ColItem[] = []

        private breadcrumbs: BreadcrumbItem[] = []

        private showStartBtn = false
        private showFinishBtn = false
        private showEditBtn = false
        private showDeleteBtn = false

        private showList = false

        private showPublishPop = false

        private status: Status | null = null

        private columns: IndicatorColumn[] = []
        private rows: IndicatorDataRows[] = []
        private details: DetailRow | null = null
        private tableColumns: TableColumn[] | any[] = []

        // private exportDescription: any = {}
        private exportRows: any[] = []

        private childrenColumnsLabel: string[] = []

        private get labelStyle() {
            return {
                minWidth: "88px",
                textAlign: "left",
                color: "#555",
            }
        }

        private get noStart() {
            return this.status === Status.未开始
        }

        private get isGoing() {
            return this.status === Status.进行中
        }

        private get isFinish() {
            return this.status === Status.已完成
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.reportManage.creator.detail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "报表详情",
                    to: {
                        name: routesMap.reportManage.creator.detail,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.reportManage.creator.detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private actionTask(action: string) {
            return pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_task")
                    .action(action)
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: this.reportId }],
                    })
                    .execute()
                    .then(() => {
                        this.init()
                        this.callRefresh(routesMap.reportManage.creator.index)
                        this.callRefresh(routesMap.reportManage.creator.list)
                    })
            })
        }

        private reportId = ""

        private get isMultiple() {
            return this.details && this.details.type === DetailItemType.Multiple
        }

        created() {
            this.init()
        }

        private init() {
            this.details = null
            this.items = []
            this.columns = []
            this.tableColumns = []
            this.rows = []

            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_task")
                    .detail(
                        this.$route.query.id as string,
                        "for_operate_two_dimensional"
                    )
                    .query()
                    .then((res) => {
                        this.details = sdk.buildRow(res.row, {
                            type: "xg_indicator_group_ref#item_type",
                            name: "",
                        })
                        this.reportId = this.details?.id + ""
                        this.showStartBtn = getShowBtn4Page(res, "start")
                        this.showFinishBtn = getShowBtn4Page(res, "finish")
                        this.showEditBtn = getShowBtn4Page(
                            res,
                            "update_two_dimensional"
                        )
                        this.showDeleteBtn = getShowBtn4Page(res, "delete")
                        this.status = res.row.status?.value as Status
                        this.items = res.meta.header.field_groups
                            .map((item) => {
                                return {
                                    label: item.label + "：",
                                    value: item.template,
                                    hide: !item.visible,
                                    span: 8,
                                }
                            })
                            .filter((i) => i) as ColItem[]
                        this.columns = res.row.indicator_meta
                            ?.value as IndicatorColumn[]
                        this.tableColumns = this.columns?.map((i) => {
                            return {
                                label: i.display_name,
                                prop: i.union_code,
                                description: i.description,
                                value_type: i.value_type,
                                showOverflowTip: false,
                                children:
                                    i.children?.map((j) => {
                                        return {
                                            label: j.display_name,
                                            prop: j.union_code,
                                            showOverflowTip: false,
                                            description: j.description,
                                            children: j.children || [],
                                            value_type: j.value_type,
                                        }
                                    }) || [],
                            }
                        })

                        this.rows = res.row.indicator_data
                            ?.value as IndicatorDataRows[]
                        let data = {}
                        this.rows = this.rows.map((i) => {
                            data = Object.keys(i.indicator_data).reduce(
                                (acc: any, key) => {
                                    acc[i.indicator_data[key].union_code] =
                                        i.indicator_data[key].input_value
                                    return acc
                                },
                                {}
                            )
                            return {
                                ...i,
                                ...data,
                            }
                        })
                        this.setChildrenColumnsLabel()
                    })
            })
        }

        private setChildrenColumnsLabel() {
            this.childrenColumnsLabel = []
            this.columns.forEach((item: any) => {
                if (item.children?.length === 0) {
                    this.childrenColumnsLabel.push("")
                } else {
                    item.children?.forEach((child: any) => {
                        this.childrenColumnsLabel.push(child.display_name)
                    })
                }
            })
            this.childrenColumnsLabel.unshift("")
            const isEmptyString = (str: string) => {
                return str === ""
            }
            if (this.childrenColumnsLabel.every(isEmptyString)) {
                this.childrenColumnsLabel.length = 0 // 将数组置为空
            }
        }

        private edit() {
            this.showPublishPop = true
        }

        private deleteDetail() {
            MessageBox.confirm(`确认删除？`, "删除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_indicator_task")
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.reportId }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("删除成功")
                            closeCurrentTap()
                            this.callRefresh(routesMap.reportManage.creator.index)
                            this.callRefresh(routesMap.reportManage.creator.list)
                        })
                })
            })
        }

        private getRows(rows: any[]) {
            this.exportRows = cloneDeep(rows)
        }

        private exportTemplate(showPrimary = true) {
            // 获取表格表头
            const columnsLabel = [] as any[]
            this.columns.forEach((item: any) => {
                if (item.union_code !== "h") {
                    columnsLabel.push(item.display_name)
                }

                if (item.children?.length > 0) {
                    for (let i = 0; i < item.children.length - 1; i++) {
                        columnsLabel.push("")
                    }
                }
            })

            // const description = Object.values(this.exportDescription)
            // description.unshift("填写说明")
            columnsLabel.unshift("标题")
            let rows = cloneDeep(this.exportRows)
            rows.forEach((item) => {
                delete item.id
                delete item.indicator_data
                delete item.isEdit
                delete item.v
                delete item._access_key
            })
            // 将多级表头平铺成一级
            const flattenArray = (arr: TableColumn[]) => {
                const result = [] as any[]
                function flatten(obj: any) {
                    result.push(obj)
                    if (obj.children && obj.children.length > 0) {
                        obj.children.forEach((child: any) => {
                            flatten(child)
                        })
                    }
                }
                arr.forEach((item) => {
                    flatten(item)
                })

                return result
            }
            const flattenColumns = flattenArray(this.tableColumns).filter(
                (i) => !i.children.length
            )
            // 模板下载时，将填写数据都置空
            if (showPrimary) {
                rows = rows.map((item) => {
                    const newItem = { name: item.name } as any // 保留 name 属性
                    if (item.name === "填写说明") {
                        // 如果是 '说明1'，保留其他属性的值
                        for (const key in item) {
                            if (key !== "name") {
                                newItem[key] = item[key] // 保留原有值
                            }
                        }
                    } else {
                        // 其他对象的属性置为空
                        for (const key in item) {
                            if (key !== "name") {
                                newItem[key] = "" // 置为空
                            }
                        }
                    }
                    return newItem
                })
                // rows = rows.map((item) => {
                //     return Object.keys(item).reduce((acc: any, key) => {
                //         acc[key] = key === "name" ? item[key] : ""
                //         return acc
                //     }, {})
                // })
            }
            // 为空的填写说明，置为-
            const description = rows.find((i) => i.name === "填写说明")
            Object.keys(description).forEach((key) => {
                if (description[key] === "") {
                    description[key] = "-"
                }
            })
            const exportRows = rows.map((targetItem) => {
                const values = [targetItem.name] // 将 name 值放在最前面
                flattenColumns.forEach((sourceItem) => {
                    values.push(targetItem[sourceItem.prop]) // 提取其他属性值
                })
                return values // 返回组成的新数组
            })
            ExcelColumnsGenerator.execute({
                primaryRows: showPrimary ? primaryRows : [],
                columns: columnsLabel,
                childColumns: this.childrenColumnsLabel,
                rows: exportRows as any[],
                fileName: this.details?.name || "报表填报",
            })
        }

        private toExport() {
            this.exportTemplate(false)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .detail-top-header {
        padding-left: 20px;
        padding-right: 20px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .detail-top-title {
        font-weight: 600;
        font-size: 18px;
        color: #222222;
        line-height: 18px;
    }

    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
    }
</style>
