<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20" v-loading="formLoading">
            <form-builder
                v-if="value"
                class="form"
                modelName="xg_organization"
                actionName="insert"
                ref="formBuilder"
                label-position="right"
                label-width="100px"
            ></form-builder>
            <div class="u-flex u-m-t-30 u-row-center">
                <el-button type="primary" @click="close" plain>
                    取消
                </el-button>
                <el-button
                    type="primary"
                    :loading="loading"
                    @click="confirm"
                    class="u-m-l-30"
                >
                    保存
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        buildSelectSource,
        FormController,
        FormItem,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { sleep } from "@/utils"
    import { find, map } from "lodash"
    import { TagManagerTypes } from "uniplat-sdk"
    import { Component, Mixins, Prop } from "vue-property-decorator"

    @Component({ components: { FormBuilder } })
    export default class Add extends Mixins(DialogController, FormController) {
        @Prop()
        private addType!: number

        @Prop({ default: () => null })
        private cusConfig!: any

        formLoading = false

        private get title() {
            return `${this.rowId ? "编辑" : "新增"}组织架构`
        }

        private getAction() {
            return sdk.core
                .model("xg_organization")
                .action(this.rowId ? "update" : "insert")
                .updateInitialParams({
                    selected_list: this.rowId ? [{ id: this.rowId, v: 0 }] : [],
                })
        }

        loading = false

        onOpen() {
            this.init()
        }

        confirm() {
            this.validateForm((isValid) => {
                if (!isValid) {
                    return
                }
                this.loading = true
                const action = this.getAction()
                const data = this.getFormValues()
                if (this.cusConfig?.parent_id) {
                    data.parent_id = this.cusConfig.parent_id
                }
                action.addInputs_parameter(data)
                action
                    .execute({})
                    .then(() => {
                        this.close()
                        this.callRefresh(routesMap.base.org.list)
                    })
                    .finally(() => {
                        this.loading = false
                        this.$emit("update")
                    })
            })
        }

        private tags: TagManagerTypes.TagInfo[] = []
        @Prop()
        private readonly rowId?: number

        private init() {
            this.formLoading = true
            const forms: FormItem[] = this.rowId
                ? [
                      {
                          label: "机构名称",
                          type: FormType.Text,
                          prop: "name",
                          required: true,
                          disabledUniplatRule: true,
                      },
                  ]
                : [
                      {
                          label: "机构名称",
                          type: FormType.Text,
                          prop: "name",
                          required: true,
                          disabledUniplatRule: true,
                      },
                      {
                          label: "上级机构",
                          type: FormType.Tree,
                          prop: "parent_id",
                          option: {
                              setDefaultValue2RootCode: true,
                          },
                          notDisplay: this.cusConfig?.parent_id,
                      },
                      {
                          label: "所属区域",
                          type: FormType.SuperCascader,
                          option: {
                              onlySelectChildNode: true,
                              defaultRegionLevel: 3,
                          },
                          prop: "region_code",
                          required: true,
                      },
                  ]

            return buildFormSections({
                action: this.getAction(),
                needSourceData: true,
                forms,
            })
                .then((r) => {
                    const tagsFormItem = find(r.forms, { prop: "tags" })

                    if (tagsFormItem) {
                        this.tags =
                            r.inputsParameters?.tagGroups.tagGroups[0].tags || []
                        tagsFormItem.sourceInputsParameter = buildSelectSource(
                            map(this.tags, (i) => {
                                return {
                                    key: i.tagName,
                                    value: i.tagName,
                                }
                            })
                        )
                        tagsFormItem.defaultValue = map(
                            r.inputsParameters?.tagGroups.tags,
                            (i) => i.tagName
                        ).join(",")
                    }
                    this.buildForm(r.forms)
                })
                .finally(() => {
                    this.formLoading = false
                    if (this.cusConfig) {
                        sleep(200).then(() => {
                            this.formBuilder?.onInput(
                                { prop: "parent_id" },
                                this.cusConfig.parent_id
                            )
                        })
                    }
                })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
