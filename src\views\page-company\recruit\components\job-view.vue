<template>
    <div class="u-p-x-20 u-font-14 u-m-b-40">
        <div class="title u-m-b-20" v-if="showTitle">岗位信息</div>
        <div class="u-flex u-m-b-8 u-row-between">
            <div class="u-flex">
                <div class="label">岗位名称</div>
                <div class="value">
                    {{ row.name }}
                </div>
            </div>
            <div class="u-flex">
                <div
                    class="primary pointer"
                    @click="toJob"
                    v-if="!showAll || (showAll && row.source_page_url)"
                >
                    {{ detailText }}
                </div>
                <div class="operated primary pointer" @click="onChangeStatus()">
                    {{ openStatus ? "收缩" : "展开" }}
                    <i
                        class="el-icon-arrow-down expend-icon"
                        :class="{ rotated: openStatus }"
                    ></i>
                </div>
            </div>
        </div>
        <detail-row-col
            v-show="openStatus"
            :labelStyle="labelStyle"
            :list="items"
            class="u-p-x-20"
        >
        </detail-row-col>
    </div>
</template>

<script lang='ts'>
    import { config } from "@/config"
    import { routesMap } from "@/router/direction"
    import { getSalary } from "@/utils"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { Row } from "../job/index"

    @Component({ components: { DetailRowCol } })
    export default class JobView extends Vue {
        @Prop()
        private row!: Row

        @Prop({ default: false })
        showAll!: boolean

        @Prop({ default: true })
        showTitle!: boolean

        @Prop({ default: false })
        expand!: boolean

        private get labelStyle() {
            return {
                minWidth: this.showAll ? "126px" : "56px",
                textAlign: "right",
                marginRight: "10px",
                color: "#9098A6",
                lineHeight: "20px",
            }
        }

        private openStatus = false

        mounted() {
            this.openStatus = this.expand
            console.log(JSON.parse(JSON.stringify(this.row)))
        }

        private get detailText() {
            if (!this.showAll) {
                return "岗位详情"
            }
            if (this.showAll && this.row.source_page_url) {
                return ""
            }
        }

        private get items(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            const h = this.$createElement
            const hidden = !this.showAll
            const des = (this.row.description || "").split("\n").filter(Boolean)
            const des2 = (this.row.function_detail || "")
                .split("\n")
                .filter(Boolean)
            return [
                {
                    label: "最低/最高薪资",
                    vNode: h(
                        "span",
                        { class: "salary" },
                        getSalary(this.row.salary_desc)
                    ),
                    span: 8,
                },
                {
                    label: "结算方式",
                    value: this.row.salary_type_label,
                    span: 8,
                    hidden,
                },
                {
                    label: "工作性质",
                    value: this.row.work_type_label,
                    hidden,
                    span: 8,
                },
                { label: "招聘人数", value: this.row.recruit_count, span: 8 },
                { label: "岗位地址", value: this.row.cal_address_detail, span: 8 },
                {
                    label: "招聘过期时间",
                    value: this.row.expired_date_label,
                    hidden,
                    span: 8,
                },

                {
                    label: "年龄要求",
                    value: this.row.age_require_label,
                    span: 8,
                    hidden,
                },
                {
                    label: "最低工作年限",
                    value: this.row.experience_min_label,
                    span: 8,
                    hidden,
                },
                {
                    label: "最高工作年限",
                    value: this.row.experience_max_label,
                    span: 8,
                    hidden,
                },

                {
                    label: "学历要求",
                    value: this.row.education_label,
                    span: 8,
                    hidden,
                },
                {
                    label: "性别要求",
                    value: this.row.gender_require_label,
                    span: 8,
                    hidden: config.projectConfig.hideGender || hidden,
                },
                {
                    label: "语言种类",
                    value: this.row.language_label,
                    hidden,
                    span: 8,
                },
                {
                    label: "外语水平",
                    value: this.row.language_level_label,
                    hidden,
                    span: 8,
                },
                {
                    label: "联系人",
                    value:
                        this.row.position_contact_person || this.row.contact_person,
                    span: 8,
                },
                {
                    label: "联系电话",
                    vNode: renDesensitizationView(h, {
                        value:
                            this.row.position_contact_mobile ||
                            this.row.contact_mobile,
                    }),
                    span: 8,
                },
                {
                    label: "联系邮箱",
                    value: this.row.contact_email,
                    hidden,
                    span: 8,
                },
                {
                    label: "岗位链接",
                    value: this.row.source_page_url,
                    // vNode: h(
                    //     "div",
                    //     {
                    //         style: {
                    //             paddingLeft: "8px",
                    //             color: "#5782EC",
                    //         },
                    //         class: "pointer",
                    //         on: {
                    //             click: () => {
                    //                 this.goOutUrl(this.row.source_page_url)
                    //             },
                    //         },
                    //     },
                    //     "岗位链接"
                    // ),
                    hidden,
                    span: this.row.source_page_url ? 24 : 8,
                },

                {
                    label: "发布时间",
                    value: this.row.online_time_label,
                    span: 8,
                    hidden,
                },

                // {
                //     label: "发布时间",
                //     value:
                //         this.row.position_create_time_label ||
                //         this.row.create_time_label,
                //     span: 8,
                //     hidden: !hidden,
                // },

                // { label: "岗位行业", value: this.row.industry, span: 8, hidden },

                // {
                //     label: "年龄要求",
                //     vNode: h(
                //         "span",
                //         {},
                //         this.row.age_require_min && this.row.age_require_max
                //             ? `${this.row.age_require_min} - ${this.row.age_require_max}`
                //             : "--"
                //     ),
                //     hidden,
                //     span: 8,
                // },
                {
                    label: "职能",
                    value: this.row.function_categories,
                    hidden,
                    span: 8,
                },
                {
                    label: "岗位职责",
                    vNode: h(
                        "div",
                        {},
                        des2.length
                            ? des2.map((e: string) => {
                                  return h("div", {}, e)
                              })
                            : "--"
                    ),
                    // value: this.row.description,
                    span: 24,
                    hidden,
                },
                {
                    label: "描述岗位",
                    vNode: h(
                        "div",
                        {},
                        des.length
                            ? des.map((e: string) => {
                                  return h("div", {}, e)
                              })
                            : "--"
                    ),
                    // value: this.row.description,
                    span: 24,
                    hidden,
                },
                {
                    label: "附件简历必填",
                    value: this.row.require_profile_label,
                    span: 8,
                },
                {
                    label: "工作经历必填",
                    value: this.row.require_work_label,
                    span: 8,
                },
                {
                    label: "学历信息必填",
                    value: this.row.require_education_label,
                    span: 8,
                },
                {
                    label: "来源",
                    vNode: this.row.source_from_logo_label
                        ? h("img", {
                              class: "logo",
                              attrs: {
                                  src: this.row.source_from_logo_label,
                              },
                          })
                        : h("span", {}, this.row.source_from_type_label),
                    span: 8,
                },
                {
                    label: "创建人",
                    value: this.row.creator,
                    hidden,
                    span: 8,
                },
                {
                    label: "创建时间",
                    value: this.row.create_time_label,
                    hidden,
                    span: 8,
                },
                {
                    label: "审核状态",
                    value: this.row.job_fair_audit_status,
                    span: 24,
                    hidden: !this.row.job_fair_audit_status,
                },
                {
                    label: "审核失败原因",
                    value: this.row.job_fair_audit_memo,
                    span: 24,
                    hidden: !this.row.job_fair_audit_memo,
                },
                {
                    label: "岗位是否已删除",
                    value: "是",
                    span: 24,
                    hidden: !this.row.is_del,
                },
            ].filter((e) => !e.hidden)
        }

        private toJob() {
            if (this.showAll) {
                window.open(this.row.source_page_url, "_blank")
            } else {
                this.$router.push({
                    name: routesMap.company.recruit.jobDetail,
                    query: {
                        id:
                            this.row.position_access_key ||
                            (this.row.position_id || this.$route?.query?.id) + "",
                    },
                })
            }
        }

        private goOutUrl(url: string) {
            window.open(url)
        }

        private onChangeStatus() {
            this.openStatus = !this.openStatus
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        font-weight: 500;
        color: #9098a6;
    }
    .value {
        margin-left: 20px;
    }
    .salary {
        color: #ff8d59;
    }
    .logo {
        height: 25px;
    }
    ::v-deep .detail-row {
        background: rgba(#f2f7ff, 0.5);
        border-radius: 5px;
        padding: 15px 0;
        line-height: 20px;
        .item:nth-of-type(3n-1) .label {
            width: 120px;
        }
        .item {
            line-height: 20px;
        }
    }

    .operated {
        margin-left: 20px;
    }
    .rotated {
        transform: rotate(-180deg);
        transition: transform 0.3s;
    }
</style>
