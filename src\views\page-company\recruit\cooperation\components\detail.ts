import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    // {
    //     label: "机构名称",
    //     type: FormType.Text,
    //     prop: "name",
    // },
]
export const tableConfig = (recommend_position_id: string): TableConfig => ({
    model: sdk.core.model("xg_company_bidding").list("hm_agent_list"),
    filter: tableFilter,
    defaultPageSize: 10,
    preFilter: { recommend_position_id },
    predict: {
        recommend_type: "label",
        recommend_desc: "",
        recommend_duration: "label",
        audit_status: "label",
        apply_time: "label",
        start_time: "label",
        end_time: "label",
        agent_name: "hm_agent#agent_name",
        recommend_num: "label",
        create_time: "label",
    },
    oneTabFilter: true,
})
export const columns: TableColumn[] = [
    {
        label: "接单机构名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "接单时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "推荐求职者数量",
        prop: "recommend_num",
        showOverflowTip: true,
    },
    // {
    //     label: "操作",
    //     prop: "h",
    //     showOverflowTip: true,
    // },
]

export interface Row {
    id: number
}

const tableFilter2: TableFilter[] = [
    {
        label: "姓名",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
        prop: "name",
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    {
        label: "当前城市",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
        prop: "permanent_detail",
    },
    {
        label: "年龄",
        type: FormType.TextRange,
        option: {
            type: "number",
        },
        prop: "age",
    },
    {
        label: "学历",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
        prop: "education",
    },
    // {
    //     label: "当前状态",
    //     type: FormType.Text,
    //     prop: "name",
    // },
]

export const tableConfig2 = (created_from_id: string): TableConfig => ({
    model: sdk.core
        .model("xg_candidate_order")
        .list("company_recommend_profile_4_list"),
    filter: tableFilter2,
    defaultPageSize: 10,
    preFilter: { created_from_id, created_from: "agent_bidding" },
    predict: {
        name: "",
        sex: "label",
        age: "",
        id_card: "",
        mobile: "",
        mobile_encode: "",
        region_name: "permanent_city#region_name",
        update_time: "",
        status: "label",
        created_from: "label",
        agent_name: "xg_company_bidding#hm_agent#agent_name",
        education: "label",
        nation: "label",
        province: "permanent_province#region_name_label",
        city: "permanent_city#region_name_label",
        area: "permanent_area#region_name_label",
        address_detail: "permanent_detail",
        status_memo: "label",
        profile_id: "profile#id",
        status4: "label",
        contact_status: "label",
        household_address_detail: "label",
        permanent_detail: "label",
        show: false,
    },
    oneTabFilter: true,
})

export const columns2: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "mobile",
        width: "140px",
        showOverflowTip: true,
        render(h, row) {
            return renDesensitizationView(h, {
                value: row.mobile,
            })
        },
    },
    {
        label: "性别",
        prop: "sex_label",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "年龄",
        prop: "age",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "学历",
        minWidth: "80px",
        prop: "education_label",
        showOverflowTip: true,
    },
    {
        label: "民族",
        prop: "nation",
        showOverflowTip: true,
    },
    {
        label: "户籍城市",
        prop: "household_address_detail",
        showOverflowTip: true,
    },
    {
        label: "常住地址",
        prop: "permanent_detail",
        showOverflowTip: true,
    },
    {
        label: "当前状态",
        prop: "status4_label",
        showOverflowTip: true,
    },
    {
        label: "机构名称",
        prop: "agent_name",
        width: "140px",
        showOverflowTip: true,
    },
    {
        label: "操作",
        width: "175px",
        prop: "h",
    },
]

export interface Row2 {
    id: number
    status_memo: string
    created_from: string
}
