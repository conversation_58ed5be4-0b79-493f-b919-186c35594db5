import {
    BuildFormConfig,
    buildSelectSource,
    FileType,
    FormType,
} from "@/core-ui/component/form"
import { sdk } from "@/service"
import { AuditStatus } from "../human-resource-check"
import { desensitization } from "@/utils/tools"
import { renDesensitizationView } from "@/views/components/common-comps"
import { config, EnvProject } from "@/config"
import { buildBaiduFormItem } from "@/plugins/baidu-map-selector"
import { get } from "lodash"
import { rules } from "@/core-ui/component/form/rule"
import { ColItem } from "@/views/components/detail-row-col"
import { TagManagerTypes } from "uniplat-sdk"
import { getAddress } from "@/utils"
const isXg = config.envProject === EnvProject.孝感项目

export const enum IsFocus {
    否 = 0,
    是 = 1,
}
export interface Row {
    is_open_human_agent: string
    business: string
    enterpriseId: string
    xgHumanAgentId: string
    agent_name: string
    company_code: string
    company_size: string
    legal_person: string
    legal_card_open_id: string
    contact_person: string
    contact_mobile: string
    tele_phone: string
    industory_catalog_label: string
    province: string
    city: string
    region_name: string
    address_detail: string
    reg_origin: string
    pay_amount: string
    oper_status: string
    found_time: string
    reg_authority: string
    object_type_label: string
    xgHumanAgentStatus: AuditStatus
    logo: string
    file: string
    business_file: string
    v: string
    id: string
    remark: string
    webpage: string
    external_contact_person: string
    external_contact_mobile: string
    elegant_image: string
    elegant_video: string
    agent_type: string
    agent_type_label: string
    is_focus: IsFocus
    access_key: string
    [key: string]: any
    tags: { [key: string]: TagManagerTypes.TagInfo[] }
    web_show: boolean
    web_show_label: string
}

export const predict = {
    agent_name: "",
    company_code: "",
    company_size: "label",
    legal_person: "",
    legal_person_hide: "",
    legal_card_open_id: "",
    legal_card_open_id_hide: "",
    contact_person: "",
    contact_mobile: "",
    tele_phone: "",
    industory_catalog: "label",
    industory_catalog_display: "",
    province: "province#region_name",
    city: "city#region_name",
    region_name: "area#region_name",
    business: "tg_enterprise#business",
    create_type: "label",
    address_detail: "address_detail",
    reg_origin: "enterprise_info#reg_origin",
    enterpriseId: "tg_enterprise#id",
    // 实缴资本
    pay_amount: "enterprise_info#pay_amount",
    oper_status: "tg_enterprise#oper_status",
    found_time: "tg_enterprise#found_time",
    reg_authority: "tg_enterprise#reg_authority",
    object_type: "tg_enterprise#object_type_label",
    logo: "logo",
    file: "file",
    business_file: "",
    is_open_human_agent: "is_open_human_agent",
    xgHumanAgentId: "xg_human_agent#id",
    xgHumanAgentAccess_key: "xg_human_agent#_access_key",
    xgHumanAgentStatus: "xg_human_agent#audit_status",
    remark: "",
    black_id: "black#id",
    black_is_del: "black#is_del_label",
    blacktype: "black#type_label",
    black_memo: "black#memo",
    black_update_time: "black#update_time_label",
    black_update_by: "black#update_by",
    black_creator_real_name: "black#creator#real_name",
    recommend_number: "",
    webpage: "",
    external_contact_person: "",
    external_contact_mobile: "",
    elegant_image: "",
    elegant_video: "",
    agent_type: "label",
    tags: "tags",
    is_focus: "",
    access_key: "",
    mgt_province_region_name: "mgt_province#region_name",
    mgt_city_region_name: "mgt_city#region_name",
    mgt_area_region_name: "mgt_area#region_name",
    web_show: "label",
    push_position_count: "label",
    valid_position_count: "label",
    valid_position_recruit_count: "label",
    position_offer_count: "",
    job_fair_position_count: "",
    job_fair_recruit_count: "",
    job_fair_order_offer_num: "",
}

export function buildItems(rows: Row | {}, vue: Vue): ColItem[] {
    const row = (rows || {}) as Row
    const h = vue.$createElement
    console.log("rrr", JSON.parse(JSON.stringify(row)))
    return [
        { label: "企业名称", value: row.agent_name || "" },
        { label: "统一信用代码", value: row.company_code || "" },
        {
            label: "法人姓名",
            vNode: renDesensitizationView(h, {
                value: row.legal_person,
                dValue: row.legal_person_hide,
            }),
        },
        { label: "法人身份证", value: row.legal_card_open_id_hide || "" },
        { label: "企业联系人", value: row.contact_person || "" },
        {
            label: "手机号",
            //  value: row.contact_mobile || "",
            vNode: renDesensitizationView(h, {
                value: row.contact_mobile,
            }),
        },
        // { label: "固定电话", value: row.tele_phone || "" },
        { label: "所属行业", value: row.industory_catalog_display || "" },
        {
            label: "企业所在地",
            value:
                (row.province || "") +
                (row.city || "") +
                (row.region_name || ""),
        },
        { label: "详细地址", value: row.address_detail || "" },
        { label: "注册资本", value: row.reg_origin || "" },
        { label: "营业状态", value: row.oper_status || "" },
        { label: "实缴资本", value: row.pay_amount || "" },
        { label: "成立日期", value: row.found_time || "" },
        { label: "登记机关", value: row.reg_authority || "" },
        { label: "人员规模", value: row.company_size_label || "" },
        { label: "企业类型", value: row.agent_type_label || "" },
        // { label: "企业来源", value: row.object_type_label },
        { label: "推荐权重", value: row.recommend_number || "", hide: !isXg },
        { label: "经营范围", value: row.business || "" },
        {
            label: "管理区域",
            value:
                getAddress(row, [
                    "mgt_province_region_name",
                    "mgt_city_region_name",
                    "mgt_area_region_name",
                ]) || "",
            hide: config.envProject === EnvProject.黄州项目,
        },
        { label: "附件资料", vNode: handlePreImg(row.file, vue) },
        { label: "营业执照", vNode: handlePreImg(row.business_file, vue) },
    ]
        .map((i: any) => {
            return { ...i, span: i?.span || 8 }
        })
        .filter((e) => !e?.hide)
}

function handlePreImg(value: string, vue: Vue) {
    console.log("handlePreImg", value)
    const h = vue.$createElement
    const logoArr = (value || "")
        .split(",")
        .filter((i) => i)
        .map((i: string) => sdk.buildImage(i))
    return !logoArr.length
        ? h("span", undefined, "未上传")
        : h("el-image", {
              props: {
                  "preview-src-list": [...logoArr],
                  fit: "contain",
                  src: logoArr.length ? logoArr[0] : "",
              },
              style: {
                  width: "80px",
                  height: "80px",
                  display: !logoArr.length && "none",
              },
          })
}

export function companyFormConfig(
    id: number,
    obj: {
        v: string
    }
): BuildFormConfig {
    return {
        sdkModel: "xg_agent",
        sdkAction: "edit_agent",
        id,
        forms: [
            {
                label: "企业名称",
                type: FormType.Text,
                prop: "agent_name",
                noEdit: true,
                col: {
                    span: 8,
                },
            },
            {
                label: "统一信用代码",
                type: FormType.Text,
                prop: "company_code",
                noEdit: true,
                col: {
                    span: 8,
                },
            },
            {
                label: "法人姓名",
                type: FormType.Text,
                prop: "legal_person",
                col: {
                    span: 8,
                },
            },
            {
                label: "法人身份证",
                type: FormType.Text,
                prop: "legal_card_open_id",
                col: {
                    span: 8,
                },
                option: {
                    handlerDisplay: (value: any) => {
                        return value ? desensitization(value, 5) : ""
                    },
                },
                rules: [
                    {
                        validator(rule, value, callback) {
                            if (
                                !_.isIdentityNumberWidthPassportAndHongkongAndTaiwan(
                                    value
                                )
                            ) {
                                callback(new Error("请填写正确的身份证号"))
                                return
                            }
                            callback()
                        },
                        message: "请填写正确的身份证号",
                    },
                ],
            },
            {
                label: "企业联系人",
                type: FormType.Text,
                prop: "contact_person",
                col: {
                    span: 8,
                },
            },
            {
                label: "手机号",
                type: FormType.Text,
                prop: "contact_mobile",
                col: {
                    span: 8,
                },
            },
            // {
            //     label: "固定电话",
            //     type: FormType.Text,
            //     prop: "tele_phone",
            //     col: {
            //         span: 8,
            //     },
            // },
            {
                label: "所属行业",
                type: FormType.Cascader,
                prop: "industory_catalog",
                option: {
                    elProps: { checkStrictly: true },
                },
                col: {
                    span: 8,
                },
                required: true,
            },
            // {
            //     label: "企业所在地",
            //     type: FormType.Cascader,
            //     prop: "region_code",
            //     emptyDisplay: "未选择",
            //     col: {
            //         span: 8,
            //     },
            // },
            {
                prop: "address_detail",
                label: "",
                type: FormType.Text,
                hide: true,
            },
            {
                prop: "lat",
                label: "",
                type: FormType.Text,
                hide: true,
            },
            {
                prop: "lng",
                label: "",
                type: FormType.Text,
                hide: true,
            },
            {
                label: "",
                type: FormType.Cascader,
                prop: "region_code",
                option: { elProps: { checkStrictly: true } },
                required: true,
                col: { span: 0 },
                hide: true,
            },
            buildBaiduFormItem({
                label: "详细地址",
                prop: "addressEditFiled",
                col: { span: 8 },
                options: {
                    placeholder: "请选择企业所在地详细地址",
                },
                required: true,
                rules: [
                    {
                        validator: (_, value, callback) => {
                            if (!get(value, "address_detail")) {
                                callback(new Error("未设置企业详细地址"))
                                return
                            }
                            if (!get(value, "lng") || !get(value, "lat")) {
                                callback(
                                    new Error(
                                        "地址经纬度信息缺失，请重新选择地址"
                                    )
                                )
                                return
                            }
                            callback()
                        },
                        trigger: "change",
                    },
                ],
            }),
            {
                label: "注册资本",
                type: FormType.Text,
                prop: "reg_origin",
                col: {
                    span: 8,
                },
            },
            {
                label: "营业状态",
                type: FormType.Select,
                prop: "oper_status",
                col: {
                    span: 8,
                },
            },
            {
                label: "实缴资本",
                type: FormType.Text,
                prop: "pay_amount",
                option: {
                    append: "元人民币",
                    type: "number",
                    handlerDisplay(v, emptyDisplay) {
                        if (!v) return emptyDisplay
                        else if (+v < 10000) {
                            return v + "元人民币"
                        }
                        return Math.ceil(+v / 10000) + "万人民币"
                    },
                },
                col: {
                    span: 8,
                },
            },
            {
                label: "成立日期",
                type: FormType.DatePicker,
                prop: "found_time",
                option: { type: "date" },
                col: {
                    span: 8,
                },
            },
            {
                label: "登记机关",
                type: FormType.Text,
                prop: "reg_authority",
                col: {
                    span: 8,
                },
            },
            {
                label: "人员规模",
                type: FormType.Select,
                prop: "company_size",
                col: {
                    span: 8,
                },
            },
            {
                label: "企业类型",
                type: FormType.Select,
                prop: "agent_type",
                col: {
                    span: 8,
                },
            },
            {
                label: "推荐权重",
                type: FormType.Text,
                prop: "recommend_number",
                defaultValue: obj.v,
                hide: !isXg,
                col: {
                    span: 8,
                },
                option: {
                    disabled: true,
                },
            },
            {
                label: "企业介绍",
                type: FormType.Text,
                prop: "remark",
                hide: true,
            },
            {
                label: "附件资料",
                type: FormType.MyUpload,
                prop: "file",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式",
                    limit: 1,
                },
                col: {
                    span: 8,
                },
            },
            {
                label: "营业执照",
                type: FormType.MyUpload,
                prop: "business_file",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式",
                    limit: 1,
                },
                col: {
                    span: 8,
                },
            },
        ],
    }
}

export function companyFormConfig2(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_agent",
        sdkAction: "edit_remark",
        id,
        forms: [
            {
                label: "是否重点企业",
                type: FormType.Select,
                prop: "is_focus",
                col: {
                    span: 8,
                },
                required: true,
            },
            {
                label: "是否在荆州官网显示",
                type: FormType.Switch,
                prop: "web_show",
                col: {
                    span: 8,
                },
                hide: config.envProject !== EnvProject.荆州项目,
            },
            {
                prop: "公司福利",
                useTag: "公司福利",
                label: "企业福利",
                type: FormType.Select2,
                option: {
                    multiple: true,
                },
                col: {
                    span: 24,
                },
                required: false,
            },
            {
                label: "企业官网",
                type: FormType.Text,
                prop: "webpage",
                col: {
                    span: 8,
                },
                rules: [...rules.httpUrl],
            },
            {
                label: "对外联系人",
                type: FormType.Text,
                prop: "external_contact_person",
                col: {
                    span: 8,
                },
            },
            {
                label: "对外联系电话",
                type: FormType.Text,
                prop: "external_contact_mobile",
                col: {
                    span: 8,
                },
            },
            {
                label: "企业logo",
                type: FormType.MyUpload,
                prop: "logo",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式",
                    limit: 1,
                },
                col: {
                    span: 8,
                },
            },
            {
                label: "企业风采图片",
                type: FormType.MyUpload,
                prop: "elegant_image",
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "支持上传jpg、png等图片格式，最多支持20张",
                    limit: 20,
                },
                col: {
                    span: 16,
                },
            },
            {
                label: "企业风采视频",
                type: FormType.MyUpload,
                prop: "elegant_video",
                option: {
                    fileType: [FileType.Video],
                    placeholder:
                        "支持上传mp4、avi视频格式，最多支持5个，单个不超过20M。",
                    limit: 5,
                },
                col: {
                    span: 12,
                },
            },
            {
                label: "企业介绍",
                type: FormType.Text,
                prop: "remark",
                emptyDisplay: "暂无内容",
                option: {
                    type: "textarea",
                    autosize: { minRows: 3, maxRows: 6 },
                    maxlength: 1000,
                    showWordLimit: true,
                },
                col: {
                    span: 24,
                },
                required: true,
            },
        ],
    }
}
