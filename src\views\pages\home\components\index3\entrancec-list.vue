<template>
    <div class="container">
        <div
            class="card"
            v-for="(i, key) in menuList"
            :key="key"
            @click="nav(i)"
        >
            <div class="title bold u-font-16" :title="i.name">
                {{ i.name }}
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import router, { RouteConfig, routes } from "@/router"
    import { routesMap } from "@/router/direction"
    import { find, flatMapDeep } from "lodash"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { toItemPage } from "../.."
    import { EntranceMenuItem } from "../../entrance-menu-controller-remote"

    @Component({ components: {} })
    export default class EntranceList extends Vue {
        @Prop()
        private menuList!: EntranceMenuItem[]

        private entrance = [
            {
                label: "市场主体管理",
                routeName: routesMap.employmentManage.employmentManage,
            },
            {
                label: "居民管理",
                routeName: routesMap.labourManage.root,
            },
            {
                label: "居民管理",
                routeName: routesMap.labourManage.root,
            },
            {
                label: "居民管理",
                routeName: routesMap.labourManage.root,
            },
            {
                label: "居民管理",
                routeName: routesMap.labourManage.root,
            },
        ]

        private nav(item: EntranceMenuItem) {
            if (!item.meta.children?.length) {
                this.$message.error("您无此模块权限，请联系管理员开通")
            }
            const c = item.meta.children![0]
            toItemPage(c)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px 20px;
        overflow: hidden;

        .card {
            height: 44px;
            color: #4e7beb;
            background: #e5ecff;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            padding: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .title {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
            text-align: center;
        }
    }
</style>
