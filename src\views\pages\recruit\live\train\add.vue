<template>
    <div class="core-ui-table-container container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :items="breadcrumbs" />
            </div>
        </div>
        <div class="bg-white u-p-y-30">
            <div class="u-p-x-20 u-flex u-row-center u-col-top">
                <div class="content u-p-x-40">
                    <form-builder
                        ref="formBuilder"
                        labelWidth="140px"
                        :onValueChange="onValueChange"
                        @updateQueryRemote="updateQueryRemote"
                    ></form-builder>
                </div>
            </div>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button type="primary" @click="close" plain>
                    取消
                </el-button>
                <el-button type="primary" @click="confirm('')">
                    保存至草稿状态
                </el-button>
                <el-button type="primary" @click="confirm('1')">
                    提交审核
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { buildBaiduFormItem } from "@/plugins/baidu-map-selector"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { find, forEach, get } from "lodash"
    import moment from "moment"
    import { Component } from "vue-property-decorator"
    import { LiveAuditStatus } from "."

    @Component({
        name: routesMap.recruit.live.trainAdd,
        components: { FormBuilder },
    })
    export default class Template extends FormController {
        private id = ""
        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: this.id ? "编辑培训" : "新增培训",
                },
            ]
            updateTagItem({
                name: routesMap.recruit.live.add,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.live.trainAdd,
        }

        private data: any = null

        private get getSrc() {
            if (!this.data?.live_cover) return ""
            return sdk.buildImage(this.data.live_cover)
        }

        private get from() {
            return this.$route.query.from as string
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = (this.$route.query.id as string) || ""
            this.setBreadcrumbs()
            return buildFormSections({
                sdkAction: this.id ? "edit_training" : "create_training",
                sdkModel: "training_record",
                forms: [],
                id: this.id,
                sortFormItem: (forms: any) => {
                    forms.push(
                        buildBaiduFormItem({
                            label: "培训地址：",
                            prop: "addressEditFiled",
                            col: { span: 24 },
                            options: {
                                placeholder: "请选择地址",
                                regionKey: "region_code",
                            } as any,
                            required: true,
                            rules: [
                                {
                                    validator: (_, value, callback) => {
                                        if (!get(value, "address_detail")) {
                                            callback(new Error("未设置培训地址"))
                                            return
                                        }
                                        if (
                                            !get(value, "lng") ||
                                            !get(value, "lat")
                                        ) {
                                            callback(
                                                new Error(
                                                    "地址经纬度信息缺失，请重新选择地址"
                                                )
                                            )
                                            return
                                        }
                                        callback()
                                    },
                                },
                            ],
                        })
                    )
                    return forms.map((e: any) => {
                        if (
                            [
                                "train_audit_status",
                                "lng",
                                "lat",
                                "region_code",
                                "address_detail",
                            ].includes(e.prop)
                        ) {
                            e.hide = true
                        }
                        return e
                    })
                },
            }).then((r) => {
                const addressEditFiled = find(r.forms, { prop: "addressEditFiled" })
                if (addressEditFiled) {
                    const data: any = {}
                    const subForms: any = {}
                    ;["address_detail", "lat", "lng"].forEach((i) => {
                        const d = find(r.forms, { prop: i })
                        data[i] = r.data[i]
                        subForms[i] = d
                    })
                    addressEditFiled.extendData = {
                        subForms,
                    }
                    addressEditFiled.defaultValue = data
                }
                this.data = r.data
                this.buildFormFull(r)
            })
        }

        private onValueChange(prop = "", value = "") {
            this.data[prop] = value
            this.data = { ...this.data }
        }

        private confirm(audit = "") {
            const data = this.getFormValues()
            const addressEditFiled = data.addressEditFiled || {}
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                        ...addressEditFiled,
                        addressEditFiled: undefined,
                        train_audit_status: audit
                            ? LiveAuditStatus.待审核
                            : LiveAuditStatus.草稿,
                    })
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model("training_record")
                    .action(this.id ? "edit_training" : "create_training")
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: this.id
                            ? [
                                  {
                                      id: this.id,
                                      v: 0,
                                  },
                              ]
                            : [],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success(this.id ? "编辑成功" : "创建成功")
                        this.callRefresh(routesMap.recruit.live.index)
                        this.callRefresh(routesMap.recruit.live.detail)
                        this.close()
                    })
            })
        }

        private close() {
            closeCurrentTap({
                name: this.from,
                query: this.id
                    ? {
                          id: this.id,
                      }
                    : undefined,
            })
        }

        private updateQueryRemote(property: string, r: any) {
            forEach(r.masters, (e: any) => {
                if (e.type === "multi_intentSearch") {
                    e.type = "multi_intentSearchRemote"
                } else if (e.type === "intentSearch") {
                    e.type = "intentSearchRemote"
                }
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        width: 800px;
    }
    .preview {
        width: 167px;
        .preview-content {
            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #d8d8d8;
            .title {
                line-height: 15px;
                font-size: 13px;
            }
            .img {
                flex: none;
                border-radius: 8px 8px 0 0;
                overflow: hidden;
            }
            img {
                width: 167px;
                margin-right: 8px;
            }
            .btn {
                width: 72px;
                line-height: 28px;
                background: rgba(87, 130, 236, 0.1);
                border-radius: 100px 100px 100px 100px;
                font-size: 12px;
                color: #2254d4;
                text-align: center;
                margin-left: 5px;
            }
        }
    }
</style>
