<template>
    <Container class="seeker-info-container">
        <div id="base-height-div"></div>
        <div
            class="home-container flex-fill"
            id="list-content-container"
            v-if="show"
        >
            <template v-if="pageSize">
                <list1 :cusPageSize="pageSize" v-if="curList === 0"></list1>
                <list2
                    :cusPageSize="pageSize"
                    v-else-if="curList === 1"
                ></list2>
                <list3
                    :cusPageSize="pageSize"
                    v-else-if="curList === 2"
                ></list3>
                <list4
                    :cusPageSize="pageSize"
                    v-else-if="curList === 3"
                ></list4>
                <list5
                    :cusPageSize="pageSize"
                    v-else-if="curList === 4"
                ></list5>
                <list6
                    :cusPageSize="pageSize"
                    v-else-if="curList === 5"
                ></list6>
                <list7
                    :cusPageSize="pageSize"
                    v-else-if="curList === 6"
                ></list7>
                <list8
                    :cusPageSize="pageSize"
                    v-else-if="curList === 7"
                ></list8>
                <List9
                    :cusPageSize="pageSize"
                    v-else-if="curList === 8"
                ></List9>
            </template>
        </div>
    </Container>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { Component, Vue } from "vue-property-decorator"
    import Container from "../common/container.vue"
    import List1 from "./components/居民信息查看.vue"
    import List2 from "./components/注册居民管理.vue"
    import List3 from "./components/已入群人数.vue"
    import List4 from "./components/累计入职人次.vue"
    import List5 from "./components/七步列表.vue"
    import List6 from "./components/政策离线申报.vue"
    import List7 from "./components/总服务人数.vue"
    import List8 from "./components/总服务人次.vue"
    import List9 from "./components/居民信息查看v2.vue"
    import { ResizeController } from "@/views/pages/big-screen/model/resize"

    @Component({
        name: routesMap.bigScreen.smartEmploymentMonitor.list1,
        components: {
            Container,
            List1,
            List2,
            List3,
            List4,
            List5,
            List6,
            List7,
            List8,
            List9,
        },
    })
    export default class Template extends Vue {
        private curList = 0
        private show = false
        private pageSize = 0

        private get type(): string {
            return (this.$route.query?.type as string) || ""
        }

        created() {
            ResizeController.register(this, () => {
                this.show = false
                this.pageSize = 0
                this.$nextTick(() => {
                    this.init()
                })
            })
        }

        mounted() {
            this.init()

            ResizeController.setup()
        }

        private init() {
            if (
                [
                    "劳动力-劳动总数",
                    "人岗匹配-求职者数",
                    "重点人群帮扶-人群总数",
                ].includes(this.type)
            ) {
                this.curList = 0
            }
            if (this.type === "劳动力-已注册劳动力总数") {
                this.curList = 1
            }
            if (this.type === "劳动力-已入群人数") {
                this.curList = 2
            }
            if (this.type === "人岗匹配-累计入职人次") {
                this.curList = 3
            }
            if (this.type.includes("七步工作法")) {
                this.curList = 4
            }
            if (this.type.startsWith("政策_")) {
                this.curList = 5
            }
            if (this.type.includes("重点人群帮扶-总服务人数")) {
                this.curList = 6
            }
            if (this.type.includes("重点人群帮扶-总服务人次")) {
                this.curList = 7
            }
            if (this.type.includes("留孝就创业人数")) {
                this.curList = 8
            }
            this.show = true
            this.$nextTick(() => {
                this.computedHeight()
            })
        }

        private computedHeight() {
            const container = document.getElementById(
                "list-content-container"
            ) as HTMLElement
            const baseTableHeight = (
                document.getElementById("base-height-div") as HTMLElement
            ).clientHeight
            const safeHeight = baseTableHeight + 2
            const innerHeight =
                container.clientHeight - (28 + 143 + 47 + 62 + 28) - safeHeight
            this.pageSize = Math.ceil(innerHeight / baseTableHeight)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .home-container {
        width: 1860px;
        height: 100%;
        background: rgba(16, 66, 180, 0.6);
        border-radius: 16px 16px 16px 16px;
        margin: auto;
        padding: 28px 20px;
        min-height: auto;
        max-height: none;
        overflow-y: auto;
    }
    /deep/ td {
        height: 62px;
    }

    #base-height-div {
        height: 62px;
        width: 20px;
        position: fixed;
        left: -9999px;
    }

    .seeker-info-container {
        /deep/ .container-list-slot {
            min-height: 500px;
            padding-bottom: 20px;
            flex: 1;
        }

        // /deep/ .router.u-flex {
        //     position: fixed;
        //     left: 35px;
        //     top: 80px;
        // }

        /deep/.el-pagination__jump {
            display: none;
        }

        /deep/ .el-pager {
            .more {
                display: none;
            }

            .number {
                display: none;

                &.active {
                    display: block;
                    margin-left: 0px;
                }
            }
        }

        /deep/ .btn-quicknext {
            display: none;
        }

        /deep/ .btn-quicknext + li {
            display: none;
        }
    }
</style>
