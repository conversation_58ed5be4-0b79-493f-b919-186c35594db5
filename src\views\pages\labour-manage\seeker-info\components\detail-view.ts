import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import { getAddress } from "@/utils"
import { formatTime } from "@/utils/tools"
import { renDesensitizationView } from "@/views/components/common-comps"
import { forEach } from "lodash"
import { CreateElement } from "vue"

export interface InjectObj {
    uniplat_uid_calc: number
    groups: string
    friends: string
}

export function companyFormConfigPop1(id: number): BuildFormConfig {
    return {
        sdkModel: "user_profile_basic",
        sdkAction: "back_basic_info_edit",
        id,
        forms: [
            {
                label: "姓名",
                type: FormType.Text,
                prop: "name",
                noEdit: true,
            },
            {
                label: "性别",
                type: FormType.Select,
                prop: "sex",
                noEdit: true,
            },
            {
                label: "身份证",
                type: FormType.Text,
                prop: "id_card_hide",
                noEdit: true,
            },
            {
                label: "联系电话",
                type: FormType.Text,
                required: true,
                prop: "mobile",
            },
            {
                label: "民族",
                type: FormType.Select,
                required: true,
                prop: "nation",
            },
            {
                label: "户口性质",
                type: FormType.Select,
                required: true,
                prop: "reg_residence_property",
            },
            {
                label: "政治面貌",
                type: FormType.Select,
                required: true,
                prop: "political_outlook",
            },
            {
                label: "户籍地址",
                type: FormType.SuperCascader,
                prop: "household_cities",
                required: true,
                emptyDisplay: "未选择",
            },
            {
                label: "常住地址",
                type: FormType.SuperCascader,
                prop: "permanent_cities",
                required: true,
                emptyDisplay: "未选择",
            },
            {
                label: "常住地址详情",
                type: FormType.Text,
                prop: "permanent_detail",
            },
        ],
    }
}

export function companyFormConfigPop2(id: number): BuildFormConfig {
    return {
        sdkModel: "user_profile_basic",
        sdkAction: "back_education_edit",
        id,
        forms: [
            {
                label: "文化程度",
                type: FormType.Select,
                prop: "education",
            },
            {
                label: "毕业院校",
                type: FormType.Text,
                prop: "graduate_school",
            },
            {
                label: "所学专业",
                type: FormType.Text,
                prop: "study_speciality",
            },
            {
                label: "毕业时间",
                type: FormType.DatePicker,
                prop: "graduate_date",
            },
        ],
    }
}

export function buildItems(
    rows: any | {},
    h: CreateElement,
    editTag: Function,
    tagHistory: Function
) {
    const row = (rows || {}) as any
    // console.log("row")
    // console.log(row)
    // const tags1 = ((row as any).tags || {})["重点人群类标签"] || []
    // const tags2 = ((row as any).tags || {})["服务需求标签"] || []
    // const tags3 = ((row as any).tags || {})["业务经办标签"] || []
    // const tags4 = ((row as any).tags || {})["注册用户标签组"] || []
    // const allTags = [...tags4, ...tags1, ...tags2, ...tags3]

    const tags = JSON.parse(row.profile_labels || "{}")
    let tagArr: string[] = []
    forEach(tags, (v) => {
        tagArr.push(...v)
    })
    tagArr = tagArr.filter(Boolean)
    const list1 = [
        { label: "姓名", value: row.name || "" },
        {
            label: "身份证",
            // value: row.id_card_hide || "",
            vNode: renDesensitizationView(h, {
                value: row.id_card,
            }),
        },
        { label: "性别", value: row.sex_label || "" },
        { label: "年龄", value: row.getAge || "" },
        {
            label: "联系电话",
            vNode: renDesensitizationView(h, {
                value: row.mobile,
            }),
        },
        { label: "民族", value: row.nation || "" },
        {
            label: "注册状态",
            vNode: [h("span", row.uniplat_uid_calc ? "已注册" : "未注册")],
        },
        { label: "户口性质", value: row.reg_residence_property_label || "" },
        { label: "政治面貌", value: row.political_outlook_label || "" },
        {
            label: "户籍地址",
            vNode: handleAddress(h, row, [
                "household_province",
                "household_city",
                "household_area",
                "household_countryside",
                "household_village",
            ]),
        },
        {
            label: "常住地址",
            vNode: h("span", [
                handleAddress(h, row, [
                    "permanent_province",
                    "permanent_city",
                    "permanent_area",
                    "permanent_countryside",
                    "permanent_village",
                ]),
                h("span", row.permanent_detail),
            ]),
        },
        {
            label: "标签",
            vNode: h(
                "div",
                {
                    class: "tags-list u-flex",
                },
                [
                    tagArr?.length
                        ? tagArr.map((tag) =>
                              h(
                                  "div",
                                  {
                                      class: "tag",
                                  },
                                  tag
                              )
                          )
                        : h(
                              "span",
                              {
                                  class: "color-9 u-m-r-10",
                                  style: {
                                      "margin-top": "-1px",
                                  },
                              },
                              "暂无标签"
                          ),
                    h(
                        "div",
                        {
                            class: "primary pointer",
                            on: {
                                click: () => editTag(),
                            },
                        },
                        "编辑标签"
                    ),
                    h(
                        "div",
                        {
                            class: "primary pointer",
                            on: {
                                click: () => tagHistory(),
                            },
                        },
                        "历史标签"
                    ),
                ]
            ),
        },
    ].map((i) => {
        if (i.label === "标签") {
            return { ...i, span: 24 }
        }
        return { ...i, span: 12 }
    })

    const list2 = [
        { label: "文化程度", value: row.education_label || "" },
        { label: "所学专业", value: row.study_speciality || "" },
        {
            label: "毕业时间",
            value: formatTime.day(row.graduate_date_label) || "",
        },
        { label: "毕业院校", value: row.graduate_school || "" },
    ].map((i) => {
        return { ...i, span: 12 }
    })

    return [list1, list2]
}

function handleAddress(h: CreateElement, row: any, arr: string[]) {
    return h(
        "span",
        {
            class: "u-line-1",
        },
        getAddress(row, arr) || "--"
    )
}

export const enum Education {
    博士研究生 = 1,
    硕士研究生 = 2,
    大学本科 = 3,
    大学专科 = 4,
    中等专科 = 5,
    职业高中 = 6,
    技工学校 = 7,
    普通高中 = 8,
    初中 = 9,
    小学 = 10,
    其他 = 11,
    未设置 = 0,
}

export const enum JzEducation {
    博士研究生 = "11",
    硕士研究生 = "14",
    大学本科 = "21",
    大学专科 = "31",
    中等专科 = "41",
    职业高中 = "44",
    技工学校 = "47",
    普通高中 = "61",
    初中 = "71",
    小学 = "81",
    其他 = "90",
    不限学历 = "0",
}
