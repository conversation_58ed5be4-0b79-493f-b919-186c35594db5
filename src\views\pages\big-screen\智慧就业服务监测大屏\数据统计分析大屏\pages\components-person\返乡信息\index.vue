<template>
    <div class="content">
        <Header title="返乡信息"></Header>
        <div class="inner">
            <div v-for="item in info" :key="item.title">
                <div class="title u-flex u-col-center">
                    <div class="icon"></div>
                    {{ item.title }}
                </div>
                <div class="items u-flex u-flex-wrap">
                    <div
                        v-for="i in item.items"
                        :key="i.label"
                        class="item-box u-flex"
                        :class="{ full: i.full }"
                    >
                        <div v-if="i.full" class="u-flex full-item">
                            <div class="label">{{ i.label }}：</div>
                            <div>
                                <el-radio-group
                                    v-model="i.value"
                                    class="radio-box"
                                    :disabled="true"
                                >
                                    <el-radio label="是" class="radio">
                                        是
                                    </el-radio>
                                    <el-radio label="否" class="radio">
                                        否
                                    </el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div v-else class="u-flex">
                            <div class="label">{{ i.label }}：</div>
                            <div>{{ i.value || "无" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import Header from "../../../common/header.vue"
    import BaseItem from "../../../common/base-item"
    import { getAddress } from "@/utils"
    import { exportPdfService } from "@/views/common/exportPdf"
    import { forEach } from "lodash"

    @Component({ components: { Header } })
    export default class Index extends BaseItem {
        private info: any[] = []

        protected refresh() {
            this.info = [
                {
                    title: "返乡人员类型",
                    items: [
                        {
                            label: "返乡人员类型",
                            value: this.row?.types_of_returnees,
                        },
                        {
                            label: "返乡原因",
                            value: this.row?.return_reason,
                        },
                    ],
                },
                {
                    title: "返乡前情况",
                    items: [
                        {
                            label: "工作地/居住地",
                            value: getAddress(this.row, [
                                "rehome_province",
                                "rehome_city",
                                "rehome_area",
                            ]),
                        },
                        {
                            label: "从事行业",
                            value: this.row?.rehome_industry,
                        },
                        {
                            label: "平均月工资",
                            value: this.row?.rehome_income,
                        },
                    ],
                },
                {
                    title: "技能情况",
                    items: [
                        {
                            label: "技能特长情况",
                            value:
                                this.row?.skill_detail ||
                                this.row?.skill_detail_fill_in,
                        },
                        {
                            label: "技能等级",
                            value: this.row?.skill_level,
                        },
                        {
                            label: "是否参加过就业创业培训",
                            value: "否",
                            full: true,
                        },
                        {
                            label: "是否有就业创业培训需求",
                            value: this.row.training_or_service_needs || "否",
                            full: true,
                        },
                    ],
                },
                {
                    title: "返乡就业情况",
                    items: [
                        {
                            label: "本地就业创业情况",
                            value: this.row?.return_to_hometown_situation,
                        },
                        {
                            label: "就业创业意愿",
                            value: this.row
                                ?.employment_and_entrepreneurship_intention,
                        },
                        {
                            label: "期望薪资",
                            value: this.row?.expect_salary,
                        },
                    ],
                },
                {
                    title: "包保信息",
                    items: [
                        {
                            label: "包保人",
                            value: this.row?.broker_name,
                        },
                        {
                            label: "包保人负责人",
                            value: "",
                        },
                        {
                            label: "乡镇负责人",
                            value: "",
                        },
                        {
                            label: "对口就业专员",
                            value: "",
                        },
                    ],
                },
            ]
            const arr: any = []
            forEach(this.info, (e) => {
                forEach(e.items, (i) => {
                    arr.push({ label: i.label, value: i.value || "--" })
                })
                if (e.items.length === 3) {
                    arr.push({ label: "", value: "" })
                }
            })
            exportPdfService.pushItem({
                title: "返乡信息",
                order: 2,
                data: arr,
                infoClass: "back_info",
            })
            console.log("info", JSON.parse(JSON.stringify(this.info)))
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .content {
        width: 580px;
        height: 680px;
        position: relative;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;
        /deep/ .header-content {
            height: 40px;
            margin-bottom: 0;
        }
        .inner {
            padding: 10px 20px;
            .title {
                background: linear-gradient(
                    90deg,
                    #073fba 0%,
                    rgba(24, 95, 255, 0.5) 50%,
                    rgba(0, 61, 199, 0) 100%
                );
                width: 100%;
                height: 36px;
                font-weight: 500;
                font-size: 14px;
                color: #fdc850;
                .icon {
                    width: 8px;
                    height: 8px;
                    background: #6b99f6;
                    margin-left: 17px;
                    margin-right: 14px;
                }
            }
            .items {
                gap: 4px 10px;
                padding: 4px 0 10px;
            }
            .item-box {
                width: 265px;
                height: 36px;
                color: #fff;
                border-radius: 4px;
                border: 1px solid #3971e8;
                padding: 0 10px;
                .label {
                    color: #add1ff;
                    margin-right: 8px;
                }
            }
            .full {
                width: 540px;
            }
        }
        /deep/.radio-box {
            margin-left: 72px;
            .el-radio__label {
                color: #fff;
            }
            .el-radio__inner {
                background-color: #3346a9; /* 修改禁用状态下的圆圈背景色 */
                border-color: #3346a9; /* 修改禁用状态下的圆圈边框颜色 */
            }
            .el-radio__inner::after {
                width: 6px;
                height: 6px;
                transform: translate(-50%, -50%) scale(1);
                background-color: #182a88;
            }
            .is-checked {
                .el-radio__inner {
                    background-color: #fdc850; /* 修改禁用状态下的圆圈背景色 */
                    border-color: #3346a9; /* 修改禁用状态下的圆圈边框颜色 */
                }
            }
        }
    }
</style>
