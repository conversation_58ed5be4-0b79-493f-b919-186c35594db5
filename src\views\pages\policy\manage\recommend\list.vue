<template>
    <div id="intelligent-recommend-container">
        <div class="u-flex u-row-right u-m-t-20">
            <el-button
                type="primary"
                v-if="items.length"
                plain
                :loading="exportLoading"
                @click="exportExcel"
            >
                导出
            </el-button>
            <el-button type="primary" plain @click="init()">
                重新匹配
            </el-button>
        </div>
        <common-table
            v-loading="loading"
            class="u-m-t-20 u-p-20 bg-white"
            :data="items"
            :columns="getColumns()"
        >
            <div slot="h" class="u-flex u-row-center" slot-scope="scope">
                <div class="handler-btn" @click="toDetail(scope.row)">详情</div>
            </div>
        </common-table>

        <div class="u-flex u-row-center u-m-t-30" v-if="loaded && page.total">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-sizes="pageSizes"
                :current-page="page.index"
                :page-size="page.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { ExcelGenerator } from "@/core-ui/component/table/excel-generator"
    import { getTextFromVNode } from "@/core-ui/helpers/tools"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Breadcrumb from "@/views/components/breadcrumb/index.vue"
    import { map } from "lodash"
    import { VNode } from "vue"
    import { Component, Mixins, Prop, Vue } from "vue-property-decorator"
    import { columns } from "."
    import { Row as SeekerInfoRow } from "@/views/pages/labour-manage/seeker-info"
    import { Row as PolicyRow } from ".."
    import { tableDataToArray } from "@/core-ui/component/table/base-table"
    @Component({
        name: routesMap.recruit.personIntelligentRecommendDetail,
        components: { CommonTable, Breadcrumb },
    })
    export default class IntelligentRecommend extends Mixins(Vue) {
        isInDetail = true
        exportLoading = false
        getColumns(isExp?: boolean) {
            return columns(isExp)
        }

        items: SeekerInfoRow[] = []
        breadcrumbs: BreadcrumbItem[] = []
        pageSizes = [6, 10, 20, 50, 100]
        loaded = false
        page = {
            size: this.pageSizes[0],
            index: 1,
            total: 0,
            filter: {
                tags: "",
            },
        }

        @Prop()
        private policyRow?: PolicyRow

        private async exportExcel() {
            if (this.exportLoading) {
                return
            }
            if (this.page.total > 2000) {
                this.$message.error("数据量过大超多2000行，请联系开发导出")
                return
            }
            const myColumns = this.getColumns(true).filter(
                (i) => !i.hide && i.prop !== "select" && i.prop !== "h"
            )
            const d: SeekerInfoRow[] = []
            let page = 1
            this.exportLoading = true
            try {
                while (d.length < this.page.total) {
                    const dd = await this.getList(page, 100)
                    d.push(...dd.data)
                    page++
                }
                ExcelGenerator.execute({
                    primaryRows: [],
                    columns: myColumns.map((i) => i.label),
                    rows: tableDataToArray(
                        d,
                        this.getColumns(true),
                        this.$createElement
                    ),
                    fileName: `${this.policyRow?.title}匹配导出`,
                })
            } finally {
                this.exportLoading = false
            }
        }

        protected handleSizeChange(size: number) {
            this.page.size = size
            this.fetchData()
        }

        public async handleCurrentChange(i: number) {
            this.page.index = i
            this.fetchData()
        }

        mounted() {
            this.page.filter.tags = this.policyRow?.second_title || ""
            this.items = []
            this.init()
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.personIntelligentRecommendDetail,
        }

        loading = false

        async init() {
            this.items = []
            this.loaded = false
            this.page.index = 1
            this.fetchData()
        }

        fetchData() {
            this.loading = true
            const promise = [
                this.getList().then((r) => {
                    this.loaded = true
                    this.items = r.data
                }),
            ]
            if (!this.page.total) {
                promise.push(
                    sdk.core
                        .domainService(
                            "xg_project",
                            "back_api",
                            "fetch_profile_list_total"
                        )
                        .post(this.page.filter)
                        .then((r: any) => {
                            this.page.total = +r.total_count
                        })
                )
            }
            return Promise.all(promise).finally(() => {
                this.loading = false
            })
        }

        getList(page?: number, size?: number) {
            return sdk.core
                .domainService("xg_project", "back_api", "fetch_profile_list")
                .post<{ data: SeekerInfoRow[] }>({
                    position_id: this.policyRow?.id,
                    ...this.page.filter,
                    page_index: page || this.page.index,
                    page_size: size || this.page.size,
                })
        }

        private toDetail(row: SeekerInfoRow) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: row._access_key,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
</style>
