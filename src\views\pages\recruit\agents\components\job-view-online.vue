<template>
    <div class="list-view u-p-15">
        <table-container
            v-if="jobConfig"
            v-model="jobConfig"
            filedWidth="200"
            ref="table"
            :useTab="false"
        >
            <div slot="table" slot-scope="{ data }" class="bg-white">
                <div slot="header-right">
                    <div class="u-flex u-row-right u-m-b-15"></div>
                </div>
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button type="text" @click="goJoin(scope.row)">
                            报名
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { Component, Prop } from "vue-property-decorator"
    import { DetailRow, JobRow } from "../detail"
    import { sdk } from "@/service"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import { buildConfig4RemoteMeta } from "@/views/pages/collect-task-manage/components/build-table"
    import { ListTypes } from "uniplat-sdk"

    @Component({
        components: { TableContainer, CommonTable },
    })
    export default class JobView extends BaseTableController<JobRow> {
        @Prop({ default: "" })
        private id!: string

        @Prop()
        private detail!: DetailRow

        private jobConfig: TableConfig | null = null

        private columns: any[] = []

        mounted() {
            pageLoading(() => {
                return buildConfig4RemoteMeta(
                    "xg_company_position",
                    "job_fair_for_operate",
                    {
                        prefilters: {
                            agent_id: this.detail.agentId,
                        },
                        optColumn: {
                            label: "操作",
                            prop: "h",
                            fixed: "right",
                            minWidth: "150px",
                        },
                        useLabelWidth: true,
                    }
                ).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            this.jobConfig = null

            this.$nextTick(() => {
                this.jobConfig = tableConfig
            })

            this.columns = r.columns
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.recruit.jobDetail,
                query: {
                    id: row._access_key,
                    from: this.$route.name,
                },
            })
        }

        private deleteItem(row: any) {
            MessageBox.confirm(`确认移除？`, "移除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("job_fair_agent_position")
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ id: row.id, v: 0 }],
                            prefilters: [
                                {
                                    property: "job_fair_id",
                                    value: this.$route.query.jobFairId as string,
                                },
                                {
                                    property: "agent_id",
                                    value: this.id as string,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("移除成功")
                            this.reloadList()
                        })
                })
            })
        }

        private goJoin(row: any) {
            pageLoading(() => {
                return this.checkStatus(row.name).then((r) => {
                    if (r.length > 0 && r.find((i) => i.positionId === row.id)) {
                        MessageBox.confirm(`该岗位已报名当前招聘会！`, "提示", {
                            showCancelButton: false,
                        }).then(() => {})
                    } else {
                        MessageBox.confirm(`确定报名该岗位至当前招聘会？`, "提示", {
                            confirmButtonText: "确认报名",
                        }).then(() => {
                            sdk.core
                                .model("xg_company_position")
                                .action("job_fair_apply")
                                .addInputs_parameter({
                                    job_fair_id: this.$route.query.jobFairId,
                                })
                                .updateInitialParams({
                                    selected_list: [{ v: 0, id: row.id }],
                                    prefilters: [
                                        {
                                            property: "agent_id",
                                            value: this.detail.agentId,
                                        },
                                    ],
                                })
                                .execute()
                                .then((r) => {
                                    this.$message.success("报名成功")
                                    this.$emit("refresh")
                                })
                        })
                    }
                })
            })
        }

        private checkStatus(positionName: string) {
            // 拿标题去过滤列表，拿到数据，通过id匹配，能匹配到就是已报名，没有匹配到就是未报名
            return new Promise<{ positionId: string }[]>((resolve, reject) => {
                const model = sdk.core
                    .model("job_fair_agent_position")
                    .list("in_job_fair_detail_for_operate")
                    .addPrefilter({
                        job_fair_id: this.$route.query.jobFairId,
                        agent_id: this.detail.agentId,
                        is_del: "0",
                    })
                model
                    .query({
                        pageIndex: 1,
                        item_size: 1,
                    })
                    .then(() => {
                        model
                            .addFilter({
                                match: ListTypes.filterMatchType.exact,
                                property: "position.name",
                                value: positionName,
                            })
                            .queryTab2("全部", {
                                pageIndex: 1,
                                item_size: 99,
                            })
                            .then((r) => {
                                const res = sdk.buildRows<{ positionId: string }>(
                                    r.rows,
                                    {
                                        positionId: "position#id",
                                    }
                                )
                                resolve(res)
                            })
                            .catch(reject)
                    })
                    .catch(reject)
            })
        }
    }
</script>

<style lang='scss' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .list-view {
        background: #fff;
        min-height: 568px;
        ::v-deep .table-tabs {
            display: none !important;
        }
    }
</style>
