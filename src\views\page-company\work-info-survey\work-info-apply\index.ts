import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    {
        label: "任务名称",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "create_time",
        label: "提交时间",
        type: FormType.DatePicker,
    },
]

export const rowPredict = {
    title: "company_task#title",
    real_name: "company_task#creator#real_name",
    start_time: "company_task#start_time",
    end_time: "company_task#end_time",
    finish_time: "company_task#finish_time",
    _access_key: "",
    status: "label",
}

export const detailPredict = {
    name: "tg_enterprise#name",
    title: "",
    status: "label",
    type: "label",
    create_time: "",
    finish_time: "",
    version: "",
    description: "",
    file_id: "company_task_file_record#file_id",
    task_id: "",
    tg_enterprise_id: "",
    enterprise_access_key: "tg_enterprise#_access_key",
    start_time: "company_task#start_time",
    end_time: "company_task#end_time",
}

export const detailRowPredict = {
    agent_name: "company_task_record#xg_agent#agent_name",
    person_id_card_type: "label",
    person_id_card_encode: "",
    person_name: "",
    person_sex: "label",
    person_birthday: "",
    person_nationality: "label",
    education: "label",
    household: "label",
    household_province: "household_province#region_name",
    household_city: "household_city#region_name",
    household_area: "household_area#region_name",
    household_address: "household_address",
    permanent_province: "permanent_province#region_name",
    permanent_city: "permanent_city#region_name",
    permanent_area: "permanent_area#region_name",
    permanent_address: "permanent_address",
    permanent: "label",
    person_phone: "",
    person_phone_encode: "",
    registration_category: "label",
    employment_group: "label",
    employment_category: "label",
    work_start_date: "",
    job_name: "",
    job_type: "label",
    professional_qualification_level: "label",
    professional: "label",
    min_salary: "",
    max_salary: "",
    mama_flag: "label",
    entrepreneurship_flag: "label",
    remark: "",
    status: "label",
    two_check_status: "label",
    error_message: "",
}

const commonColumn: TableColumn[] = [
    {
        label: "任务名称",
        prop: "title",
        showOverflowTip: true,
        width: "240",
    },
    {
        label: "发起人",
        prop: "real_name",
        showOverflowTip: true,
    },
]

export const column: TableColumn[] = [
    ...commonColumn,
    {
        label: "任务统计期间",
        prop: "start_time",
        showOverflowTip: true,
        formatter: (row) => {
            return `${formatTime.day(row.start_time)}-${formatTime.day(
                row.end_time
            )}`
        },
    },
    {
        label: "计划完成时间",
        prop: "finish_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.finish_time)
        },
    },
    {
        label: "状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        showOverflowTip: true,
    },
]

export const column1: TableColumn[] = [
    ...commonColumn,
    {
        label: "填报时间",
        prop: "upload_date",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        showOverflowTip: true,
    },
]

export const column2: TableColumn[] = [
    ...commonColumn,
    {
        label: "审核时间",
        prop: "update_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "未通过原因",
        prop: "error_message",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        showOverflowTip: true,
    },
]

export function tableConfig(
    listName: string,
    defaultPage?: string
): TableConfig {
    return {
        model: sdk.core
            .model("company_task_record")
            .list(listName || "list_for_company_report"),
        filter: tableFilter,
        // preFilter: {
        //     "tg_enterprise.social_credit_code": company_code,
        // },
        defaultPageSize: 10,
        predict: rowPredict,
        column,
        oneTabFilter: true,
        defaultPage,
    }
}

export enum Status {
    草稿 = 6,
    已过期 = 5,
    未发布 = 4,
    审核通过 = 3,
    审核不通过 = 2,
    审核中 = 1,
    待填报 = 0,
}

export interface Row {
    title: string
    real_name: string
    start_time: string
    end_time: string
    finish_time: string
    status: Status
    status_label: string
    _access_key: string
    id: number
    v: number
}

export interface DetailRow {
    name: string
    title: string
    status: Status
    status_label: string
    type_label: string
    create_time: string
    finish_time: string
    version: string
    description: string
    file_id: number
    task_id: string
    tg_enterprise_id: string
    enterprise_access_key: string
    start_time: string
    end_time: string
    id: number
    v: number
}

export const forms = [
    {
        label: "任务名称",
        type: FormType.Text,
        prop: "name",
        col: {
            span: 14,
            offset: 6,
        },
        required: true,
    },
    {
        label: "任务类型",
        type: FormType.Select,
        prop: "date_type",
        col: {
            span: 14,
            offset: 6,
        },
        required: true,
    },
    {
        label: "计划完成时间",
        type: FormType.DatePicker,
        prop: "start_date",
        col: {
            span: 8,
            offset: 6,
        },
    },
    {
        label: "任务内容描述",
        type: FormType.Text,
        prop: "description",
        option: {
            type: "textarea",
            rows: 7,
        },
        col: {
            span: 14,
            offset: 6,
        },
    },
]

export function getEditForm(id: string): BuildFormConfig {
    return {
        sdkModel: "xg_agent_manager",
        sdkAction: "update",
        id: +id,
        forms: forms as any,
    }
}

const recordTableFilter: TableFilter[] = [
    {
        label: "核查状态",
        type: FormType.Select,
        prop: "agent_name",
    },
    {
        label: "工种类别",
        prop: "create_time",
        type: FormType.Select,
    },
]

export function recordTableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("serve_task")
            .list("company_questionnaire_for_operate"),
        filter: recordTableFilter,
        defaultPageSize: 8,
        predict: rowPredict,
    }
}

export const recordColumn: TableColumn[] = [
    {
        label: "岗位名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "工种类别",
        prop: "company_code",
        showOverflowTip: true,
    },
    {
        label: "用工人数",
        prop: "legal_person",
        showOverflowTip: true,
    },
    {
        label: "工作性质",
        prop: "province_code",
        showOverflowTip: true,
    },
    {
        label: "劳动关系",
        prop: "contact_person",
        showOverflowTip: true,
    },
    {
        label: "学历要求",
        prop: "company_code",
        width: "220",
        showOverflowTip: true,
    },
    {
        label: "年龄要求",
        prop: "legal_person",
        showOverflowTip: true,
    },
    {
        label: "用高峰期",
        prop: "province_code",
        showOverflowTip: true,
    },
    {
        label: "技能要求",
        prop: "contact_person",
        showOverflowTip: true,
    },
]

export const checkColumn: TableColumn[] = [
    // {
    //     label: "企业名称",
    //     prop: "agent_name",
    //     showOverflowTip: true,
    //     width: "180",
    // },
    {
        label: "证件类型",
        prop: "person_id_card_type_label",
        showOverflowTip: true,
        width: "120",
    },
    {
        label: "身份证号",
        prop: "person_id_card_encode",
        showOverflowTip: true,
        width: "160",
    },
    {
        label: "姓名",
        prop: "person_name",
        showOverflowTip: true,
    },
    {
        label: "性别",
        prop: "person_sex_label",
        showOverflowTip: true,
    },
    {
        label: "出生日期",
        prop: "person_birthday",
        showOverflowTip: true,
    },
    {
        label: "民族",
        prop: "person_nationality_label",
        showOverflowTip: true,
    },
    {
        label: "文化程度",
        prop: "education_label",
        showOverflowTip: true,
    },
    {
        label: "户籍所在地",
        prop: "household_label",
        showOverflowTip: true,
        formatter(row) {
            return (
                (row.household_province || "") +
                (row.household_city || "") +
                (row.household_area || "") +
                (row.household_address || "")
            )
        },
        width: "150",
    },
    // {
    //     label: "户籍详细地址",
    //     prop: "household_address",
    //     showOverflowTip: true,
    //     width: "180",
    // },
    {
        label: "常住地",
        prop: "permanent_label",
        showOverflowTip: true,
        formatter(row) {
            return (
                (row.permanent_province || "") +
                (row.permanent_city || "") +
                (row.permanent_area || "") +
                (row.permanent_address || "")
            )
        },
        width: "150",
    },
    // {
    //     label: "常住地详址",
    //     prop: "permanent_address",
    //     showOverflowTip: true,
    //     width: "180",
    // },
    {
        label: "手机号",
        prop: "person_phone_encode",
        width: "130px",
        render(h, row) {
            return renDesensitizationView(h, {
                value: row.person_phone,
            })
        },
        showOverflowTip: true,
    },
    {
        label: "登记类别",
        prop: "registration_category_label",
        showOverflowTip: true,
    },
    {
        label: "就业前所属群体",
        prop: "employment_group_label",
        showOverflowTip: true,
        width: "120",
    },
    {
        label: "就业形式",
        prop: "employment_category_label",
        showOverflowTip: true,
    },
    {
        label: "入职日期",
        prop: "work_start_date",
        showOverflowTip: true,
    },
    {
        label: "岗位名称",
        prop: "job_name",
        showOverflowTip: true,
    },
    {
        label: "岗位类别",
        prop: "job_type_label",
        showOverflowTip: true,
        width: "100",
    },
    {
        label: "岗位技能",
        prop: "professional_qualification_level_label",
        showOverflowTip: true,
    },
    {
        label: "职业资格工种",
        prop: "professional_label",
        showOverflowTip: true,
        width: "120",
    },
    {
        label: "最低薪资待遇",
        prop: "min_salary",
        showOverflowTip: true,
        width: "110",
    },
    {
        label: "最高薪资待遇",
        prop: "max_salary",
        showOverflowTip: true,
        width: "110",
    },
    {
        label: "“妈妈岗”标识",
        prop: "mama_flag_label",
        showOverflowTip: true,
        width: "110",
    },
    {
        label: "创业标识",
        prop: "entrepreneurship_flag_label",
        showOverflowTip: true,
    },
    {
        label: "备注",
        prop: "remark",
        showOverflowTip: true,
    },
    {
        label: "审核状态",
        prop: "status_label",
        fixed: "right",
        showOverflowTip: true,
    },
    {
        label: "审核反馈",
        prop: "error_message",
        showOverflowTip: true,
        width: "120",
        fixed: "right",
        render: (h, row) => {
            return h(
                "span",
                {
                    class: "color-red",
                },
                row.error_message
            )
        },
    },
]

export const checkColumn2: TableColumn[] = [
    ...checkColumn,
    {
        label: "审核信息",
        prop: "error_message",
        showOverflowTip: true,
    },
]

export function checkTableConfig(company_task_record_id: string): TableConfig {
    return {
        model: sdk.core
            .model("company_task_emprequire_record")
            .list("list_for_company"),
        filter: [],
        defaultPageSize: 10,
        predict: detailRowPredict,
        column: checkColumn,
        oneTabFilter: true,
        preFilter: { company_task_record_id },
    }
}

export async function getVersion(company_task_record_id: string) {
    return sdk.core
        .model("company_task_emprequire_record")
        .action("batchInsertData")
        .updateInitialParams({
            prefilters: [
                {
                    property: "company_task_record_id",
                    value: company_task_record_id,
                },
            ],
        })
        .query()
        .then((r) => {
            return r.parameters.inputs_parameters[0].default_value
        })
}
