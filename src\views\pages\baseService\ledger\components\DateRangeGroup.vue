<template>
    <el-dropdown
        trigger="click"
        popper-class="date-range-dropdown"
        @command="handleCommand"
        @visible-change="handleVisibleChange"
    >
        <div
            class="date-range-trigger"
            :class="{ 'is-active': dropdownVisible }"
        >
            <span>{{ selectedLabel }}</span>
            <i class="el-icon-arrow-down" />
        </div>
        <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
                v-for="option in dateOptions"
                :key="option.value"
                :command="option.value"
                :class="{ 'is-active': currentDate === option.value }"
            >
                {{ option.label }}
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"

    @Component({
        name: "DateRangeRadioGroup",
    })
    export default class DateRangeRadioGroup extends Vue {
        private currentDate = 1
        private dropdownVisible = false

        private dateOptions = [
            { label: "近一周", value: 1 },
            { label: "近一月", value: 2 },
            { label: "今年", value: 3 },
            { label: "近一年", value: 4 },
            { label: "累计", value: 0 },
        ]

        get selectedLabel(): string {
            const selected = this.dateOptions.find(
                (opt) => opt.value === this.currentDate
            )
            return selected ? selected.label : ""
        }

        handleCommand(value: number) {
            this.currentDate = value
            this.emitDateRange(this.currentDate)
        }

        handleVisibleChange(visible: boolean) {
            this.dropdownVisible = visible
        }

        mounted() {
            this.emitDateRange(this.currentDate)
        }

        private formatDate(date: Date): string {
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, "0")
            const day = date.getDate().toString().padStart(2, "0")
            return `${year}-${month}-${day}`
        }

        private emitDateRange(value: number) {
            const endDate = new Date()
            const startDate = new Date()
            if (value === 0) {
                this.$emit("change", {
                    startTime: "",
                    endTime: "",
                })
                return
            }

            switch (value) {
                case 1: // 近一周
                    startDate.setDate(endDate.getDate() - 7)
                    break
                case 2: // 近一月
                    startDate.setMonth(endDate.getMonth() - 1)
                    break
                case 3: // 今年
                    startDate.setFullYear(endDate.getFullYear(), 0, 1)
                    break
                case 4: // 近一年
                    startDate.setFullYear(endDate.getFullYear() - 1)
                    break
            }

            this.$emit("change", {
                startTime: this.formatDate(startDate),
                endTime: this.formatDate(endDate),
            })
        }
    }
</script>

<style scoped lang="scss">
    .date-range-trigger {
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        font-size: 14px;
        color: #000;
        .el-icon-arrow-down {
            margin-left: 5px;
            transition: transform 0.3s;
        }
    }

    .date-range-trigger.is-active .el-icon-arrow-down {
        transform: rotate(180deg);
    }
</style>
