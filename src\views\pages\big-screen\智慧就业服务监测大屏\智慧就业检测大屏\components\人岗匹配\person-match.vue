<template>
    <div class="card">
        <div class="card-title">近6个月人岗匹配分析</div>
        <div class="shape-container">
            <shape-view
                v-for="(item, index) in itemList"
                :key="index"
                v-bind="item"
                :showRight="false"
            ></shape-view>
        </div>
        <div class="card-bottom">
            人岗匹配响应率<span>{{ percent }}</span>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"
    import ShapeView from "./shape-view.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { ChartQueryResultItem } from "../../../../model"
    import { getXgRegionMapping } from "../../common/regionTree/cache-tree"
    import { config } from "@/config"

    const sourceData = {
        孝感市: {
            匹配人次: 4985,
            浏览人次: 3241,
            入职人次: 276,
            人岗匹配响应率: "55%",
        },
        市辖区: {
            匹配人次: 672,
            浏览人次: 470,
            入职人次: 1,
            人岗匹配响应率: "41%",
        },
        孝南区: {
            匹配人次: 3189,
            浏览人次: 1912,
            入职人次: 185,
            人岗匹配响应率: "60%",
        },
    }

    @Component({ components: { ShapeView } })
    export default class Template extends BaseItem {
        private percent = "0%"

        private itemList = [
            {
                top: 123,
                bottom: 95,
                height: 38,
                color: "#28D3D3",
                title: "匹配人次",
                text: "0",
            },
            {
                top: 95,
                bottom: 83,
                height: 38,
                color: "#3BBCD9",
                title: "浏览人次",
                text: "0",
            },
            {
                top: 83,
                bottom: 64,
                height: 38,
                color: "#378BCC",
                title: "入职人次",
                text: "0",
            },
        ]

        protected async onQueryRegionCodeChange() {
            await getXgRegionMapping().then((r: any) => {
                const mapping = {} as any
                r.forEach((item: any) => {
                    const regionName = item.data.region_name
                    const regionCode = item.data.region_code

                    if ((sourceData as any)[regionName]) {
                        mapping[regionCode] = {
                            ...(sourceData as any)[regionName],
                        }
                    }
                })

                if (!mapping[config.defaultRegion.region_code]) {
                    mapping[config.defaultRegion.region_code] = {
                        ...sourceData["孝感市"],
                    }
                }
                const cur = mapping[+this.currentRegion!.code]
                this.itemList.forEach((item) => {
                    if (cur[item.title] !== undefined) {
                        item.text = cur[item.title].toString()
                    }
                })
                this.percent = cur["人岗匹配响应率"]
            })
            this.refresh()
        }

        protected refresh() {
            // this.query<ChartQueryResultItem[]>(
            //     `talent_six_month_all_data`,
            //     "dashboard_xg_recruit_service_data"
            // ).then((r) => {
            //     this.handleData(r)
            // })
        }

        private handleData(r: ChartQueryResultItem[]) {
            this.percent = this.getV(r, "人岗匹配响应率") + "%"
            this.itemList = this.itemList.map((i) => {
                return {
                    ...i,
                    text: this.getV(r, i.title) + "",
                }
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 220px;
        height: 200px;
        background-size: 100% 100%;
        position: relative;
    }

    .card {
        max-width: unset !important;
    }
    .card-title,
    .card-bottom {
        font-size: 16px;
        color: #9ac3ff;
        line-height: 14px;
        padding-top: 14px;
        padding-bottom: 19px;
        position: absolute;
        span {
            color: #fdc850;
            font-weight: bold;
        }
    }
    .card-bottom {
        width: 100%;
        text-align: center;
        padding-top: 18px;
        padding-bottom: 0;
        margin: 0 auto;
    }
    .shape-container {
        margin-top: 50px;
    }
    /deep/.shape {
        .content {
            font-size: 10px;
            transform: scale(0.83);
            line-height: 0;
            padding-top: 13px !important;
            .chart-title {
                margin-bottom: 6px;
            }
        }
    }
</style>
