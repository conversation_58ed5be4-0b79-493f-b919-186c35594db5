<template>
    <div class="core-ui-table-container" :key="refreshQueryParams" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <el-button
                v-if="show"
                @click="cancel"
                type="primary"
                plain
                class="custom-btn back-btn"
                v-role="[
                    'model.xg_company_position_recommend.action.audit_cancel',
                ]"
            >
                撤回审核
            </el-button>
        </div>
        <template v-if="row">
            <job-detail-view :row="row"></job-detail-view>
            <list-view
                :id="row.position_id"
                v-show="row.audit_status"
            ></list-view>
        </template>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { predict, Row } from "."
    import { cancel_audit } from "../service"
    import JobDetailView from "./components/detail-view.vue"
    import ListView from "./components/list-view.vue"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"

    @Component({
        name: routesMap.recruit.cooperationDetail,
        components: { JobDetailView, ListView },
    })
    export default class CooperationDetail extends Vue {
        private row: Row | null = null
        private id = ""
        refreshConfig = {
            fun: this.init,
            name: "refreshCooperationDetail",
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `机构代招服务`,
                    to: {
                        name: routesMap.recruit.cooperation,
                    },
                },
            ]
            if (this.from === "job-detail") {
                d = [...getCacheBreadcrumbsByRoutePath(routesMap.recruit.jobDetail)]
            }
            d = [
                ...d,
                {
                    label: "人力资源撮合申请详情",
                    to: {
                        name: routesMap.recruit.cooperationDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.cooperationDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string | undefined
        }

        private get show() {
            return (
                this.row &&
                ["待推荐", "待推广", "推广中", "已推荐"].includes(
                    this.row.status_label
                )
            )
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("xg_company_position_recommend")
                    .detail(this.id, "manage_apply_detail")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                    })
            })
        }

        private cancel() {
            cancel_audit(this.row!.id, this.init, () => {
                this.callRefresh("refreshRecruitCooperationList")
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
</style>
