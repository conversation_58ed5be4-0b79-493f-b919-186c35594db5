<template>
    <div class="u-p-20 job-item d-flex" v-if="detail">
        <div class="">
            <div class="u-flex u-m-b-16">
                <div
                    class="u-line-1 w160 u-font-16 color-2 font-family-medium pointer"
                    @click="toJobDetail"
                >
                    {{ detail.name }}
                </div>
                <div
                    @click="toJobDetail"
                    class="status u-m-l-10 pointer"
                    :class="'status_' + detail.online_status"
                >
                    {{ detail.online_status_label }}
                    <span class="el-icon-arrow-right" />
                </div>
                <div class="color-8 u-m-l-10 u-font-14 u-line-1 w160">
                    {{ detail.address_detail }}
                </div>
            </div>
            <div class="u-flex u-m-b-16">
                <div class="w160 u-font-16 price font-family-medium">
                    {{ detail.salary_desc }}
                </div>
                <div class="tags u-flex flex-wrap">
                    <div
                        class="tag"
                        v-for="(tag, index) in formatTags"
                        :key="index"
                    >
                        {{ tag }}
                    </div>
                </div>
            </div>
            <div class="u-flex">
                <div class="w160 u-font-14 u-line-1 u-m-l-10">
                    {{ detail.company }}
                </div>
                <div class="color-8 u-font-14 u-m-l-10">
                    来自：{{ detail.source }} 招聘时间：{{
                        detail.time | time2String
                    }}
                </div>
            </div>
        </div>
        <div v-if="inTaskDetail" class="summary">
            <div class="item" v-for="(item, key) in detail.summary" :key="key">
                <span class="color-2">{{ item.label }}</span>
                <span style="color: #ff8b16">{{ item.content }}</span>
            </div>
        </div>
        <slot />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { flatMap } from "lodash"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { TaskJobRow } from "./task-job.vue"

    @Component({ components: {} })
    export default class JobItem extends Vue {
        @Prop()
        private inTaskDetail!: boolean

        @Prop()
        private detail?: TaskJobRow

        private get formatTags() {
            return flatMap(this.detail?.tags, (item) =>
                flatMap(item, (i) => i.tagName)
            )
        }

        private toJobDetail() {
            this.$router.push({
                name: routesMap.recruit.jobDetail,
                query: { id: this.detail?.position_id },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .job-item {
        width: 100%;
        background: rgba(87, 130, 236, 0.05);
        position: relative;
        & + .job-item {
            margin-top: 10px;
        }
    }

    .w160 {
        max-width: 160px;
    }
    .status {
        &_0 {
            background: #e1e5ea;
            color: #888888;
        }
        &_1 {
            background: #e3ecfe;
            color: #5782ec;
        }

        width: 55px;
        height: 18px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .tags {
        gap: 10px;

        .tag {
            padding: 4px;
            background: #f0f0f0;
            border-radius: 2px;

            font-size: 12px;
            color: #555555;

            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .price {
        color: #e04b2d;
    }

    .summary {
        display: flex;
        flex-wrap: wrap;
        .item {
            padding-left: 10px;
            min-width: 140px;
        }
    }
</style>
