import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import { renDesensitizationView } from "@/views/components/common-comps"

const rowPredict = {
    mobile: "",
    create_time: "",
    name: "user_account#user_profile_basic#name",
    getAge: "user_account#user_profile_basic#getAge",
    id_card_hide: "user_account#user_profile_basic#id_card_hide",
    user_account_id: "user_account#user_profile_basic#id",
    profile_access_key: "user_account#user_profile_basic#_access_key",
    is_registered: "is_registered_label",
    education: "user_account#user_profile_basic#education_info#education_label",
    employment_status:
        "user_account#user_profile_basic#user_profile_current_job_info#employment_status_label",
    sms_send_history_status: "sms_send_history#status_label",
}

export interface Row {
    /** 接收手机号 */
    mobile: string
    /** 发送时间 */
    create_time: string
    /** 姓名 */
    name: string
    /** 年龄 */
    getAge: string
    /** 身份证号 */
    id_card_hide: string
    user_account_id: string
    /** 是否注册 */
    is_registered: number
    is_registered_label: string
    /** 文化程度 */
    education: string
    /** 文化程度[文本] */
    education_label: string
    /** 就业状态 */
    employment_status: string
    /** 就业状态[文本] */
    employment_status_label: string
    id: number
    v: number
    sms_send_history_status_label: string
    profile_access_key: string
}

export const tableFilter: TableFilter[] = [
    {
        prop: "name",
        label: "用户信息",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "create_time",
        label: "发送时间",
        type: FormType.DatePicker,
    },
    // {
    //     prop: "is_passport_user",
    //     label: "模版编号",
    //     type: FormType.Text,
    //     keyValueFilter: {
    //         match: ListTypes.filterMatchType.fuzzy,
    //     },
    // },
    {
        prop: "employment_status",
        label: "就业状态",
        type: FormType.Select,
    },
    {
        prop: "education",
        label: "文化程度",
        type: FormType.Select,
    },
    // {
    //     prop: "birth_date",
    //     label: "出生年月",
    //     type: FormType.Select,
    // },
]

export const column: TableColumn[] = [
    {
        label: "接受手机号",
        prop: "mobile",
        minWidth: "90px",
        align: "left",
        render: (h, row) =>
            renDesensitizationView(h, {
                value: row.mobile,
            }),
    },
    {
        label: "发送时间",
        prop: "create_time",
        minWidth: "120px",
        align: "left",
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "姓名",
        prop: "name",
        align: "left",
    },
    {
        label: "年龄",
        prop: "getAge",
        align: "left",
    },
    {
        label: "身份证号",
        prop: "id_card_hide",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "是否注册",
        prop: "is_registered_label",
        align: "left",
    },
    {
        label: "文化程度",
        prop: "education_label",
        minWidth: "120px",
        align: "left",
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        align: "left",
    },
    {
        label: "短信发送状态",
        prop: "sms_send_history_status_label",
        align: "left",
    },
    {
        label: "关联档案",
        prop: "h",
        align: "left",
    },
]

export function tableConfig(history_id: string): TableConfig {
    return {
        model: sdk.core.model("sms_send_history_detail").list(),
        filter: tableFilter,
        preFilter: {
            history_id: history_id,
        },
        defaultPageSize: 10,
        predict: rowPredict,
        column,
    }
}
