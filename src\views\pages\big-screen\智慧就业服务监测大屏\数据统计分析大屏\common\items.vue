<template>
    <div class="d-flex items-content">
        <Item
            v-for="(item, index) in items"
            :label="item.label"
            :value="item.value"
            :customValue="item.customValue"
            :longValue="item.longValue"
            :digitSuf="item.digitSuf || ''"
            :key="`item-${index}`"
            :len="items.length"
            :isRoute="item.isRoute"
            :unit="item.unit"
            @toRoute="() => $emit('toRoute', item.label)"
        ></Item>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from "vue-property-decorator"
    import Item from "./item.vue"

    interface ItemBox {
        label: string
        value: string
        customValue?: string
        longValue?: string
        digitSuf?: string
        unit?: string
        isRoute?: boolean
    }

    @Component({ components: { Item } })
    export default class Template extends Vue {
        @Prop({ default: () => [] })
        private items!: ItemBox[]
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .items-content {
        gap: 20px;
    }
</style>
