import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { app } from "@/main"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { desensitization } from "@/utils/tools"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"
const tableFilter: TableFilter[] = [
    {
        label: "用户信息",
        option: { placeholder: "请输入姓名、手机号" },
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.exact,
        },
    },
    {
        label: "所在城市",
        type: FormType.MultipleCascader,
        prop: "permanent_province_code",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "就业状态",
        type: FormType.Select,
        prop: "employment_status",
        option: {
            multiple: true,
        },
    },
    {
        label: "服务类型",
        type: FormType.Select,
        prop: "serve_type",
    },
    {
        label: "任务名称",
        type: FormType.Text,
        prop: "p_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "服务状态",
        type: FormType.Select,
        prop: "status",
    },
    {
        label: "发布时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
    // {
    //     label: "获取渠道",
    //     type: FormType.Select,
    //     prop: "获取渠道",
    // },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    // {
    //     label: "年龄",
    //     type: FormType.Select,
    //     prop: "年龄",
    // },
]

export const predict = {
    name: "task_serve_target#user_profile#name",
    sex: "task_serve_target#user_profile#sex_label",
    age: "task_serve_target#user_profile#getAge",
    birth_date: "task_serve_target#user_profile#birth_date_label",
    mobile: "task_serve_target#user_profile#mobile",
    source_type: "task_serve_target#user_profile#source_type_label",
    province:
        "task_serve_target#user_profile#basic_info#permanent_province#region_name",
    city: "task_serve_target#user_profile#basic_info#permanent_city#region_name",
    area: "task_serve_target#user_profile#basic_info#permanent_area#region_name",
    province2:
        "task_serve_target#user_profile#basic_info#household_province#region_name",
    city2: "task_serve_target#user_profile#basic_info#household_city#region_name",
    area2: "task_serve_target#user_profile#basic_info#household_area#region_name",
    employment_status:
        "task_serve_target#user_profile#user_profile_current_job_info#employment_status_label",
    service_type: "serve_project#serve_type_label",
    service_start_date: "serve_project#start_date",
    service_end_date: "serve_project#end_date",
    service_status: "serve_task#status_label",
    status: "label",
    p_name: "serve_task#p_name",
    task_no: "serve_task#task_no",
    task_id: "serve_task#id",
    related_model: "serve_task#related_model",
    project_id: "serve_task#project_id",
    service_change_time: "serve_task#final_change_time_label",
    service_created_time: "serve_task#create_time_label",
    related_serve_content: "label",
    content: "serve_task#serve_questionnaire#content",
    related_id: "serve_task#serve_questionnaire#related_id",
    id_card: "task_serve_target#user_profile#id_card",
    id_card_hide: "task_serve_target#user_profile#id_card_hide",
    uniplat_uid_calc: "task_serve_target#user_profile#uniplat_uid_calc",
    task_type: "serve_task#task_type_label",
    uniplat_version: "",
    task_serve_call_back_id: "",
    job_fair_title: "job_fair#title",
    job_fair_id: "job_fair#id",
    job_fair_access_key: "job_fair#_access_key",
    record_type: "label",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("task_serve_record").list("for_operate"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict,
    }
}

export interface Row {
    id: number
    name: string
    sex: string
    sex_label: string
    birth_date: string
    mobile: string
    source_type: string
    province: string
    city: string
    area: string
    employment_status: string
    service_type: string
    service_start_date: string
    service_end_date: string
    service_status: string
    status: string
    p_name: string
    task_no: string
    task_id: string
    related_model: string
    project_id: string
    service_change_time: string
    service_created_time: string
    related_serve_content: string
    content: string
    related_id: string
    id_card: string
    uniplat_uid_calc: string
    uniplat_uid_calc_label: string
    uniplat_version: string
    task_serve_call_back_id: string
    [key: string]: any
}

export const columns: TableColumn[] = [
    // {
    //     prop: "select",
    //     width: "58",
    //     type: "selection",
    // },
    {
        label: "姓名",
        prop: "name",
        width: "100px",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "性别",
        prop: "sex_label",
        width: "60px",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "年龄",
        prop: "age",
        width: "50px",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "手机号",
        prop: "mobile",
        width: "100px",
        render(h, row) {
            return h("span", desensitization(row.mobile))
        },
        showOverflowTip: true,
    },
    {
        label: "当前城市",
        prop: "city",
        width: "170px",
        render(h, row) {
            return h("span", getAddress(row))
        },
        showOverflowTip: true,
    },
    {
        label: "就业状态",
        width: "100px",
        prop: "employment_status_label",
        showOverflowTip: true,
    },
    {
        label: "服务类型",
        width: "200px",
        prop: "service_type_label",
        showOverflowTip: true,
    },
    {
        label: "任务名称",
        prop: "p_name",
        render(h, row) {
            return h(
                "span",
                {
                    class: "primary pointer",
                    on: {
                        click: () => {
                            app.$router.push({
                                name: routesMap.groupService.serviceManageDetail
                                    .task,
                                query: { id: row.task_id + "" },
                            })
                        },
                    },
                },
                row.p_name
            )
        },
        showOverflowTip: true,
    },
    {
        label: "服务状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "状态更新时间",
        prop: "service_change_time",
        width: "160px",
        render(h, row) {
            return h("span", (row.service_change_time || "").replace("T", " "))
        },
        showOverflowTip: true,
    },
    {
        label: "服务发布时间",
        prop: "service_created_time_label",
        width: "160px",
        render(h, row) {
            return h("span", (row.service_created_time || "").replace("T", " "))
        },
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
    },
]

export interface RecommendRow {
    id: number
    from_id: string
    _access_key: string
    from_access_key: string
}

const recommendTableFilter: TableFilter[] = [
    {
        label: "姓名",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "匹配岗位",
        type: FormType.Select,
        prop: "匹配岗位",
    },
    {
        label: "就业状态",
        type: FormType.Select,
        prop: "就业状态",
        option: {
            multiple: true,
        },
    },
    {
        label: "学历",
        type: FormType.Select,
        prop: "学历",
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "性别",
    },
    {
        label: "年龄",
        type: FormType.TextRange,
        option: { type: "number" },
        prop: "年龄",
    },
    {
        label: "匹配企业",
        type: FormType.Text,
        prop: "匹配企业",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "当前城市",
        type: FormType.Cascader,
        prop: "当前城市",
    },
    {
        label: "推荐人",
        type: FormType.Text,
        prop: "推荐人",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "推荐结果",
        type: FormType.Select,
        prop: "推荐结果",
    },
    {
        label: "推荐时间",
        type: FormType.DatePicker,
        option: { type: "daterange" },
        prop: "推荐时间",
    },
]

export const recommendPredict = {
    name: "task_serve_target#user_profile#name",
    sex: "task_serve_target#user_profile#sex_label",
    birth_date: "task_serve_target#user_profile#birth_date_label",
    mobile: "task_serve_target#user_profile#mobile",
    mobile_hide: "task_serve_target#user_profile#mobile_hide",
    id_card: "task_serve_target#user_profile#id_card",
    id_card_hide: "task_serve_target#user_profile#id_card_hide",
    tags: "tags",
    employment_status:
        "task_serve_target#user_profile#user_profile_current_job_info#employment_status",
    position_name: "xg_candidate_order#position#name",
    recommend_status: "xg_candidate_order#status1_label",
    agent_name: "xg_candidate_order#position#agent#agent_name",
    agent_contact_person: "xg_candidate_order#position#agent#contact_person",
    agent_contact_mobile: "xg_candidate_order#position#agent#contact_mobile",
    recommend_person: "xg_candidate_order#create_user#name",
    recommend_time: "create_time_label",
    age: "task_serve_target#user_profile#getAge",
    education: "task_serve_target#user_profile#basic_info#education_label",
    from_id: "xg_candidate_order#created_from_id",
    from: "xg_candidate_order#created_from_label",
    from_access_key: "recommend_access_key",
    province:
        "task_serve_target#user_profile#basic_info#permanent_province#region_name",
    city: "task_serve_target#user_profile#basic_info#permanent_city#region_name",
    area: "task_serve_target#user_profile#basic_info#permanent_area#region_name",
}
export function recommendTableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("task_serve_record")
            .list("for_hr_recommend_operate"),
        filter: recommendTableFilter,
        defaultPageSize: 10,
        predict: recommendPredict,
    }
}

export const recommendColumns: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        align: "left",
        width: "80px",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile_hide",
        align: "left",
        width: "100px",
        showOverflowTip: true,
    },
    {
        label: "匹配岗位",
        prop: "position_name",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "匹配企业",
        prop: "agent_name",
        width: "100px",
        showOverflowTip: true,
    },
    {
        label: "企业联系人",
        prop: "contact_person",
        render(h, row) {
            return h(
                "div",
                {
                    class: "u-flex u-flex-col",
                },
                row.agent_contact_person || row.agent_contact_mobile
                    ? [
                          h("div", {}, row.agent_contact_person),
                          //   h("div", {}, row.agent_contact_mobile),
                          renDesensitizationView(h, {
                              value: row.agent_contact_mobile,
                          }),
                      ]
                    : "-"
            )
        },
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "性别",
        prop: "sex_label",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "年龄",
        prop: "age",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "身份证号",
        prop: "id_card_hide",
        width: "150px",
        showOverflowTip: true,
    },
    {
        label: "学历",
        prop: "education_label",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "当前城市",
        prop: "address",
        render(h, row) {
            return h("span", {}, getAddress(row))
        },
        showOverflowTip: true,
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        showOverflowTip: true,
    },
    {
        label: "推荐时间",
        prop: "recommend_time_label",
        showOverflowTip: true,
    },
    {
        label: "推荐人",
        prop: "recommend_person",
        width: "70px",
        showOverflowTip: true,
    },
    {
        label: "人员状态",
        prop: "recommend_status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "70px",
        showOverflowTip: true,
    },
]

const tableFilter2: TableFilter[] = [
    {
        label: "用户姓名",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "用户手机号",
        type: FormType.Text,
        prop: "mobile",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "所在城市",
        type: FormType.Cascader,
        prop: "permanent_province_code",
        option: { filterable: true, elProps: { checkStrictly: true } } as any,
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    {
        label: "学历",
        type: FormType.Select,
        prop: "education",
    },
    {
        label: "出生日期",
        type: FormType.DatePicker,
        prop: "birth_date",
        option: {
            type: "daterange",
        },
    },
    {
        label: "招聘会时间",
        type: FormType.DatePicker,
        prop: "start_time",
        option: {
            type: "daterange",
        },
    },
]

export const predict2 = {
    name: "task_serve_target#user_profile#name",
    sex: "task_serve_target#user_profile#sex",
    birth_date: "task_serve_target#user_profile#birth_date",
    id_card_hide: "task_serve_target#user_profile#id_card_hide",
    mobile_hide: "task_serve_target#user_profile#mobile_hide",
    education: "task_serve_target#user_profile#education_info#education_label",
    province:
        "task_serve_target#user_profile#basic_info#permanent_province#region_name",
    city: "task_serve_target#user_profile#basic_info#permanent_city#region_name",
    area: "task_serve_target#user_profile#basic_info#permanent_area#region_name",
    employment_status:
        "task_serve_target#user_profile#user_profile_current_job_info#employment_status_label    ",
    job_fair_title: "job_fair#title",
    job_fair_id: "job_fair#id",
    job_fair_access_key: "job_fair#_access_key",
}
export function tableConfig2(): TableConfig {
    return {
        model: sdk.core.model("task_serve_record").list("for_job_fair_operate"),
        filter: tableFilter2,
        defaultPageSize: 10,
        predict: predict2,
    }
}

export const columns2: TableColumn[] = [
    { label: "姓名", prop: "name", align: "left", showOverflowTip: true },
    {
        label: "性别",
        prop: "sex_label",
        width: "50px",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "出生日期",
        prop: "birth_date",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile_hide",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "身份证号",
        prop: "id_card_hide",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "学历",
        prop: "education_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "当前城市",
        prop: "id",
        align: "left",
        formatter(row) {
            return getAddress(row)
        },
        showOverflowTip: true,
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "招聘会",
        prop: "job_fair_title",
        align: "left",
        showOverflowTip: true,
    },
    { label: "操作", prop: "h", align: "left", showOverflowTip: true },
]
