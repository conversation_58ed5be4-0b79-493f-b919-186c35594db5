<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex" v-if="row">
                <template v-if="row.status === 0">
                    <el-button
                        type="primary"
                        plain
                        @click="toOperate('delete')"
                        class="custom-btn batch-btn"
                        v-if="showdeleteBtn"
                    >
                        删除
                    </el-button>
                    <el-button
                        type="primary"
                        plain
                        @click="toOperate('online')"
                        class="custom-btn batch-btn"
                        v-if="showOnlineBtn"
                    >
                        上架
                    </el-button>

                    <el-button
                        type="primary"
                        @click="toEdit"
                        class="custom-btn batch-btn"
                        v-if="showEditBtn"
                    >
                        编辑
                    </el-button>
                </template>
                <template v-else>
                    <el-button
                        type="primary"
                        plain
                        @click="toOperate('offline')"
                        class="custom-btn batch-btn"
                        v-if="showOfflineBtn"
                    >
                        下架
                    </el-button>
                </template>
            </div>
        </div>
        <div class="detail-container" v-if="row">
            <div class="content u-flex-1 u-p-20">
                <div class="title">基本信息</div>
                <div class="u-p-20">
                    <detail-row-col
                        :labelStyle="{ width: '120px' }"
                        :list="items"
                    ></detail-row-col>

                    <div class="u-flex u-p-t-22 u-col-top">
                        <div class="label flex-none">政策内容：</div>
                        <div v-html="row.description" class="content-row"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="u-m-t-20" v-if="row">
            <el-tabs v-model="cur">
                <el-tab-pane name="1" :label="`${row.serve_type_label}申报`">
                    <!-- <div class="title apply-title u-flex u-row-between">
                        <div class="u-p-l-40">
                            {{ row.serve_type_label }}申报
                        </div>
                    </div> -->
                    <ApplyList
                        :id="row.id"
                        :type="row.serve_target_type"
                        :policyName="row.policy_name"
                        ref="applyList"
                        @onTabChange="onTabChange"
                    >
                        <div class="u-flex u-row-right u-m-b-20">
                            <el-button
                                @click="showImport"
                                type="primary"
                                v-if="applyTabName === '离线申请'"
                            >
                                导入政策办理人员
                            </el-button>
                            <el-button
                                type="primary"
                                @click="exportCurrent"
                                plain
                            >
                                导出
                            </el-button>
                        </div>
                    </ApplyList>
                </el-tab-pane>
                <el-tab-pane
                    name="2"
                    :label="`员工列表`"
                    v-if="
                        row.no === 'jiuyeshehuobutie' ||
                        row.no === 'jiuyeshehuobutie_bk'
                    "
                >
                    <!-- <div class="title apply-title u-flex u-row-between">
                        <div class="u-p-l-40">
                            {{ row.serve_type_label }}申报
                        </div>
                    </div> -->
                    <PersonList
                        :id="row.id"
                        :type="row.serve_target_type"
                        :policyName="row.policy_name"
                        ref="personList"
                        :showImport1Btn="row.no === 'jiuyeshehuobutie_bk'"
                    >
                    </PersonList>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { ColItem } from "@/views/components/detail-row-col"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../single-page/components/tags-view"
    import { detailPredict, DetailRow, TemplateItem } from "./index"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { getShowBtn4Page } from "../collect-task-manage/components/build-table"
    import ApplyList from "./components/apply-list.vue"
    import { MessageBox } from "element-ui"
    import FileList from "@/views/page-company/policy/components/file-list.vue"
    import { getColumns, getFormData } from "../policy-apply-record"
    import { config, EnvProject } from "@/config"
    import PersonList from "./components/person-list.vue"

    @Component({
        name: routesMap.publishPolicyDD.policyDetail,
        components: { DetailRowCol, ApplyList, FileList, PersonList },
    })
    export default class PublishPolicyDetail extends Vue {
        breadcrumbs: BreadcrumbItem[] = []

        private templateList: TemplateItem[] = []

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from as string),
                {
                    label: "政策详情",
                    to: {
                        name: routesMap.publishPolicyDD.policyDetail,
                        query: {
                            id: this.id,
                            from: this.from,
                        },
                    },
                },
            ]

            updateTagItem({
                name: routesMap.publishPolicyDD.policyDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private get isCompany() {
            return this.row?.serve_type_label === "企业"
        }

        private get isPerson() {
            return this.row?.serve_type_label === "居民"
        }

        private id = ""
        private row: DetailRow | null = null
        private showdeleteBtn = false
        private showOnlineBtn = false
        private showOfflineBtn = false
        private showEditBtn = false

        private applyTabName = ""
        cur = "1"

        private get items() {
            if (!this.row) return []
            const item: ColItem[] = [
                { label: "政策名称：", value: this.row.name || "" },

                {
                    label: "服务对象：",
                    value: this.row.serve_type_label || "",
                },
                { label: "政策类型：", value: this.row.no_label || "" },
                {
                    label: "申报时间：",
                    value: [
                        this.row.start_time_label,
                        this.row.end_time_label,
                    ].join(" - "),
                },
                {
                    label: "养老医疗缴费起止时间：",
                    value:
                        [
                            this.row.social_start_time_label,
                            this.row.social_end_time_label,
                        ].join("至") || "",
                    hide: this.row.no !== "linghuojiuyeshebao",
                    span: 24,
                },
                { label: "申报限制：", value: this.row.apply_limit_label || "" },
                { label: "受惠对象：", value: this.row.award_type_label },
                { label: "对外发布单位：", value: this.row.open_org || "" },
                { label: "所属科室：", value: this.row.belong_office || "" },
                {
                    label: "联系电话：",
                    value: this.row.contact_mobile || "",
                },
                { label: "联系人：", value: this.row.contact_person || "" },
            ]
            return item.map((i) => {
                return { ...i, span: i.span || 12 }
            })
        }

        refreshConfig = {
            name: routesMap.publishPolicyDD.policyDetail,
            fun: this.init,
        }

        mounted() {
            this.init()
        }

        init() {
            if (this.$route.name === routesMap.publishPolicyDD.policyDetail) {
                this.id = this.$route.query.id as string
            }
            this.row = null
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("zc_apply_type")
                    .detail(this.id, "manage")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, detailPredict)
                        console.log("row", JSON.parse(JSON.stringify(this.row)))
                        this.showdeleteBtn = true || getShowBtn4Page(r, "delete")
                        this.showOnlineBtn = true || getShowBtn4Page(r, "online")
                        this.showOfflineBtn = true || getShowBtn4Page(r, "offline")
                        this.showEditBtn = true || getShowBtn4Page(r, "update")
                        // this.browser()
                    })
            })
        }

        private browser() {
            return sdk.core
                .model("user_browse")
                .action("client_browse")
                .addInputs_parameter({
                    model_name: "zc_apply_type",
                    object_id: this.row!.id,
                })
                .execute()
        }

        private toEdit() {
            this.$router.push({
                name: routesMap.publishPolicyDD.create,
                query: { id: this.row?.id + "", from: this.$route.name },
            })
        }

        private toOperate(actionName: string) {
            const label =
                actionName === "online"
                    ? "上架"
                    : actionName === "offline"
                    ? "下架"
                    : "删除"
            MessageBox.confirm(`确认${label}？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("zc_apply_type")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.row?.id + "" }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            if (actionName === "delete") {
                                closeCurrentTap({
                                    name: routesMap.publishPolicyDD.list,
                                })
                                return
                            }
                            this.init()
                        })
                })
            })
        }

        isJz = config.envProject === EnvProject.荆州项目

        private async exportCurrent() {
            if (this.isJz) {
                const type = +this.row!.serve_target_type === 2 ? "agent" : ""
                const col = await getColumns(this.row!.question_id, type)
                getFormData(this.row!.question_id, type, col, this.id + "")
            } else {
                const ref: any = this.$refs.applyList
                if (this.row?.no === "shoucilaiebutie") {
                    ref?.exportExcelUniplatV2({
                        template_name: "首次来鄂补贴人员导出表",
                    })
                }
                if (this.row?.no === "linghuojiuyeshebao") {
                    ref?.exportExcelUniplatV2({
                        template_name: "掇刀区灵活就业人员社会保险补贴导出表",
                    })
                } else {
                    ref?.exportToExcel()
                }
            }
        }

        private toQuestionDetail() {
            this.$router.push({
                name: routesMap.publishPolicyDD.formDetail,
                query: {
                    id: this.row?.question_access_key + "",
                    from: this.$route.name,
                },
            })
        }

        private showImport() {
            ;(this.$refs.applyList as any).showImportPop = true
        }

        private onTabChange(tabName: string) {
            this.applyTabName = tabName
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
    .detail-container {
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
            &.apply-title {
                padding-left: 0;
                padding-right: 0;
                margin: 10px 0;
            }
        }
        .content {
            background: #fff;
            .label {
                width: 98px;
                margin-right: 10px;
                color: #555;
            }
        }
        .content-row {
            color: #333;
        }
        /deep/.attachment-template {
            width: 50%;
        }
    }

    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
        margin-left: 30px;
    }
</style>
