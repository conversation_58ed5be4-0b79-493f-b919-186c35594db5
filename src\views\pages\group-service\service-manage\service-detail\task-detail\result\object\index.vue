
<template>
    <div v-if="tableConfig">
        <table-container
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            @setTotal="$emit('setTotal', $event)"
            class="container"
        >
            <div slot="title" v-if="!inTaskMessage">
                <breadcrumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div slot="table" slot-scope="{ data }">
                <div
                    class="u-flex u-row-right u-p-b-20 u-p-t-20"
                    v-if="isUnStart"
                >
                    <el-button
                        type="primary"
                        size="mini"
                        plain
                        @click="showUserImport = true"
                    >
                        注册居民导入
                    </el-button>
                    <el-button
                        type="primary"
                        size="mini"
                        plain
                        @click="showProfileImport = true"
                    >
                        档案数据导入
                    </el-button>
                    <el-button
                        type="primary"
                        size="mini"
                        plain
                        @click="showCreate = true"
                    >
                        单条新增
                    </el-button>
                    <el-button
                        type="primary"
                        size="mini"
                        plain
                        @click="removeBatch"
                    >
                        批量删除
                    </el-button>
                    <el-button
                        type="primary"
                        size="mini"
                        plain
                        @click="showObjectImport = true"
                    >
                        批量导入
                    </el-button>
                </div>
                <common-table
                    :data="data"
                    :tableConfig="tableConfig"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="viewDetail(scope.row)">
                            查看详情
                        </el-button>
                        <el-button
                            type="text"
                            v-if="isUnStart"
                            @click="onDelete(scope.row)"
                        >
                            删除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <excel-import
            v-if="importConfig"
            v-model="showObjectImport"
            title="设置服务对象"
            :importConfig="importConfig"
            @refresh="refresh2"
        />
        <create v-model="showCreate" :taskId="taskId" @refresh="refresh2" />
        <profile-import
            v-model="showProfileImport"
            :taskId="taskId"
            @refresh="refresh2"
        />
        <users-import
            v-model="showUserImport"
            :taskId="taskId"
            @refresh="refresh2"
        />
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import {
        TableColumn as TableColumnImpl,
        TableConfig,
    } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { checkRole } from "@/installer/role"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Breadcrumb from "@/views/components/breadcrumb/index.vue"
    import { MessageBoxConfirm } from "@/views/components/common-pop"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { MessageBox } from "element-ui"
    import { omit } from "lodash"
    import { Component, Prop } from "vue-property-decorator"
    import { batchDelete, columns, Row, tableConfig } from "."
    import Create from "./components/create.vue"
    import ProfileImport from "./components/profile-import.vue"
    import UsersImport from "./components/users-import.vue"

    const isDev = process.env.VUE_APP_ENV === "test"

    @Component({
        name: routesMap.groupService.serviceManageDetail.result.serve_target_count,
        components: {
            TableContainer,
            CommonTable,
            Breadcrumb,
            ExcelImport,
            Create,
            ProfileImport,
            UsersImport,
        },
    })
    export default class TaskObjectServeList extends BaseTableController<Row> {
        @Prop({ default: false })
        private inTaskMessage!: boolean

        @Prop({ default: "" })
        private fromProp!: string

        @Prop({ default: false })
        private hideAdd!: boolean

        showProfileImport = false
        showUserImport = false
        taskId = 0
        private tableConfig: TableConfig | null = null
        private readonly columns: TableColumnImpl[] = columns
        private get from() {
            return this.$route.query.from as "" | "task" | "project" | "pc"
        }

        private get isUnStart() {
            if (!isDev && checkRole("/tablelist/user_profile_basic/manage_v3")) {
                return false
            }
            return (
                (this.inTaskMessage || this.$route.query.status === "0") &&
                !this.hideAdd
            )
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: this.isUnStart ? "配置服务对象" : "服务对象",
                    to: {
                        name: routesMap.groupService.serviceManageDetail.result
                            .serve_target_count,
                        query: {
                            ...this.$route.query,
                        },
                    },
                },
            ]
            if (this.from) {
                d.unshift(...getCacheBreadcrumbsByRoutePath(this.from))
            }
            updateTagItem({
                name: routesMap.groupService.serviceManageDetail.result
                    .serve_target_count,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        showCreate = false
        showObjectImport = false
        importConfig: any = null
        refreshConfig = {
            name: routesMap.groupService.serviceManageDetail.result
                .serve_target_count,
            fun: this.init,
        }

        // 新增数据后刷新
        refresh2() {
            this.refreshList()
            this.callRefresh(routesMap.groupService.serviceManageDetail.task)
        }

        init() {
            this.taskId = +(this.$route.query.task_id || 0)
            this.tableConfig = tableConfig(
                omit(this.$route.query, "from", "status")
            )
            this.setBreadcrumbs()
            this.importConfig = {
                templateUrl: window.location.origin + "/file/导入服务对象.xls",
                modelName: "serve_task",
                actionName: "upload_serve_target",
                bigActionImportParams: {
                    inputs_parameters: [
                        {
                            property: "task_id",
                            value: this.taskId,
                        },
                    ],
                    selected_list: [
                        {
                            id: this.taskId,
                            v: 0,
                        },
                    ],
                    batchSchema: "导入服务对象身份证",
                    batchSchemaTable: {
                        schema: "导入服务对象身份证",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
        }

        mounted() {
            this.init()
        }

        private onClick() {}
        viewDetail(row: Row) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: row.profile_access_key || row.userId,
                    from:
                        this.fromProp ||
                        routesMap.groupService.serviceManageDetail.result
                            .serve_target_count,
                },
            })
        }

        ids: number[] = []

        handleSelectionChange(e: { ids: number[] }) {
            this.ids = e.ids
        }

        removeBatch() {
            batchDelete(this.ids).then(() => {
                this.refresh2()
            })
        }

        onDelete(row: Row) {
            MessageBox.confirm("请确认是否删除？", "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        instance.confirmButtonLoading = true
                        sdk.core
                            .model("task_serve_target")
                            .action("delete_for_task")
                            .updateInitialParams({
                                selected_list: [{ id: row.id, v: 0 }],
                            })
                            .execute()
                            .then(() => {
                                done()
                                this.refresh2()
                            })
                            .finally(() => {
                                instance.confirmButtonLoading = false
                            })
                    } else {
                        done()
                    }
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
