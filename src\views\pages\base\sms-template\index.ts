import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    {
        label: "短信模板编码",
        type: FormType.Text,
        prop: "template_code",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "模板标题",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "短信内容",
        type: FormType.Text,
        prop: "content",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
]

export const enum Type {
    定向发送短信 = 0,
    验证码短信 = 1,
    任务短信 = 2,
    小程序订阅通知 = 3,
    人才推荐短信 = 4,
    招聘会短信 = 5,
}
export interface Row {
    template_code: string
    title: string
    content: string
    type: Type
    type_label: string
    create_time: string
    real_name: number
    id: number
    v: number
}
export const predict = {
    template_code: "",
    title: "",
    content: "",
    type: "label",
    create_time: "label",
    real_name: "creator#real_name",
    format_params: "",
    preview_params: "",
}
export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("sms_template").list("for_operate"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
    }
}

export const columns: TableColumn[] = [
    {
        label: "短信模板编码",
        prop: "template_code",
        width: "170px",
        showOverflowTip: true,
    },
    { label: "模板标题", prop: "title", showOverflowTip: true },
    {
        label: "短信内容",
        prop: "content",
        width: "200px",
        // showOverflowTip: true,
    },
    {
        label: "内容预览",
        prop: "preview_params",
        width: "200px",
        // showOverflowTip: true,
    },
    // {
    //     label: "参数值",
    //     prop: "format_params",
    //     width: "200px",
    //     // showOverflowTip: true,
    // },
    { label: "模板类型", prop: "type_label", showOverflowTip: true },
    { label: "创建时间", prop: "create_time_label", showOverflowTip: true },
    { label: "创建人", prop: "real_name", showOverflowTip: true },
    { label: "操作", prop: "h", width: "180px", showOverflowTip: true },
]

export function createFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "sms_template",
        sdkAction: id ? "update" : "insert",
        id: id || undefined,
        needSourceData: true,
        forms: [
            {
                label: "短信渠道",
                type: FormType.Select,
                prop: "mgt_region_code",
                required: true,
                option: {
                    disabled: !!id,
                },
            },
            {
                label: "短信模板编码",
                type: FormType.Text,
                prop: "template_code",
                required: true,
                option: {
                    disabled: !!id,
                },
            },
            {
                label: "模板标题",
                type: FormType.Text,
                prop: "title",
                required: true,
                option: {
                    disabled: !!id,
                },
            },
            {
                label: "短信内容",
                type: FormType.Text,
                prop: "content",
                required: true,
                option: {
                    disabled: !!id,
                },
            },
            {
                label: "模板类型",
                type: FormType.Select,
                prop: "type",
                required: true,
                hide: true,
                option: {
                    disabled: true,
                },
            },
            {
                label: "预设参数值",
                type: FormType.DetailList,
                option: {
                    addTip: "添加预设参数值",
                    columns: [
                        { prop: "param_key_label", label: "参数名：" },
                        { prop: "param_value", label: "参数值：" },
                    ],
                    forms: [
                        {
                            label: "参数名",
                            type: FormType.Text,
                            prop: "param_key",
                            required: true,
                        },
                        {
                            label: "参数值",
                            type: FormType.Text,
                            prop: "param_value",
                            required: true,
                        },
                    ],
                },
                prop: "sms_template_params",
            },
        ],
    }
}
export interface Item {
    value: any
    display?: any
}
export interface CheckItem {
    keyValue: string
    rowData: {
        create_by: Item
        final_change_time?: Item
        is_need_attach_file: Item
        is_need_check_item: Item
        is_need_finish_condition: Item
        name: Item
        update_by: Item
        update_time?: Item
        is_lock?: Item
    }
}

const tableFilter2: TableFilter[] = [
    {
        prop: "real_name",
        label: "操作人",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "create_time",
        label: "发送时间",
        type: FormType.DatePicker,
    },
    {
        prop: "template_code",
        label: "模版编号",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "title",
        label: "模版名称",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    // {
    //     prop: "title",
    //     label: "发送状态",
    //     type: FormType.Select,
    //     keyValueFilter: {
    //         match: ListTypes.filterMatchType.fuzzy,
    //     },
    // },
]

export const predict2 = {
    mobile: "",
    name: "user_profile_basic#name",
    template_code: "",
    title: "sms_template#title",
    type: "sms_template#type_label",
    create_time: "",
    real_name: "creator_account#real_name",
    user_name: "user_account#user_profile_basic#name",
    user_age: "user_account#user_profile_basic#getAge",
    user_id_card: "user_account#user_profile_basic#id_card_hide",
    employment_status:
        "user_account#user_profile_basic#user_profile_current_job_info#employment_status_label",
    user_account_id: "user_account#user_profile_basic#id",
    params: "",
    status: "label",
}
export function tableConfig2(): TableConfig {
    return {
        model: sdk.core.model("sms_send_history").list("for_operate_v2"),
        filter: tableFilter2,
        defaultPageSize: 10,
        predict: predict2,
    }
}

export const columns2: TableColumn[] = [
    { label: "短信模板编号", prop: "template_code", showOverflowTip: true },
    { label: "模板名称", prop: "title", showOverflowTip: true },
    { label: "发送人", prop: "real_name", showOverflowTip: true },
    {
        label: "发送时间",
        prop: "create_time",
        minWidth: "120px",
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    { label: "发送状态", prop: "status_label", showOverflowTip: true },
    { label: "操作", prop: "h", showOverflowTip: true },
]
