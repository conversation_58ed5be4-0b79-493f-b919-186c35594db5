import { CreateElement } from "vue"
import { Type } from "."
import { ListTypes } from "uniplat-sdk"
import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
    getStatusLabel,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { routesMap } from "@/router/direction"
import { renDesensitizationView } from "@/views/components/common-comps"
import { Vue } from "vue-property-decorator"

export const detailPredict = {
    title: "job_fair#title",
    type: "job_fair#type_label",
    theme_type: "job_fair#theme_type_label",
    start_time: "job_fair#start_time_label",
    end_time: "job_fair#end_time_label",
    region_name: "job_fair#last_place#region_name",
    organizer: "job_fair#organizer",
    audit_status: "label",
    hosted_by: "job_fair#hosted_by",
    contact_person: "agent#contact_person",
    contact_mobile: "agent#contact_mobile",
    description: "job_fair#description",
    apply_time: "label",
    audit_uid: "",
    wx_qr: "",
    agent_name: "agent#agent_name",
    company_code: "agent#company_code",
    company_size: "agent#company_size_label",
    industory_catalog: "agent#industory_catalog_label",
    remark: "agent#remark",
    province: "agent#province#region_name",
    city: "agent#city#region_name",
    area: "agent#area#region_name",
    deadline: "job_fair#apply_time_label",
    address_detail: "job_fair#cal_address_detail",
    agent_address_detail: "agent#address_detail",
    job_fair_id: "",
    agent_id: "",
    sign_status: "label",
    qr_code: "",
    audit_date: "label",
    audit_memo: "",
    agent_enterprise_type: "agent#tg_enterprise#enterprise_type_label",
    break_info: "",
    found_time: "agent#tg_enterprise#found_time_label",
    cal_scale: "agent#tg_enterprise#cal_scale",
    company_name: "position#company_name",
    logo: "agent#logo",
    agentId: "agent#id",
}

const enum ThemeType {
    综合类 = 1,
    就业援助月专场 = 2,
    春风行动专场 = 3,
    大众城市联合招聘高校毕业生专场 = 4,
    民营企业招聘月专场 = 5,
    残疾人就业帮扶专场 = 6,
    百日千万网络招聘行动专场 = 7,
    全国高校毕业生就业服务专场 = 8,
    就业扶贫行动日专场 = 9,
    金秋招聘月专场 = 10,
    人力资源市场高校毕业生就业服务周专场 = 11,
    中央企业面向西藏青海新疆高校毕业生专场 = 12,
    下岗失业人员专场 = 13,
    大中专毕业生专场 = 14,
    残疾人专场 = 15,
    技校毕业生专场 = 16,
    农民工专场 = 17,
    失地农民专场 = 18,
    高级人才专场 = 19,
    其他 = 21,
    百日冲刺专场 = 22,
    重大项目重点企业用工服务保障专场 = 23,
}

export enum AuditStatus {
    审核中 = 0,
    审核通过 = 1,
    审核不通过 = 2,
}

export interface DetailRow {
    /** 招聘会名称 */
    title: string

    /** 招聘会类型 */
    type: Type

    /** 招聘会类型[文本] */
    type_label: string

    /** 主题类型 */
    theme_type: ThemeType

    /** 主题类型[文本] */
    theme_type_label: string

    /** 召开时间-起 */
    start_time: string

    /** 召开时间-止 */
    end_time: string

    /** 区域名称 */
    region_name: string

    /** 主办单位 */
    organizer: string

    /** 审核状态 */
    audit_status: AuditStatus

    /** 审核状态[文本] */
    audit_status_label: string

    /** 承办单位 */
    hosted_by: string

    /** 企业联系人 */
    contact_person: string

    /** 联系手机号 */
    contact_mobile: string

    /** 介绍 */
    description: string

    /** 报名时间 */
    apply_time: string

    /** 操作人 */
    audit_uid: string

    /** 二维码 */
    wx_qr: string

    /** 企业名称 */
    agent_name: string

    /** 统一信用代码 */
    company_code: string

    /** 人员规模 */
    company_size: string

    /** 人员规模[文本] */
    company_size_label: string

    /** 所属行业 */
    industory_catalog: string

    /** 所属行业[文本] */
    industory_catalog_label: string

    /** 企业简介 */
    remark: string
    job_fair_id: string
    agent_id: string
    agent_address_detail: string
    id: number
    v: number
    position_wait_count: string
    agentId: string
}

export const detailListConfig = (obj_id: string): TableConfig => {
    return {
        model: sdk.core.model("work_flow_log@xg_project").list("detail_log"),
        defaultPageSize: 4,
        preFilter: { model_name: "job_fair_agent_apply", obj_id },
        predict: {
            action_name: "",
            name: "system_user_references#info#name",
            mobile: "system_user_references#mobile",
            create_time: "label",
        },
        column: [
            {
                label: "事项名称",
                prop: "action_name",
                showOverflowTip: true,
            },
            {
                label: "操作人",
                prop: "name",
                render(h, row) {
                    return h("div", {}, row.name || row.mobile)
                },
                showOverflowTip: true,
            },
            {
                label: "操作时间",
                prop: "create_time_label",
                showOverflowTip: true,
            },
        ],
    }
}

const jobPredict2 = {
    agent_name: "position#agent#agent_name",
    name: "position#name",
    industry: "position#industry",
    salary: "position#salary",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    recruit_count: "position#recruit_count",
    qr_code: "",
    audit_status: "label",
    function_categories: "position#function_categories",
    position_online_status: "position#online_status_label",
    _access_key: "",
    salary_desc: "position#salary_desc",
}

export const enum JobAuditStatus {
    待审核 = 0,
    允许参会 = 1,
    不允许参会 = 2,
}

export interface JobRow {
    /** 企业名称 */
    agent_name: string

    /** 岗位名称 */
    name: string

    /** 岗位职能 */
    industry: number

    /** 薪资待遇 */
    salary: string

    /** 区域名称 */
    region_name: number

    /** 报名人数 */
    recruit_count: number
    audit_status: JobAuditStatus
    audit_status_label: string
    id: number
    v: number
    _access_key: string
}

export const jobConfig = (
    job_fair_id: string,
    agent_id: string
): TableConfig => {
    return {
        model: sdk.core
            .model("job_fair_agent_position")
            .list("in_job_fair_detail_for_operate"),
        defaultPageSize: 10,
        preFilter: {
            job_fair_id,
            agent_id,
            is_del: "0",
        },
        predict: jobPredict2,
        column: [
            {
                prop: "select",
                type: "selection",
                selectable(row: any) {
                    return row.audit_status_label === "待审核"
                },
            },
            {
                label: "岗位名称",
                prop: "name",
                showOverflowTip: true,
            },
            {
                label: "岗位职能",
                prop: "function_categories",
                showOverflowTip: true,
            },
            {
                label: "薪资待遇",
                prop: "salary",
                render(h, row) {
                    const salary = row.salary_desc || row.salary + "元/月"
                    return h("div", {}, salary)
                },
                showOverflowTip: true,
            },
            {
                label: "工作地址",
                prop: "region_name",
                formatter(row) {
                    return `${row.province || ""}${row.city || ""}${
                        row.area || ""
                    }`
                },
                showOverflowTip: true,
            },
            {
                label: "招聘人数",
                prop: "recruit_count",
                formatter(row) {
                    return row.recruit_count || 0
                },
                showOverflowTip: true,
            },
            // {
            //     label: "岗位状态",
            //     prop: "position_online_status_label",
            //     showOverflowTip: true,
            // },
            {
                label: "参会状态",
                prop: "audit_status_label",
                render(h, row) {
                    return getStatusLabel(
                        h,
                        {
                            label: row.audit_status_label,
                            value: row.audit_status,
                        },
                        ["#aaa", "#65D2A3", "#E04B2D"]
                    )
                },
                showOverflowTip: true,
            },
            { label: "操作", prop: "h", width: "220px", showOverflowTip: true },
        ],
        filter: [
            {
                type: FormType.Select,
                label: "审核状态",
                prop: "audit_status",
            },
        ],
    }
}

const jobFairPredict = {
    name: "name",
    mobile_encode: "",
    sex: "label",
    age: "",
    education: "",
    agent_name: "agent_name",
    position_name: "position#name",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    salary: "salary",
    create_time: "label",
    status: "label",
    profile_id: "profile#id",
    profile_access_key: "profile#_access_key",
    position_online_status: "position#online_status_label",
}

export interface Row {
    /** 投递岗位名称 */
    name: string

    /** 手机号 */
    mobile_encode: string

    /** 性别 */
    sex: number
    sex_label: string

    /** 年龄 */
    age: string

    /** 学历 */
    education: string

    /** 投递企业名称 */
    agent_name: string

    /** 区域名称 */
    region_name: string

    /** 薪资待遇 */
    salary: string

    /** 报名时间 */
    create_time: string
    create_time_label: string

    /** 状态 */
    status: string
    status_label: string
    id: number
    v: number
}

export const jobFairConfig = (
    job_fair_id: string,
    agent_id: string
): TableConfig => {
    return {
        model: sdk.core.model("xg_candidate_order").list("job_fair_agent_list"),
        defaultPageSize: 10,
        filter: [
            {
                label: "求职者名称",
                type: FormType.Text,
                prop: "name",
                keyValueFilter: {
                    match: ListTypes.filterMatchType.fuzzy,
                },
            },
        ],
        preFilter: {
            created_from: "job_fair",
            created_from_id: job_fair_id,
            "position.agent_id": agent_id,
        },
        predict: jobFairPredict,
        column: [
            {
                label: "姓名",
                prop: "name",
                showOverflowTip: true,
            },
            {
                label: "电话",
                prop: "mobile_encode",
                showOverflowTip: true,
            },
            {
                label: "性别/学历",
                prop: "industry",
                render(h, row) {
                    return h("div", {}, row.sex_label + "/" + row.education)
                },
                showOverflowTip: true,
            },
            {
                label: "投递企业名称",
                prop: "agent_name",
                showOverflowTip: true,
            },
            {
                label: "投递岗位名称",
                prop: "position_name",
                showOverflowTip: true,
            },
            {
                label: "薪资待遇",
                prop: "salary",
                showOverflowTip: true,
            },
            {
                label: "工作区域",
                prop: "area",
                showOverflowTip: true,
            },
            {
                label: "报名时间",
                prop: "create_time_label",
                // formatter: (row) => {
                //     return formatTime.seconds(row.create_time)
                // },
                showOverflowTip: true,
            },
            {
                label: "投递状态",
                prop: "status_label",
                showOverflowTip: true,
            },
            { label: "操作", prop: "h", width: "160px", showOverflowTip: true },
        ],
    }
}

export function buildAgentItems(rows: any | {}, vue: Vue) {
    const row = (rows || {}) as any
    return [
        {
            label: "报名时间：",
            value: row.apply_time_label || "",
        },
        {
            label: "审核时间：",
            value: row.audit_date_label || "",
        },
        {
            label: "审核状态：",
            vNode:
                handleStatus(vue.$createElement, row.audit_status_label) || "",
        },
        {
            label: "审核备注：",
            value: row.audit_memo || "",
        },
        { label: "企业宣传码：", vNode: handlePreImg(row.wx_qr, vue) },
        { label: "违约信息：", value: row.break_info || "" },
        // { label: "签到状态：", value: row.sign_status_label || "" },
    ].map((i) => {
        return { ...i, span: 12 }
    })
}

export function buildAgentItems2(rows: any | {}, vue: any) {
    const row = (rows || {}) as any
    const h = vue.$createElement
    const hideBack = vue.breadcrumbs.some(
        (i: any) => i?.to?.name === routesMap.recruit.jobFairDetail
    )
    console.log(hideBack)
    return [
        {
            label: "招聘会名称：",
            vNode: h(
                "div",
                { class: "u-flex" },
                [
                    h("span", {}, row.title),
                    hideBack
                        ? 0
                        : h(
                              "el-button",
                              {
                                  attrs: {
                                      type: "text",
                                  },
                                  class: "u-m-l-20",
                                  on: {
                                      click: () => {
                                          vue.$router.push({
                                              name: routesMap.recruit
                                                  .jobFairDetail,
                                              query: {
                                                  id: row.job_fair_id,
                                              },
                                          })
                                      },
                                  },
                              },
                              "详情"
                          ),
                ].filter(Boolean)
            ),
            span: 24,
        },
        {
            label: "企业名称：",
            value: row.agent_name || "",
        },
        {
            label: "统一信用代码：",
            value: row.company_code || "",
        },
        {
            label: "所属地区：",
            value: row.agent_address_detail || "",
        },
        {
            label: "经济类型：",
            value: row.agent_enterprise_type_label || "",
        },
        // {
        //     label: "企业类型：",
        //     value: row.company_code || "",
        // },
        { label: "所属行业：", value: row.industory_catalog_label || "" },
        {
            label: "企业规模：",
            value: row.company_size_label || row.cal_scale || "",
        },
        { label: "工商注册时间：", value: row.found_time_label || "" },
        // { label: "企业联系人：", value: row.contact_person || "" },
        // { label: "联系手机：", value: row.contact_mobile || "" },
        {
            label: "企业地址：",
            value: `${row.province || ""}${row.city || ""}${row.area || ""}${
                row.agent_address_detail || ""
            }`,
            span: 24,
        },
        { label: "企业简介：", value: row.remark || "", span: 24 },
    ].map((i) => {
        return { ...i, span: i.span || 12 }
    })
}

export function buildAgentItems3(rows: any | {}, h: any) {
    const row = (rows || {}) as any
    return [
        {
            label: "联系人：",
            value: row.contact_person || "",
        },
        {
            label: "联系人手机号：",
            vNode: renDesensitizationView(h, row.contact_mobile),
        },
        // {
        //     label: "办公电话：",
        //     value: row.apply_time_label || "",
        // },
        // {
        //     label: "电子邮箱：",
        //     value: row.apply_time_label || "",
        // },
    ].map((i) => {
        return { ...i, span: 12 }
    })
}

export function buildJobItems(rows: any | {}) {
    const row = (rows || {}) as any
    console.log(JSON.parse(JSON.stringify(row)))
    return [
        {
            label: "岗位名称：",
            value: row.name || "",
        },
        {
            label: "薪资待遇：",
            value: row.salary || "",
        },
        { label: "职能：", value: row.function_categories || "" },
        {
            label: "招聘人数：",
            value: row.recruit_count || "",
        },
        {
            label: "工作地址：",
            value: `${row.province || ""}${row.city || ""}${row.area || ""}`,
        },
        { label: "工作性质：", value: row.work_type_label || "" },
        {
            label: "福利待遇：",
            value:
                (row.tags["职位福利"] || [])
                    .map((e: any) => e.tagName)
                    .filter(Boolean)
                    .join("，") || "",
        },
        { label: "企业名称：", value: row.agent_name || "" },
    ].map((i) => {
        return { ...i, span: 12 }
    })
}

export function buildJobItems2(rows: any | {}) {
    const row = (rows || {}) as any
    return [
        {
            label: "工作年限：",
            value: row.experience || "",
        },
        {
            label: "学历要求：",
            value: row.education_label || "",
        },
        {
            label: "年龄要求：",
            value: row.age_require || "",
        },
        {
            label: "语种要求：",
            value: row.language_label || "",
        },
        {
            label: "专业要求：",
            value: row.major_desc_label || "",
        },
        {
            label: "外语水平：",
            value: row.language_level_label || "",
        },
        {
            label: "户口要求：",
            value: row.position_cal_household_address || "",
            span: 24,
        },
        {
            label: "岗位职责：",
            value: row.description || "",
            span: 24,
        },
        {
            label: "任职要求：",
            value: row.function_detail || "",
            span: 24,
        },
    ].map((i) => {
        return { span: 8, ...i }
    })
}

export function buildJobItems3(rows: any | {}, vue: Vue) {
    const row = (rows || {}) as any
    const h = vue.$createElement
    return [
        {
            label: "联系人：",
            value: row.contact_person || "",
        },
        {
            label: "电子邮箱：",
            value: row.contact_email || "",
        },
        {
            label: "联系手机号：",
            // value: row.contact_mobile || "",
            vNode: renDesensitizationView(h, {
                value: row.contact_mobile,
            }),
        },
    ].map((i) => {
        return { ...i, span: 12 }
    })
}

export function buildJobItems4(rows: any | {}) {
    const row = (rows || {}) as any
    return [
        {
            label: "工作年限：",
            value: row.experience || "",
        },
        {
            label: "学历要求：",
            value: row.education_label || "",
        },
        { label: "年龄要求：", value: row.age_require || "" },
        { label: "语言语种：", value: row.language_label || "" },
        { label: "外语水平：", value: row.language_level || "" },
    ].map((i) => {
        return { ...i, span: 12 }
    })
}

export function handlePreImg(value: string, vue: Vue) {
    const h = vue.$createElement
    const logoArr = (value || "")
        .split(",")
        .filter((i) => i)
        .map((i: string) => sdk.buildImage(i))
    return !logoArr.length
        ? h("span", undefined, "--")
        : h("el-image", {
              props: {
                  "preview-src-list": [...logoArr],
                  fit: "contain",
                  src: logoArr.length ? logoArr[0] : "",
              },
              style: {
                  width: "80px",
                  height: "80px",
                  display: !logoArr.length && "none",
              },
          })
}

export const detailJobPredict = {
    title: "job_fair#title",
    type: "job_fair#type_label",
    theme_type: "job_fair#theme_type_label",
    start_time: "job_fair#start_time_label",
    end_time: "job_fair#end_time_label",
    region_name: "job_fair#last_place#region_name",
    organizer: "job_fair#organizer",
    audit_status: "label",
    hosted_by: "job_fair#hosted_by",
    contact_person: "agent#contact_person",
    contact_mobile: "agent#contact_mobile",
    description: "job_fair#description",
    apply_time: "label",
    audit_uid: "",
    wx_qr: "",
    agent_name: "agent#agent_name",
    company_code: "agent#company_code",
    company_size: "agent#company_size_label",
    industory_catalog: "agent#industory_catalog_label",
    remark: "agent#remark",
    province: "agent#province#region_name",
    city: "agent#city#region_name",
    area: "agent#area#region_name",
    deadline: "job_fair#apply_time_label",
    address_detail: "job_fair#cal_address_detail",
    job_fair_id: "",
    agent_id: "",
}

function handleStatus(h: CreateElement, row: any) {
    return h(
        "span",
        {
            class: "green",
        },
        row || "--"
    )
}

export const jobDetailPredict = {
    name: "position#name",
    position_id: "",
    salary: "position#salary",
    recruit_count: "position#recruit_count",
    work_type: "position#work_type_label",
    description: "position#description",
    function_detail: "position#function_detail",
    contact_person: "position#contact_person",
    contact_mobile: "position#contact_mobile",
    contact_email: "position#contact_email",
    experience: "position#experience",
    education: "position#education_label",
    age_require: "position#age_require",
    language: "position#language_label",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    position_cal_household_address: "position#cal_household_address",
    audit_status: "label",
    job_fair_id: "",
    agent_id: "",
    function_categories: "position#function_categories",
    language_level: "position#language_level_label",
    major_desc: "position#major_desc_label",
    position_online_status: "position#online_status_label",
    company_name: "position#company_name",
    tags: "tags",
    agent_name: "position#agent#agent_name",
    company_code: "position#agent#company_code",
    company_size: "position#agent#company_size_label",
    industory_catalog: "position#agent#industory_catalog_label",
    remark: "position#agent#remark",
    agent_province: "position#agent#province#region_name",
    agent_city: "position#agent#city#region_name",
    agent_area: "position#agent#area#region_name",
    agent_address_detail: "position#agent#address_detail",
    agent_enterprise_type: "position#agent#tg_enterprise#enterprise_type_label",
    found_time: "position#agent#tg_enterprise#found_time_label",
    cal_scale: "position#agent#tg_enterprise#cal_scale",
    agent_company_name: "position#company_name",
    logo: "position#agent#logo",
    break_info: "agent_apply#break_info",
}

export interface JobDetailRow {
    /** 岗位名称 */
    name: string

    /** 职位id */
    position_id: string

    /** 薪资待遇 */
    salary: string

    /** 招聘人数 */
    recruit_count: number

    /** 工作性质 */
    work_type: number

    /** 岗位描述 */
    description: string

    /** 岗位职责 */
    function_detail: number

    /** 联系人 */
    contact_person: string

    /** 联系手机号 */
    contact_mobile: string

    /** 联系邮箱 */
    contact_email: number

    /** 工作年限 */
    experience: number

    /** 学历要求 */
    education: string

    /** 学历要求[文本] */
    education_label: string

    /** 年龄要求 */
    age_require: number

    /** 语言语种 */
    language: string

    /** 语言语种[文本] */
    language_label: string
    province: string
    city: string
    area: string
    audit_status: JobAuditStatus
    audit_status_label: string
    job_fair_id: string
    agent_id: string
    function_categories: string
    language_level: string
    id: number
    v: number
}
// function handleAddress(h: CreateElement, row: any, arr: string[]) {
//     return h(
//         "span",
//         {
//             class: "u-line-1",
//         },
//         getAddress(row, arr) || "--"
//     )
// }

// 参会岗位
const jobTableFilter: TableFilter[] = [
    {
        label: "状态",
        type: FormType.Select,
        prop: "audit_status",
    },
]

export const jobPredict = {
    salary_desc: "position#salary",
    name: "position#name",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    recruit_count: "position#recruit_count",
    gender_require: "position#gender_require_label",
    age_require: "position#age_require_label",
    industry: "position#industry_label",
    online_status: "position#online_status_label",
    create_time: "position#create_time_label",
    source_from_type: "position#source_from_type_label",
    status: "position#status_label",
    position_id: "position#id",
    position_online_status: "position#online_status_label",
    qr_code: "",
    audit_status: "label",
    agent_name: "position#agent#agent_name",
    function_categories: "position#function_categories",
    job_fair_id: "",
    agent_id: "",
}
export function jobTableConfig(
    agent_id: string,
    job_fair_id: string
): TableConfig {
    return {
        model: sdk.core
            .model("job_fair_agent_position")
            .list("in_job_fair_detail_for_operate"),
        filter: jobTableFilter,
        defaultPageSize: 10,
        predict: jobPredict,
        preFilter: {
            is_del: 0,
            agent_id,
            job_fair_id,
        },
    }
}

export const jobColumns: TableColumn[] = [
    {
        prop: "select",
        width: "58",
        type: "selection",
        selectable: (row: any) => {
            return row.audit_status === 0
        },
    },
    {
        label: "企业名称",
        prop: "agent_name",
        align: "left",
        showOverflowTip: true,
    },
    { label: "岗位名称", prop: "name", align: "left", showOverflowTip: true },
    {
        label: "岗位职能",
        prop: "function_categories",
        showOverflowTip: true,
    },
    {
        label: "薪资待遇",
        prop: "salary",
        render(h, row) {
            return h("span", {}, row.salary_desc)
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "工作地址",
        prop: "address",
        render: (h, row) => {
            return h("span", {}, getAddress(row))
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "岗位状态",
        prop: "position_online_status_label",
        showOverflowTip: true,
    },
    {
        label: "参会状态",
        prop: "audit_status_label",
        render(h, row) {
            return h(
                "span",
                {
                    style: {
                        color: ["#333", "#22BD7A", "#E87005"][row.audit_status],
                    },
                },
                row.audit_status_label
            )
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "招聘人数",
        prop: "recruit_count",
        showOverflowTip: true,
    },
    { label: "操作", prop: "h", align: "left", showOverflowTip: true },
]

export function createFormConfig(): BuildFormConfig {
    return {
        forms: [
            {
                label: "拒绝原因",
                type: FormType.Text,
                prop: "audit_memo",
                option: { type: "textarea", rows: 4, resize: "none" },
                required: true,
            },
        ],
    }
}
