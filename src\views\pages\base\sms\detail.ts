export const rowPredict = {
    type: "sms_template#type_label",
    title: "sms_template#title",
    real_name: "creator_account#real_name",
    create_time: "",
    related_model: "",
    related_id: "",
    template_code: "",
    content: "sms_template#content",
    xg_company_position: "xg_company_position#name",
    serve_task: "serve_task#p_name",
    notify_time: "serve_task#sms_task_notify#notify_time_label",
    notify_time_type: "serve_task#sms_task_notify#notify_time_type",
    send_time: "send_time_label",
}

const enum Type {
    定向发送短信 = 0,
    验证码短信 = 1,
    任务短信 = 2,
}

export interface Row {
    /** 模板分类 */
    type: Type
    /** 模板分类[文本] */
    type_label: string
    /** 模板名称 */
    title: string
    /** 发送人 */
    real_name: string
    xg_company_position: string
    serve_task: string
    notify_time: string
    notify_time_label: string
    notify_time_type: string
    /** 发送时间 */
    create_time: string
    /** 关联模型 */
    related_model: string
    /** 关联id */
    related_id: string
    /** 模板编号 */
    template_code: string
    /** 模板内容 */
    content: string
    id: number
    v: number
    send_time: string
}
