const defaultDomainAllInOne = {
    VUE_APP_UNIPLAT: "/api",
    VUE_APP_UNIPLAT_WEB: "/uniplat",
    VUE_APP_H5: "/h5",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
    // 公招网url
    VUE_APP_GZW_URL: "/",
    // 启用升级页面
    VUE_APP_DISABLE_UPGRADE: "",
}
const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "掇刀",
    VUE_APP_CITY_SAMPLE_NAME2: "掇刀区",
    VUE_APP_DEFAULT_REGION_CODE: "420804000000",
    VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
    VUE_APP_DEFAULT_REGION_NAME: "掇刀区",
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "掇刀区",
    VUE_APP_BAIDU_KEY: "Xa338L0G79uVCEmkg7tIXOZtCLEPFfMY",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "SYNBSP96NG248ZED",
    VUE_APP_CITY_COMP_CODE: "420000,420800,420804",
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-duodao",
    VUE_APP_AES_ENCRYPT_KEY_PATH_BK: "./bk-key-duodao",
    VUE_APP_MINI_PROGRAM_NAME: "掇刀就业",
    VUE_APP_BIGSCREEN_BI_PATH: "/bigScreen",
}

const { config } = require("./duodao_p.js")
module.exports = {
    name: "掇刀项目",
    env: {
        test: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_UNIPLAT: "http://jzpes-h5-test.teammix.com:8800/api",
            // VUE_APP_UNIPLAT: "http://jzpes.test-api.qqxb.jinsehuaqin.com:8800",
            // 共享用工局方端
            VUE_APP_BK_JOB_SHARE_ADMIN:
                "http://jzpes.admin-test.qqxb.jinsehuaqin.com:8800",
            // 共享用工企业端
            VUE_APP_BK_ENT_ADMIN_PATH:
                "http://jzpes.com-test.qqxb.jinsehuaqin.com:8800",
            VUE_APP_requestEncoder: "", // 入参加密 aes
            VUE_APP_responseEncoder: "", // 返回参加密 aes
        },
        pro: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_UNIPLAT: "https://www.ddggjy.com/api",
            // 共享用工局方端
            VUE_APP_BK_JOB_SHARE_ADMIN: "",
            // 共享用工企业端
            VUE_APP_BK_ENT_ADMIN_PATH: "",
            VUE_APP_requestEncoder: "aes", // 入参加密 aes
            VUE_APP_responseEncoder: "aes", // 返回参加密 aes
            VUE_APP_BK_LINGGONG_ADMIN: "https://admin.ddggjy.com",
            VUE_APP_BK_LINGGONG_JIGOU_ADMIN: "https://yz.ddggjy.com",
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "掇刀区数字化公共就业服务平台",
                BASE_URL: "platform/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/platform-duodao",
                    host: "88",
                    env: {
                        BASE_URL: "platform-duodao/",
                        VUE_APP_OPERATE_URL: "/platform-duodao",
                        VUE_APP_UNIPLAT_ENTRANCE: "荆州市智慧就业服务工作台",
                    },
                },
                pro: {
                    path: "/mnt/web/platform/production-pro",
                    host: "qqxb-hz",
                    env: {},
                },
            },
        },
        // 企业端
        {
            name: "企业端",
            env: {
                VUE_APP_HEADER_TITLE: "掇刀区企业智慧就业服务平台",
                VUE_APP_UNIPLAT_ENTRANCE: "企业智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org",
                BASE_URL: "org/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["企业端"],
            },
            deploy: {
            },
        },
        // 机构端
        {
            name: "机构端",
            env: {
                VUE_APP_HEADER_TITLE: "掇刀区人资机构就业服务平台",
                VUE_APP_UNIPLAT_ENTRANCE: "人力资源机构智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org_hr",
                BASE_URL: "hr/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["机构端"],
            },
            deploy: {
            },
        },
        // 网格员端
        {
            name: "网格员端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "居民就业服务客户端",
                VUE_APP_HEADER_TITLE: "劳动力资源信息采集平台",
                VUE_APP_APP_TITLE: "劳动力资源信息采集平台",
                BASE_URL: "grid/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["网格员端"],
                VUE_APP_APP_NAME: "xg_project_grid",
            },
            deploy: {
            },
        },
    ],
}
