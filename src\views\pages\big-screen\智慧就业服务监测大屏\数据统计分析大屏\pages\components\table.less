/deep/ .filter-container {
    background: transparent;
    .filter-container-form {
        .label {
            color: #89b9ff !important;
            font-size: 15px;
            font-weight: 500;
            white-space: nowrap;
        }
        input {
            background: #2e64d5;
            border-color: #2e64d5;
            color: #cfe3ff;
            &::placeholder {
                color: #cfe3ff;
            }
        }
        .el-date-editor {
            background: #2e64d5;
            border: none;
        }
    }
    .el-button--default.is-plain {
        background: rgba(16, 66, 180, 0.6);
        color: #b4d2ff;
        border: 1px solid #6893d2;
    }
    .el-button--primary {
        color: #dceaff;
        background: #366ef8;
    }
}
/deep/ .list-container {
    .el-table {
        background: transparent !important;
    }

    .el-table__row {
        color: #fff;
        background-color: transparent;
        &.el-table__row--striped {
            background-image: linear-gradient(
                90deg,
                #003dc7 0%,
                #3a6ee3 50%,
                #003dc7 100%
            );
            td {
                background-color: transparent;
            }
        }
    }
    td {
        border: none;
    }
    thead {
        tr {
            background-image: linear-gradient(
                90deg,
                #003dc7 0%,
                #3a6ee3 50%,
                #003dc7 100%
            );
        }
        .el-table__cell {
            background-image: none !important;
            background-color: transparent !important;
            color: #6fa9ff !important;
            border: none;
        }
    }
    .el-pagination__total,
    .el-pagination__sizes,
    .el-select,
    .el-pagination__jump,
    .number,
    input {
        color: #6fa9ff;
    }

    .number,
    .more,
    button {
        color: #6fa9ff;
        background: transparent;
    }

    input {
        background: transparent;
    }
}
/deep/ .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.2);
}
/deep/ .handler-btn {
    color: #ffc136 !important;
}
/deep/ .el-table__body tr:hover > td {
    background-color: transparent !important;
}
