<template>
    <div class="d-flex item-content flex-column" :class="`item-${len}`">
        <div>{{ label }}</div>
        <div class="u-flex u-col-bottom">
            <number-scroll
                v-if="!customValue && !longValue"
                :value="value"
                :digit-height="digitHeight"
                :digitWidth="digitWidth"
                :duration="1500"
                :digitSuf="digitSuf"
                :isRoute="isRoute"
                @toRoute="() => $emit('toRoute')"
            />
            <div
                class="u-flex u-row-center"
                v-else-if="customValue"
                :style="{ height: `${digitHeight * rate()}px` }"
            >
                {{ customValue || value }}
            </div>

            <div
                class="u-flex u-row-center long-value"
                v-else-if="longValue"
                :style="{ height: `${digitHeight * rate()}px` }"
                :title="longValue || value"
            >
                {{ longValue || value }}
            </div>
            <div class="unit" v-if="unit">{{ unit }}</div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from "vue-property-decorator"
    import NumberScroll from "../../智慧就业检测大屏/common/number-scroll.vue"
    import { rate } from "../../../common/rem"

    @Component({ components: { NumberScroll } })
    export default class Template extends Vue {
        @Prop({ default: "" })
        private label!: string

        @Prop({ default: "" })
        private value!: string

        @Prop({ default: "" })
        private customValue!: string

        @Prop({ default: "" })
        private longValue!: string

        @Prop({ default: "" })
        private len!: number

        @Prop({ default: "" })
        private digitSuf!: string

        @Prop({ default: 28 })
        private digitHeight!: number

        @Prop({ default: 16 })
        private digitWidth!: number

        @Prop({ default: false })
        private isRoute!: boolean

        @Prop({ default: "" })
        private unit!: string

        private rate = rate
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .item-content {
        border-radius: 12px 12px 12px 12px;
        border: 2px solid #266ACD;
        // width: 172px;
        flex: 1;
        height: 68px;
        gap: 5px;
        justify-content: center;
        align-items: center;

        & > div {
            &:first-child {
                font-size: 16px;
                color: #89b9ff;
                // line-height: 14px;
            }
            &:nth-child(2) {
                font-weight: bold;
                font-size: 28px;
                color: #fdc850;
                line-height: 18px;
            }
            .unit {
                font-size: 14px;
                margin-left: 2px;
            }
        }

        // &.item-4 {
        //     // background-size: 127px 52px;
        //     width: 127px;

        //     margin-left: 11px;

        //     &:first-child {
        //         margin-left: 0px;
        //     }
        // }
    }
</style>
