<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns"> </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import { Component, Prop } from "vue-property-decorator"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { buildConfig4RemoteMeta } from "../../collect-task-manage/components/build-table"

    @Component({ components: { CommonTable, TableContainer, ExcelImport } })
    export default class DetailList extends BaseTableController<any> {
        @Prop({ default: "" })
        private readonly modelName!: string

        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []

        mounted() {
            this.init()
        }

        private init() {
            return buildConfig4RemoteMeta(this.modelName, "back_list", {
                predicts: {},
                useLabelWidth: true,
                useRowFieldGroups: true,
                disabledOpt: true,
            }).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            tableConfig.predict = {
                ...r.tableConfig.predict,
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            this.columns = r.columns
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
