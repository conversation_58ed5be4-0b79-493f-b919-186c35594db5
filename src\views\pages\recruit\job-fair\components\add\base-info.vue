<template>
    <div class="u-flex-col step1-box">
        <form-builder
            ref="formBuilder"
            labelWidth="138px"
            @updateQueryRemote="updateQueryRemote"
            :onValueChange="onValueChange"
        ></form-builder>
        <div class="u-flex btns">
            <el-button type="primary" @click="goNext"> 下一步 </el-button>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop } from "vue-property-decorator"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import {
        BuildFormConfig,
        buildFormSections,
        FormController,
        FormItem,
    } from "@/core-ui/component/form"
    import { pageLoading } from "@/views/controller"
    import { cloneDeep, find, get } from "lodash"
    import { addForm1, addForm2, addForm3 } from ".."
    import { config, EnvProject } from "@/config"

    function createPositionFormSections(id?: string): BuildFormConfig {
        return {
            forms: [...addForm1(id), ...addForm2(id), ...addForm3(id)],
            sdkModel: "job_fair",
            sdkAction: id ? "update" : "create",
            id: id ? +id : undefined,
        }
    }

    @Component({
        components: { FormBuilder },
    })
    export default class BaseInfo extends FormController {
        @Prop({ default: "" })
        private id!: string

        private curInfo: any = {}

        private msg = ""

        private msgCode = 1

        created() {
            this.init()
        }

        private get forms() {
            return this.inputParams?.forms || []
        }

        private getDisplay(prop: string, value: any) {
            if (!value) return ""
            const mapping =
                get(
                    find(this.forms, { prop }),
                    "sourceInputsParameter.ext_properties.mapping.mapping_values"
                ) || []
            function fn(v: string, m: any, label = ""): any {
                if (!m?.length) return label
                const arr = v.split(",")
                const item = find(m, (e) => e.key === `${arr[0]}`)
                return fn(
                    v.replace(arr[0] + ",", "") + "",
                    item.children,
                    (label ? label + "/" : label) + item.value
                )
            }
            return fn(value + "", mapping, "")
        }

        private showDialog = false

        private inputParams: any

        private updateQueryRemote(_key: string, r: { masters: any[] }) {
            r.masters.forEach((i) => {
                Object.assign(this.curInfo, {
                    [i.property]: i.default_value,
                })
            })
        }

        private goNext() {
            const values = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.$emit(
                        "next",
                        Object.assign(this.curInfo, {
                            ...values,
                        }),
                        this.inputParams
                    )
                }
            })
        }

        private init() {
            pageLoading(() => {
                return buildFormSections(createPositionFormSections(this.id)).then(
                    (r) => {
                        if (+r.data.type === 3) {
                            const t = r.forms.find((i) => i.prop === "type_channel")
                            t!.required = true
                            t!.rules = [
                                {
                                    required: true,
                                    message: "渠道不能为空",
                                },
                            ]
                        }
                        this.inputParams = r
                        const params = cloneDeep(r)
                        params.forms = params.forms.filter((obj) =>
                            addForm1(this.id).some((obj2) => obj.prop === obj2.prop)
                        )
                        this.buildFormFull(params)
                    }
                )
            })
        }

        private onValueChange(prop: string, value: string, forms: FormItem[]) {
            if (
                [
                    "theme_type",
                    "activity_type",
                    "place_detail",
                    "is_activity_type",
                ].includes(prop) &&
                [EnvProject.荆州项目].includes(config.envProject)
            ) {
                const d = this.getFormValues()
                this.formBuilder?.onInput(
                    { prop: "title" },
                    [
                        this.getDisplay("theme_type", d.theme_type),
                        +d.is_activity_type
                            ? this.getDisplay("activity_type", d.activity_type)
                            : "综合类",
                        d.place_detail || "",
                    ]
                        .filter(Boolean)
                        .join("")
                )
            }
            if (prop === "type") {
                const t = forms.find((i: any) => i.prop === "type_channel")
                // 招聘会类型是直播时
                if (+value === 3) {
                    t!.required = true
                    t!.rules = [
                        {
                            required: true,
                            message: "渠道不能为空",
                        },
                    ]
                } else {
                    t!.required = false
                    t!.rules = [
                        {
                            required: false,
                        },
                    ]
                }
            }
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .step1-box {
        // height: 50vh;

        min-height: 458px;
        justify-content: space-between;
        padding-top: 50px;
    }

    .btns {
        width: 100%;
        justify-content: center;
        margin-top: 30px;

        /deep/ .el-button {
            width: 460px;
        }
    }
</style>
