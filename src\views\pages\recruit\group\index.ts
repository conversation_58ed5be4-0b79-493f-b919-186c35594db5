import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"
const tableFilter: TableFilter[] = [
    {
        label: "申请时间",
        type: FormType.DatePicker,
        prop: "apply_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "申请单位",
        type: FormType.Text,
        prop: "agent_name",
    },
    {
        label: "需求描述",
        type: FormType.Text,
        prop: "recommend_desc",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "推广开始时间",
        type: FormType.DatePicker,
        prop: "start_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "推广结束时间",
        type: FormType.DatePicker,
        prop: "end_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "申请推广岗位",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    // {
    //     label: "状态",
    //     type: FormType.Select,
    //     prop: "status",
    // },
    // {
    //     label: "推广时长",
    //     type: FormType.Select,
    //     prop: "recommend_duration",
    // },
]

export const predict = {
    name: "position#name",
    recommend_desc: "",
    agent_name: "position#agent#agent_name",
    contact_person: "",
    contact_mobile: "",
    status_memo: "",
    apply_time: "apply_time_label",
    apply_type: "label",
    status: "",
    source_page_url: "position#source_page_url",
    position_start_time: "position#recruit_start_time",
    position_end_time: "position#recruit_end_time",
    audit_status: "label",
    audit_time: "label",
    audit_memo: "label",
    audit_user: "auditor_name",
    recommend_type: "label",
    recommend_duration: "label",
    province: "city_ref#region_name",
    city: "city_ref#region_name ",
    area: "area_ref#region_name",
    address_detail: "countryside_ref#region_name",
    end_time: "label",
    start_time: "label",
    apply_region: "label",
    socialize_group_share_url: "label",
    position_id: "position#id",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("xg_company_position_recommend")
            .list("manage_apply2_list"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict,
        oneTabFilter: true,
    }
}

export enum ApplyType {
    后台发起 = 0,
    企业发起 = 1,
}
export enum AuditStatus {
    待审核 = 0,
    审核通过 = 1,
    审核不通过 = 2,
}

export interface Row {
    id: number
    name: string
    recommend_desc: string
    agent_name: string
    contact_person: string
    contact_mobile: string
    status_memo: string
    apply_time: string
    apply_type: ApplyType
    status: string
    source_page_url: string
    position_start_time: string
    position_end_time: string
    audit_status: AuditStatus
    audit_time: string
    audit_memo: string
    audit_user: string
    recommend_type: string
    recommend_duration: string
    position_id: string
}

export const columns: TableColumn[] = [
    {
        label: "申请推广岗位",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "申请企业",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "需求描述",
        prop: "recommend_desc",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "联系信息",
        prop: "info",
        showOverflowTip: true,
    },
    {
        label: "推广时间/推广范围",
        prop: "recommend_duration_label",
        width: "180px",
        showOverflowTip: true,
    },
    {
        label: "申请时间",
        prop: "apply_time_label",
        showOverflowTip: true,
        width: "150px",
    },
    {
        label: "申请状态",
        prop: "status_memo",
        showOverflowTip: true,
    },
    {
        label: "发起类型",
        prop: "apply_type_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "90",
    },
]
