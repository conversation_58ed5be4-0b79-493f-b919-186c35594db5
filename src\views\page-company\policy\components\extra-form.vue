<template>
    <div v-if="source.length">
        <PreviewInFormBuilder
            v-if="!isPreview"
            :items="source"
            labelPosition="top"
            :showLabel="true"
            ref="eF"
            :activeRules="true"
            @changeData="changeData"
            @changeValidate="(e) => $emit('changeValidate', e)"
        ></PreviewInFormBuilder>
        <div v-else>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="detailRowItems"
                class="u-p-x-20"
            >
            </detail-row-col>
        </div>
    </div>
</template>

<script lang='ts'>
    import { sdk } from "@/service"
    import { getQuestionList } from "@/views/common/form/service"
    import { rowPredict, Type } from "@/views/common/form/detail"
    import { Component, Prop, Ref, Vue } from "vue-property-decorator"
    import { uuid } from "uniplat-sdk/build/main/helpers/uuid"
    import { cloneDeep, find, forEach, map, sortBy, split } from "lodash"
    import PreviewInFormBuilder from "@/views/common/form/preview-common/preview-in-formbuilder.vue"
    import { CustomProfile } from "@/service/profile"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { renAreaView } from "@/views/components/common-comps"

    @Component({ components: { PreviewInFormBuilder, DetailRowCol } })
    export default class ExtraForm extends Vue {
        @Ref()
        eF!: any

        @Prop()
        private detail!: any

        @Prop({ default: false })
        private isPreview!: boolean

        @Prop({
            default: () => ({
                minWidth: "98px",
                textAlign: "right",
                marginRight: "10px",
                color: "#9098A6",
                lineHeight: "20px",
            }),
        })
        private labelStyle!: any

        @Prop()
        private companyData!: { agent_name: string; company_code: string }

        @Prop()
        private profile!: CustomProfile

        @Prop()
        private applyId!: string

        private source: any[] = []
        mounted() {
            this.init()
        }

        private async init() {
            if (!this.detail.questionnaire_id) return
            if (this.applyId) {
                try {
                    await this.getAnswer()
                } catch (e) {}
            }
            getQuestionList(this.detail.questionnaire_id).then((r) => {
                const data: any[] = sdk.buildRows(r.pageData.rows, rowPredict)
                const contactItemArr: string[] = []
                const newData = data.map((i, idx) => {
                    const ext_property = i.ext_property || ""
                    const contactItem = (
                        (i.field_groups && i.field_groups[5]) ||
                        ""
                    ).split("</br>")
                    contactItemArr.push(...contactItem)
                    return {
                        ...i,
                        options: i.options || "",
                        actions: r.pageData.rows[idx].actions,
                        intents: r.pageData.rows[idx].intents,
                        uuid: uuid(),
                        options_limit: i.options_limit || 0,
                        ext_property: ext_property,
                        openExtra: !!ext_property,
                        extraDefaultValue:
                            find(this.answer, (e) => e.question_id === i.id)
                                ?.question_extra_answer || "",
                    }
                })
                let offset = 2
                const source = cloneDeep(sortBy(newData, "seq_number"))
                    .filter((e) => e.is_using)
                    .map((e) => {
                        let span = 11
                        // 有额外回答的单独一行
                        if (e.options.includes("额外回答")) {
                            offset = 2
                            span = 24
                        }
                        offset = 2 - offset
                        const d = {
                            ...this.setDefaultValue(e),
                            col: {
                                span,
                                offset,
                            },
                        }
                        if (e.options.includes("额外回答")) {
                            offset = 2
                        }
                        return d
                    })
                this.source = source
            })
        }

        private get detailRowItems() {
            const h = this.$createElement
            return map(this.source, (e) => {
                const label = e.question
                const v = e.default_value_text
                const type = find(this.questions, (i) => +i.id === +e.id)?.type
                const open = (e: string) => {
                    window.open(sdk.buildFilePath(e), "__blank")
                }
                if ([Type.区域级联显示, Type.地区].includes(type)) {
                    return {
                        label,
                        vNode: renAreaView(h, e.default_value),
                    }
                }
                if (type === Type.上传附件) {
                    return {
                        label,
                        vNode: h(
                            "div",
                            { class: "value" },
                            v
                                ? split(v, ",")
                                      .filter(Boolean)
                                      .map((e) => {
                                          return h(
                                              "div",
                                              {
                                                  class: "pointer primary",
                                                  on: {
                                                      click: () => open(e),
                                                  },
                                              },
                                              e.replace("fs/", "")
                                          )
                                      })
                                : "暂无附件"
                        ),
                    }
                }
                if (!e.extraDefaultValue) {
                    return {
                        label,
                        value: e.default_value_text || "",
                    }
                }
                const o: any = {}
                const arr = split(e.default_value_text, ",").filter(Boolean)
                const arr2 = split(e.default_value, ",").filter(Boolean)
                forEach(arr2, (e, i) => {
                    o[e] = arr[i]
                })
                return {
                    label,
                    value: `${
                        sortBy(arr2)
                            .map((e) => o[e])
                            .join(",") || ""
                    }--${e.extraDefaultValue || ""}`,
                }
            })
        }

        private answer: any = []
        private questions: any = []
        private getAnswer() {
            return sdk
                .getDomainService(
                    "get_questionnaire",
                    "general_questionnaire",
                    "xg_project"
                )
                .get({
                    task_id: this.detail.id,
                    questionnaire_id: this.detail.questionnaire_id,
                    apply_id: this.applyId,
                    action: "answer",
                })
                .then((r: any) => {
                    this.answer = r?.user_answer?.answers || []
                    this.questions = r?.questionaire?.questions || []
                })
        }

        private setDefaultValue(item: any) {
            const isAnswered = !!this.applyId
            if (isAnswered) {
                const q = find(this.answer, (e) => e.question_id === item.id)
                if (q) {
                    item.default_value = q.question_answer
                    item.default_value_text = q.question_answer_text
                }
            } else {
                if (item.type === Type.手机号 && this.profile.mobile) {
                    item.default_value = this.profile.mobile
                }
                if (item.type === Type.民族 && this.profile.nation) {
                    item.default_value = this.profile.nation
                }
                if (item.type === Type.身份证号) {
                    item.default_value = this.profile.id_card
                }
                if (item.type === Type.民族) {
                    item.default_value = "汉族"
                }
                if (item.type === Type.姓名) {
                    item.default_value = this.profile.name
                }
                if (
                    item.type === Type.组织机构代码 &&
                    this.companyData.company_code
                ) {
                    item.default_value = this.companyData.company_code
                }
            }
            return item
        }

        private formData: any = {}
        private changeData(data: any) {
            this.formData = { ...this.formData, ...data }
            this.$emit("getValue", this.formData)
        }

        validate() {
            this.eF.validate()
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
