import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import { TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { groupBy } from "lodash"

export const tableConfig = (tag: string): TableConfig => ({
    model: sdk.core.model("g_policy_advice").list("manage_for_company"),
    defaultPageSize: 5,
    preFilter: {
        aprover_status: "1",
        shelves_status: "1",
    },
    filter: [
        {
            useTag: "政策类型",
            type: FormType.Select,
            prop: "tag",
            defaultValue: tag,
        },
    ],
    predict: {
        title: "",
        second_title: "",
        url_path: "",
        type: 0,
    },
    oneTabFilter: true,
})
export interface SubsidyDetail {
    id: number
    content: string
    apply_condition: string
    acceptance_way: string
    contact_info: string
    [key: string]: any
}

export enum Category {
    就业创业补贴 = 1,
    技能培训补贴,
}
export const predict = {
    category: "label",
    content: "label",
    apply_condition: "label",
    create_time: "label",
    title: "",
    status: "label",
    acceptance_way: "",
    contact_info: "",
}

export function getList() {
    return sdk.core
        .model("company_subsidy")
        .list("for_org")
        .query({
            pageIndex: 1,
            item_size: 99,
        })
        .then((r) => {
            const rowList = sdk.buildRows<SubsidyDetail>(
                r.pageData.rows,
                predict
            )
            return {
                ...groupBy(rowList, "category_label"),
                rowList,
            } as {
                [key: string]: SubsidyDetail[]
            }
        })
}

export function getDetail(id: string) {
    return sdk.core
        .model("company_subsidy")
        .detail(id, "for_operate")
        .query()
        .then((r) => {
            return sdk.buildRow<SubsidyDetail>(r.row, predict)
        })
}

export function createFormConfig(agent_id: string): BuildFormConfig {
    return {
        sdkModel: "company_subsidy_apply",
        sdkAction: "insert",
        forms: [
            {
                label: "业务名称",
                type: FormType.Text,
                prop: "title",
                required: true,
            },
            {
                label: "申请企业",
                type: FormType.Text,
                prop: "agent_id",
                hide: true,
                defaultValue: agent_id,
                required: true,
            },
            {
                label: "申请人",
                type: FormType.Text,
                prop: "contact_person",
                required: true,
            },
            {
                label: "申请人联系方式",
                type: FormType.Text,
                prop: "contact_mobile",
                required: true,
            },
        ],
    }
}
