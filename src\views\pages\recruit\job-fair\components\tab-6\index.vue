<template>
    <div class="detail-container">
        <div
            class="content u-m-b-20 u-p-b-20 u-flex u-row-between u-col-top gap-80"
        >
            <!-- <div class="u-flex-1">
                <div class="title u-flex">
                    <div class="mr-auto">基础信息</div>
                    <el-button
                        type="primary"
                        size="mini"
                        plain
                        @click="showAddAreaBooth = true"
                    >
                        导入场地
                    </el-button>
                    <el-button
                        type="primary"
                        size="mini"
                        plain
                        @click="showAdd = true"
                    >
                        修改
                    </el-button>
                </div>
                <div class="u-flex base-info u-col-top">
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">场地名称：</div>
                            <div v-if="row.activity_area_name">
                                {{ row.activity_area_name }}
                            </div>
                            <div v-else class="color-9">暂无</div>
                        </div>
                        <div class="u-flex" v-if="row.activity_area_address">
                            <div class="label">所在地：</div>
                            <div>{{ row.activity_area_address }}</div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="u-flex-1">
                <div class="u-flex-1">
                    <div class="title u-flex u-row-between u-flex-1">
                        <div>现场图片</div>
                        <el-button
                            type="primary"
                            size="mini"
                            plain
                            @click="showUploadImg = true"
                            >上传</el-button
                        >
                    </div>
                    <div v-if="row.booth_image" class="u-m-t-20 flex-gap-5">
                        <el-image
                            :src="img"
                            v-for="img in srcList"
                            :key="img"
                            fit="fill"
                            class="img pointer"
                            :preview-src-list="srcList"
                        />
                    </div>
                    <div v-else class="base-info color-9">平面图未上传</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="header u-flex u-flex-wrap u-row-between">
                <div class="u-flex">
                    当前有
                    <span> {{ areaCount }} </span>
                    个展区
                    <div class="gap"></div>
                    <span> {{ areaListCount }} </span>
                    个展位
                    <div class="gap"></div>
                    还有
                    <span>
                        {{ row.un_set_booth_company_count || 0 }}
                    </span>
                    个企业未设置展位
                </div>

                <el-button
                    @click="showImportPop = true"
                    class="btn"
                    size="mini"
                    type="primary"
                    plain
                >
                    导入招聘会企业信息
                </el-button>
            </div>
            <div class="title u-flex">
                <div class="u-flex-1">展区信息</div>
                <!-- <div class="toggle u-flex u-row-center" @click="toggle">
                    <img
                        src="/img/recruit/graph.png"
                        class="icon"
                        v-if="isList"
                    />
                    <img src="/img/recruit/list.png" class="icon" v-else />
                </div> -->

                <el-button
                    type="primary"
                    size="mini"
                    class="ml-auto"
                    plain
                    @click="showAddAreaBooth = true"
                >
                    导入场地
                </el-button>
                <el-button
                    class="btn"
                    @click="addArea(0)"
                    size="mini"
                    type="primary"
                >
                    新增展区
                </el-button>
            </div>
            <Area
                ref="area"
                :job_fair="row"
                :currentId="currentArea.id"
                @edit="addArea"
                @dblclick="dblclick"
                @click="onClick"
                @setTotal="areaCount = $event"
            />
        </div>
        <Addarea
            v-model="showAddArea"
            :job_fair_id="row.id"
            :id="currentAreaId"
            @refresh="areaRefresh"
        />

        <AddAreaBooth
            v-model="showAddAreaBooth"
            :curId="row.id"
            @refresh="areaBoothRefresh"
        ></AddAreaBooth>

        <div class="content">
            <div class="title u-flex">
                <div class="u-flex-1">
                    展位信息<span v-if="currentArea.id">
                        （{{ currentArea.name }}）
                    </span>
                </div>
                <!-- <el-button
                    @click="showImportPop = true"
                    class="btn"
                    size="mini"
                    type="primary"
                    plain
                >
                    导入招聘会企业信息
                </el-button> -->
                <el-button
                    @click="addAreaItem()"
                    class="btn"
                    size="mini"
                    type="primary"
                >
                    新增展位
                </el-button>
            </div>
            <AreaList
                ref="areaList"
                :job_fair="row"
                :currentAreaId="currentArea.id"
                @setTotal="areaListCount = $event"
                @edit="addAreaItem"
            />
        </div>
        <AddareaItem
            @refresh="areaListRefresh"
            v-model="showAddAreaItem"
            :job_fair_id="row.id"
            :id="areaItemId"
        />
        <upload-img
            v-model="showUploadImg"
            :job_fair_id="row.id"
            :sourceImg="row.booth_image"
        />
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入招聘会企业信息"
            placeholder="请点击「确定」将招聘会企业信息上传"
            :importConfig="importConfig"
            @refresh="refreshImport"
        />
        <add-pop
            v-model="showAdd"
            :rowid="row.id"
            @refresh="$emit('refresh')"
        ></add-pop>
    </div>
</template>

<script lang="ts">
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import VFile from "@/core-ui/component/form/filed/v-file.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Prop, Ref, Vue } from "vue-property-decorator"
    import { Row } from "../.."
    import AddareaItem from "./components/add-area-item.vue"
    import Addarea from "./components/add-area.vue"
    import AddAreaBooth from "./components/add-area-booth.vue"
    import AddPop from "./components/add-pop.vue"
    import { AreaRow } from "./components/area"
    import AreaList from "./components/area-list.vue"
    import Area from "./components/area.vue"
    import UploadImg from "./upload-img.vue"

    @Component({
        components: {
            VFile,
            UploadImg,
            Area,
            AreaList,
            Addarea,
            AddareaItem,
            ExcelImport,
            AddPop,
            AddAreaBooth,
        },
    })
    export default class Tab6 extends Vue {
        @Prop()
        private row!: Row

        @Ref()
        private area!: Area

        areaCount = 0
        areaListCount = 0
        private isList = false

        showAddAreaItem = false
        showAddArea = false
        showAddAreaBooth = false
        showImportPop = false
        currentAreaId = 0
        importConfig: any = null
        private showAdd = false
        private areaItemId = ""

        addArea(id: number) {
            this.currentAreaId = id
            this.showAddArea = true
        }

        @Ref()
        private areaList!: AreaList

        private showUploadImg = false

        private toggle() {
            this.isList = !this.isList
            this.area.toggle(this.isList)
        }

        dblclick(e: AreaRow) {
            this.currentArea = {
                name: `${e.booth_name}：${e.booth_address}`,
                id: e.id,
            }
        }

        onClick(e: AreaRow) {
            if (this.currentArea.id === e.id) {
                this.currentArea = {
                    id: 0,
                    name: "",
                }
            }
        }

        private currentArea = {
            name: "",
            id: 0,
        }

        areaRefresh() {
            this.area.refreshList()
            this.areaList.refreshList()
        }

        areaListRefresh() {
            this.areaList.refreshList()
        }

        areaBoothRefresh() {
            this.$emit("refresh")
            this.area.refreshList()
            this.areaList.refreshList()
        }

        refreshImport() {
            this.areaRefresh()
            this.callRefresh(routesMap.recruit.jobFairDetail)
        }

        mounted() {
            this.importConfig = {
                templateUrl:
                    window.location.origin + "/file/批量导入展位信息0125.xls",
                modelName: "job_fair_booth",
                actionName: "import_job_fair_booth_area",
                bigActionImportParams: {
                    inputs_parameters: [],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                    prefilters: [
                        {
                            property: "job_fair_id",
                            value: this.row.id,
                            prefilters: null,
                            relationalOperator: null,
                        },
                    ],
                },
            }
        }

        private get srcList() {
            if (!this.row || !this.row.booth_image) {
                return []
            }
            return this.row.booth_image
                .split(",")
                .map((i: string) => this.getUrl(i))
        }

        private getUrl(v: string) {
            return sdk.buildImage(v)
        }

        private addAreaItem(id = "") {
            this.areaItemId = id
            this.showAddAreaItem = true
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .detail-container {
        .header {
            background: #fff5f0;
            min-height: 60px;
            padding-top: 10px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 10px;
            border-radius: 8px;
            color: #333333;
            .gap {
                margin-left: 20px;
            }
            span {
                color: #d0021b;
            }
            margin-bottom: 20px;
        }
        .gap-80 {
            gap: 80px;
        }
        .lh-34 {
            line-height: 34px;
        }
        .content {
            background: #fff;
            padding: 15px 20px 0;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
            .label {
                width: 80px;
            }
            .btn {
                margin-left: 10px;
            }
            .base-info {
                line-height: 34px;
                padding-left: 20px;
            }
            .img {
                height: 76px;
            }
            .icon {
                width: 14px;
                height: 14px;
            }
            .toggle {
                border: 1px solid rgb(87, 130, 236);
                background: #fff;
                height: 30px;
                border-radius: 4px;
                width: 36px;
                cursor: pointer;
            }
        }
    }

    .mr-auto {
        margin-right: auto;
    }

    .flex-gap-5 {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }
</style>
