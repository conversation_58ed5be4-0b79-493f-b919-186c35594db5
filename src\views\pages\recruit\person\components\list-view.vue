<template>
    <div class="list-view u-p-x-20">
        <div class="title">推荐记录</div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            :extraStyle="false"
        >
            <div slot="pages-tabs-right" class="u-flex-1 u-flex u-row-right">
                <el-button
                    class="custom-btn remove-btn u-m-b-8"
                    plain
                    type="primary"
                    v-if="show"
                    @click="batchRemove"
                    v-role="['model.xg_candidate_order.action.batch_delete']"
                >
                    批量移除人才
                </el-button>
            </div>
            <div slot="table" slot-scope="{ data }" class="t bg-white">
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="mobile_encode" slot-scope="scope">
                        {{
                            scope.row.showMobile
                                ? scope.row.mobile
                                : scope.row.mobile_encode
                        }}
                        <el-button
                            v-if="!scope.row.showMobile"
                            class="u-m-l-10"
                            type="text"
                            @click="scope.row.showMobile = true"
                            >查看</el-button
                        >
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button
                            type="text"
                            @click="del(scope.row)"
                            v-if="show && scope.row.status1_label === '待处理'"
                            v-role="['model.xg_candidate_order.action.delete']"
                        >
                            移除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "../../person/components/detail"
    import { TableConfig } from "@/core-ui/component/table"
    import { routesMap } from "@/router/direction"
    import { MessageBox } from "element-ui"
    import { sdk } from "@/service"
    import { map } from "lodash"
    import { pageLoading } from "@/views/controller"

    @Component({ components: { TableContainer, CommonTable } })
    export default class ListView extends BaseTableController<{ id: number }> {
        @Prop()
        private id!: string

        @Prop()
        private show!: boolean

        private tableConfig: TableConfig | null = null
        private columns = columns
        private ids: string[] = []
        mounted() {
            this.tableConfig = tableConfig(this.id)
        }

        private toDetail(row: Row) {
            if (!row.profile_access_key) {
                return this.$message.warning("无匹配档案")
            }
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: { id: row.profile_access_key, from: "person-detail" },
            })
        }

        private del(row: Row) {
            MessageBox.confirm("是否移除?", "移除").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_candidate_order")
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                            prefilters: [
                                { property: "created_from_id", value: this.id },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private handleSelectionChange(e: { ids: string[] }) {
            this.ids = e.ids
        }

        private batchRemove() {
            if (!this.ids.length) {
                this.$message.warning("请选择数据")
                return
            }
            console.log(this.ids)
            const selected_list = map(this.ids, (e) => ({
                id: +e,
                v: 0,
            }))
            let errorList: any = []
            MessageBox.confirm("是否移除?", "移除").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_candidate_order")
                        .action("batch_delete")
                        .updateInitialParams({
                            selected_list,
                            prefilters: [
                                { property: "created_from_id", value: this.id },
                            ],
                        })
                        .executeEach({
                            inputs_parameters: [],
                            selected_list: selected_list,
                        } as any)(
                            () => {},
                            (errorRow) => {
                                console.log(JSON.parse(JSON.stringify(errorRow)))
                                errorList = errorRow
                            }
                        )
                        .awaiting.then(() => {
                            if (errorList.length) {
                                this.$message.error(
                                    `${errorList.map(
                                        (i: any) => `id:${i.id}: ${i.error}`
                                    )}`
                                )
                            }
                            this.reloadList()
                        })
                })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .list-view {
        background: #fff;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
        .remove-btn {
            width: 120px;
            height: 40px;
        }
    }
</style>
