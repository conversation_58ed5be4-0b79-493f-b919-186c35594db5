import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getSalary } from "@/utils"
import { ListTypes } from "uniplat-sdk"
const tableFilter: TableFilter[] = [
    {
        label: "岗位名称",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    // {
    //     label: "岗位薪资",
    //     type: FormType.TextRange, // 暂无枚举 7.24
    //     prop: "salary",
    // },
    {
        label: "工作地区",
        type: FormType.Cascader,
        prop: "province_code",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "来源",
        type: FormType.Select,
        option: {
            multiple: true,
        },
        prop: "source_from_type",
    },
    {
        label: "申请状态",
        type: FormType.Select,
        prop: "status",
    },
    // {
    //     label: "区域类型",
    //     type: FormType.Select,
    //     prop: "region_type",
    // },
]
/** 审核状态 */
export enum AuditStatus {
    草稿 = -1,
    待审核 = 0,
    审核通过 = 1,
    审核不通过 = 2,
}

/** 上架状态 */
export enum OnlineStatus {
    待上架 = 0,
    已上架 = 1,
    已下架 = 2,
}
export const predict = {
    name: "",
    salary: "label",
    address_detail: "",
    city: "city#region_name",
    province: "province#region_name",
    area: "area#region_name",
    agent_id: "",
    source_from_type: "label",
    online_status: "label",
    online_user_name: "online_user#username",
    status: "label",
    contact_person: "label",
    contact_mobile: "",
    company_name: "agent#agent_name",
    create_time: "label",
    source_page_url: "",
    online_time: "label",
    recruit_start_time: "label",
    recruit_end_time: "label",
    update_time: "label",
    final_change_time: "label",
    audit_user_name: "audit_user#username",
    audit_time: "",
    audit_memo: "",
    education: "label",
    description: "",
    salary_pay_type: "label",
    experience_min: "label",
    experience_max: "label",
    wait_num: "label",
    source_from_logo: "label",
    recruit_count: "label",
    cal_address_detail: "",
    region_type: "label",
    has_recommend_1: "label",
    has_recommend_2: "label",
    has_recommend_3: "label",
    has_recommend_4: "label",
    creator: "creator_user#display_name",
    age_require_min: "",
    age_require_max: "",
    industry: "label",
    salary_desc: "label",
    salary_type: "label",
    gender_require: "label",
    age_require: "label",
    function_categories: "",
    function_detail: "",
    work_type: "label",
    language: "label",
    language_level: "label",
    expired_date: "label",
    contact_email: "",
    position_access_key: "_access_key",
    is_del: "label",
    object_data: "object_data",
    salary_max: "",
    salary_min: "",
    require_education: "label",
    require_profile: "label",
    require_work: "label",
    trading_area: "label",
}

export function tableConfig(defaultPage: string): TableConfig {
    return {
        model: sdk.core.model("xg_company_position").list("company"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict,
        oneTabFilter: true,
        defaultPage: defaultPage,
    }
}

export interface Row {
    _access_key: string
    id: number
    name: string
    salary: string
    address_detail: string
    city: string
    province: string
    area: string
    agent_id: string
    source_from_type: string
    online_status: OnlineStatus
    status: AuditStatus
    contact_person: string
    contact_mobile: string
    company_name: string
    source_page_url: string
    source_from_logo: string
    source_from_logo_label: string
    online_time: string
    online_user_name: string
    recruit_start_time: string
    recruit_end_time: string
    update_time: string
    final_change_time: string
    audit_user_name: string
    audit_time: string
    audit_memo: string
    education: string
    description: string
    salary_pay_type: string
    salary_pay_type_label: string
    experience_min: string
    experience_min_label: string
    experience_max: string
    experience_max_label: string
    wait_num: string
    online_time_label: string
    source_from_type_label: string
    create_time_label: string
    recruit_count: string
    cal_address_detail: string
    region_type: string
    region_type_label: string
    position_contact_person: string
    position_contact_mobile: string
    position_create_time_label: string
    has_recommend_1: string
    has_recommend_2: string
    has_recommend_3: string
    has_recommend_4: string
    creator: string
    create_time: string
    age_require_max: string
    age_require_min: string
    industry: string
    salary_max: string
    salary_min: string
    [key: string]: any

    /** 职能 */
    function_categories: string

    /** 任职要求 */
    function_detail: string

    /** 工作性质 */
    work_type: string

    /** 语言种类 */
    language: Language

    /** 语言种类[文本] */
    language_label: string

    /** 外语水平 */
    language_level: LanguageLevel

    /** 外语水平[文本] */
    language_level_label: string

    /** 岗位有效期 */
    expired_date: string

    /** 联系邮箱 */
    contact_email: string
    require_education_label: string
    require_profile_label: string
    require_work_label: string
}

export const columns: TableColumn[] = [
    {
        label: "岗位名称",
        prop: "name",
        width: "200px",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "薪资待遇",
        prop: "salary",
        render(h, row) {
            return h("span", {}, getSalary(row.salary_desc))
        },
        showOverflowTip: true,
    },
    {
        label: "工作地区",
        prop: "cal_address_detail",
        showOverflowTip: true,
    },
    {
        label: "招聘人数",
        prop: "recruit_count",
        showOverflowTip: true,
    },
    {
        label: "来源",
        prop: "source_from_type_label",
        showOverflowTip: true,
    },
    {
        label: "年龄要求",
        prop: "age_require_label",
        showOverflowTip: true,
    },
    {
        label: "岗位状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "上架状态",
        prop: "online_status_label",
        showOverflowTip: true,
    },
    {
        label: "发布时间",
        prop: "create_time_label",
        showOverflowTip: true,
        width: "160px",
    },
    {
        label: "操作",
        width: "160px",
        prop: "h",
    },
]

const enum Language {
    英语 = 1,
    日语 = 2,
    俄语 = 3,
    法语 = 4,
    意大利语 = 5,
    德语 = 6,
    韩语 = 7,
    蒙古语 = 8,
    葡萄牙语 = 9,
    西班牙语 = 10,
    巴士克语 = 11,
    冰岛语 = 12,
    丹麦语 = 13,
    法罗语 = 14,
    芬兰语 = 15,
    荷兰语 = 16,
    加泰隆语 = 17,
    马来语 = 18,
    南非语 = 19,
    挪威语 = 20,
    瑞典语 = 21,
    斯瓦西里语 = 22,
    印度尼西亚语 = 23,
    汉语 = 24,
    其他语言 = 90,
}

const enum LanguageLevel {
    一般 = 1,
    熟练 = 2,
    精通 = 3,
}

const tableFilter2: TableFilter[] = [
    {
        label: "岗位名称",
        type: FormType.Text,
        prop: "name",
    },
]

export const predict2 = {}
export function tableConfig2(): TableConfig {
    return {
        domainService: sdk.getDomainService(
            "get_wait_to_handle_positions",
            "agent_api",
            "xg_project"
        ),
        filter: tableFilter2,
        defaultPageSize: 10,
        predict: predict2,
    }
}

export const columns2: TableColumn[] = [
    {
        label: "岗位名称",
        prop: "name",
        width: "200px",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "薪资待遇",
        prop: "salary_desc",
        showOverflowTip: true,
    },
    {
        label: "工作地区",
        prop: "cal_address_detail",
        showOverflowTip: true,
    },
    {
        label: "招聘人数",
        prop: "recruit_count",
        formatter(row) {
            return row.recruit_count || 0
        },
        showOverflowTip: true,
    },
    {
        label: "来源",
        prop: "source_from_type",
        showOverflowTip: true,
    },
    {
        label: "年龄要求",
        prop: "age_require",
        showOverflowTip: true,
    },
    {
        label: "岗位状态",
        prop: "status",
        showOverflowTip: true,
    },
    {
        label: "上架状态",
        prop: "online_status",
        showOverflowTip: true,
    },
    {
        label: "待处理投递者数",
        prop: "wait_num",
        formatter(row) {
            return row.wait_num || 0
        },
        showOverflowTip: true,
    },
    // {
    //     label: "发布时间",
    //     prop: "create_time_label",
    //     showOverflowTip: true,
    //     width: "160px",
    // },
    {
        label: "操作",
        width: "160px",
        prop: "h",
    },
]
