<template>
    <div class="core-ui-table-container detail-index" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title u-flex">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="bg-white u-p-20 u-m-b-24">
            <detail-row-col
                :labelStyle="{ width: 'auto' }"
                :list="items"
            ></detail-row-col>
        </div>
        <div class="bg-white u-p-l-20 u-p-r-20 u-p-b-20 u-m-b-24 u-p-t-10">
            <detail-row-col
                :labelStyle="{ width: 'auto' }"
                :list="items2"
            ></detail-row-col>

            <div class="model-box u-m-t-10 u-p-t-10 u-p-r-20 u-p-b-10 u-p-l-20">
                {{ content }}
            </div>
        </div>
        <div class="bg-white">
            <div class="u-p-20 color-2 font-family-medium">短信发送详情</div>
            <DetailTable v-if="show"></DetailTable>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"
    import DetailTable from "@/views/pages/base/sms/components/detail-table.vue"
    import { routesMap } from "@/router/direction"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Row, rowPredict } from "./detail"
    import { formatTime } from "@/utils/tools"
    import { updateTagItem } from "../../single-page/components/tags-view"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"

    @Component({
        name: routesMap.base.sms.detail,
        components: { DetailTable, DetailRowCol },
    })
    export default class Detail extends Vue {
        breadcrumbs: BreadcrumbItem[] = []

        private items = this.buildItems({})
        private items2 = this.buildItems2({})
        private content = ""

        private show = false

        refreshConfig = {
            fun: this.init,
            name: routesMap.base.sms.detail,
        }

        mounted() {
            this.init()
        }

        private setTagItem() {
            this.breadcrumbs = [
                {
                    label: "短信发送管理",
                    to: {
                        name: routesMap.base.sms.list,
                    },
                },

                {
                    label: "短信发送详情",
                    to: {
                        name: routesMap.base.sms.detail,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.base.sms.detail,
                breadcrumb: this.breadcrumbs,
            })
        }

        private init() {
            this.show = false
            this.$nextTick(() => {
                this.show = true
            })
            this.setTagItem()
            pageLoading(() => {
                return sdk.core
                    .model("sms_send_history")
                    .detail(this.$route.query.id as string)
                    .query()
                    .then((r) => {
                        const row = sdk.buildRow<Row>(r.row, rowPredict)
                        console.log(row)
                        console.log(row)
                        this.items = this.buildItems(row)
                        this.items2 = this.buildItems2(row)
                        this.content = row.content
                    })
            })
        }

        private buildItems(rows: Row | {}) {
            const h = this.$createElement
            const row = (rows || {}) as Row
            const list1 = [
                { label: "模版分类：", value: row.type_label || "" },
                { label: "模版名称：", value: row.title || "" },
            ]

            const list2 = []
            if (row.related_model) {
                if (row.related_model === "xg_company_position") {
                    list2.push({
                        label: "关联岗位人才推荐详情：",
                        vNode: [
                            this.buildLink(
                                h,
                                row.xg_company_position,
                                row.related_id,
                                row.related_model
                            ),
                        ],
                    })
                }

                if (row.related_model === "serve_task") {
                    list2.push({
                        label: "关联任务：",
                        vNode: [
                            this.buildLink(
                                h,
                                row.xg_company_position,
                                row.related_id,
                                row.related_model
                            ),
                        ],
                    })
                }
            }

            const list3 = [
                {
                    label: "发送人：",
                    value: row.real_name,
                },
                {
                    label: "发送时间：",
                    value: formatTime.seconds(row.send_time || ""),
                },
            ]
            return [...list1, ...list2, ...list3].map((i) => {
                return { ...i, span: 8 }
            })
        }

        private buildLink(
            h: Function,
            label: string,
            id: string,
            related_model: string
        ) {
            return h(
                "div",
                {
                    style: {
                        paddingLeft: "8px",
                        color: "#5782EC",
                    },
                    class: "pointer",
                    on: {
                        click: () => {
                            this.goDetail(id, related_model)
                        },
                    },
                },
                label
            )
        }

        private goDetail(id: string, related_model: string) {
            if (related_model === "xg_company_position") {
                this.$router.push({
                    name: routesMap.recruit.personDetail,
                    query: {
                        id: id + "",
                        from: routesMap.base.sms.detail,
                    },
                })
            }
            if (related_model === "serve_task") {
                this.$router.push({
                    name: routesMap.groupService.taskManage,
                    query: {
                        id: id + "",
                        from: routesMap.base.sms.detail,
                    },
                })
            }
            return {
                id: id,
                related_model: related_model,
            }
        }

        private buildItems2(rows: Row | {}) {
            const row = (rows || {}) as Row
            return [
                { label: "模版编号：", value: row.template_code || "" },
                // {
                //     label: "发送方式：",
                //     value: row.notify_time_type
                //         ? `按照固定时间发送（${row.notify_time_label}）`
                //         : "立即发送",
                // },
            ].map((i) => {
                return { ...i, span: 8 }
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .model-box {
        background: rgba(87, 130, 236, 0.05);
        font-size: 14px;
        line-height: 34px;
    }
</style>
