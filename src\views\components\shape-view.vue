<template>
    <div class="u-rela" :style="[style]">
        <div class="u-rela u-flex u-row-center">
            <div class="shape u-flex-none">
                <div class="content u-flex u-flex-col u-col-center u-p-y-20">
                    <div class="chart-title">{{ title }}</div>
                    <div class="text">{{ text }}</div>
                </div>
                <div class="right u-flex" v-if="showRight">
                    <div class="line u-flex-none"></div>
                    <div class="num">{{ right || 0 }}</div>
                    <div>{{ unit }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: {} })
    export default class ShapeView extends Vue {
        @Prop({ default: 0 })
        private top!: number | string

        @Prop({ default: 0 })
        private bottom!: number | string

        @Prop({ default: 83 })
        private height!: number | string

        @Prop({ default: "#598BFF" })
        private color!: string

        @Prop({ default: "#598BFF" })
        private title!: string

        @Prop({ default: "#598BFF" })
        private text!: string

        @Prop({ default: "#598BFF" })
        private right!: string

        @Prop({ default: "次" })
        private unit!: string

        @Prop({ default: true })
        private showRight!: boolean

        private get slideWidth() {
            return (+this.top - +this.bottom) / 2
        }

        private get style() {
            return {
                "--top": this.top + "px",
                "--bottom": this.bottom + "px",
                "--slideWidth": this.slideWidth + "px",
                "--color": this.color,
                "--height": this.height + "px",
            }
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .shape {
        width: var(--top, 400px);
        height: var(--height, 83px);
        border-top: var(--height, 83px) solid var(--color);
        border-left: var(--slideWidth, 60px) solid transparent;
        border-right: var(--slideWidth, 60px) solid transparent;
        border-bottom: none;
        position: relative;
        .content {
            position: absolute;
            width: 100%;
            height: var(--height, 83px);
            bottom: 0;
            left: 0;
            color: #fff;
            z-index: 1;
            font-size: 12px;
            line-height: 17px;
            .chart-title {
                color: rgba(#fff, 0.75);
                margin-bottom: 11px;
            }
            .text {
                font-size: 14px;
                line-height: 20px;
            }
        }
    }
    .right {
        line-height: 25px;
        font-size: 12px;
        color: #9098a6;
        position: absolute;
        left: calc(var(--bottom) + var(--slideWidth) / 2 + 10px);
        top: calc(var(--height) / 2 * -1);
        height: 25px;
        z-index: 2;
        .line {
            width: 43px;
            height: 1px;
            background: #c2d5f2;
            margin-right: 10px;
        }
        .num {
            font-size: 18px;
            color: var(--color);
            margin-right: 9px;
        }
    }
</style>
