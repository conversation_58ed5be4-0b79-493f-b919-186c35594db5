import { Component, Prop, Vue, Watch } from "vue-property-decorator"
import BaseMap from "../../../common/base-map"
import { ChartQueryResultItem } from "../../../model"
import { MockData } from "../../mock/data"
import { rate as r } from "../../../common/rem/"
import { get } from "lodash"
import { ResizeController } from "../../../model/resize"

@Component({ components: {} })
export default class Template extends BaseMap {
    protected chart: echarts.ECharts | null = null

    @Prop()
    protected data!: MockData

    private start = false

    protected autoLoad = true

    created() {
        ResizeController.register(this, () => this.refresh())
    }

    protected onQueryRegionCodeChange() {
        if (!this.start) {
            return
        }
        this.refresh()
    }

    protected refresh() {
        // eslint-disable-next-line no-useless-return
        return
    }

    mounted() {
        this.autoLoad && this.refresh()
    }

    protected refreshCache() {}

    protected getV(r: ChartQueryResultItem[], name: string) {
        const t = r.find((i) => i.key_name === name)
        return t ? t.key_value : 0
    }

    protected getPercent(r: ChartQueryResultItem[], name: string) {
        const t = r.find((i) => i.key_name === name)
        return t ? t.percent : 0
    }

    protected rate() {
        return r()
    }

    protected formatData2Remote(model: any[], obj: Record<string, any>) {
        const middleModel = this.formatData(model, [])
        Object.keys(obj).forEach((i) => {
            const t = middleModel.find((j) => j.key === i)
            if (t) {
                t.value = (t.value || 0) * 1 + (obj[i] || 0) * 1
            }
        })
        return middleModel
    }

    protected formatData(model: any[], r: ChartQueryResultItem[]) {
        return model.map((i) => {
            return {
                ...i,
                value: this.getV(r, i.remoteKey || i.label),
                percent: this.getPercent(r, i.label),
            }
        })
    }

    @Prop({ default: () => null })
    row: any
}
