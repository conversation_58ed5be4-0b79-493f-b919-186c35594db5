const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "孝感",
    VUE_APP_CITY_SAMPLE_NAME2: "孝感市",
    VUE_APP_DEFAULT_REGION_CODE: "420900",
    VUE_APP_DEFAULT_REGION_NAME: "荆州市",
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "孝感市",
    VUE_APP_BAIDU_KEY: "Xa338L0G79uVCEmkg7tIXOZtCLEPFfMY",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "",
    // VUE_APP_LOGIN_AES_ENCRYPT_KEY: "LYNBSP96NG248ZHE",
    VUE_APP_CITY_COMP_CODE: "420000,420900",
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-xg",
    VUE_APP_AES_ENCRYPT_KEY_PATH_BK: "./bk-key-xg",
    VUE_APP_MINI_PROGRAM_NAME: "微孝就业",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
    VUE_APP_BIGSCREEN_BI_PATH: "/bigScreen",
}

const { config } = require("./xg_p.js")
module.exports = {
    name: "孝感项目",
    env: {
        test: {
            ...commonEnv,
            VUE_APP_UNIPLAT: "http://anlusc.beikesmart.com/api",
            VUE_APP_UNIPLAT_WEB:
                "http://xgpes.test-web.qqxb.jinsehuaqin.com:8800",
            VUE_APP_H5: "http://xgpes-h5-test.teammix.com",
            // VUE_APP_OPERATE_URL:
            //     "http://xgpes.test-web.qqxb.jinsehuaqin.com:8800/platform/",
            // VUE_APP_ENTERPRISE_URL:
            //     "http://xgpes.test-web.qqxb.jinsehuaqin.com:8800/org/",
            // VUE_APP_HR_URL:
            //     "http://xgpes.test-web.qqxb.jinsehuaqin.com:8800/hr/",
            VUE_APP_BK_LINGGONG_ADMIN:
                "http://xgpes.admin-test.qqxb.jinsehuaqin.com:8800",
            VUE_APP_BK_LINGGONG_JIGOU_ADMIN:
                "http://xgpes.yz-test.qqxb.jinsehuaqin.com:8800",
            VUE_APP_requestEncoder: "", // 入参加密 aes
            VUE_APP_responseEncoder: "", // 返回参加密 aes
        },
        pro: {
            ...commonEnv,
            VUE_APP_UNIPLAT: "https://api2.xg12333.cn:11123",
            VUE_APP_UNIPLAT_WEB: "https://yy.xg12333.cn:11123",
            VUE_APP_OPERATE_URL: "https://jyj.xg12333.cn:11123",
            VUE_APP_H5: "https://h5.xg12333.cn:11123",
            VUE_APP_ENTERPRISE_URL: "https://qy.xg12333.cn:11123",
            VUE_APP_HR_URL: "https://rz.xg12333.cn:11123",
            VUE_APP_BK_LINGGONG_ADMIN: "https://lgadmin.xg12333.cn:11123",
            VUE_APP_BK_LINGGONG_JIGOU_ADMIN: "https://yz.xg12333.cn:11123",
            VUE_APP_requestEncoder: "aes", // 入参加密 aes
            VUE_APP_responseEncoder: "aes", // 返回参加密 aes
            // 培训H5 由于端口限制代理 实际访问的是 https://lgadmin.xg12333.cn:11123/xgpx
            VUE_APP_BK_PEIXUN_H5_URL: "https://jzpes-web.teammix.com/xgpx",
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "孝感市智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "就在孝感 - 就业管理工作台",
                VUE_APP_APP_TITLE: "孝感市智慧就业服务工作台",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/xiaogan/front/platform",
                    host: "88",
                    env: {
                        BASE_URL: "platform/",
                    },
                },
                pro: {
                    path: "/mnt/xiaogan/web/pc/platform/dist",
                    host: "xiaogan-bus",
                },
            },
        },
        // 企业端
        {
            name: "企业端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "孝感市企业智慧就业服务平台",
                VUE_APP_HEADER_TITLE: "就在孝感 - 企业智慧就业服务平台",
                VUE_APP_APP_TITLE: "就在孝感 - 企业智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["企业端"],
                VUE_APP_HEADER_META_TITLE: "就在孝感 - 孝感智慧就业",
                VUE_APP_HEADER_META_DESCRIPTION: "就在孝感企业智慧服务平台",
                VUE_APP_HEADER_META_KEYWORDS:
                    "就在孝感,孝感智慧就业,就在孝感企业智慧服务平台",
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/xiaogan/front/org",
                    host: "88",
                    env: {
                        BASE_URL: "org/",
                    },
                },
                pro: {
                    path: "/mnt/xiaogan/web/pc/org/dist",
                    host: "xiaogan-bus",
                },
            },
        },
        // 机构端
        {
            name: "机构端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "孝感市人力资源机构智慧就业服务平台",
                VUE_APP_HEADER_TITLE: "就在孝感 - 人资机构就业服务平台",
                VUE_APP_APP_TITLE: "孝感市人力资源机构智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org_hr",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["机构端"],
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/xiaogan/front/hr",
                    host: "88",
                    env: {
                        BASE_URL: "hr/",
                    },
                },
                pro: {
                    path: "/mnt/xiaogan/web/pc/hr/dist",
                    host: "xiaogan-bus",
                },
            },
        },
        // 局方端
        {
            name: "局方端",
            memo: "-国产化",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "孝感市智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "就在孝感 - 就业管理工作台",
                VUE_APP_APP_TITLE: "孝感市智慧就业服务工作台",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                test: {
                    path: "/data/sdb/web/xiaogan/platform",
                    host: "87",
                    env: {
                        BASE_URL: "platform/",
                        VUE_APP_OPERATE_URL: "/platform",
                        VUE_APP_UNIPLAT:
                            "https://xgpes.test-ob-web.qqxb.jinsehuaqin.com/api",
                    },
                },
            },
        },
        // 企业端
        {
            name: "企业端",
            memo: "-国产化",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "孝感市企业智慧就业服务平台",
                VUE_APP_HEADER_TITLE: "就在孝感 - 企业智慧就业服务平台",
                VUE_APP_APP_TITLE: "就在孝感 - 企业智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["企业端"],
                VUE_APP_HEADER_META_TITLE: "就在孝感 - 孝感智慧就业",
                VUE_APP_HEADER_META_DESCRIPTION: "就在孝感企业智慧服务平台",
                VUE_APP_HEADER_META_KEYWORDS:
                    "就在孝感,孝感智慧就业,就在孝感企业智慧服务平台",
            },
            deploy: {
                test: {
                    path: "/data/sdb/web/xiaogan/org",
                    host: "87",
                    env: {
                        BASE_URL: "org/",
                        VUE_APP_OPERATE_URL: "/org",
                        VUE_APP_UNIPLAT:
                            "https://xgpes.test-ob-web.qqxb.jinsehuaqin.com/api",
                    },
                },
            },
        },
        // 机构端
        {
            name: "机构端",
            memo: "-国产化",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "孝感市人力资源机构智慧就业服务平台",
                VUE_APP_HEADER_TITLE: "就在孝感 - 人资机构就业服务平台",
                VUE_APP_APP_TITLE: "孝感市人力资源机构智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org_hr",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["机构端"],
            },
            deploy: {
                test: {
                    path: "/data/sdb/web/xiaogan/hr",
                    host: "87",
                    env: {
                        BASE_URL: "hr/",
                        VUE_APP_OPERATE_URL: "/hr",
                        VUE_APP_UNIPLAT:
                            "https://xgpes.test-ob-web.qqxb.jinsehuaqin.com/api",
                    },
                },
            },
        },
    ],
}
