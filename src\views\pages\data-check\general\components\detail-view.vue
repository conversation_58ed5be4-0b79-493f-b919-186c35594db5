<template>
    <div v-if="detailRow">
        <div class="title u-flex">
            {{ detailRow.name }}
            <div
                class="u-flex u-col-center status"
                :class="'status-' + +detailRow.status"
            >
                {{ detailRow.status_label }}
            </div>
        </div>
        <div>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
                class="u-p-x-20"
            >
            </detail-row-col>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { DetailRow, Status, CheckType } from "../detail"
    import { sdk } from "@/service"

    @Component({
        components: { TableContainer, CommonTable, DetailRowCol },
    })
    export default class DetailView extends Vue {
        @Prop({ default: () => {} })
        private detailRow!: DetailRow | any

        private get isWaiting() {
            return this.detailRow?.status === Status.待处理
        }

        private get isQueryType() {
            return this.detailRow?.check_type === CheckType.查询类
        }

        private get labelStyle() {
            return {
                minWidth: "110px",
                textAlign: "left",
                color: "#555",
            }
        }

        private get items() {
            const h = this.$createElement
            const item: ColItem[] = [
                {
                    label: "核查模版：",
                    value: this.detailRow?.tool_name,
                    span: 8,
                },
                {
                    label: "核查需求简述：",
                    value: this.detailRow?.require_note,
                    span: 8,
                },
                {
                    label: "核查时间段：",
                    value: this.detailRow?.start
                        ? `${this.detailRow?.start?.replace(
                              /-/g,
                              "/"
                          )} - ${this.detailRow?.end?.replace(/-/g, "/")}`
                        : "--",
                    span: 8,
                },
                {
                    label: "关联单位：",
                    value: this.detailRow?.org_name,
                    span: 8,
                },
                {
                    label: "创建人：",
                    value: `${this.detailRow?.real_name} ${this.detailRow?.mobile_hide}`,
                    span: 8,
                },
                {
                    label: "创建时间：",
                    value: this.detailRow?.create_time_label,
                    span: 8,
                },
                {
                    label: "结果简述：",
                    vNode: h("div", {
                        domProps: {
                            innerHTML:
                                this.formatNumber(this.detailRow?.result_note) ||
                                "--",
                        },
                    }),
                    span: 24,
                },
            ]
            return item
        }

        private formatNumber(text: string) {
            if (!text) return ""
            return text.replace(/(\d+)/g, '<span style="color: red">$1</span>')
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    .title {
        margin-bottom: 20px;
        color: #000000;
        font-size: 18px;
        font-weight: 500;
        padding-left: 20px;
        .status {
            padding: 2px 6px;
            color: white;
            line-height: 16px;
            border-radius: 2px;
            margin-left: 10px;
            font-size: 14px;
            &.status-1 {
                background-color: #22bd7a;
            }
            &.status-0 {
                background-color: #f5820f;
            }
            &.status-2 {
                background-color: #6b99f6;
            }
            &.status-3 {
                background-color: #d0021b;
            }
        }
    }
</style>
