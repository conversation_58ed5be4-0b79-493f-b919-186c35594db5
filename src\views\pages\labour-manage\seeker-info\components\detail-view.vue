<template>
    <div class="detail-container u-flex u-row-center u-col-top">
        <div class="content u-flex-1 u-m-t-20 u-p-x-20 u-p-b-10">
            <div class="title">个人基本信息</div>
            <div class="u-p-x-20 u-p-b-14 u-p-t-10">
                <div class="items under-line">
                    <el-button
                        v-role="
                            'model.user_profile_basic.action.back_basic_info_edit'
                        "
                        class="edit-btn"
                        type="primary"
                        plain
                        @click="displayEditPop1 = true"
                    >
                        编辑基础信息
                    </el-button>
                    <div class="info-box">
                        <detail-row-col
                            :list="allList[0]"
                            :labelStyle="labelStyle"
                        ></detail-row-col>
                    </div>
                </div>
                <div class="items under-line">
                    <el-button
                        v-role="
                            'model.user_profile_basic.action.back_education_edit'
                        "
                        class="edit-btn"
                        type="primary"
                        plain
                        @click="displayEditPop2 = true"
                    >
                        编辑学历信息
                    </el-button>
                    <div class="info-box">
                        <detail-row-col
                            :list="allList[1]"
                            :labelStyle="labelStyle"
                        ></detail-row-col>
                    </div>
                </div>
                <div class="items h-68 under-line">
                    <el-col :span="24" class="item">
                        <div class="label" :style="labelStyle">
                            政务微信群：
                        </div>
                        <div>
                            <div class="u-flex" v-if="groups.length">
                                <div
                                    class="group"
                                    @click="goWXGroup(item.id)"
                                    v-for="(item, index) in groups"
                                    :key="index"
                                >
                                    {{ item.name }}
                                </div>
                            </div>
                            <div v-else>--</div>
                        </div>
                    </el-col>

                    <el-col :span="24" class="item">
                        <div class="label" :style="labelStyle">
                            政务微信好友：
                        </div>
                        <div>
                            {{ detail.friends || "--" }}
                        </div>
                    </el-col>
                </div>
                <div class="items w-80">
                    <!-- 拉黑相关 -->
                    <detail-row-col
                        v-if="blacklistInfo.length"
                        :list="blacklistInfo"
                        :labelStyle="labelStyle"
                    ></detail-row-col>
                </div>
            </div>
        </div>
        <DetailViewPop1
            @refresh="refresh"
            v-model="displayEditPop1"
            :id="row.id"
        ></DetailViewPop1>
        <DetailViewPop2
            @refresh="refresh"
            v-model="displayEditPop2"
            :id="row.id"
        ></DetailViewPop2>
        <black-pop
            v-model="showBlackPop"
            :id="row.uniplat_uid"
            @refresh="refresh"
        ></black-pop>
        <set-tag-dialog
            model="user_profile_basic"
            action="set_labels"
            v-model="showSetTag"
            :ids="checkEdIds"
            @refresh="refresh"
            :isBatch="false"
        ></set-tag-dialog>
        <TagHistory
            :id_card_openid="row.id_card_openid"
            v-model="showTagHistory"
        />
    </div>
</template>

<script lang='ts'>
    import { sdk } from "@/service"
    import { cloneDeep } from "lodash"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { Row } from ".."
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { buildItems } from "./detail-view"
    import { routesMap } from "@/router/direction"
    import DetailViewPop1 from "./detail-view-pop1.vue"
    import DetailViewPop2 from "./detail-view-pop2.vue"
    import { formatTime } from "@/utils/tools"
    import { config, EnvProject } from "@/config"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import BlackPop from "./black-pop.vue"
    import SetTagDialog from "../../seeker-info/components/set-tag.vue"
    import TagHistory from "./tag-history.vue"

    interface Res {
        friends: string
        groups: { name: string; id: string }[]
    }

    @Component({
        components: {
            DetailViewPop1,
            DetailViewPop2,
            DetailRowCol,
            BlackPop,
            SetTagDialog,
            TagHistory,
        },
    })
    export default class DetailView extends Vue {
        @Prop()
        private row!: Row

        detail: any = {}

        private isEdit = false

        private displayEditPop1 = false
        private displayEditPop2 = false
        private showBlackPop = false

        private labelStyle = {
            fontSize: "14px",
            width: "98px",
            marginRight: "10px",
            lineHeight: "34px",
            color: "#555",
        }

        private showSetTag = false

        private get uid() {
            return this.row?.uid || ""
        }

        private get checkEdIds() {
            return [this.row!.id]
        }

        private groups: Res["groups"] = []

        private goWXGroup(id: string) {
            this.$router.push({
                name: routesMap.wx.groupDetail,
                query: { id: id, from: "labourManage-seekerDetail" },
            })
        }

        private getGroupInfo() {
            return sdk
                .getDomainService(
                    "getUserWecomRelation",
                    "client_api",
                    "wecom_core"
                )
                .get<Res>({
                    uniplat_uid: this.uid,
                })
                .then((r) => {
                    this.groups = r.groups || []
                    this.detail = cloneDeep(r)
                })
        }

        private blackListClick() {
            const detail = { ...this.row } as any
            if (+detail.black_id) {
                // 解除拉黑
                MessageBox.confirm(`确认移除黑名单？`, "确认").then(() => {
                    pageLoading(() => {
                        return sdk.core
                            .model("black_list")
                            .action("delete")
                            .updateInitialParams({
                                selected_list: [{ v: 0, id: detail.black_id }],
                            })
                            .execute()
                            .then(() => {
                                this.refresh()
                                this.callRefresh(routesMap.blacklist.index)
                            })
                    })
                })
            } else {
                this.showBlackPop = true
                // 拉黑
            }
        }

        private get blacklistInfo() {
            const isXg = config.envProject === EnvProject.孝感项目
            if (!this.row || !isXg) return []
            const detail = { ...this.row } as any
            const h = this.$createElement
            const blackListClick = this.blackListClick
            return [
                {
                    label: "拉黑原因：",
                    value: detail!.black_memo,
                    span: 12,
                },
                {
                    label: "拉黑操作人：",
                    value:
                        detail!.black_creator_real_name || detail!.black_update_by,
                    span: 12,
                },
                {
                    label: "拉黑时间：",
                    value: formatTime.default(detail!.black_update_time),
                    span: 12,
                },
                {
                    label: "拉黑操作：",
                    vNode: h(
                        "el-button",
                        {
                            attrs: {
                                type: "text",
                                size: "mini",
                            },
                            on: {
                                click: () => blackListClick(),
                            },
                        },
                        !+detail.black_id ? "拉黑" : "解除拉黑"
                    ),
                    span: 12,
                },
            ]
        }

        mounted() {
            this.getGroupInfo()
        }

        private refresh() {
            this.$emit("refresh")
        }

        private showTagHistory = false

        private get allList() {
            return buildItems(
                this.row,
                this.$createElement,
                () => {
                    return this.editTag()
                },
                () => {
                    this.showTagHistory = true
                }
            )
        }

        private editTag() {
            this.showSetTag = true
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        background: #fff;
        .content {
            width: calc(800 / 1440 * 100vw);
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
        }
        ::v-deep .detail-row .item {
            line-height: 34px;
            font-size: 14px;
            color: #333;
            margin: 0;
        }
    }

    .group {
        color: rgba(87, 130, 236, 1);
        cursor: pointer;
        text-decoration: underline;

        & + .group {
            margin-left: 10px;
        }
    }

    .items {
        width: 100%;
        position: relative;

        .info-box {
            width: 80%;
        }

        & + .items {
            margin-top: 22px;
        }

        .item {
            line-height: 2;
            display: flex;
        }

        .label {
            flex-shrink: 0;
            color: #666;
        }

        .edit-btn {
            position: absolute;
            right: 0;
            top: 6px;
            padding: 8px;
        }
        &.h-68 {
            height: 68px;
        }
        &.w-80 {
            width: 80%;
        }
    }

    .under-line {
        position: relative;

        &::after {
            content: "";
            display: block;
            border-bottom: 0.5px dashed #d8d8d8;
            position: absolute;
            width: 100%;
            left: 0px;
            bottom: -11px;
        }
    }
</style>
