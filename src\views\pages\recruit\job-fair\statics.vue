<template>
    <div>
        <div class="core-ui-custom-header">
            <div class="title">招聘会统计</div>
        </div>
        <div class="filter u-m-b-24">
            <table-filter
                v-if="filter"
                :tableFilter="filter"
                @search="search"
            ></table-filter>
        </div>
        <div v-if="show">
            <statics-1
                :filterData="filterData"
                @initFilter="initFilter"
            ></statics-1>
            <!-- <statics-2 :filterData="filterData"></statics-2>
            <statics-3 :filterData="filterData"></statics-3> -->
        </div>
    </div>
</template>

<script lang='ts'>
    import { Component, Vue } from "vue-property-decorator"
    import TableFilter from "@/core-ui/component/table/filter.vue"
    import { filter } from "."
    import Statics1 from "./components/statics-1.vue"
    import Statics2 from "./components/statics-2.vue"
    import Statics3 from "./components/statics-3.vue"
    import moment from "moment"
    import { formatDate } from "@/core-ui/helpers/tools"

    @Component({ components: { TableFilter, Statics1, Statics2, Statics3 } })
    export default class JobFairStatics extends Vue {
        private filter: any = null
        private show = false

        private filterData = {}

        private search(v: { time: string[] }) {
            let timeF: any = {}
            if (typeof v.time !== "string") {
                v.time = v.time || ""
                timeF = {
                    start_date: v.time[0],
                    end_date: v.time[1],
                }
            }
            this.getList({
                ...timeF,
                ...{ ...v, time: undefined },
            })
        }

        mounted() {
            this.getList({
                start_date: formatDate(+moment().startOf("month")) || "",
                end_date: formatDate(+moment().endOf("month")) || "",
            })
        }

        private initFilter(areas: any) {
            if (this.filter) return
            this.filter = filter(areas)
        }

        getList(filter: Record<string, string> = {}) {
            console.log("getList", JSON.parse(JSON.stringify(filter)))
            this.filterData = filter
            this.show = false
            this.$nextTick(() => {
                this.show = true
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .filter {
        ::v-deep .form-filed {
            width: 300px !important;
        }
    }
    ::v-deep .content {
        margin-bottom: 24px;
        background: #ffffff;
        padding: 20px;
        .title {
            font-weight: 600;
            color: #222;
            font-size: 18px;
            line-height: 18px;
            margin-bottom: 20px;
        }
    }
</style>
