import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
const isEz = config.envProject === EnvProject.鄂州项目
export const users = [
    {
        path: "/users",
        name: routesMap.home.users,
        meta: {
            title: "注册用户管理",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: isEz
                ? require("@/assets/icon/menu2/labourManage_ez.svg")
                : require("@/assets/icon/menu2/labourManage.svg"),
            hidden: isQj,
        },
        component: layout,
        children: [
            // 市场主体管理改名企业信息管理
            {
                path: "/employmentManage",
                redirect: "employmentManage/company-mange",
                name: routesMap.employmentManage.employmentManage,
                meta: {
                    // svgIcon: require("@/assets/icon/menu/employmentManage.svg"),
                    title: [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                        config.envProject
                    )
                        ? "企业用户管理"
                        : isJz || isSaas
                        ? "企业用户管理"
                        : "企业信息管理",
                },
                component: RouteView,
                children: [
                    {
                        path: "company-mange",
                        name: routesMap.employmentManage.companyManage,
                        meta: {
                            title: "企业列表",
                            role: "/tablelist/xg_agent/company_back_list",
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/company-manage/index.vue"
                            ),
                    },
                    {
                        path: "enterprise-settled-apply",
                        name: routesMap.employmentManage.enterpriseSettledApply,
                        meta: {
                            title: "企业入驻申请管理",
                            role: "/tablelist/xg_agent_apply",
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/enterprise-settled-apply/index.vue"
                            ),
                    },
                    {
                        path: "company-manage-add",
                        name: routesMap.employmentManage.companyManageAdd,
                        meta: {
                            title: "新增企业",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.companyManage,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/company-manage/add.vue"
                            ),
                    },
                    {
                        path: "company-detail",
                        name: routesMap.employmentManage.companyManageDetail,
                        meta: {
                            title: "企业详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.companyManage,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/company-manage/detail.vue"
                            ),
                    },
                    {
                        path: "human-resource-check",
                        name: routesMap.employmentManage.humanResourceCheck,
                        meta: {
                            title: "人力资源机构入驻审核",
                            role: "/tablelist/xg_human_agent/audit_back_list",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/human-resource-check/index.vue"
                            ),
                    },
                    {
                        path: "human-resource-check-detail",
                        name: routesMap.employmentManage
                            .humanResourceCheckDetail,
                        meta: {
                            title: "入驻审核",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.humanResourceCheck,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/human-resource-check/detail.vue"
                            ),
                    },
                    {
                        path: "human-resource",
                        name: routesMap.employmentManage.humanResource,
                        meta: {
                            title: "人力资源机构列表",
                            role: "/tablelist/xg_human_agent/operator_back_list",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/human-resource/index.vue"
                            ),
                    },
                    {
                        path: "human-resource-add",
                        name: routesMap.employmentManage.humanResourceAdd,
                        meta: {
                            title: "新增人力资源机构",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.humanResource,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/human-resource/add.vue"
                            ),
                    },
                    {
                        path: "human-resource-detail",
                        name: routesMap.employmentManage.humanResourceDetail,
                        meta: {
                            title: "人力资源机构详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.humanResource,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/human-resource/detail.vue"
                            ),
                    },
                    {
                        path: "questionnaire",
                        name: routesMap.employmentManage.questionnaire,
                        meta: {
                            title: "企业调查问卷管理",
                            role: "/tablelist/serve_task/company_questionnaire_for_operate",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/questionnaire/index.vue"
                            ),
                    },
                    {
                        path: "questionnaire-add",
                        name: routesMap.employmentManage.questionnaireAdd,
                        meta: {
                            title: "新增调查问卷",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.questionnaire,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/questionnaire/add.vue"
                            ),
                    },
                    {
                        path: "questionnaire-statistics",
                        name: routesMap.employmentManage
                            .questionnaireStatistics,
                        meta: {
                            title: "问卷统计",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.questionnaire,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/questionnaire/statistics.vue"
                            ),
                    },
                    {
                        path: "questionnaire-detail",
                        name: routesMap.employmentManage.questionnaireDetail,
                        meta: {
                            title: "员工调查问卷",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.questionnaire,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/questionnaire/detail.vue"
                            ),
                    },
                    {
                        path: "company-questionnaire",
                        name: routesMap.employmentManage.companyQuestionnaire,
                        meta: {
                            title: "已提交用工调查问卷",
                            role: "/tablelist/task_serve_record/for_agent_questionnaire_operate",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/company-questionnaire/index.vue"
                            ),
                    },
                    {
                        path: "questionnaire-detail-readonly",
                        name: routesMap.employmentManage
                            .questionnaireDetailReadonly,
                        meta: {
                            title: "员工调查问卷",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.companyQuestionnaire,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/questionnaire/detail.vue"
                            ),
                    },
                    {
                        path: "company-join-cms",
                        name: routesMap.employmentManage.companyJoinCms,
                        meta: {
                            title: "企业邀约入驻列表",
                            role: "/tablelist/invite_company_record",
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/company-join-cms/index.vue"
                            ),
                    },
                    {
                        path: "company-blacklist",
                        name: routesMap.employmentManage.blacklist,
                        meta: {
                            title: "企业拉黑求职者管理",
                            hidden: [
                                EnvProject.荆州项目,
                                EnvProject.黄州项目,
                                EnvProject.宜都项目,
                                EnvProject.saas项目,
                            ].includes(config.envProject),
                            role: "/tablelist/xg_agent/black_list",
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/black-list/index.vue"
                            ),
                    },
                    {
                        path: "company-blacklist-detail",
                        name: routesMap.employmentManage.blacklistDetail,
                        meta: {
                            title: "企业拉黑求职者详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.blacklist,
                            // role: "/tablelist/xg_agent/company_back_list",
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/black-list/detail.vue"
                            ),
                    },
                    {
                        path: "tools-inner",
                        name: routesMap.toolsInner.index,
                        meta: {
                            title: "预处理",
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/tools-inner/index.vue"),
                    },
                    {
                        path: "black-list",
                        name: routesMap.blacklist.company,
                        meta: {
                            title: "企业黑名单",
                            role: "/tablelist/black_list/manage",
                            hidden:
                                [
                                    EnvProject.荆州项目,
                                    EnvProject.黄州项目,
                                    EnvProject.宜都项目,
                                ].includes(config.envProject) && !isDev,
                            // single: true,
                            // // svgIcon: require("@/assets/icon/menu/labourManage.svg"),
                        },
                        component: () =>
                            import("@/views/pages/blacklist/company.vue"),
                    },
                ],
            },

            // 居民管理
            {
                path: "/labourManage",
                redirect: "labourManage/seekerInfo",
                name: routesMap.labourManage.root,
                meta: {
                    title: "居民管理",
                    // svgIcon: require("@/assets/icon/menu/labourManage.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "seekerInfo",
                        name: routesMap.labourManage.seekerInfo,
                        meta: {
                            title: "居民信息查看",
                            role: "/tablelist/user_profile_basic",
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/seeker-info/index.vue"
                            ),
                    },
                    {
                        path: "seekerDetail",
                        name: routesMap.labourManage.seekerDetail,
                        meta: {
                            title: "档案详情",
                            hidden: true,
                            parentMenuName: routesMap.labourManage.seekerInfo,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/seeker-info/detail.vue"
                            ),
                    },
                    {
                        path: "recordList",
                        name: routesMap.labourManage.recordList,
                        meta: {
                            title: "服务记录列表",
                            role: "/tablelist/task_serve_record/for_operate",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/record-list/index.vue"
                            ),
                    },
                    {
                        path: "recordDetail",
                        name: routesMap.labourManage.recordDetail,
                        meta: {
                            title: "服务记录详情",
                            hidden: true,
                            parentMenuName: routesMap.labourManage.recordList,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/record-list/detail.vue"
                            ),
                    },
                    {
                        path: "seeker-question",
                        name: routesMap.labourManage.seekerQuestion,
                        meta: {
                            title: "居民问卷管理",
                            role: "/tablelist/general_questionnaire/list_for_operate_personal",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/seeker-question/index.vue"
                            ),
                    },
                    {
                        path: "seeker-question-detail",
                        name: routesMap.labourManage.seekerQuestionModel,
                        meta: {
                            title: "问卷统计",
                            hidden: true,
                            parentMenuName:
                                routesMap.labourManage.seekerQuestion,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/seeker-question/detail.vue"
                            ),
                    },
                    {
                        path: "seeker-question-statistics",
                        name: routesMap.labourManage.seekerQuestionStatistics,
                        meta: {
                            title: "问卷统计",
                            hidden: true,
                            parentMenuName:
                                routesMap.labourManage.seekerQuestion,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/seeker-question/statistics.vue"
                            ),
                    },
                    {
                        path: "seeker-question-service-list",
                        name: routesMap.labourManage.seekerQuestionServiceList,
                        meta: {
                            title: "服务对象列表",
                            hidden: true,
                            parentMenuName:
                                routesMap.labourManage.seekerQuestion,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/seeker-question/service-list.vue"
                            ),
                    },
                    {
                        path: "service-record-manage",
                        name: routesMap.labourManage.serviceRecordManage,
                        meta: {
                            title: "我的服务记录处理",
                            role: "/tablelist/task_serve_call_back/for_operate_user",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/service-record-manage/index.vue"
                            ),
                    },
                    {
                        path: "user-account",
                        name: routesMap.labourManage.userAccount,
                        meta: {
                            title: "注册居民管理",
                            role: "/tablelist/user_account/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/user-account/index.vue"
                            ),
                    },
                    {
                        path: "user-account-detail",
                        name: routesMap.labourManage.userAccountDetail,
                        meta: {
                            title: "注册居民详情",
                            hidden: true,
                            parentMenuName: routesMap.labourManage.userAccount,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/user-account/detail.vue"
                            ),
                    },
                    {
                        path: "channel",
                        name: routesMap.base.channel.index,
                        meta: {
                            title: "注册渠道管理",
                            role: "/tablelist/reg_channel_manage/manager",
                        },
                        component: () =>
                            import("@/views/pages/base/channel/index.vue"),
                    },
                    {
                        path: "register-statistics",
                        name: routesMap.labourManage.registerStatistics,
                        meta: {
                            title: "实名数据统计",
                            hidden: ![EnvProject.孝感项目].includes(
                                config.envProject
                            ),
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/register-statistics/index.vue"
                            ),
                    },
                    {
                        path: "xcd-manage",
                        name: routesMap.labourManage.xcdManage,
                        meta: {
                            title: "孝创贷数据管理",
                            hidden: [
                                EnvProject.荆州项目,
                                EnvProject.黄州项目,
                                EnvProject.宜都项目,
                            ].includes(config.envProject),
                            role: "/tablelist/t_loan_person_info/loan_list",
                        },
                        component: () =>
                            import("@/views/pages/labour-manage/xcd/index.vue"),
                    },
                    {
                        path: "xcd-detail",
                        name: routesMap.labourManage.xcdDetail,
                        meta: {
                            title: "孝创贷数据详情",
                            parentMenuName: routesMap.labourManage.xcdManage,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/xcd/detail.vue"
                            ),
                    },
                    {
                        path: "black-list",
                        name: routesMap.blacklist.index,
                        meta: {
                            title: "居民黑名单",
                            role: "/tablelist/black_list/manage",
                            hidden:
                                [
                                    EnvProject.荆州项目,
                                    EnvProject.黄州项目,
                                    EnvProject.宜都项目,
                                ].includes(config.envProject) && !isDev,
                            // single: true,
                            // // svgIcon: require("@/assets/icon/menu/labourManage.svg"),
                        },
                        component: () =>
                            import("@/views/pages/blacklist/index.vue"),
                    },
                ],
            },
        ],
    },
]
