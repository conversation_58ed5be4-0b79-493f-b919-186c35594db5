{
    "cSpell.enabled": true,
    // 保存自动格式化
    // "editor.formatOnSave": true,
    "typescript.tsdk": "node_modules/typescript/lib",
    "typescript.enablePromptUseWorkspaceTsdk": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[vue]": {
        "editor.defaultFormatter": "octref.vetur"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "vetur.format.options.tabSize": 4,
    "files.exclude": {
        "node_modules": false
    },
    "prettier.semi": false,
    "vetur.format.scriptInitialIndent": true,
    "vetur.format.styleInitialIndent": true,
    "vetur.format.defaultFormatterOptions": {
        "prettyhtml": {
            "printWidth": 80
        },
        "prettier": {
            "semi": false,
            "singleQuote": false,
            "printWidth": 80
        }
    },
    "javascript.format.insertSpaceAfterConstructor": true,
    "typescript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": false,
    "[javascript][typescript]": {
        "editor.maxTokenizationLineLength": 2500
    },
    "cSpell.words": [
        "btns",
        "Cascader",
        "QQXB",
        "xiaogan"
    ],
    "[env]": {
        "editor.defaultFormatter": "IronGeek.vscode-env"
    },
    "search.followSymlinks": false,
    "editor.linkedEditing": true,
    "php.linkedEditing.variables": true,
    "editor.guides.bracketPairs": true
}