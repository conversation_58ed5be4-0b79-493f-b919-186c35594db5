<template>
    <div
        class="container-box core-ui-table-container"
        :key="refreshQueryParams"
    >
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <div class="u-flex">
                    <el-button
                        type="primary"
                        class="u-m-r-15"
                        v-if="showAreaBtn"
                        @click="showAreaPop = true"
                    >
                        修改管理区域
                    </el-button>
                    <el-button
                        v-if="isXg"
                        class="u-m-r-15"
                        type="primary"
                        v-role="'model.xg_agent.action.update_recommend_number'"
                        @click="recommendPop = true"
                    >
                        推荐权重
                    </el-button>
                    <el-button
                        v-role="'model.xg_agent.action.edit_agent'"
                        type="primary"
                        class="u-m-r-15"
                        @click="showEditPop = true"
                    >
                        编辑
                    </el-button>
                    <!-- <template v-else>
                        <el-button
                            type="primary"
                            key="cancel"
                            plain
                            @click="cancel"
                        >
                            取消
                        </el-button>
                        <el-button type="primary" @click="confirm">
                            保存
                        </el-button>
                    </template> -->
                </div>
                <template v-if="!hideHumanAgent">
                    <el-button
                        v-role="'model.xg_agent.action.open_human_agent'"
                        @click="applyPop = true"
                        v-if="!is_open_human_agent"
                        type="primary"
                    >
                        开通人力资源机构
                    </el-button>

                    <el-button
                        v-else-if="xgHumanAgentStatus4Pending"
                        @click="openTips"
                        type="primary"
                    >
                        开通人力资源机构
                    </el-button>

                    <el-button
                        v-else-if="xgHumanAgentId"
                        @click="goResourceDetail"
                    >
                        跳转到人力资源机构详情
                    </el-button>
                </template>
                <!-- <el-button @click="copy"> 复制小程序地址 </el-button> -->
            </div>
        </div>
        <div class="contain-box">
            <div
                class="title font-family-medium u-flex u-row-between u-m-b-20 u-p-l-20"
            >
                基本信息
                <el-image
                    src="/img/icon/key-org.png"
                    class="key-icon"
                    v-if="detail && +detail.is_focus"
                />
            </div>
            <div class="u-p-l-20">
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="items"
                ></detail-row-col>
                <detail-row-col
                    :labelStyle="labelStyle"
                    :list="blacklistInfo"
                    class="black-list"
                ></detail-row-col>
            </div>
        </div>
        <div class="fill-box">
            <div class="core-ui-table-container">
                <div class="d-flex flex-column table-tabs">
                    <el-tabs v-model="currentPageName">
                        <el-tab-pane
                            v-for="item in tabs"
                            :key="item.name"
                            :label="item.name"
                            :name="item.component"
                        >
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <div v-if="detail">
                <components
                    v-if="enterpriseId"
                    :enterpriseId="enterpriseId"
                    :detailId="detail.id"
                    :business="business"
                    :detail="detail"
                    :is="currentPageName"
                ></components>
            </div>
        </div>
        <template v-if="detail">
            <apply-pop
                v-model="applyPop"
                :v="detail.v"
                :rowId="detail.id"
                @refresh="getDetail"
            ></apply-pop>
            <black-pop
                v-model="showBlackPop"
                :id="detail.id"
                @refresh="init"
            ></black-pop>
            <recommend-pop
                v-model="recommendPop"
                :id="detail.id"
                @refresh="init"
            ></recommend-pop>
            <edit-pop
                v-model="showEditPop"
                :detail="detail"
                @refresh="init"
            ></edit-pop>
        </template>
        <set-tag-dialog
            v-model="showSetTag"
            :ids="[id]"
            :isBatch="false"
            @refresh="init"
        ></set-tag-dialog>
        <AreaPop
            v-if="detail"
            v-model="showAreaPop"
            :id="detail.id"
            @refresh="init"
        ></AreaPop>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import PositionList from "@/views/pages/employment-manage/company-manage/components/position-list.vue"
    import ApplyPop from "@/views/pages/employment-manage/company-manage/components/apply-pop.vue"
    import ManageList from "@/views/pages/employment-manage/company-manage/components/manage-list.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { sdk } from "@/service"
    import { routesMap } from "@/router/direction"
    import { pageLoading } from "@/views/controller"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { Row, predict, buildItems, companyFormConfig } from "./detail"
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import CompanyRemark from "./components/company-remark.vue"
    import RecruitRecord from "./components/recruit-record.vue"
    import DataStorage from "./components/data-storage.vue"
    import { AuditStatus } from "../human-resource-check"
    import { config, EnvProject } from "@/config"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { formatTime } from "@/utils/tools"
    import { MessageBox } from "element-ui"
    import BlackPop from "./components/black-pop.vue"
    import BlackList from "./components/black-list.vue"
    import RecommendPop from "./components/recommend-pop.vue"
    import MessageList from "./components/message-list.vue"
    import EditPop from "./components/edit-company-pop.vue"
    import SetTagDialog from "./components/set-tag.vue"
    import { flatMap } from "lodash"
    import AreaPop from "./components/area-pop.vue"
    import { hasRole2ChangeArea } from "."
    import PreferentialPolicyEmp from "@/views/pages/policy-apply-record/index.vue"
    import TaskList from "@/views/pages/employment-manage/human-resource/components/task-list.vue"
    import { copyTextToClipboard } from "uniplat-sdk/build/main/helpers/clipboard"

    const isXg = config.envProject === EnvProject.孝感项目

    @Component({
        name: routesMap.employmentManage.companyManageDetail,
        components: {
            PositionList,
            ApplyPop,
            ManageList,
            FormBuilder,
            CompanyRemark,
            RecruitRecord,
            DataStorage,
            DetailRowCol,
            BlackPop,
            BlackList,
            RecommendPop,
            MessageList,
            EditPop,
            SetTagDialog,
            AreaPop,
            PreferentialPolicyEmp,
            TaskList,
        },
    })
    export default class Detail extends FormController {
        private items: ColItem[] = buildItems({}, this)

        private recommendPop = false
        private showEditPop = false
        private isXg = isXg
        private isYD = config.envProject === EnvProject.宜都项目
        private isEZ = config.envProject === EnvProject.鄂州项目
        showSetTag = false
        private showAreaPop = false
        private showAreaBtn = false

        hideHumanAgent = [EnvProject.宜都项目, EnvProject.掇刀项目].includes(
            config.envProject
        )

        private labelStyle = {
            width: "120px",
            textAlign: "right",
            paddingRight: "12px",
        }

        breadcrumbs: BreadcrumbItem[] = [
            {
                label: `企业列表`,
                to: {
                    name: routesMap.employmentManage.companyManage,
                },
            },
            {
                label: this.title,
            },
        ]

        refreshConfig = {
            fun: this.init,
            name: routesMap.employmentManage.companyManageDetail,
        }

        private applyPop = false

        private detail: Row | null = null

        private id = ""

        private currentPageName = "PositionList"

        private get tabs() {
            return [
                {
                    name: "岗位列表",
                    component: "PositionList",
                },
                {
                    name: "招聘会记录",
                    component: "RecruitRecord",
                },
                {
                    name: "政策申报",
                    component: "PreferentialPolicyEmp",
                },
                {
                    name: "服务订单",
                    component: "TaskList",
                    hide: !this.xgHumanAgentId,
                },
                {
                    name: "企业介绍",
                    component: "CompanyRemark",
                },
                {
                    name: "企业管理员",
                    component: "ManageList",
                },
                {
                    name: "数据仓库详情",
                    component: "DataStorage",
                    hide: config.envProject !== EnvProject.孝感项目,
                },
                {
                    name: "拉黑用户列表",
                    component: "BlackList",
                    hide: config.envProject !== EnvProject.孝感项目,
                },
                {
                    name: "消息记录",
                    component: "MessageList",
                    hide: config.envProject !== EnvProject.孝感项目,
                },
            ].filter((e) => !e.hide)
        }

        private get title() {
            return this.$route.meta?.title || ""
        }

        private get is_open_human_agent() {
            return this.detail?.is_open_human_agent
        }

        private get business() {
            return this.detail?.business
        }

        private get enterpriseId() {
            return this.detail?.enterpriseId
        }

        private get xgHumanAgentId() {
            return this.detail?.xgHumanAgentId
        }

        private get xgHumanAgentStatus4Pending() {
            return this.detail?.xgHumanAgentStatus === AuditStatus.待审核
        }

        mounted() {
            this.setBreadcrumbs()
            this.init()
        }

        setBreadcrumbs() {
            this.id = this.$route.query.id as string
            let d: BreadcrumbItem[] = [
                {
                    label: `企业列表`,
                    to: {
                        name: routesMap.employmentManage.companyManage,
                    },
                },
            ]
            if (this.from) {
                d = [...getCacheBreadcrumbsByRoutePath(this.from)]
            }
            d = [
                ...d,
                {
                    label: this.title,
                    to: {
                        name: routesMap.employmentManage.companyManageDetail,
                        query: {
                            id: this.id,
                            from: this.from,
                        },
                    },
                },
            ]
            this.breadcrumbs = d
            updateTagItem({
                name: routesMap.employmentManage.companyManageDetail,
                breadcrumb: d,
            })
        }

        init() {
            pageLoading(() => {
                return this.getDetail()
            })
        }

        get from() {
            return this.$route.query.from as string | undefined
        }

        private getDetail() {
            if (this.id !== this.$route.query.id) {
                this.detail = null
                this.currentPageName = this.tabs[0].component
            }
            if (this.from === routesMap.employmentManage.blacklist) {
                this.currentPageName = "BlackList"
            }
            this.id = this.$route.query.id as string

            const model = sdk.core.model("xg_agent").detail(this.id, "back_detail")
            return model.query().then((r) => {
                const row = sdk.buildRow<Row>(r.row, predict)
                this.detail = row
                console.log("jjrr", JSON.parse(JSON.stringify(row)))
                this.items = this.buildItemsWithTags(buildItems(row, this))
                hasRole2ChangeArea(row.id).then((r) => {
                    this.showAreaBtn = !!r
                })
                buildFormSections(
                    companyFormConfig(+row.id, {
                        v: this.detail.recommend_number,
                    })
                ).then((r) => {
                    this.buildForm(r.forms)
                })
                return row
            })
        }

        buildItemsWithTags(items: ColItem[]) {
            const allTags = flatMap(this.detail?.tags)
            const h = this.$createElement
            items.push({
                label: "企业标签",
                span: 24,
                vNode: h(
                    "div",
                    {
                        class: "tags-list",
                    },
                    [
                        allTags?.length
                            ? allTags.map((tag) =>
                                  h(
                                      "div",
                                      {
                                          class: "tag",
                                          style: {
                                              "--color": tag.tagColor,
                                          },
                                      },
                                      tag.tagName
                                  )
                              )
                            : h(
                                  "span",
                                  {
                                      class: "color-9 u-m-r-10",
                                      style: {
                                          "margin-top": "-1px",
                                      },
                                  },
                                  "暂无标签"
                              ),
                        h(
                            "div",
                            {
                                class: "primary pointer",
                                on: {
                                    click: () => (this.showSetTag = true),
                                },
                            },
                            this.isYD || this.isEZ ? "" : "编辑标签"
                        ),
                    ]
                ),
            })
            return items
        }

        private goResourceDetail() {
            this.$router.push({
                name: routesMap.employmentManage.humanResourceDetail,
                query: {
                    id:
                        this.detail!.xgHumanAgentAccess_key ||
                        this.xgHumanAgentId + "",
                },
            })
        }

        private openTips() {
            this.$message.warning("该企业已申请开通人力资源，暂未审核通过")
        }

        private get blacklistInfo() {
            if (!this.detail || !isXg) return []
            const h = this.$createElement
            const blackListClick = this.blackListClick
            return [
                {
                    label: "是否被拉黑",
                    value: +this.detail.black_id ? "是" : "否",
                    span: 8,
                },
                {
                    label: "拉黑原因",
                    value: this.detail!.black_memo,
                    span: 8,
                },
                {
                    label: "拉黑操作人",
                    value:
                        this.detail!.black_creator_real_name ||
                        this.detail!.black_update_by,
                    span: 8,
                },
                {
                    label: "拉黑时间",
                    value: formatTime.default(this.detail!.black_update_time),
                    span: 8,
                },
                {
                    label: "拉黑操作：",
                    vNode: h(
                        "el-button",
                        {
                            attrs: {
                                type: "text",
                                size: "mini",
                            },
                            on: {
                                click: () => blackListClick(),
                            },
                        },
                        !+this.detail.black_id ? "拉黑" : "解除拉黑"
                    ),
                },
            ]
        }

        private showBlackPop = false

        private blackListClick() {
            if (+this.detail!.black_id) {
                // 解除拉黑
                MessageBox.confirm(`确认移除黑名单？`, "确认").then(() => {
                    pageLoading(() => {
                        return sdk.core
                            .model("black_list")
                            .action("delete")
                            .updateInitialParams({
                                selected_list: [
                                    { v: 0, id: this.detail!.black_id },
                                ],
                            })
                            .execute()
                            .then(() => {
                                this.init()
                                this.callRefresh(routesMap.blacklist.index)
                            })
                    })
                })
            } else {
                this.showBlackPop = true
                // 拉黑
            }
        }

        private copy() {
            copyTextToClipboard(`/pages/tabbar/job/org-detail?id=${this.id}`)
            this.$message.success("复制成功")
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .contain-box {
        background-color: #fff;
        padding: 20px;
        margin-bottom: 24px;
    }

    ::v-deep .black-list.detail-row .item:nth-child(2) {
        margin: 0 -10px 0 -5px;
    }

    .title {
        width: 100%;
        height: 36px;
        font-size: 16px;
        background: #f8f8f8;
        padding-left: 20px;
        .key-icon {
            width: 90px;
        }
    }

    .container-box {
        height: 100%;
    }

    .component {
        background-color: #fff;
    }
</style>
