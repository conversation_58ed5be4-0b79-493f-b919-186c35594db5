<template>
    <div class="content">
        <div class="content-inner">
            <Header title="七步工作法"></Header>
            <Box :row="row" :summaryData="summaryData"></Box>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop } from "vue-property-decorator"
    import Header from "../../common/header.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { ChartQueryResultItem } from "@/views/pages/big-screen/model"
    import Box from "./box.vue"

    @Component({ components: { Header, Box } })
    export default class Template extends BaseItem {
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 538px;
        height: 152px;
        // background: rgba(1, 25, 155, 0.7);
        // border-radius: 0px 0px 20px 20px;
        position: relative;
        z-index: 9;

        /deep/ .header-content {
            height: 40px;
        }

        .content-inner {
            position: absolute;
            left: 0px;
            right: 0px;
            width: 1287px;
            height: 152px;
            background: rgba(1, 25, 155, 0.7);
            border-radius: 0px 0px 20px 20px;
            .header-content {
                margin-bottom: 4px;
            }
        }
    }
    /deep/.header-content {
        background-image: url("../../../assets/智慧就业监测/title-l.png");
    }
</style>
