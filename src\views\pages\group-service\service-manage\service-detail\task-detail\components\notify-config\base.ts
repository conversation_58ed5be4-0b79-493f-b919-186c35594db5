import { buildFormSections, FormItem, FormType } from "@/core-ui/component/form"
import { TableColumn } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { debounce, find, split } from "lodash"
import { Component, Prop, Vue } from "vue-property-decorator"
import { NotifyConfigRow } from "../../../notify"

export const mpColumn: TableColumn[] = [
    {
        prop: "select",
        width: "58px",
        type: "selection",
        align: "center",
    },
    {
        prop: "app_id",
        width: "120px",
        label: "appId",
    },
    {
        prop: "app_name",
        label: "小程序名称",
    },
    {
        prop: "status_label",
        label: "状态",
        width: "90",
    },
]

@Component({})
export class BaseConfig extends Vue {
    @Prop()
    protected readonly item!: {
        label: string
        tip?: string
        data?: NotifyConfigRow
        action: string
    }

    @Prop()
    protected taskId!: number

    protected expand = false
    protected data: NotifyConfigRow | null = null
    // protected formItem: FormItem | null = null
    protected msg_params: Record<string, string> = {}

    protected get formItem() {
        const f = find(this.forms, { prop: "msg_code" })
        return f || null
    }

    protected change() {
        this.expand = this.data?.enable ?? false
    }

    protected getAction() {
        return sdk.core
            .model("serve_task")
            .action(this.item.action)
            .updateInitialParams({ selected_list: [{ id: this.taskId, v: 0 }] })
    }

    protected forms: FormItem[] = []

    protected getFormConfig() {
        return buildFormSections({
            action: this.getAction(),
            forms: [],
        }).then((r) => {
            this.forms = r.forms
            // this.formItem = find(r.forms, { prop }) || null
            return r.data
        })
    }

    confirm() {
        const d = {
            ...this.data,
        }
        d.notify_time = formatTime.seconds(d.notify_time, "YYYY-MM-DD HH:mm:ss")
        if (Object.keys(this.msg_params).length) {
            d.msg_params = JSON.stringify(this.msg_params)
        }
        return this.getAction().addInputs_parameter(d).execute()
    }

    changeMsgParams = debounce(this.doChangeMsgParams, 400)

    doChangeMsgParams() {
        this.data!.msg_params = JSON.stringify(this.msg_params)
        this.getAction()
            .addInputs_parameter({
                ...this.data,
            })
            .updateControlsProperties("msg_params")
            .then((r) => {
                this.data!.sms_template_tip = find(r.masters, {
                    property: "sms_template_tip",
                })?.default_value as string
            })
    }

    protected subscribeMsg: string[] = []

    protected buildSubscribeMsg(sms_template_tip: string) {
        this.subscribeMsg = split(sms_template_tip, "\n").map((i) => {
            if (i.includes("：")) {
                const v = i.split(/[：]/)
                const c = v[1].replace(/\{[^}]+\}/g, "") || "--"
                if (v.length > 1) {
                    return `<span class="color-9">${v[0]}：</span>${c}`
                }
            }
            return i
        })
    }

    changeMsgCode(v: string) {
        this.data!.msg_code = v
        if (!this.data!.msg_code) {
            this.msg_params = {}
            this.data!.sms_template_tip = ""
            this.buildSubscribeMsg(this.data!.sms_template_tip)
            return
        }
        this.getAction()
            .addInputs_parameter({
                msg_code: v,
            })
            .updateControlsProperties("msg_code")
            .then((r) => {
                try {
                    this.msg_params = JSON.parse(
                        (find(r.masters, {
                            property: "msg_params",
                        })?.default_value as string) || "{}"
                    )
                    this.data!.msg_params = JSON.stringify(this.msg_params)
                } catch {}

                this.data!.sms_template_tip = find(r.masters, {
                    property: "sms_template_tip",
                })?.default_value as string
                this.buildSubscribeMsg(this.data!.sms_template_tip)
            })
    }
}
