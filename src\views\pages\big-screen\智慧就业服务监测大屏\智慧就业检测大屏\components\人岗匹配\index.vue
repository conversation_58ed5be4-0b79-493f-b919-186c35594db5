<template>
    <div class="content">
        <Header :title="title"></Header>

        <div class="d-flex tabs">
            <div
                class="tab"
                v-for="(item, index) in tabs"
                :key="index"
                :class="{ active: index === curSelectedIdx }"
                @click="onCheck(index)"
            >
                {{ item }}
            </div>
        </div>

        <div v-show="curSelectedIdx === 0">
            <Items :items="items3"></Items>
            <div class="u-flex u-m-l-22 u-m-r-22">
                <Box1></Box1>
                <Box2></Box2>
            </div>
        </div>

        <div v-show="curSelectedIdx === 1">
            <Items :items="items" @toRoute="toRoute"></Items>
            <div class="u-flex u-m-l-22 u-m-r-22">
                <Canvas :data="data"></Canvas>
                <PersonMatch></PersonMatch>
            </div>
        </div>

        <div v-show="curSelectedIdx === 2">
            <Items :items="items2" @toRoute="toRoute2"></Items>
            <Tables
                :defaultDisableCanClick="true"
                :titles="titles"
                :data="tableData"
                :disabledClickItems="disabledClickItems"
                :canClickItems="canClickItems"
                @click="toList"
            ></Tables>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import Header from "../../common/header.vue"
    import Items from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/items.vue"
    import Tables from "../../common/tables.vue"
    import Canvas from "./canvas.vue"
    import PersonMatch from "./person-match.vue"
    import Box1 from "./学历分布.vue"
    import Box2 from "./就业地意愿分布.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { ChartQueryResultItem } from "@/views/pages/big-screen/model"
    import { routesMap } from "@/router/direction"

    @Component({
        components: { Box1, Box2, Header, Items, Canvas, PersonMatch, Tables },
    })
    export default class Template extends BaseItem {
        private tabs = ["高校毕业生", "人岗匹配", "返乡服务"]
        private curSelectedIdx = 0

        private onCheck(index: number) {
            this.curSelectedIdx = index
            // this.refresh()
        }

        private model = [
            {
                label: "求职者数",
                value: 0,
                isRoute: true,
            },
            {
                label: "预计招聘人数",
                value: 0,
            },
            {
                label: "人岗比",
                value: 0,
                customValue: 0,
            },
            // {
            //     label: "累计入职人次",
            //     value: 0,
            //     isRoute: true,
            // },
        ]

        private items: any[] = this.model

        private items2: any[] = [
            {
                label: "返乡总数",
                value: 380763,
                isRoute: true,
            },
            {
                label: "有留孝意愿人数",
                value: 25853,
            },
            {
                label: "留孝就创业人数",
                value: 14967,
                isRoute: true,
            },
            {
                label: "返乡服务次数",
                value: 572763,
            },
        ]

        private items3: any[] = [
            {
                label: "人群总数",
                value: 7540,
            },
            {
                label: "服务次数",
                value: 17076,
            },
            {
                label: "已落实就业意愿人数",
                value: 7052,
            },
            {
                label: "本地就业意愿人数",
                value: 40,
            },
        ]

        private titles = ["区域", "返乡总数", "有留孝意愿人数", "留孝就创业人数"]

        private tableData = [
            ["市辖区", 5504, 458, 2321],
            ["孝南区", 48036, 4320, 2490],
            ["汉川市", 35360, 4217, 2381],
            ["应城市", 70640, 2993, 1855],
            ["云梦县", 52533, 3312, 1880],
            ["安陆市", 48512, 2315, 1284],
            ["大悟县", 71079, 4901, 1597],
            ["孝昌县", 49099, 3337, 1159],
        ]

        private regionCodeMap: any = {
            市辖区: 420901,
            孝南区: 420902,
            汉川市: 420984,
            应城市: 420981,
            云梦县: 420923,
            安陆市: 420982,
            大悟县: 420922,
            孝昌县: 420921,
        }

        private canClickItems = ["返乡总数", "留孝就创业人数"]

        private title = "亮点服务"

        protected refresh() {
            this.query<ChartQueryResultItem[]>(
                `talent_count`,
                "dashboard_xg_recruit_service_data"
            ).then((r) => {
                this.items = this.getItems(r)
            })
        }

        private toList(title: string, item: any, idx: number, itemSelf: any) {
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list1,
                query: {
                    type: `${this.title}-${title}`,
                    cIndex: itemSelf + "",
                    regionCode: this.regionCodeMap[item],
                },
            })
        }

        private getItems(r: ChartQueryResultItem[]) {
            return this.formatData(this.model, r).map((i) => {
                if (i.label === "人岗比") {
                    return {
                        ...i,
                        customValue: `${i.value}:${i.percent.toFixed(1)}`,
                    }
                }
                return i
            })
        }

        private toRoute(label: string, value: number) {
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list1,
                query: {
                    type: `${this.title}-${label}`,
                    cIndex: value + "",
                },
            })
        }

        private toRoute2(label: string, value: number) {
            let suffix = ""
            if (label === "留孝就创业人数") {
                suffix = "留孝就创业人数"
            }
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list1,
                query: {
                    type: suffix || `劳动力-劳动力总数`,
                    cIndex: value + "",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 613px;
        height: 323px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;
        position: relative;

        /deep/ .table-box {
            margin-top: 10px;

            .line-box {
                height: 140px;
            }

            .line {
                .row {
                    width: 25%;
                    &:first-child {
                        width: 20%;
                    }
                }
            }
        }

        .tabs {
            position: absolute;
            right: 50px;
            top: 0px;
            display: flex;
            gap: 5px;
            margin-bottom: 5px;
            width: 300px;

            .tab {
                width: 277px;
                height: 43px;
                display: flex;
                align-items: center;
                justify-content: center;

                font-size: 16px;
                color: #9ac3ff;
                line-height: 14px;
                cursor: pointer;
                background-size: cover;
                position: relative;

                &.active {
                    color: #fdce83;

                    &::after {
                        content: "";
                        position: absolute;
                        bottom: 3px;
                        width: 30px;
                        display: block;
                        left: 50%;
                        transform: translateX(-50%);
                        height: 4px;
                        background-color: #fdce83;
                        border-radius: 2px;
                    }
                }
            }
        }
    }
</style>
