import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { desensitization, formatTime } from "@/utils/tools"
const tableFilter: TableFilter[] = [
    {
        label: "类型",
        type: FormType.Select,
        prop: "type",
    },
    {
        label: "申请状态",
        type: FormType.Select,
        prop: "status",
    },
    {
        label: "上架状态",
        type: FormType.Select,
        prop: "shelf_status",
    },
    {
        label: "创建时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "datetimerange",
        },
    },
]

export const predict = {
    type: "label",
    person_num: "",
    contact_person: "",
    contact_number: "",
    apply_description: "",
    region: "label",
    address: "",
    status: "label",
    shelf_status: "label",
    username: "create_by#systemUser.username",
    create_time: "label",
    expired_date: "label",
    address_detail: "agentRef#address_detail_label",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("share_employee_apply").list("list_for_agent"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict,
        // tabPages: ["全部"],
        oneTabFilter: true,
    }
}

export interface Row {
    id: number
    source_page_url: string
    position_id: string
    audit_status: any
    contact_person: string
    contact_mobile: string
    audit_memo: string
    recommend_desc: string
    expired_date: string
    expired_date_label: string
    address_detail: string
    address_detail_label: string
}

export const columns: TableColumn[] = [
    {
        label: "类型",
        prop: "type_label",
        showOverflowTip: true,
    },
    {
        label: "涉及人数",
        prop: "person_num",
        showOverflowTip: true,
    },
    {
        label: "所在地",
        prop: "address_detail_label",
        showOverflowTip: true,
        minWidth: "130",
    },
    {
        label: "联系人",
        prop: "contact_person",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "contact_number",
        formatter(row) {
            return desensitization(row.contact_number)
        },
        showOverflowTip: true,
    },
    {
        label: "有效期",
        prop: "expired_date_label",
        formatter: (row) => formatTime.day(row.expired_date),
        showOverflowTip: true,
    },
    {
        label: "审核状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "上架状态",
        prop: "shelf_status_label",
        showOverflowTip: true,
    },
    {
        label: "创建人",
        prop: "username",
        showOverflowTip: true,
    },
    {
        label: "创建时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
    },
]

export enum Type {
    人工富裕 = 1,
    人工短缺 = 2,
}

export enum Region {
    北京 = 110000,
    天津 = 120000,
    河北省 = 130000,
    山西省 = 140000,
    内蒙古自治区 = 150000,
    辽宁省 = 210000,
    吉林省 = 220000,
    黑龙江省 = 230000,
    上海 = 310000,
    江苏省 = 320000,
    浙江省 = 330000,
    安徽省 = 340000,
    福建省 = 350000,
    江西省 = 360000,
    山东省 = 370000,
    河南省 = 410000,
    湖北省 = 420000,
    湖南省 = 430000,
    广东省 = 440000,
    广西壮族自治区 = 450000,
    海南省 = 460000,
    重庆 = 500000,
    四川省 = 510000,
    贵州省 = 520000,
    云南省 = 530000,
    西藏自治区 = 540000,
    陕西省 = 610000,
    甘肃省 = 620000,
    青海省 = 630000,
    宁夏回族自治区 = 640000,
    新疆维吾尔自治区 = 650000,
    台湾 = 710000,
    香港特别行政区 = 810000,
    澳门特别行政区 = 820000,
    钓鱼岛 = 900000,
}

export enum Status {
    待审核 = 0,
    审核通过 = 1,
    审核不通过 = 2,
    未提交 = 3,
}

export enum ShelfStatus {
    未上架 = 0,
    已上架 = 1,
}
