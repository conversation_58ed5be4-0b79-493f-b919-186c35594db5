<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="list-container"
            :showExpand="false"
            :defaultFilterIsExpand="true"
            :outCustomTotal="outCustomTotal"
            @getData="loadingChange"
        >
            <div slot="table" slot-scope="{ data, emptyText }">
                <common-table
                    :emptyText="emptyText"
                    :data="data"
                    :columns="columns"
                >
                    <div slot="extend_name" slot-scope="scope">
                        <span>{{
                            desensitizationName(scope.row.extend_name)
                        }}</span>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { desensitization } from "@/utils/tools"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"

    import { renDesensitizationView } from "@/views/components/common-comps"
    import { FormType } from "@/core-ui/component/form"

    const serveTargetType = 1

    const enum Status {
        待提交 = -1,
        已提交审核 = 0,
        通过 = 1,
        不通过 = 2,
    }

    interface Row {
        /** 处理人 */
        real_name: number

        /** 申报时间 */
        create_time: string
        create_time_label: string

        /** 联系方式 */
        contact_mobile: string

        /** 处理时间 */
        dealer_time: number
        dealer_time_label: number

        /** 状态 */
        status: Status

        /** 状态[文本] */
        status_label: string
        _access_key: string
        profile_access_key: string
        agent_access_key: string
        id: number
        v: number
    }

    export const predict2 = {
        real_name: "profile_v2#name",
        create_time: "label",
        contact_mobile: "",
        dealer_time: "label",
        status: "label",
        profile_access_key: "profile_v2#_access_key",
        handle_org: "",
        handle_region: "handle_region#region_name",
        showMobile: false,
        handle_region_region_name: "handle_region#region_name",
        extend_id_card: "extend#id_card",
        extend_name: "extend#name",
        extend_contact_mobile: "extend#contact_mobile",
        extend_nation: "extend#nation_label",
        extend_reg_residence_property: "extend#reg_residence_property_label",
        extend_education: "extend#education_label",
        extend_graduation_school: "extend#graduation_school_label",
        extend_graduation_time: "extend#graduation_time_label",
        extend_employment_time: "extend#employment_time_label",
        extend_is_disabled: "extend#is_disabled_label",
        extend_employment_category: "extend#employment_category_label",
        extend_employment_unit: "extend#employment_unit_label",
        extend_reg_region_code: "extend#reg_region_code_label",
        extend_permanent_region_code: "extend#permanent_region_code_label",
        profile_v2_access_key: "profile_v2#_access_key",
    }

    const tableConfig = (form_id: string, type: number): TableConfig => ({
        model: sdk.core
            .model("policy_form_apply")
            .list(
                type === serveTargetType
                    ? "apply_profile_for_policy"
                    : "apply_company_for_policy"
            ),
        preFilter: { form_id },
        defaultPageSize: 10,
        predict: predict2,
        // oneTabFilter: true,
        tabPages: ["离线申报", "线上申报"],
    })

    @Component({ components: { TableContainer, CommonTable } })
    export default class Template extends Mixins(BaseTableController, BaseItem) {
        @Prop({ default: 10 })
        private cusPageSize!: number

        @Prop({ default: false })
        private is_verified!: boolean

        protected pageSizes = [10]

        private id = ""

        private outCustomTotal = 0

        tableConfig: TableConfig | null = null

        private pageData: { item_index: number; item_size: number } | any = {}

        private desensitization = desensitization

        private loading = true

        created() {
            this.pageSizes = [this.cusPageSize]
        }

        private loadingChange() {
            this.loading = false
            this.setTotal()
        }

        private get emptyText() {
            return this.loading ? "加载中..." : "暂无数据"
        }

        public setTotal() {
            if (this.$route.query?.cIndex) {
                this.outCustomTotal = (this.$route.query?.cIndex as any) * 1
            }
        }

        private get tableFilter(): any {
            return []
        }

        private desensitizationName(name: string) {
            if (!name || typeof name !== "string") {
                return ""
            }

            if (name.trim() === "") {
                return ""
            }

            const familyName = name.trim().charAt(0)

            const stars = "*".repeat(name.trim().length - 1)

            return familyName + stars
        }

        private columns: any[] = [
            { label: "所属区域", prop: "handle_region_region_name" },
            { label: "姓名", prop: "extend_name" },
            {
                label: "身份证号",
                prop: "extend_id_card",
                width: "160px",
                formatter(row: any) {
                    return desensitization(row.extend_id_card, 5)
                },
            },
            {
                label: "联系电话",
                prop: "extend_contact_mobile",
                width: "120px",
                formatter(row: any) {
                    return desensitization(row.extend_contact_mobile, 3)
                },
            },
            { label: "民族", prop: "extend_nation_label" },
            { label: "户口性质", prop: "extend_reg_residence_property_label" },
            { label: "文化程度", prop: "extend_education_label" },
            { label: "毕业院校", prop: "extend_graduation_school" },
            { label: "毕业时间", prop: "extend_graduation_time_label" },
            { label: "就业形式", prop: "extend_employment_category_label" },
            { label: "就业单位", prop: "extend_employment_unit_label" },
            { label: "就业时间", prop: "extend_employment_time_label" },
            { label: "残疾人标识", prop: "extend_is_disabled_label" },
            // { label: "户口所在地", prop: "extend_reg_region_code" },
            // { label: "常住地区划", prop: "extend_permanent_region_code_label" },
            // { label: "操作", prop: "h1" },
        ].map((e) => ({ ...e, showOverflowTip: true }))

        mounted() {
            this.init()
        }

        private init() {
            const isDev = process.env.VUE_APP_ENV === "test"
            this.id = isDev
                ? "9e55588790f969ddea10b896290ce5ac8564dbc7a791e3e9cbf080ebc5e96254"
                : "66fde1680057ffee1194f7ef222f59217727924339d54fb5f720672ef56d2717"
            this.tableConfig = {
                model: sdk.core.model("policy_form_apply").list(
                    // true
                    //     ? "apply_profile_for_policy"
                    //     : "apply_company_for_policy"
                    "apply_profile_for_policy"
                ),
                preFilter: { form_id: this.id },
                defaultPageSize: 10,
                predict: predict2,
                filter: [
                    {
                        label: "所属区域",
                        prop: "region_code",
                        type: FormType.Cascader,
                        option: {
                            elProps: {
                                checkStrictly: true,
                                // lazy: true,
                                // lazyLoad: codeLazyLoad,
                            } as any,
                            filterable: true,
                        },
                        // sourceInputsParameter: buildSelectSource([]),
                    },
                ],
                handleFilterData: (params) => {
                    const res: any = {
                        ...params,
                    }
                    if (!res.region_code) {
                        res.region_code = "420901"
                    }
                    return res
                },
                // oneTabFilter: true,
                tabPages: ["离线申报", "线上申报"],
            }
        }

        toExtendPerson(row: any) {
            if (!row.profile_v2_access_key) {
                return
            }
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.personDetail,
                query: {
                    id: row.profile_v2_access_key + "",
                    from: this.$route.name,
                    cIndex: this.$route.query?.cIndex,
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "./table.less";
    /deep/ .table {
        background: transparent !important;
    }
    /deep/ .table-tabs {
        display: none !important;
    }
</style>
