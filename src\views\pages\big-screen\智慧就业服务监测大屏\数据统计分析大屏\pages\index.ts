export const detailPredict = {
    create_time: "",
    update_time: "",
    name: "",
    sex: "",
    age: "",
    region_name: "permanent_village#region_name",
    education: "label",
    skill_registration: "",
    rehome_province: "rehome_province#region_name",
    rehome_city: "rehome_city#region_name",
    rehome_area: "rehome_area#region_name",
    return_date: "",
    before_rehome_time: "",
    rehome_industry: "",
    job_of_return_way: "",
    rehome_income: "",
    return_reason: "",
    training_or_service_needs: "",
    training_intention: "",
    after_is_employed: "",
    is_willing_to_job: "",
    stay_home: "",
    migrant_employment_intention_province: "",
    migrant_employment_intention_city: "",
    intended_job_type: "",
    is_send_sms: "",
    mobile: "",
    id_card: "",
    employment_and_entrepreneurship_intention: "",
    expect_salary: "",
    graduate_school: "",
    graduate_time: "",
    major: "",
    nation: "",
    registered_nature: "label",
    return_to_hometown_situation: "",
    skill_detail: "",
    skill_detail_fill_in: "",
    skill_level: "",
    types_of_returnees: "",
    household_province: "household_province#region_name",
    household_city: "household_city#region_name",
    household_area: "household_area#region_name",
    household_countryside: "household_countryside#region_name",
    household_village: "household_village#region_name",
    permanent_province: "permanent_province#region_name",
    permanent_city: "permanent_city#region_name",
    permanent_area: "permanent_area#region_name",
    permanent_countryside: "permanent_countryside#region_name",
    permanent_village: "permanent_village#region_name",
    broker_name: "",
}

export interface DetailRow {
    /** 创建时间 */
    create_time: string

    /** 更新时间 */
    update_time: string

    /** 姓名 */
    name: string

    /** 性别 */
    sex: number

    /** 年龄 */
    age: string

    /** 区域名称 */
    region_name: string

    /** 学历 */
    education: string

    /** 技能登记 */
    skill_registration: string

    /** 返乡前的工作地-省 */
    rehome_province: string

    /** 返乡前的工作地-市 */
    rehome_city: string

    /** 返乡前的工作地-区 */
    rehome_area: number

    /** 返乡时间 */
    return_date: string

    /** 外出工作累计总时 */
    before_rehome_time: string

    /** 返乡前最近从事的工作 */
    rehome_industry: string

    /** 返乡前最近从事的工作就业方式 */
    job_of_return_way: string

    /** 返乡前最近从事的工作的月收入 */
    rehome_income: string

    /** 返乡的原因 */
    return_reason: string

    /** 近期是否有技能培训或提升的需求 */
    training_or_service_needs: string

    /** 培训意向 */
    training_intention: number

    /** 是否已经就业 */
    after_is_employed: string

    /** 近期是否有找工作的需求 */
    is_willing_to_job: string

    /** 是否有留在家乡就业的意愿 */
    stay_home: string

    /** 意向务工地-省 */
    migrant_employment_intention_province: number

    /** 意向务工地-市 */
    migrant_employment_intention_city: number

    /** 意向工作类型 */
    intended_job_type: number

    /** 是否发送短信 */
    is_send_sms: number

    /** 手机号 */
    mobile: string

    /** 身份证 */
    id_card: string

    /** 就业创业意愿 */
    employment_and_entrepreneurship_intention: number

    /** 返乡人员期望工资 */
    expect_salary: number

    /** 毕业学校 */
    graduate_school: number

    /** 毕业时间 */
    graduate_time: number

    /** 所学专业 */
    major: number

    /** 民族 */
    nation: number

    /** 户口性质 */
    registered_nature: number

    /** 返乡人员在本地就业创业情况 */
    return_to_hometown_situation: number

    /** 返乡人员技能特长情况 */
    skill_detail: number

    /** 返乡人员技能特长情况-选其他的时候填写的内容 */
    skill_detail_fill_in: number

    /** 返乡人员技能等级 */
    skill_level: number

    /** 返乡人员类型 */
    types_of_returnees: number
    /** 包保人 */
    broker_name: string
    id: number
    v: number
    profile_labels: string
}
