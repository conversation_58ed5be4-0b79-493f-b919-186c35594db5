<template>
    <div class="core-ui-table-container" :key="refreshQueryParams" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex" v-if="row">
                <el-button
                    v-if="show"
                    type="primary"
                    @click="finishRecommend"
                    plain
                    v-role="
                        'model.xg_company_position_recommend.action.end_recommend'
                    "
                >
                    结束推广
                </el-button>
            </div>
        </div>
        <DetailView
            :canEdit="canEdit"
            v-if="row"
            :row="row"
            @refresh="init()"
        />
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { MessageBoxConfirm } from "@/views/components/common-pop"
    import { pageLoading } from "@/views/controller"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"
    import { canEnd, canEdit, ModelConfig, modelTitle, predict, Row } from "."
    import DetailView from "./components/detail-view.vue"

    @Component({
        name: routesMap.baseService.policyDetail,
        components: { DetailView },
    })
    export default class GridDetail extends Vue {
        private row: Row | null = null
        private id = ""
        show = false
        private canEdit = false
        refreshConfig = {
            fun: this.init,
            name: routesMap.baseService.policyDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: modelTitle,
                    to: {
                        name: routesMap.baseService.policy,
                    },
                },
            ]
            d = [
                ...d,
                {
                    label: modelTitle + "详情",
                    to: {
                        name: routesMap.baseService.policyDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.gridDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model(ModelConfig.model)
                    .detail(this.id, ModelConfig.detail)
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow<Row>(r.row, predict)
                        this.show = canEnd(this.row.status_memo)
                        this.canEdit = canEdit(this.row.status_memo)
                    })
            })
        }

        private finishRecommend() {
            MessageBoxConfirm({
                content: "是否要结束推广？",
                fun: () => {
                    return sdk.core
                        .model(ModelConfig.model)
                        .action(ModelConfig.action1)
                        .updateInitialParams({
                            selected_list: [{ id: this.row!._access_key, v: 0 }],
                        })
                        .execute()
                },
            }).then(() => {
                this.init()
                this.callRefresh(routesMap.baseService.policy)
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
</style>
