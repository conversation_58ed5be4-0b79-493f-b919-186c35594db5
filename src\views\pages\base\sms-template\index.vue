<template>
    <div>
        <table-container
            v-if="tableConfig"
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
        >
            <div slot="title" class="d-flex-item-center bold">短信模板</div>

            <div slot="header-right">
                <el-button type="primary" @click="toSend" plain>
                    发送记录查询
                </el-button>
                <el-button
                    v-role="'model.sms_template.action.insert'"
                    type="primary"
                    @click="edit('')"
                    plain
                >
                    添加模板
                </el-button>
                <el-button
                    v-role="
                        'model.sms_send_history.action.insert_directional_sms'
                    "
                    slot="header-right"
                    type="primary"
                    @click="add"
                >
                    发送短信
                </el-button>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20">
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <el-button
                            type="text"
                            @click="edit(scope.row)"
                            v-role="'model.sms_template.action.update'"
                            >编辑</el-button
                        >
                        <!-- <el-button type="text" @click="del(scope.row)"
                            >删除</el-button
                        > -->
                    </div>
                    <div slot="format_params" slot-scope="scope">
                        <el-popover placement="top-end" trigger="hover">
                            <div
                                v-for="(c, index) in handlerContent(
                                    scope.row.format_params
                                )"
                                :key="c + index"
                            >
                                {{ c }}
                            </div>
                            <div class="u-line-1" slot="reference">
                                {{ scope.row.format_params }}
                            </div>
                        </el-popover>
                    </div>
                    <div slot="content" slot-scope="scope">
                        <el-popover placement="top" trigger="hover" width="200">
                            <div>
                                {{ scope.row.content }}
                            </div>
                            <div class="u-line-1" slot="reference">
                                {{ scope.row.content }}
                            </div>
                        </el-popover>
                    </div>
                    <div slot="preview_params" slot-scope="scope">
                        <el-popover placement="top" trigger="hover" width="200">
                            <div>
                                {{ scope.row.preview_params }}
                            </div>
                            <div class="u-line-1" slot="reference">
                                {{ scope.row.preview_params }}
                            </div>
                        </el-popover>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { Component } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "."
    import { cacheKey } from "../sms"

    @Component({
        name: routesMap.base.smsTemplate.list,
        components: { TableContainer, CommonTable },
    })
    export default class SMSTemplate extends BaseTableController<Row> {
        tableConfig: TableConfig | null = tableConfig()
        private readonly columns: TableColumn[] = columns

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.base.smsTemplate.list,
        }

        edit(row: Row) {
            this.callRefresh(routesMap.base.smsTemplate.add)
            this.$router.push({
                name: routesMap.base.smsTemplate.add,
                query: row ? { id: row.id + "" } : undefined,
            })
        }

        private toSend() {
            this.$router.push({
                name: routesMap.base.smsTemplate.send,
            })
        }

        del(row: Row) {
            MessageBox.confirm("是否删除", "删除").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("sms_template")
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .execute()
                        .then(() => {
                            this.refreshList()
                        })
                })
            })
        }

        private handlerContent(v: string) {
            return (v || "").split("</br>").filter(Boolean)
        }

        add() {
            sessionStorage.removeItem(cacheKey)
            this.$router.push({
                name: routesMap.base.sms.add,
                query: { from: routesMap.base.smsTemplate.list },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
