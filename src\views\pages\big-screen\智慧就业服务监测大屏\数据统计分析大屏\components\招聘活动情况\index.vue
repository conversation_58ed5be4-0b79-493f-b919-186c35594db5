<template>
    <Card label="招聘活动情况">
        <Items :items="items"></Items>
    </Card>
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import Card from "../../common/card.vue"
    import Items from "../../common/items.vue"

    @Component({ components: { Card, Items } })
    export default class Template extends Vue {
        @Prop({ default: {} })
        private info!: any

        @Watch("info", { immediate: true })
        private onInfoChange() {
            if (!this.info) {
                return
            }
            this.items = this.buildInfo()
        }

        private items = this.buildInfo()

        private buildInfo() {
            return [
                {
                    label: "招聘会总场次",
                    value: this.info["招聘会总场次"] || 0,
                    unit: "场",
                },
                {
                    label: "带岗直播场次",
                    value: this.info["带岗直播场次"] || 0,
                    unit: "场",
                },
            ]
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 909px;
        height: 139px;
    }
</style>
