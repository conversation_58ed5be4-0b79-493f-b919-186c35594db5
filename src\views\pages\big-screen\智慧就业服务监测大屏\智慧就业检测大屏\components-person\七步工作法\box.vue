<template>
    <div class="card u-flex u-row-center">
        <div :id="id" class="chart u-flex">
            <div
                class="item"
                v-for="(item, index) in list"
                :key="item.label + index"
            >
                <div class="u-flex u-col-center">
                    <div class="u-flex-col u-col-center">
                        <div
                            class="item-box u-flex-col u-col-center u-row-around"
                        >
                            <Item
                                label="服务次数"
                                :value="item.value1"
                                :digitHeight="20"
                                :digitWidth="12"
                            ></Item>
                        </div>
                        <div class="label">{{ item.label }}</div>
                    </div>
                    <div class="arrow-right" v-if="index < list.length - 1">
                        <i class="el-icon-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop } from "vue-property-decorator"
    import ScrollNumber from "../../../../common/number-scroll/scroll-number.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import Item from "../../common/item.vue"
    import { getRandom } from "../../common/tools"

    @Component({ components: { ScrollNumber, Item } })
    export default class Index extends BaseItem {
        private readonly id = `id-${Math.random()}`

        private get list() {
            return [
                {
                    label: "短信邀约注册",
                    value1: this.d?.短信邀约注册 || 0,
                },
                {
                    label: "电话邀约注册",
                    value1: this.d?.电话邀约注册 || 0,
                },
                {
                    label: "就业政策宣传",
                    value1: this.d?.就业政策宣传 || 0,
                },
                {
                    label: "职业指导规划",
                    value1: Math.ceil((this.d?.职业指导规划 || 0) * 0.05),
                },
                {
                    label: "岗位推送",
                    value1: this.d?.岗位推送 || 0,
                },
                {
                    label: "培训/见习推荐",
                    value1: (this.d ? this.d["培训/见习推荐"] : 0) || 0,
                },
                {
                    label: "创业政策推广",
                    value1: this.d?.创业政策推广 || 0,
                },
            ]
        }

        private d: any = {}

        protected refresh() {
            this.d = this.summaryData?.七步工作法
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .chart {
        .item {
            .item-box {
                width: 84px;
                height: 77px;
                background-image: url("../../../assets/智慧就业监测//item-box.png");
                background-repeat: no-repeat;
                background-size: 68px 100%;
                background-position: center;
            }
            /deep/.item-content {
                border: none;
                & > div {
                    &:first-child {
                        font-size: 18px;
                    }
                    &:last-child {
                        font-size: 12px;
                    }
                }
            }
            .arrow-right {
                margin: 0 30px 20px;
                .el-icon-right {
                    color: #428dfb;
                    font-size: 16px;
                    font-weight: bold;
                }
            }
            .label {
                font-size: 14px;
                color: #89b9ff;
                line-height: 16px;
                text-align: center;
                margin-top: 8px;
            }
        }
        .text1 {
            text-align: center;
            color: #89b9ff;
            font-size: 14px;
            line-height: 16px;
            font-weight: 500;
            white-space: nowrap;
        }
    }
</style>
