import { BuildFormConfig, FormType } from "@/core-ui/component/form"
export function getAdminForm(): BuildFormConfig {
    return {
        sdkModel: "xg_agent_manager",
        sdkAction: "add_agent_manager",
        forms: [
            {
                label: "姓名",
                type: FormType.Text,
                prop: "name",
            },
            {
                label: "角色",
                type: FormType.Select,
                prop: "type",
            },
            {
                label: "登录手机号",
                type: FormType.Text,
                prop: "mobile",
            },
        ],
    }
}

export function getEditForm(id: string): BuildFormConfig {
    return {
        sdkModel: "xg_agent_manager",
        sdkAction: "update",
        id: +id,
        forms: [
            {
                label: "姓名",
                type: FormType.Text,
                prop: "name",
            },
            {
                label: "角色",
                type: FormType.Select,
                prop: "type",
            },
        ],
    }
}
