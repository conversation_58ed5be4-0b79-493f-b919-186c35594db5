<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="700px"
        top="8vh"
    >
        <div class="u-p-x-20 content">
            <div class="w-100 u-flex u-row-right u-m-b-20">
                <el-button type="primary" @click="toAdd"> 添加入口 </el-button>
            </div>
            <!-- <div class="placeholder" v-show="loading" v-loading="true"></div> -->
            <el-table :data="rows" stripe>
                <el-table-column
                    v-for="(item, index) in columns"
                    :key="`c-${index}`"
                    :label="item.label"
                    align="center"
                    :prop="item.prop"
                    :width="item.width"
                    class-name="text-truncate"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            v-if="item.prop === 'pageNameLabel'"
                            @click="nav(scope.row)"
                        >
                            {{ scope.row[item.prop] }}
                        </el-button>
                        <template v-else-if="item.prop === 'h'">
                            <el-button type="text" @click="toEdit(scope.row)">
                                编辑
                            </el-button>
                            <el-button
                                :loading="btnLoading"
                                type="text"
                                @click="toDelete(scope.row)"
                            >
                                删除
                            </el-button>
                        </template>
                        <span v-else>{{ scope.row[item.prop] }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <AddEntrancePagePop
            v-model="showAddPop"
            :row="curRow"
            @onSuccess="onSuccess"
        />
    </el-dialog>
</template>

<script lang='ts'>
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Component, Prop } from "vue-property-decorator"
    import { toItemPage } from "../.."
    import {
        entranceMenuController,
        EntranceMenuItem,
    } from "../../entrance-menu-controller-remote"
    import AddEntrancePagePop from "./add-entrance-page-pop.vue"
    import { cloneDeep } from "lodash"

    @Component({ components: { AddEntrancePagePop } })
    export default class EditEntrancePop extends DialogController {
        @Prop()
        private menuList!: EntranceMenuItem[]

        // private rows: any[] = [
        //     {
        //         id: 1,
        //         index: 1,
        //         name: "居民管理",
        //         pageName: "居民信息查看",
        //         sort: 1,
        //     },
        // ]
        private curRow: EntranceMenuItem | null = null

        private readonly columns = [
            { label: "序号", prop: "index", width: 50 },
            { label: "入口名称", prop: "name", showOverflowTip: true },
            {
                label: "跳转页面",
                prop: "pageNameLabel",
                showOverflowTip: true,
            },
            { label: "排序值", prop: "sort", width: 70 },
            { label: "操作", prop: "h", width: 100 },
        ]

        private showAddPop = false

        private btnLoading = false

        private get title() {
            return "编辑快捷入口"
        }

        private get rows() {
            console.log("this.menuList")
            console.log(this.menuList)
            const sortedArray = cloneDeep(this.menuList)

            sortedArray.sort((a, b) => {
                if (a.sort > b.sort) return -1
                if (a.sort < b.sort) return 1
                return 0
            })

            return sortedArray.map((item, index) => ({
                ...item,
                pageNameLabel: item?.meta?.meta?.title || "",
                index: index + 1,
            }))
        }

        onOpen() {
            this.init()
        }

        onClosing() {}

        private init() {
            this.loading = true
        }

        private onSuccess() {
            this.$emit("onSuccess")
        }

        private toAdd() {
            if (this.rows?.length === 6) {
                return this.$message.warning("最多仅支持设置6个快捷入口！")
            }
            this.curRow = null
            this.showAddPop = true
        }

        private toEdit(row: EntranceMenuItem) {
            this.curRow = row
            this.showAddPop = true
        }

        private toDelete(row: EntranceMenuItem) {
            this.btnLoading = true
            entranceMenuController
                .deleteEntry(row.id)
                .then(() => {
                    this.onSuccess()
                })
                .finally(() => {
                    this.btnLoading = false
                })
        }

        private nav(item: EntranceMenuItem) {
            if (!item.meta.children?.length) {
                this.$message.error("您无此模块权限，请联系管理员开通")
            }
            const c = item.meta.children![0]
            toItemPage(c)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
