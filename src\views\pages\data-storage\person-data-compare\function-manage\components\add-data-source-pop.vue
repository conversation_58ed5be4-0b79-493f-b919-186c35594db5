<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 content">
            <div class="placeholder" v-show="loading" v-loading="true"></div>
            <form-builder
                ref="formBuilder"
                labelWidth="100px"
                v-show="!loading"
            ></form-builder>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="submitValidate"
                    class="custom-btn btn u-m-0"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FileType,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { Action } from "uniplat-sdk"
    import { routesMap } from "@/router/direction"

    @Component({ components: { FormBuilder } })
    export default class AddDataSourcePop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: "" })
        private readonly id!: number

        @Prop({ default: "" })
        private readonly compareId!: number

        @Prop({ default: "" })
        private readonly rowId!: number

        private action?: Action

        private get title() {
            return "增加比对数据源"
        }

        private get modelName() {
            return this.rowId ? "data_comparison_export_data" : "data_comparison"
        }

        private get actionName() {
            return this.rowId ? "update_export_data" : "add_db_column"
        }

        onOpen() {
            this.init()
        }

        onClosing() {
            this.resetFormFields()
        }

        private getAction() {
            return (this.action = sdk.core
                .model(this.modelName)
                .action(this.actionName)
                .updateInitialParams({
                    selected_list: [{ v: 0, id: this.rowId || this.id }],
                    prefilters: this.compareId
                        ? [
                              {
                                  property: "data_comparison_id",
                                  value: this.compareId,
                              },
                              {
                                  property: "is_del",
                                  value: 0,
                              },
                          ]
                        : [],
                }))
        }

        private init() {
            this.loading = true
            return buildFormSections({
                action: this.getAction(),
                forms: [],
            }).then((r) => {
                this.buildFormFull(r)
                this.loading = false
            })
        }

        private submitValidate() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                    })
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model(this.modelName)
                    .action(this.actionName)
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: this.rowId || this.id }],
                        prefilters: this.compareId
                            ? [
                                  {
                                      property: "data_comparison_id",
                                      value: this.compareId,
                                  },
                                  {
                                      property: "is_del",
                                      value: 0,
                                  },
                              ]
                            : [],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success(this.rowId ? "编辑成功" : "新建成功")
                        this.callRefresh(
                            routesMap.dataStorage.personDataCompareDetail
                        )
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
