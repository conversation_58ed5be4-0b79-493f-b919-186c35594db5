import {
    BuildFormConfig,
    buildSelectSource,
    FileType,
    FormType,
} from "@/core-ui/component/form"
import { TableFilter } from "@/core-ui/component/table"
import { buildBaiduFormItem } from "@/plugins/baidu-map-selector"
import { get } from "lodash"
import { ListTypes } from "uniplat-sdk"

const enum Type {
    培训机构 = 1,
    零工驿站 = 2,
    孵化基地 = 3,
}

export const enum Status {
    未上架 = 0,
    已上架 = 1,
}

export interface Row {
    /** 网点名称 */
    name: string

    /** 网点类型 */
    type: Type

    /** 网点类型[文本] */
    type_label: string

    /** 区域名称 */
    region_name: string

    /** 详细地址 */
    address_detail: string

    /** 联系人名称 */
    contact_person: string

    /** 手机号 */
    mobile_hide: string

    /** 创建时间 */
    create_time: string

    /** 更新时间 */
    update_time: string

    /** 状态 */
    status: Status

    /** 状态[文本] */
    status_label: string
    id: number
    v: number
}

export const detailPredict = {
    name: "",
    type: "label",
    region_name: "area#region_name",
    address_detail: "",
    province: "province#region_name",
    city: "city#region_name",
    area: "area#region_name",
    contact_person: "",
    mobile: "",
    mobile_hide: "",
    create_time: "",
    update_time: "",
    status: "label",
    remark: "",
    image: "",
}

export interface DetailRow {
    name: string
    type: Type
    type_label: string
    region_name: string
    address_detail: string
    contact_person: string
    mobile: string
    mobile_hide: string
    create_time: string
    update_time: string
    status: Status
    status_label: string
    remark: string
    image: string
    id: number
    v: number
}

export const tableFilter: TableFilter[] = [
    {
        label: "网点名称",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "网点类型",
        type: FormType.Select,
        prop: "type",
    },
    {
        label: "所在区域",
        type: FormType.Cascader,
        prop: "province_code",
        option: {
            elProps: { checkStrictly: true },
            filterable: true,
        },
    },
    {
        label: "联系信息",
        type: FormType.Text,
        prop: "mobile",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "上架状态",
        type: FormType.Select,
        prop: "status",
        sourceInputsParameter: buildSelectSource([
            {
                key: "",
                value: "全部",
            },
            {
                key: "0",
                value: "未上架",
            },
            {
                key: "1",
                value: "已上架",
            },
        ]),
    },
    {
        label: "创建时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "datetimerange",
        },
    },
]

const col = {
    span: 16,
    offset: 3,
}

export const forms: BuildFormConfig = {
    forms: [
        {
            label: "网点类型",
            type: FormType.Select,
            prop: "type",
            col: col,
            required: true,
        },
        {
            label: "网点名称",
            type: FormType.Text,
            prop: "name",
            option: {
                maxlength: 30,
                showWordLimit: true,
            },
            col: col,
            required: true,
        },
        {
            label: "联系人",
            type: FormType.Text,
            prop: "contact_person",
            col: col,
        },
        {
            label: "联系电话",
            type: FormType.Text,
            prop: "mobile",
            col: col,
            required: true,
        },
        {
            prop: "address_detail",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        {
            prop: "lat",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        {
            prop: "lng",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        {
            label: "",
            type: FormType.Cascader,
            prop: "region_code",
            option: { elProps: { checkStrictly: true } },
            required: true,
            hide: true,
            col: { span: 0 },
            needListen: true,
        },
        buildBaiduFormItem({
            label: "详细地址",
            prop: "addressEditFiled",
            col: col,
            options: {
                placeholder: "请选择详细地址",
            },
            required: true,
            rules: [
                {
                    validator: (_, value, callback) => {
                        if (!get(value, "address_detail")) {
                            callback(new Error("未设置详细地址"))
                            return
                        }
                        if (!get(value, "lng") || !get(value, "lat")) {
                            callback(
                                new Error("地址经纬度信息缺失，请重新选择地址")
                            )
                            return
                        }
                        callback()
                    },
                },
            ],
        }),
        {
            label: "网点介绍",
            type: FormType.Text,
            prop: "remark",
            option: {
                type: "textarea",
                rows: 6,
                maxlength: 100,
                showWordLimit: true,
            },
            col: col,
        },
        {
            label: "图片",
            type: FormType.MyUpload,
            prop: "image",
            col: col,
            option: {
                fileType: [FileType.Image],
                drag: true,
                limit: 20,
                placeholder: "支持上传jpg、jpeg、png图片格式",
            },
        },
    ],
}
