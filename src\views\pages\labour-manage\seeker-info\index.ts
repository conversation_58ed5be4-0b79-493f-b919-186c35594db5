import { config, EnvProject } from "@/config"
import { FormType, buildSelectSource } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { forEach } from "lodash"
import moment from "moment"
import { ListTypes } from "uniplat-sdk"
import { ListRow } from "./model"
const tableFilter: TableFilter[] = [
    {
        label: "用户信息",
        option: { placeholder: "请输入姓名/身份证/手机号" },
        type: FormType.Text,
        prop: "user_info",
        keyValueFilter: {
            match: ListTypes.filterMatchType.exact,
        },
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "男",
            },
            {
                key: "2",
                value: "女",
            },
        ]),
    },
    {
        label: "户籍地",
        type: FormType.Cascader,
        prop: "household_code",
        useOtherProp: "household_province_code",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "是否注册",
        type: FormType.Select,
        prop: "is_register",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "已注册",
            },
            {
                key: "0",
                value: "未注册",
            },
        ]),
    },
    {
        label: "就业状态",
        type: FormType.Select,
        prop: "employment_status",
        option: {
            multiple: true,
        },
    },
    {
        label: "是否农村劳动力",
        type: FormType.Select,
        prop: "is_residence_property",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "文化程度",
        type: FormType.Select,
        prop: "education",
        option: {
            multiple: true,
        },
    },
    {
        label: "年龄段",
        type: FormType.TextRange,
        prop: "birth_date",
        option: {
            type: "number",
        },
    },
    {
        label: "是否有就业意愿",
        type: FormType.Select,
        prop: "is_job_willing",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "是否有培训意愿",
        type: FormType.Select,
        prop: "is_training_willingness",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "意向工种",
        type: FormType.Select,
        prop: "job_willing_type_work",
        option: {
            multiple: true,
        },
    },
    {
        label: "就业意向行业",
        type: FormType.Select,
        prop: "job_willing_industry",
        option: {
            multiple: true,
        },
    },
    {
        label: "期望工作地",
        type: FormType.Cascader,
        prop: "job_willing_work_code",
        useOtherProp: "job_willing_province_id",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "当前工作行业",
        prop: "job_industry",
        type: FormType.Select,
        option: {
            multiple: true,
        },
    },
    {
        label: "当前就业工种",
        prop: "job_type_work",
        type: FormType.Select,
        option: {
            multiple: true,
        },
    },
    {
        label: "简历更新时间",
        prop: "update_time",
        type: FormType.DatePicker,
        option: {
            type: "datetimerange",
        },
    },
    {
        label: "人群标签",
        type: FormType.Cascader,
        prop: "labels",
        useOtherProp: "label_nos",
        hide: config.envProject === EnvProject.黄州项目,
        option: {
            filterable: true,
            collapseTags: true,
            elProps: { multiple: false, checkStrictly: true },
        },
    },
]

export const seekerInfoPredict = {
    unionIds: "",
    name: "",
    name_hide: "name_hide",
    sex: "label",
    getAge: "",
    id_card: "id_card_openid#user_profile_basic_joint.id_card",
    id_card_hide: "",
    mobile: "",
    mobile_hide: "",
    uniplat_uid_calc: "label",
    uniplat_uid: "",
    black_id: "black#id",
    black_is_del: "black#is_del_label",
    blacktype: "black#type_label",
    black_memo: "black#memo",
    black_update_time: "black#update_time_label",
    black_update_by: "black#update_by",
    black_creator_real_name: "black#creator#real_name",
    residence_property: "basic_info#residence_property_label",
    // residence_property: "residence_property_label",
    // education: "basic_info#education_label",
    education: "basic_info#education_label",
    update_time: "update_time_label",
    household: "basic_info#household_address_full_name",
    id_card_openid: "",

    // 常住地址
    permanent_province: "basic_info#permanent_province#region_name",
    permanent_city: "basic_info#permanent_city#region_name",
    permanent_area: "basic_info#permanent_area#region_name",
    permanent_countryside: "basic_info#permanent_countryside#region_name",
    permanent_village: "basic_info#permanent_village#region_name",
    permanent_detail: "basic_info#permanent_detail",

    // 户籍地址

    household_province: "basic_info#household_province#region_name",
    household_city: "basic_info#household_city#region_name",
    household_area: "basic_info#household_area#region_name",
    household_countryside: "basic_info#household_area#region_name",
    household_village: "basic_info#household_village#region_name",

    household_detail: "basic_info#permanent_detail",
    resume: "basic_info#file_url_label",
    is_start_job: "user_profile_current_job_info#is_start_job_label",
    nation: "basic_info#nation",
    reg_residence_property: "basic_info#reg_residence_property_label",
    political_outlook: "basic_info#political_outlook_label",
    graduate_date: "basic_info#graduate_date_label",
    graduate_school: "basic_info#graduate_school",
    study_speciality: "basic_info#study_speciality",

    employment_status:
        "id_card_openid#user_profile_current_job_info.employment_status_label",
    employment_type:
        "id_card_openid#user_profile_current_job_info.employment_type_label",
    work_city: "id_card_openid#user_profile_current_job_info.work_city_label",
    company_name: "id_card_openid#user_profile_current_job_info.company_name",
    // job_type_work: "id_card_openid#user_profile_current_job_info.job_type_label",
    salary: "id_card_openid#user_profile_current_job_info.salary_label",
    // job_industry: "user_profile_current_job_info#start_job_industry_label",
    // id_card_openid#user_profile_current_job_info.job_industry
    // job_willingness#job_willing_industry  星系
    job_industry:
        "id_card_openid#user_profile_current_job_info.job_industry_label",
    job_willing_industry: "job_willingness#job_willing_industry_label",
    job_willing_type_work: "job_willingness#job_willing_type_work_label",
    job_willing_type_work_display:
        "job_willingness#job_willing_type_work_display",
    start_job_industry:
        "user_profile_current_job_info#start_job_industry_label",
    start_job_type_work:
        "user_profile_current_job_info#start_job_type_work_display_label",
    undefined_person_remark:
        "user_profile_current_job_info#undefined_person_remark_label",
    job_type_work: "user_profile_current_job_info#job_type_work_label",
    is_jobwilling: "job_willingness#is_job_willing_label",
    jobwilling_province: "job_willingness#job_province#region_name_label",
    jobwilling_city: "job_willingness#job_city#region_name_label",
    jobwilling_region: "job_willingness#job_county#region_name_label",

    jobwilling_salary: "job_willingness#job_willing_salary_label",
    jobWilling_type: "job_willing_type_label",
    jobWilling_type_work: "job_willing_type_work_label",
    is_train_job: "profile_current_train_info#is_train_job_label",
    is_training_willingess:
        "profile_current_train_info#is_training_willingness_label",
    level: "profile_current_train_info#qualification_level_label",
    speciality_detail:
        "profile_current_train_info#train_speciality_detail_label",
    train_speciality: "profile_current_train_info#train_speciality_label",
    training_willingness_work_type:
        "profile_current_train_info#training_willingness_work_type_label",
    training_start_time:
        "profile_current_train_info#train_start_datetime_label",
    training_end_time: "profile_current_train_info#train_end_datetime_label",
    flexible_employment:
        "profile_current_train_info#is_tran_flexible_employment_label",
    flexible_employment_month:
        "profile_current_train_info#flexible_employment_month",
    uid: "uniplat_uid",
    tags: "tags",

    basic_info_id: "basic_info#id",
    jon_info_id: "id_card_openid#user_profile_current_job_info.id",
    train_info_id: "profile_current_train_info#id",
    willingness_joint_id: "id_card_openid#job_willingness_joint.id",
    profile_labels: "",
}

export function computeAge(age: number) {
    if (!age) return ""
    const currentDate = new Date()
    const birthYear = currentDate.getFullYear() - age
    return moment(
        new Date(birthYear, currentDate.getMonth(), currentDate.getDate())
    ).format("YYYY-MM-DD")
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("query_user_profile").list(""),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: {},
        handleFilterData: (params) => {
            const birth_date = (params.birth_date || [])
                .map((e: number) => computeAge(e))
                .filter(Boolean)
                .reverse()

            let uniplat_uid: { min: string; max: string } | {} = {}
            if (params.uniplat_uid) {
                if (params.uniplat_uid === "0") {
                    uniplat_uid = { min: "0", max: "0" }
                } else {
                    uniplat_uid = { min: "1", max: "" }
                }
            }
            return { ...params, birth_date, uniplat_uid }
        },
    }
}

export interface Row {
    unionIds: string // json
    id: number
    v: number
    name: string
    sex: string
    getAge: string
    id_card: string
    mobile: string
    uniplat_uid_calc: string
    residence_property: string
    education: string
    employment_status: string
    update_time: string
    household: string
    id_card_openid: string
    household_province: string
    household_city: string
    household_area: string
    household_detail: string
    uid: string
    profile_id: string
    mobile_hide: string
    name_hide: string
    _access_key: string
    profile_labels: string
}

export const columns: TableColumn[] = [
    {
        prop: "select",
        width: "58",
        type: "selection",
    },
    {
        label: "姓名",
        prop: "name_hide",
        fixed: "left",
        showOverflowTip: true,
        width: "200px",
        render: (h, row: ListRow) => {
            return h(
                "div",
                {
                    class: "pre-line",
                },
                [
                    row.name_hide,
                    `  `,
                    +row.sex === 1 ? "男" : +row.sex === 2 ? "女" : "",
                    `  `,
                    row.getAge + " 岁",
                    "\n",
                    row.id_card_hide,
                ]
                    .filter(Boolean)
                    .map((i) => h("span", i + ""))
            )
        },
    },
    {
        label: "手机号",
        fixed: "left",
        prop: "mobile_hide",
        showOverflowTip: true,
        width: "190px",
        formatter: (row: ListRow) => {
            return `${row.mobile_hide} (${
                row.uniplat_uid_calc ? "已注册" : "未注册"
            })`
        },
    },
    {
        label: "标签",
        fixed: "left",
        prop: "tag_names",
        showOverflowTip: true,
        minWidth: "230px",
        hide: config.envProject === EnvProject.黄州项目,
        render: (h, row: ListRow) => {
            const tags = JSON.parse(row.labels || "{}")
            let tagArr: string[] = []
            forEach(tags, (v) => {
                tagArr.push(...v)
            })
            tagArr = tagArr.filter(Boolean)
            const tagStr = tagArr.join("，")
            return h(
                "div",
                {
                    class: "pre-line u-line-3",
                    directives: [
                        {
                            name: "customerPopover",
                            value: { content: tagStr },
                        },
                    ],
                    key: row._access_key,
                },
                tagStr || "-"
            )
        },
    },
    {
        label: "实名信息",
        prop: "实名信息",
        children: [
            {
                label: "民族",
                prop: "nation",
                minWidth: "100",
            },
            {
                label: "政治面貌",
                prop: "political_outlook_name",
                minWidth: "130",
            },
            {
                label: "户口性质",
                prop: "reg_residence_property",
                minWidth: "130",
                formatter: (row: ListRow) =>
                    +row.reg_residence_property === 2
                        ? "城镇"
                        : +row.reg_residence_property === 1
                        ? "农村"
                        : "",
            },
            {
                label: "户籍地/常住地",
                prop: "household",
                width: "220px",
                render(h, row) {
                    return h(
                        "div",
                        {
                            class: "pre-line u-text-left",
                        },
                        [
                            h("span", "户籍地："),
                            h(
                                "span",
                                {},
                                getAddress(row, [
                                    "household_province_name",
                                    "household_city_name",
                                    "household_area_name",
                                    "household_countryside_name",
                                    "household_village_name",
                                ]) || "-"
                            ),
                            h("span", "\n"),
                            h("span", "常住地："),
                            h(
                                "span",
                                {},
                                getAddress(row, [
                                    "permanent_province_name",
                                    "permanent_city_name",
                                    "permanent_area_name",
                                ]) || "-"
                            ),
                        ]
                    )
                },
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "学历信息",
        prop: "学历信息",
        children: [
            {
                label: "文化程度",
                prop: "education_name",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "所学专业",
                prop: "study_speciality",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "毕业时间",
                prop: "graduate_date",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "毕业院校",
                prop: "graduate_school",
                minWidth: "100",
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "就业状态信息",
        prop: "就业状态信息",
        children: [
            {
                label: "就业状态",
                minWidth: "100",
                prop: "employment_status_name",
                showOverflowTip: true,
            },
            {
                label: "就业方式",
                minWidth: "100",
                prop: "employment_type_name",
                showOverflowTip: true,
            },
            {
                label: "就业地点",
                minWidth: "200",
                prop: "work_province_name",
                showOverflowTip: true,
                formatter: (row) =>
                    getAddress(row, [
                        "work_province_name",
                        "work_city_name",
                        "work_county_name",
                    ]),
            },
            {
                label: "就业单位",
                prop: "company_name",
                minWidth: "150",
            },
            {
                label: "从事行业",
                prop: "job_industry_name",
                minWidth: "150",
            },
            {
                label: "就业工种",
                prop: "job_type_work_name",
                minWidth: "150",
            },
        ],
    },
    {
        label: "就业意向信息",
        prop: "就业意向信息",
        children: [
            {
                label: "是否有就业意愿",
                prop: "is_job_willing",
                minWidth: "120",
                formatter(row) {
                    return +row.is_job_willing ? "是" : "否"
                },
            },
            {
                label: "就业意向行业",
                minWidth: "200px",
                prop: "job_willing_industry",
            },
            {
                label: "意向工种",
                minWidth: "100px",
                prop: "job_willing_type_work_display",
            },
            {
                label: "择业地域意愿",
                minWidth: "200",
                prop: "work_province_name",
                showOverflowTip: true,
                formatter: (row) =>
                    getAddress(row, [
                        "​job_willing_province_id_name",
                        "job_willing_province_id_two_name",
                        "job_willing_province_id_three_name",
                    ]),
            },
            {
                label: "月薪要求",
                minWidth: "100px",
                prop: "job_willing_salary",
            },
            {
                label: "是否有创业意愿",
                minWidth: "120",
                prop: "is_start_job_name",
            },
            {
                label: "创业意向行业",
                minWidth: "120px",
                prop: "start_job_industry_name",
            },
            {
                label: "创业意向工种",
                minWidth: "120px",
                prop: "start_job_type_work_name",
            },
        ],
    },
    {
        label: "培训信息",
        prop: "培训信息",
        children: [
            {
                label: "是否有培训意愿",
                prop: "is_training_willingness_name",
                minWidth: "120px",
            },
            {
                label: "培训意向工种",
                prop: "training_willingness_work_type_name",
                minWidth: "160px",
            },
            {
                label: "职业技能特长情况",
                prop: "train_speciality_detail",
                minWidth: "160px",
            },
            {
                label: "技能等级",
                prop: "qualification_level",
                minWidth: "120px",
            },
            {
                label: "是否享受灵活就业补贴",
                prop: "is_tran_flexible_employment",
                minWidth: "170px",
            },
            {
                label: "享受灵活就业补贴期限",
                prop: "flexible_employment_month",
                minWidth: "180px",
            },
            {
                label: "是否参加职业技能培训",
                prop: "is_train_job_name",
                minWidth: "180px",
            },
        ],
    },
    {
        label: "拉黑信息",
        prop: "拉黑信息",
        children: [
            {
                label: "是否被拉黑",
                prop: "black_operator_name_1",
                minWidth: "180px",
                formatter: (row: ListRow) => (row.black_operator ? "是" : "否"),
            },
            {
                label: "拉黑操作人",
                prop: "black_operator_name",
                minWidth: "180px",
            },
            {
                label: "拉黑时间",
                prop: "black_operator_time",
                minWidth: "180px",
            },
        ],
    },
    {
        label: "操作",
        prop: "h",
        fixed: "right",
        minWidth: "100",
    },
]
