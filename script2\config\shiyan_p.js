const config = {
    局方端: {
        bannerImg: `/img/common-login/banner.webp`,
        bannerStyle: {
            maxWidth: "875px",
            minWidth: "477px",
            marginTop: "145px",
            position: "relative",
            "z-index": "9",
            "margin-right": "20px",
        },
        extraImgs: [
            {
                bgImg: `/img/common-login/bg-img1.png`,
                bgImgStyle: {
                    height: "307px",
                    width: "251px",
                    position: "absolute",
                    right: "0px",
                    top: "0px",
                },
            },
            {
                bgImg: `/img/common-login/bg-img2.png`,
                bgImgStyle: {
                    height: "278px",
                    width: "189px",
                    position: "absolute",
                    left: "0px",
                    top: "8%",
                },
            },
            {
                bgImg: `/img/common-login/bg-img3.png`,
                bgImgStyle: {
                    height: "372px",
                    width: "376px",
                    position: "absolute",
                    right: "0px",
                    bottom: "0px",
                },
            },
        ],
        theme: "#5782EC",
    },
    企业端: {
        bannerImg: `/img/company/logo-bg-show.png`,
        bannerStyle: {
            width: "100%",
        },
        headerLogo: `/img/hr/logo-show.png`,
        headerLogoStyle: {
            height: "26px",
            width: "26px",
        },
        leftStyle: {
            width: "492px",
            maxWidth: "492px",
            background: "#598BFF",
            alignItems: "flex-end",
        },
        rightStyle: {
            background: "#F5F9FF",
        },
        themeClass: "xg-project-org",
        theme: "#5782EC",
    },
    机构端: {
        bannerImg: `/img/hr/banner-show.png`,
        bannerStyle: {
            maxWidth: "600px",
            minWidth: "580px",
            marginTop: "50px",
            // height: "100%",
        },
        headerLogo: `/img/hr/logo-show.png`,
        headerLogoStyle: {
            height: "26px",
            width: "26px",
        },
        leftStyle: {
            background: "linear-gradient(360deg, #43ABFF 0%, #0169BE 100%)",
        },
        theme: "#0169BE",
    },
}

function stringifyValue(config) {
    Object.keys(config).forEach((i) => {
        config[i] = JSON.stringify(config[i])
    })
    return config
}

module.exports = {
    config: { ...stringifyValue(config) },
}
