<template>
    <div class="bg-white rounded-3">
        <div
            v-if="title"
            class="flex items-center justify-between"
            style="padding: 20px"
        >
            <div class="text-16 font-600">{{ title }}</div>
            <div
                v-if="showViewMore"
                @click="$emit('view-more')"
                class="text-14 flex items-center"
                style="cursor: pointer"
            >
                <span style="color: #5782ec"> 查看更多 </span>
                <svg
                    width="7"
                    height="11"
                    viewBox="0 0 7 11"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style="margin-left: 4px"
                >
                    <path
                        d="M0.819825 9.75L5.81982 5.25L0.819824 0.75"
                        stroke="#747EB2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    />
                </svg>
            </div>
        </div>
        <div v-if="summaryValue" class="flex items-center justify-center">
            {{ summaryPrefix }}
            <span
                :style="{
                    color: lineColor,
                    fontSize: '18px',
                    margin: '0 4px',
                }"
                >{{ summaryValue }}</span
            >
            {{ summarySuffix }}
        </div>
        <LineChart
            :data="chartData"
            :lineColor="lineColor"
            :seriesName="seriesName"
        />
    </div>
</template>

<script lang="ts">
    import { sdk } from "@/service"
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import LineChart from "./LineChart.vue"

    @Component({
        components: {
            LineChart,
        },
    })
    export default class AnalysisCard extends Vue {
        @Prop({ type: String, default: "" })
        readonly title!: string

        @Prop({ type: Boolean, default: true })
        readonly showViewMore!: boolean

        @Prop({ type: String, default: "推荐" })
        readonly summaryPrefix!: string

        @Prop({ type: String, default: "次" })
        readonly summarySuffix!: string

        @Prop({ type: String, default: "grid" })
        readonly name!: string

        @Prop({ type: String, default: "" })
        readonly region_code!: string

        @Prop({ type: String, default: "0" })
        readonly all_flag!: string

        @Prop({ type: String, default: "" })
        readonly startTime!: string

        @Prop({ type: String, default: "" })
        readonly endTime!: string

        @Prop({ type: String, default: "#1770E566" })
        readonly lineColor!: string

        @Prop({ type: String, default: "推荐次数" })
        readonly seriesName!: string

        private chartData: {
            date: string
            share_count: number
        }[] = []

        private summaryValue = "-"
        get postData() {
            return {
                name: this.name,
                region_code: this.region_code,
                all_flag: this.all_flag,
                item_size: 10,
                item_index: 1,
                start_time: this.startTime,
                end_time: this.endTime,
            }
        }

        @Watch("postData")
        private onPostDataChange(newData: any) {
            sdk.core
                .domainService(
                    "xg_project",
                    "g_service_register",
                    "service_region_dashboard_detail_list"
                )
                .post(newData)
                .then((res: any) => {
                    this.chartData = (res.list || []).map((item: any) => {
                        const d = new Date(item.date)
                        return {
                            ...item,
                            date: `${d.getMonth() + 1}月${d.getDate()}日`,
                        }
                    })
                    this.summaryValue = res.total
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "../style/tail.less";
</style>
