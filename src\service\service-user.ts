import { config, EnvApplication, EnvProject } from "@/config"
import { PassportTokenController } from "@/core-ui/service/passport/token"
import { userInfoService } from "@/core-ui/service/passport/user-info"
import { RoleController } from "@/installer/role"
import { app } from "@/main"
import router from "@/router"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { getCompanyInfo } from "@/views/page-company/company-manage/manage"
import { MessageBox } from "element-ui"
import { assign, cloneDeep, filter, find } from "lodash"
import { clientConfigService } from "./client-config"
const isXg = config.envProject === EnvProject.孝感项目
export enum CompanyRole {
    超级管理员,
    一般管理员,
}
export enum AgentStatus {
    公司已注销,
    正常访问,
}
export interface AgentInfo {
    key: string
    label: string
    data: {
        agent_name: string
        real_name: string
        status: AgentStatus
        company_code: string
        id: string
        sys_role: CompanyRole
        tg_enterprise_id: string
        region_name?: string
        region_code?: string
        region_code_last?: string
        is_black?: boolean
    }
}
export const currentAgentIdKey = "currentAgentIdKey"
export const multiAgent = [
    "xg_project_org",
    "xg_project_train",
    "xg_project_org_hr",
    "xg_project_grid",
].includes(process.env.VUE_APP_APP_NAME)

export const sceneName: Record<string, string> = {
    xg_project_org: "AgentScene",
    xg_project_train: "TrainAgentScene",
    xg_project_org_hr: "PersonAgentScene",
    xg_project_operate: "ManagerOrgScene",
    xg_project_grid: "GridRegionScene",
    dh_wxmp: "",
}

export const sceneKey = sceneName[process.env.VUE_APP_APP_NAME]
class UserService {
    private curAgent: null | AgentInfo = null
    private agentList: AgentInfo[] = []
    private isFetching: Promise<AgentInfo | null> | null = null
    private isShowContact = false
    private isShowLogout = false
    agentRow: any = null

    public setAgentRow(row: any) {
        this.agentRow = row
    }

    public role = {
        menu: [] as string[],
        action: [] as string[],
        all: [] as string[],
        has: (roleName: string, type?: "menu" | "action") => {
            this.role[type || "all"].includes(roleName)
        },
    }

    public getCurAgent() {
        return this.curAgent
    }

    public showLogout() {
        if (this.isShowLogout) {
            return
        }
        this.isShowLogout = true
        MessageBox.alert("登录过期，请重新登录", "提示", {
            showCancelButton: false,
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            callback: () => {
                this.logout()
                this.isShowLogout = false
            },
        })
    }

    public async showContact(orgCheck?: OrgLoginCheck) {
        if (
            [EnvApplication.培训机构端, EnvApplication.局方端].includes(
                config.envApplication
            )
        ) {
            this.logout()
            return app.$message("无此系统权限")
        }

        if ([EnvApplication.网格员端].includes(config.envApplication)) {
            this.logout()
            return MessageBox({
                title: "提示",
                closeOnClickModal: false,
                showClose: false,
                showCancelButton: false,
                showConfirmButton: true,
                confirmButtonText: "我知道了",
                message:
                    "当前账号无法登录，请在手机端完成注册和申请，审核通过后即可登录",
                beforeClose: (action, _instance, done) => {
                    if (action === "confirm") {
                        this.logout()
                    }
                    this.isShowContact = false
                    done()
                },
            })
        }

        if (this.isShowContact) {
            return
        }
        this.isShowContact = true
        const h = app.$createElement
        if (this.agentList.length > 1) {
            MessageBox.alert(
                "暂无当前企业管理权限。切换到其他企业进行管理？",
                "提示",
                {
                    closeOnClickModal: false,
                    showClose: false,
                    beforeClose: (action, instance, done) => {
                        if (action === "confirm") {
                            const oldKey = this.getAgentIdCache()
                            const agentList = filter(
                                this.availableAgentList,
                                (e) => e.key !== oldKey
                            )
                            window.localStorage.setItem(
                                currentAgentIdKey,
                                agentList[0].key
                            )
                            window.location.reload()
                        }
                        this.isShowContact = false
                        done()
                    },
                }
            )
        }
        let src = ""
        try {
            await clientConfigService.setup().then((r) => {
                if (r.advInfo.qr_code) {
                    src = sdk.buildImage(r.advInfo.qr_code)
                }
            })
        } catch {}
        let tipMessage = `当前账号无可管理的企业，可点击去申请进行企业入驻${
            src ? "，如有其他问题可扫码添加企业微信咨询" : ""
        }`
        if (orgCheck?.msg) {
            tipMessage = orgCheck.msg
        }
        const messageDome = h(
            "div",
            {
                class: "u-m-t-30 lh15",
            },
            tipMessage
        )
        const imgDom = src
            ? h("img", {
                  attrs: {
                      src,
                      width: "200px",
                      height: "200px",
                  },
              })
            : undefined
        MessageBox({
            title: "提示",
            closeOnClickModal: false,
            showClose: false,
            showCancelButton: true,
            showConfirmButton:
                orgCheck?.code !== OrgLoginStatus.已入驻状态为待认证,
            cancelButtonText: "我知道了",
            confirmButtonText:
                orgCheck?.code === OrgLoginStatus.已入驻状态为认证失败
                    ? "重新申请"
                    : "去申请",
            message: h(
                "div",
                { class: "u-flex u-flex-col" },
                [imgDom, messageDome].filter(Boolean)
            ),
            beforeClose: (action, instance, done) => {
                console.log({ action })
                if (action === "confirm") {
                    if (router.currentRoute.name !== routesMap.apply) {
                        router.push({ name: routesMap.apply })
                    }
                } else {
                    this.logout()
                }
                this.isShowContact = false
                done()
            },
        })
    }

    public async logout(queryParams?: Record<string, string>) {
        await sdk.core.logout(!!PassportTokenController.hasToken())
        userInfoService.cleanCache()
        this.clearCache()
        sdk.core.setInitData({
            ...sdk.core.global.initData,
            scenes: [],
        })
        router.replace({
            name: routesMap.login,
            ...(queryParams ? { query: queryParams } : {}),
        })
    }

    public login2Redirect() {
        const redirectUrl = (app.$route.query?.redirectUrl as string) || ""
        if (redirectUrl) {
            return app.$router.push({
                path: redirectUrl,
            })
        } else {
            return app.$router.push({ name: routesMap.home.page })
        }
    }

    public getAgentIdCache() {
        return window.localStorage.getItem(currentAgentIdKey) || ""
    }

    public clearCache() {
        this.agentList = []
        this.curAgent = null
    }

    get getList() {
        return this.agentList
    }

    getAgentList() {
        if (!sceneKey) {
            return Promise.resolve([])
        }
        return sdk.core
            .getAxios()
            .get<any, { list: AgentInfo[] }>(
                `general/entrances/scene/${sceneKey}/datalist?keyword=&limitBegin=0&limitSize=20`
            )
            .then((r) => {
                if (EnvApplication.网格员端 === config.envApplication) {
                    r.list.map((i) => {
                        const item = i.data as any
                        i.label =
                            item.region_village_name ||
                            item.region_town_name ||
                            item.region_district_name ||
                            item.region_city_name ||
                            item.region_province_name
                        return i
                    })
                }
                this.agentList = r.list
                return r.list
            })
    }

    /** 返回true则通过黑名单校验 */
    async black_check(curAgent: AgentInfo | null = null) {
        if (config.isOperate || !isXg) return true
        const h = app.$createElement
        let src = !isXg ? "/img/contact-jz.png" : "/img/contact.png"
        try {
            await clientConfigService.setup().then((r) => {
                if (r.advInfo.qr_code) {
                    src = sdk.buildImage(r.advInfo.qr_code)
                }
            })
        } catch {}
        if (this.whiteAgentList.length === 0) {
            MessageBox.alert("", "提示", {
                closeOnClickModal: false,
                showClose: false,
                message: h("div", { class: "u-flex u-flex-col" }, [
                    h("img", {
                        attrs: {
                            src,
                            width: "200px",
                            height: "200px",
                        },
                    }),
                    h(
                        "div",
                        {
                            class: "u-m-t-30 lh15",
                        },
                        `您所管理的企业（${curAgent?.label}）因涉及违规行为，已被平台列入限制名单，如需找回请扫码添加工作人员企业微信帮您处理`
                    ),
                ]),
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        this.logout()
                    }
                    this.isShowContact = false
                    done()
                },
            })
            return false
        }
        if (curAgent?.data.is_black) {
            MessageBox.alert(
                `您所管理的企业（${curAgent?.label}）因涉及违规行为，已被平台列入限制名单，切换到其他企业进行管理？`,
                "提示",
                {
                    closeOnClickModal: false,
                    showClose: false,
                    beforeClose: (action, instance, done) => {
                        if (action === "confirm") {
                            const oldKey = this.getAgentIdCache()
                            const agentList = filter(
                                this.whiteAgentList,
                                (e) => e.key !== oldKey
                            )
                            window.localStorage.setItem(
                                currentAgentIdKey,
                                agentList[0].key
                            )
                            window.location.reload()
                        }
                        this.isShowContact = false
                        done()
                    },
                }
            )
            return false
        }
        return true
    }

    /** 返回true则通过公司注销校验 */
    async ban_check(curAgent: AgentInfo | null = null) {
        if (config.isOperate) return true
        const h = app.$createElement
        // let src = !isXg ? "/img/contact-jz.png" : "/img/contact.png"
        let src = ""
        try {
            await clientConfigService.setup().then((r) => {
                if (r.advInfo.qr_code) {
                    src = sdk.buildImage(r.advInfo.qr_code)
                }
            })
        } catch {}
        // 培训机构端部检测
        if (
            [EnvApplication.培训机构端, EnvApplication.网格员端].includes(
                config.envApplication
            )
        ) {
            return true
        }
        if (this.openingList.length === 0) {
            MessageBox.alert("", "提示", {
                closeOnClickModal: false,
                showClose: false,
                message: h("div", { class: "u-flex u-flex-col" }, [
                    src &&
                        h("img", {
                            attrs: {
                                src,
                                width: "200px",
                                height: "200px",
                            },
                        }),
                    h(
                        "div",
                        {
                            class: "u-m-t-30 lh15",
                        },
                        `您所管理的企业（${curAgent?.label}）已注销，如有疑问请扫码添加工作人员企业微信帮您处理`
                    ),
                ]),
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        this.logout()
                    }
                    this.isShowContact = false
                    done()
                },
            })
            return false
        }
        if (!curAgent?.data.status) {
            MessageBox.alert(
                `您所管理的企业（${curAgent?.label}）已注销，切换到其他企业进行管理？`,
                "提示",
                {
                    closeOnClickModal: false,
                    showClose: false,
                    beforeClose: (action, instance, done) => {
                        if (action === "confirm") {
                            const oldKey = this.getAgentIdCache()
                            const agentList = filter(
                                this.openingList,
                                (e) => e.key !== oldKey
                            )
                            window.localStorage.setItem(
                                currentAgentIdKey,
                                agentList[0].key
                            )
                            window.location.reload()
                        }
                        this.isShowContact = false
                        done()
                    },
                }
            )
            return false
        }
        return true
    }

    async setup(force = false) {
        if (this.isFetching) {
            return this.isFetching
        }
        if (this.curAgent && !force) {
            return Promise.resolve(this.curAgent)
        }
        if (!sceneKey) {
            return {} as any
        }
        this.isFetching = this.getAgentList()
            .then(async (r) => {
                if (!r.length) {
                    this.showContact()
                    return null
                }
                await this.setCurrentAgent()
                return this.curAgent
            })
            .finally(() => {
                this.isFetching = null
            })
        return this.isFetching
    }

    loginExcute() {
        if (
            [EnvApplication.企业端, EnvApplication.培训机构端].includes(
                config.envApplication
            )
        ) {
            return sdk.core
                .model(config.isHr ? "xg_human_agent" : "xg_agent")
                .action("login")
                .updateInitialParams({
                    selected_list: [{ v: 0, id: +this.curAgent!.key }],
                })
                .execute()
        }
    }

    /** 白名单列表 */
    private get whiteAgentList() {
        return (this.agentList || []).filter((item) => !item.data.is_black)
    }

    private get availableAgentList() {
        return (this.agentList || []).filter(
            (item) => !item.data.is_black && item.data.status
        )
    }

    /** 未注销列表 */
    private get openingList() {
        return (this.agentList || []).filter((item) => item.data.status)
    }

    async setCurrentAgent(key = "") {
        key = key || this.getAgentIdCache()
        const agent =
            find(this.agentList, (e) => e.key === key) || this.whiteAgentList[0]
        if (!this.black_check(agent)) {
            return null
        }
        if (!this.ban_check(agent)) {
            return null
        }
        window.localStorage.setItem(currentAgentIdKey, agent.key)
        this.curAgent = cloneDeep(agent)
        sdk.core.setInitData(
            assign(sdk.core.global.initData, {
                scenes: [
                    {
                        name: sceneKey,
                        key: agent.key,
                    },
                ],
            })
        )
        if (config.isOperate) {
            await RoleController.getRole()
        }
        if (config.isOrg) {
            await getCompanyInfo(agent.key).then((r) => {
                this.setAgentRow(r)
            })
        }
        this.loginExcute()
        return this.curAgent
    }

    checkOrgAccountStatus(mobile: string) {
        if (![EnvApplication.企业端].includes(config.envApplication)) {
            return Promise.resolve(undefined)
        }
        return sdk.core
            .domainService("xg_project", "anonymous/agent_api", "user_check")
            .post<OrgLoginCheck>({ mobile })
            .then((r) => {
                console.log(r)
                return r
            })
    }
}

export interface OrgLoginCheck {
    code: number
    msg: string
}

export enum OrgLoginStatus {
    未入驻且手机号不存在 = 1,
    未入驻但是手机号存在,
    已入驻状态为待认证,
    已入驻状态为认证失败,
    已入驻状态为已认证,
}

export const userService = new UserService()
