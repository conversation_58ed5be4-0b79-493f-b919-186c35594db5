<template>
    <div>
        <div class="title u-flex u-row-between">
            <div>人员列表</div>
            <div v-if="policyImportConfig">
                <el-button
                    type="primary"
                    v-if="policyImportConfig.showImportBtn"
                    plain
                    @click="showImportPop = true"
                    >批量导入</el-button
                >
                <el-button
                    type="primary"
                    plain
                    v-if="policyImportConfig.single"
                    @click="showPop = true"
                >
                    单条新增
                </el-button>
                <el-button type="primary" @click="exportExcelUniplat">
                    导出
                </el-button>
            </div>
        </div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            class="container"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="tableConfig.columns">
                    <div slot="h1" slot-scope="scope">
                        <el-button type="text" @click="del_1(scope.row)">
                            删除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="批量导入"
            placeholder="请点击「确定」上传"
            :importConfig="importConfig"
            :canExport="true"
            exportFileName="列表文件"
            underListWidth="1200"
            @refresh="refreshImport"
        >
        </excel-import>
        <common-pop
            v-model="showPop"
            v-if="policyImportConfig.single"
            title="单条新增"
            sdkModel="policy_form_apply"
            :sdkAction="policyImportConfig.single"
            :prefilters="[
                { property: 'apply_batch_id', value: id },
                { property: 'form_id', value: form_id },
            ]"
            @refresh="refreshAdd"
        ></common-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { cloneDeep } from "lodash"
    import { Component, Prop } from "vue-property-decorator"
    import { createC, getSpPolicyTemplate, policyImportConfig, spPolicy } from "."
    import { column2, predict2 } from "./apply-list.vue"

    @Component({
        components: { TableContainer, CommonTable, ExcelImport, CommonPop },
    })
    export default class Template extends BaseTableController<any> {
        @Prop()
        id!: string

        @Prop()
        form_id!: string

        @Prop()
        policyName: any

        showImportPop = false
        showPop = false
        importConfig: any = null

        tableConfig: TableConfig | null = null
        private init() {
            this.importConfig = {
                modelName: "policy_form_apply",
                prefilters: [
                    {
                        property: "form_id",
                        value: this.form_id,
                        prefilters: null,
                        relationalOperator: null,
                    },
                    {
                        property: "apply_batch_id",
                        value: this.id,
                        prefilters: null,
                        relationalOperator: null,
                    },
                ],
                bigActionImportParams: {
                    inputs_parameters: [
                        {
                            property: "apply_type",
                            value: "1",
                        },
                    ],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                    prefilters: [
                        {
                            property: "form_id",
                            value: this.form_id,
                            prefilters: null,
                            relationalOperator: null,
                        },
                        {
                            property: "apply_batch_id",
                            value: this.id,
                            prefilters: null,
                            relationalOperator: null,
                        },
                    ],
                },
                ...this.policyImportConfig,
            }
            console.log("sssss", JSON.parse(JSON.stringify(this.importConfig)))
            pageLoading(() => {
                return buildConfig4RemoteMeta(
                    "policy_form_apply",
                    "apply_profile_for_policy2",
                    {
                        useLabelWidth: true,
                        useRowFieldGroups: true,
                        disabledOpt: true,
                        prefilters: {
                            apply_batch_id: this.id,
                            form_id: this.form_id,
                        },
                    }
                ).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        mounted() {
            this.init()
        }

        private get isSpecial() {
            return spPolicy.includes(this.policyName)
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            tableConfig.predict = cloneDeep(predict2)
            this.tableConfig = null
            const c = createC(this.policyName)
            this.$nextTick(() => {
                tableConfig.columns = [
                    {
                        label: "批次名称",
                        prop: "batch_name",
                        width: "120px",
                    },
                    ...(this.isSpecial ? c : cloneDeep(column2)),
                ]
                this.tableConfig = tableConfig
                console.log("t", JSON.parse(JSON.stringify(this.tableConfig)))
            })
        }

        toDetail(row: any) {}

        del_1(row: any) {
            MessageBox.confirm("是否确定删除", "删除").then(() => {
                return sdk.core
                    .model("policy_form_apply")
                    .action("delete_offline_apply_person")
                    .updateInitialParams({
                        selected_list: [
                            {
                                id: row.id,
                                v: 0,
                            },
                        ],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("删除成功")
                        this.reloadList()
                        this.$emit("refreshOperateList")
                    })
            })
        }

        exportExcelUniplat() {
            this.exportExcelUniplatV2({
                template_name: getSpPolicyTemplate(this.policyName),
            })
        }

        async refreshImport() {
            await sdk.core
                .model("policy_form_apply")
                .action("add_remark")
                .updateInitialParams({
                    selected_list: [],
                    prefilters: [
                        {
                            property: "apply_batch_id",
                            value: this.id,
                        },
                        {
                            property: "form_id",
                            value: this.form_id,
                        },
                    ],
                })
                .execute()
                .then(() => {
                    this.reloadList()
                    this.$emit("refreshOperateList")
                })
        }

        private get policyImportConfig() {
            return policyImportConfig(this.policyName)
        }

        refreshAdd() {
            this.reloadList()
            this.$emit("refreshOperateList")
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    /deep/ .filter-container-out {
        margin-bottom: 0;
    }
    /deep/ .table {
        padding: 0 !important;
    }
</style>
