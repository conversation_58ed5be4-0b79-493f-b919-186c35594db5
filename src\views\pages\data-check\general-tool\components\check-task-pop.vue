<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title=""
        width="1200px"
        top="8vh"
    >
        <div class="create-position">
            <table-container
                v-if="tableConfig"
                showTableFilter
                filedWidth="195"
                ref="table"
                v-model="tableConfig"
                class="container"
            >
                <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                    <common-table :data="data" :columns="columns">
                    </common-table>
                </div>
            </table-container>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import {
        TableColumn,
        TableConfig,
        TableFilter,
    } from "@/core-ui/component/table"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { buildSelectSource, FormType } from "@/core-ui/component/form"
    import { sdk } from "@/service"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { map } from "lodash"

    @Component({ components: { CommonTable, TableContainer } })
    export default class CheckTaskPop extends Mixins(
        DialogController,
        BaseTableController
    ) {
        @Prop({ default: "" })
        policyId!: string

        @Prop({ default: "" })
        date!: string

        tableConfig: TableConfig | null = null

        private columns: TableColumn[] = []
        private filters: TableFilter[] = []

        onOpen() {
            this.init()
        }

        private async init() {
            await this.buildColumns()
            console.log(333, this.filters)
            this.tableConfig = {
                domainService: sdk.getDomainService(
                    "get_policy",
                    "back_api",
                    "warning_platform"
                ),
                filter: [],
                preFilter: {
                    policy_id: this.policyId,
                    filter: [
                        {
                            date_type: "date",
                            filter_type: 1,
                            info_key: "handle_date",
                            info_type: "date",
                            info_value: this.date,
                        },
                    ],
                },
                defaultPageSize: 6,
            }
        }

        private async buildColumns() {
            const data: any = await sdk
                .getDomainService("get_title", "back_api", "warning_platform")
                .post({ policy_id: this.policyId })

            const i: any[] = data?.filter || []
            this.filters = i.map((e) => {
                let type = FormType.Select
                let timeType = ""
                let timeFormat = ""
                let sourceInputsParameter: any
                if (["date", "month", "quarter", "year"].includes(e.info_type)) {
                    type = FormType.DatePicker
                    timeType = (
                        {
                            date: "daterange",
                            month: "monthrange",
                            quarter: "monthrange",
                            year: "year",
                        } as any
                    )[e.info_type]
                    timeFormat = (
                        {
                            date: undefined,
                            month: "yyyy-MM",
                            quarter: "yyyy-MM",
                            year: "yyyy",
                        } as any
                    )[e.info_type]
                } else {
                    sourceInputsParameter = buildSelectSource(
                        map(e.mapping, (i) => ({
                            key: i.map_value,
                            value: i.map_key,
                        }))
                    )
                }
                return {
                    label: e.label || e.info_key,
                    type,
                    option: timeType
                        ? {
                              type: timeType,
                              filter_type: e.filter_type,
                              format: timeFormat,
                              origin_type: e.info_type,
                          }
                        : undefined,
                    prop: e.info_key,
                    sourceInputsParameter,
                }
            }) as any
            this.columns = (data?.title || []).map((e: any) => {
                return {
                    label: e.info_key,
                    prop: e.info_key,
                    require: e.require,
                }
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            this.columns = r.columns
        }

        onClosing() {}
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }

    .select {
        width: 320px;
    }

    .btn {
        width: 100px;
        height: 36px;
    }

    .create-position {
        margin-top: -20px;

        ::v-deep .el-form-item__label {
            text-align: left;
            color: #555;
            font-size: 14px;
        }
    }

    /deep/ .filter-container {
        padding-left: 0;
        padding-right: 0;

        .item.advice {
            margin-right: 8px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .gap10 {
        gap: 10px;
    }

    .slot-right {
        position: absolute;
        right: 15px;
        top: 15px;
    }
</style>
