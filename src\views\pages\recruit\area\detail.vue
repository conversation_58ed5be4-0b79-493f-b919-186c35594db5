<template>
    <div class="core-ui-table-container" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title u-flex u-row-center">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <area-content :row="row" @refresh="init"></area-content>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { predict } from "."
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import AreaContent from "./components/area-content/index.vue"

    @Component({ name: routesMap.recruit.areaDetail, components: { AreaContent } })
    export default class AreaDetail extends Vue {
        private id = ""
        row: any = null
        breadcrumbs: BreadcrumbItem[] = []

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.areaDetail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: "场地详情",
                },
            ]
            updateTagItem({
                name: routesMap.recruit.areaDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return (this.$route.query.from as string) || routesMap.recruit.area
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            pageLoading(() => {
                return (
                    sdk.core
                        // .model("job_fair")
                        // .detail(80, "for_operate")
                        .model("activity_area")
                        .detail(this.id, "")
                        .query()
                        .then((r) => {
                            this.row = sdk.buildRow(r.row, predict)
                        })
                )
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
