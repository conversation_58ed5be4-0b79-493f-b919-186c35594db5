import { buildSelectSource, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"

const rowPredict = {
    ip: "",
    create_time: "label",
    name: "system_user_references#info#name",
    model_name: "",
    action_name: "",
}

export interface Row {
    /** 操作者ip */
    ip: string
    /** 操作时间 */
    create_time: string
    create_time_label: string
    /** 操作者 */
    name: string
    /** 操作对象 */
    model_name: string
    /** 操作内容 */
    action_name: string
    id: number
    v: number
}

const models = [
    {
        key: "user_profile_basic",
        value: "用户简历",
    },
    {
        key: "xg_company_position",
        value: "岗位",
    },
    {
        key: "xg_agent",
        value: "企业",
    },
    {
        key: "job_fair",
        value: "招聘会",
    },
    {
        key: "g_policy_advice",
        value: "政策资讯",
    },
]

export const tableFilter: TableFilter[] = [
    {
        prop: "model_name",
        label: "操作对象",
        type: FormType.Select,
        option: { multiple: true },
        sourceInputsParameter: buildSelectSource(models),
    },

    {
        label: "操作人ip",
        prop: "ip",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "操作人",
        prop: "name",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "操作内容",
        prop: "action_name",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
]

export const column: TableColumn[] = [
    {
        label: "操作人ip",
        prop: "ip",
        minWidth: "180px",
        showOverflowTip: true,
    },

    {
        label: "操作对象",
        prop: "model_name",
        minWidth: "180px",
        showOverflowTip: true,
        formatter(row: Row) {
            return models.find((i) => i.key === row.model_name)?.value || ""
        },
    },
    {
        label: "操作内容",
        prop: "action_name",
        minWidth: "180px",
        showOverflowTip: true,
    },
    {
        label: "操作人",
        prop: "name",
        minWidth: "100px",
        showOverflowTip: true,
    },
    {
        label: "操作时间",
        prop: "create_time_label",
        minWidth: "180px",
        showOverflowTip: true,
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("work_flow_log@xg_project").list("manager_log"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        column,
        handleFilterData(params) {
            if (!params.model_name) {
                params.model_name = models.map((i) => i.key).join(",")
            }
            return params
        },
    }
}

export const cacheKey = "SMS_TABLE_LIST"
