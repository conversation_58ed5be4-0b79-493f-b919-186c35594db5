<template>
    <div class="switcher d-flex">
        <el-dropdown
            @command="openMenu"
            trigger="click"
            v-if="menus1.length > 1"
        >
            <div class="left" :class="{ selected: m1 }">{{ leftTitle }}</div>
            <el-dropdown-menu slot="dropdown" class="top-switcher">
                <el-dropdown-item
                    v-for="(item, index) in menus1"
                    :key="index"
                    :command="item.name"
                    :class="{
                        selected: item.name === current,
                    }"
                >
                    {{ item.text }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>

        <div class="left" @click="nav1" v-else :class="{ selected: m1 }">
            {{ leftTitle }}
        </div>

        <el-dropdown
            @command="openMenu"
            trigger="click"
            v-if="middleMenus && middleMenus.length"
        >
            <div class="middle" :class="{ selected: m3 }">人员流向分析</div>
            <el-dropdown-menu slot="dropdown" class="top-switcher">
                <el-dropdown-item
                    v-for="(item, index) in middleMenus"
                    :key="index"
                    :command="item.name"
                    :class="{
                        selected: item.name === current,
                    }"
                    >{{ item.text }}</el-dropdown-item
                >
            </el-dropdown-menu>
        </el-dropdown>

        <el-dropdown @command="openMenu" trigger="click">
            <div class="right" :class="{ selected: m2 }">重点人群分析</div>
            <el-dropdown-menu slot="dropdown" class="top-switcher">
                <el-dropdown-item
                    v-for="(item, index) in menus"
                    :key="index"
                    :command="item.name"
                    :class="{
                        selected: item.name === current,
                    }"
                    >{{ item.text }}</el-dropdown-item
                >
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import { routesMap } from "@/router/direction"
    import { find, head } from "lodash"
    import { Component, Vue } from "vue-property-decorator"
    import {
        DCollectSysConfig,
        getSys4DCollect,
    } from "../../collect-task-manage/collect-task-config"
    import { bigScreenRegionCodesStore, BigScreenStore } from "../store/module"

    @Component({ components: {} })
    export default class Index extends Vue {
        @bigScreenRegionCodesStore.Mutation(
            BigScreenStore.MUTATION_BIG_SCREEN_REGION_CODE_CLEAR
        )
        protected clearRegionCode!: BigScreenStore.MUTATION_BIG_SCREEN_REGION_CODE_CLEAR

        isHz = [EnvProject.黄州项目].includes(config.envProject)
        isEZhou = [EnvProject.鄂州项目].includes(config.envProject)

        private current = this.$router.currentRoute.name

        private readonly menus1 = [
            {
                name: routesMap.bigScreen.report1,
                text: "劳动力总况",
            },
            {
                name: routesMap.bigScreen.important.hasJob,
                text: "已就业人员",
                hide: !this.isEZhou,
            },
            {
                name: routesMap.bigScreen.important.noJob,
                text: "未就业人员",
                hide: !this.isEZhou,
            },
        ].filter((i) => !i.hide)

        private readonly menus = [
            { name: routesMap.bigScreen.important.hasJob, text: "已就业人员" },
            { name: routesMap.bigScreen.important.noJob, text: "未就业人员" },

            // { name: routesMap.bigScreen.important.graduate, text: "高校毕业生" }, // 从后端匹配
            // { name: routesMap.bigScreen.important.out, text: "外出务工人员" }, // 从后端匹配

            // { name: routesMap.bigScreen.important.in, text: "外来务工人员" },
            // { name: routesMap.bigScreen.important.hard, text: "困难就业人员" },
        ].filter(() => !this.isEZhou)

        private readonly middleMenus: {
            name: string
            text: string
        }[] = []

        private get menuNames() {
            return this.menus.map((i) => i.name)
        }

        private get middleMenuNames() {
            return this.middleMenus.map((i) => i.name)
        }

        private get leftTitle() {
            return "劳动力总况"
        }

        private get m1() {
            return this.menus1
                .map((i) => i.name)
                .includes(this.$router.currentRoute.name as string)
        }

        private get m2() {
            return this.menuNames.includes(this.current as string)
        }

        private get m3() {
            return this.middleMenuNames.includes(this.current as string)
        }

        created() {
            const isJz = [EnvProject.荆州项目].includes(config.envProject)
            getSys4DCollect().then((r) => {
                if (r[DCollectSysConfig.显示大屏高校毕业生]) {
                    let n = r[DCollectSysConfig.高校毕业生菜单名]
                    if (this.isHz) {
                        n = "两年内高校毕业生"
                    }
                    this.menus.push({
                        name: routesMap.bigScreen.important.graduate,
                        text: n || "高校毕业生",
                    })
                }

                const isXianFeng = [EnvProject.咸丰项目].includes(config.envProject)
                const isHongHu = [EnvProject.洪湖项目].includes(config.envProject)
                if (isHongHu) {
                    this.menus.push(
                        ...[
                            {
                                name: routesMap.bigScreen.important.importantType7,
                                text: "脱贫劳动力",
                            },
                            {
                                name: routesMap.bigScreen.important.importantType8,
                                text: "长江禁捕退捕&退垸还湖",
                            },
                        ]
                    )

                    if (r[DCollectSysConfig.显示大屏外出务工人员]) {
                        this.menus.push({
                            name: routesMap.bigScreen.important.out,
                            text: "省外流向人员分析",
                        })
                    }
                } else if (
                    isXianFeng &&
                    !isJz &&
                    r[DCollectSysConfig.四个新重点人群和流向]
                ) {
                    this.menus.push(
                        ...[
                            {
                                name: routesMap.bigScreen.important.importantType3,
                                text: "脱贫人口",
                            },
                            {
                                name: routesMap.bigScreen.important.importantType4,
                                text: "退役军人",
                            },
                        ]
                    )

                    if (r[DCollectSysConfig.显示大屏外出务工人员]) {
                        this.menus.push({
                            name: routesMap.bigScreen.important.out,
                            text: "省外流向人员分析",
                        })
                    }
                } else if (!isJz && r[DCollectSysConfig.四个新重点人群和流向]) {
                    this.menus.push(
                        ...[
                            {
                                name: routesMap.bigScreen.important.importantType1,
                                text: "残疾人",
                            },
                            {
                                name: routesMap.bigScreen.important.importantType2,
                                text: "退捕渔民",
                            },
                            {
                                name: routesMap.bigScreen.important.importantType3,
                                text: "脱贫人口",
                            },
                            {
                                name: routesMap.bigScreen.important.importantType4,
                                text: "退役军人",
                            },
                        ]
                    )

                    this.middleMenus.push({
                        name: routesMap.bigScreen.important.importantType6,
                        text: "劳动力流向总况",
                    })

                    if (r[DCollectSysConfig.显示大屏外出务工人员]) {
                        this.middleMenus.push({
                            name: routesMap.bigScreen.important.out,
                            text: "省外流向人员分析",
                        })
                    }

                    this.middleMenus.push({
                        name: routesMap.bigScreen.important.importantType5,
                        text: "市外省内流向人员分析",
                    })
                } else if (r[DCollectSysConfig.显示大屏外出务工人员]) {
                    this.menus.push({
                        name: routesMap.bigScreen.important.out,
                        text: "省外流向人员分析",
                    })
                }
            })
        }

        private nav1() {
            this.checkRoute2NeedClearRegionCode(routesMap.bigScreen.report1)
            this.$router.push({
                name: (this.current = routesMap.bigScreen.report1),
                query: this.$route.query || {},
            })
        }

        private openMenu(name: string) {
            this.checkRoute2NeedClearRegionCode(name)
            this.$router.push({
                name: (this.current = name),
                query: this.$route.query || {},
            })
        }

        private checkRoute2NeedClearRegionCode(name: string) {
            const needLogin = !!this.$route.meta?.needLogin
            const tNeedLogin = this.$router.resolve({ name: name }).route?.meta
                ?.needLogin
            if (tNeedLogin !== needLogin) {
                this.clearRegionCode()
            }
        }

        private nav3() {
            this.$router.push({
                name: (this.current = routesMap.bigScreen.returnHome),
                query: this.$route.query || {},
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .switcher {
        > div {
            width: 158px;
            height: 32px;
            color: #fff;
            font-size: 16px;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            background-repeat: no-repeat;
            background-size: 100% 100%;

            & + div {
                margin-left: 22px;
            }
        }

        .left {
            background-image: url("./../assets/left-menu.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            &.selected {
                background-image: url("./../assets/left-menu-selected.png");
                font-weight: 600;
            }

            // & + .el-dropdown {
            //     margin-left: 100px;
            // }
        }
        .middle {
            background-image: url("./../assets/middle.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            &.selected {
                background-image: url("./../assets/middle-selected.png");
                font-weight: 600;
            }
        }
        .right {
            background-image: url("./../assets/right-menu.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            &.selected {
                background-image: url("./../assets/right-menu-selected.png");
                font-weight: 600;
            }
        }
    }

    .top-switcher {
        background-color: #005ad5;
        border: none;
        border-radius: 8px;
        box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.3);
        margin-left: -30px;
        // width: 170px;

        min-width: 150px;

        /deep/ .popper__arrow {
            opacity: 0;
        }
        /deep/ .el-dropdown-menu__item {
            color: #fff;
            padding: 0 22px;
            font-size: 14px;
            height: auto;
            line-height: 36px;

            &.selected {
                background-color: #2980ff;
            }
        }
    }
</style>
