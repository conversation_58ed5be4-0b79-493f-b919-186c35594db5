<template>
    <div class="core-ui-table-container container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <!-- <div class="u-flex">
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    v-if="isWait || isNoPass"
                >
                    {{ isWait ? "去填报" : "重新填报" }}
                </el-button>
            </div> -->
        </div>
        <div class="detail-index shadow">
            <detail-view ref="view" :row="row"></detail-view>
            <div v-if="row">
                <list-view
                    :tableColumns="tableColumns"
                    :tableRows="tableRows"
                    :rows="rows"
                    :detail="row"
                    :isEditing="isShowEdit"
                    ref="detailTable"
                />
            </div>
            <div class="u-flex u-m-t-40 u-row-center" v-if="isShowEdit">
                <el-button
                    type="primary"
                    @click="cancel"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn btn u-m-r-30"
                    @click="toSubmit(6)"
                >
                    保存至草稿状态
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn btn u-m-0"
                    @click="toSubmit(1)"
                >
                    提交审核
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { detailPredict, DetailRow, Status } from "./index"
    import DetailView from "./components/detail-view.vue"
    import ListView from "@/views/pages/work-info-survey/work-info-audit/components/detail-table.vue"
    import {
        closeCurrentTap,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { TableColumn } from "@/core-ui/component/table"
    import {
        flattenObjectArray,
        ReportItem,
    } from "@/views/pages/work-info-survey/work-info-audit"

    @Component({
        name: routesMap.company.hrInfoManage.workInfoApplyDetail,
        components: { DetailView, ListView },
    })
    export default class WorkInfoApplyDetail extends Vue {
        private row: DetailRow | null = null
        private id = ""
        refreshConfig = {
            fun: this.init,
            name: routesMap.company.hrInfoManage.workInfoApplyDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `企业用工信息填报`,
                    to: {
                        name: routesMap.company.hrInfoManage.workInfoApply,
                    },
                },
            ]
            d = [
                ...d,
                {
                    label: "任务详情",
                    to: {
                        name: routesMap.company.hrInfoManage.workInfoApplyDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.company.hrInfoManage.workInfoApplyDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        showObjectImport = false
        importConfig: any = null

        private isShowEdit = false

        private tableColumns: TableColumn[] | any[] = []
        private tableRows: any[] = []
        private rows: any[] = []

        private get isWait() {
            return this.row && this.row.status === Status.待填报
        }

        private get isNoPass() {
            return this.row && this.row.status === Status.审核不通过
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("company_task_record")
                    .detail(this.id, "detail_report_for_review")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, detailPredict)
                        this.isShowEdit =
                            this.row?.status === Status.待填报 ||
                            this.row?.status === Status.草稿 ||
                            this.row?.status === Status.审核不通过

                        pageLoading(() => {
                            return Promise.all([
                                this.getColumns(),
                                this.getData(),
                            ]).then((r: any[]) => {
                                this.tableColumns = r[0].schema?.map(
                                    (i: ReportItem) => {
                                        return {
                                            label: i.display_name,
                                            prop: i.p_union_code,
                                            value_type: i.value_type,
                                            showOverflowTip: true,
                                            column_type: i.type,
                                            default_value: i.default_value,
                                            computer_item: i.computer_item,
                                            limit: i.limit,
                                            text_component_type:
                                                i.text_component_type,
                                            minWidth: "150px",
                                            children:
                                                i.children?.map((j: ReportItem) => {
                                                    return {
                                                        label: j.display_name,
                                                        prop: j.p_union_code,
                                                        showOverflowTip: true,
                                                        children: j.children || [],
                                                        value_type: j.value_type,
                                                        column_type: j.type,
                                                        default_value:
                                                            j.default_value,
                                                        computer_item:
                                                            j.computer_item,
                                                        limit: j.limit,
                                                        text_component_type:
                                                            j.text_component_type,
                                                        minWidth:
                                                            j.display_name.length >
                                                            10
                                                                ? `${
                                                                      j.display_name
                                                                          .length *
                                                                      13
                                                                  }px`
                                                                : "110px",
                                                    }
                                                }) || [],
                                        }
                                    }
                                )
                                this.rows = r[1]
                                this.tableRows = [
                                    this.rows.reduce((acc: any, cur: any) => {
                                        const t = flattenObjectArray(
                                            this.tableColumns
                                        ).find(
                                            (i) => i.prop === cur.p_union_code
                                        ) as any
                                        acc[cur.p_union_code] =
                                            t?.value_type === 3
                                                ? cur.indicator_value
                                                : cur.indicator_value || ""
                                        return acc
                                    }, {}),
                                ]
                            })
                        })
                    })
            })
        }

        private getColumns() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_report_indicator"
                )
                .post<{ schema: any[] }>({
                    task_id: this.row?.task_id,
                    tg_enterprise_id: this.row?.tg_enterprise_id,
                })
        }

        private getData() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_company_record_report_this_term"
                )
                .post<{ schema: any[] }>({
                    task_id: this.row?.task_id,
                    tg_enterprise_id: this.row?.tg_enterprise_id,
                })
        }

        private refreshList() {
            this.callRefresh(routesMap.company.hrInfoManage.workInfoApplyDetail)
        }

        private cancel() {
            this.$router.push({
                name: routesMap.company.hrInfoManage.workInfoApply,
            })
        }

        private toSubmit(status?: number) {
            ;(this.$refs.detailTable as any).toDraft(status).then(() => {
                this.$message.success("保存成功")
                this.init()
                this.callRefresh(routesMap.company.hrInfoManage.workInfoApply)
                this.cancel()
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
