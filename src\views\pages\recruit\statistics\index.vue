<template>
    <div>
        <div class="core-ui-custom-header">
            <div class="title">招聘数据统计</div>
        </div>
        <div class="filter u-m-b-24">
            <table-filter
                :tableFilter="filter"
                @changeData="search"
                @search="search"
            ></table-filter>
        </div>
        <template v-if="data">
            <div class="content">
                <div class="title">小程序统计</div>
                <div
                    class="list mini"
                    :style="`grid-template-columns: repeat(${list.miniProgram.length}, 1fr);`"
                >
                    <div
                        class="item u-flex u-flex-col u-row-between"
                        v-for="item in list.miniProgram"
                        :key="item.title"
                        @click="toPage(item)"
                    >
                        <div class="u-font-32">
                            {{
                                getNumber(
                                    "position_page_view_summary",
                                    item.prop
                                )
                            }}
                        </div>
                        <div class="u-font-16 color-3">{{ item.title }}</div>
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="title">岗位统计</div>
                <div class="list u-m-b-24 total">
                    <div
                        class="item u-flex u-flex-col u-row-between total"
                        v-for="item in list.total"
                        :key="item.title"
                        @click="toPage(item)"
                    >
                        <div class="u-font-32">
                            {{ getNumber("position_summary", item.prop) }}
                        </div>
                        <div class="u-font-16 color-3">{{ item.title }}</div>
                    </div>
                </div>
            </div>
            <div class="content" v-if="showPerson">
                <div class="title">智能人才推荐</div>
                <div class="list">
                    <div
                        class="item u-flex u-flex-col u-row-between person"
                        v-for="item in list.person"
                        :key="item.title"
                        @click="toPage(item)"
                    >
                        <div class="u-font-32">
                            {{
                                getNumber("talent_recommend_summary", item.prop)
                            }}
                        </div>
                        <div class="u-font-16 color-3 t">{{ item.title }}</div>
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="title u-flex u-row-between">
                    <div>岗位网格推广</div>
                    <div
                        class="primary u-font-14 font-weight-4 pointer"
                        @click="toGroup"
                    >
                        查看详细统计＞
                    </div>
                </div>
                <div class="list grid">
                    <div
                        class="item u-flex u-flex-col u-row-between grid"
                        :class="{
                            detail: hasDetail(item.prop),
                        }"
                        v-for="item in list.grid"
                        :key="item.title"
                        @click="toPage(item)"
                    >
                        <div class="u-font-32">
                            {{ getNumber("grid_recommend_summary", item.prop) }}
                        </div>
                        <div class="u-font-16 t color-3">{{ item.title }}</div>
                    </div>
                </div>
            </div>
            <div class="content" v-if="showGrid">
                <div class="title u-flex u-row-between">
                    <div>岗位社群推广</div>
                </div>
                <div class="list group">
                    <div
                        class="item u-flex u-flex-col u-row-between group"
                        :class="{
                            detail: hasDetail(item.prop),
                        }"
                        v-for="item in list.group"
                        :key="item.title"
                        @click="toPage(item)"
                    >
                        <div class="u-font-32">
                            {{
                                getNumber(
                                    "socialize_group_recommend_summary",
                                    item.prop
                                )
                            }}
                        </div>
                        <div class="u-font-16 t color-3">{{ item.title }}</div>
                    </div>
                </div>
            </div>
            <div class="content" v-if="showHr">
                <div class="title">人力资源撮合</div>
                <div class="list">
                    <div
                        class="item u-flex u-flex-col u-row-between cooperation"
                        v-for="item in list.cooperation"
                        :key="item.title"
                        @click="toPage(item)"
                    >
                        <div class="u-font-32">
                            {{
                                getNumber(
                                    "agent_bidding_recommend_summary",
                                    item.prop
                                )
                            }}
                        </div>
                        <div class="u-font-16 t color-3">{{ item.title }}</div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script lang='ts'>
    import { Component, Vue } from "vue-property-decorator"
    import TableFilter from "@/core-ui/component/table/filter.vue"
    import { filter, hasDetail, list } from "."
    import { sdk } from "@/service"
    import { routesMap } from "@/router/direction"
    import { config, EnvProject } from "@/config"
    import { cloneDeep, find, get, last, split } from "lodash"
    import { getManageAddress } from ".."

    @Component({ name: routesMap.recruit.statistics, components: { TableFilter } })
    export default class RecruitStatistics extends Vue {
        private filter: TableFilter[] = []

        private showGrid = ![EnvProject.黄州项目].includes(config.envProject)

        private showPerson = ![EnvProject.宜都项目].includes(config.envProject)

        private showHr = ![EnvProject.黄州项目, EnvProject.宜都项目].includes(
            config.envProject
        )

        private data: any = null
        private hasDetail = hasDetail
        private filterData: any = null

        private list = list

        private getNumber(type: string, prop: string) {
            return (this.data[type] || {})[prop] || 0
        }

        private search(v: { time: string[]; mgt_region_code: string }) {
            const start_date = get(v, "time[0]")
            const end_date = get(v, "time[1]")
            const code = last(split(v.mgt_region_code, ","))
            this.filterData = {
                start_date,
                end_date,
                mgt_region_code: code,
            }
            this.$nextTick(() => {
                this.getList({
                    ...this.filterData,
                })
            })
        }

        mounted() {
            this.getList()
            getManageAddress().then((r) => {
                const f = cloneDeep(filter)
                const d = find(f, { prop: "mgt_region_code" })
                if (d) {
                    d.sourceInputsParameter = r
                }
                this.filter = f as unknown as TableFilter[]
            })
        }

        private getList(filter: Record<string, string> = {}) {
            return sdk
                .getDomainService(
                    "recruit_data_summary_v2",
                    "back_api",
                    "xg_project"
                )
                .get(filter)
                .then((r) => {
                    this.data = r
                })
        }

        private toGroup() {
            this.$router.push({
                name: routesMap.recruit.gridList,
            })
        }

        private toPage(item: { title: string; prop: string }) {
            if (hasDetail(item.prop)) {
                const filterData = this.filterData ? { ...this.filterData } : {}
                this.$router.push({
                    name: routesMap.recruit.orderList,
                    query: {
                        prop: item.prop,
                        ...filterData,
                    },
                })
            }
            console.log(hasDetail(item.prop))
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .filter {
        ::v-deep .form-filed {
            width: 300px !important;
        }
    }
    .content {
        margin-bottom: 24px;
        background: #ffffff;
        padding: 20px;
        .title {
            font-weight: 600;
            color: #222;
            font-size: 18px;
            line-height: 18px;
            margin-bottom: 20px;
        }
    }
    .list {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        overflow: scroll;
        &.grid {
            grid-template-columns: repeat(6, 1fr);
        }
        &.group,
        &.total {
            grid-template-columns: repeat(5, 1fr);
        }
        // &.mini {
        //     grid-template-columns: repeat(6, 1fr);
        // }
    }
    .item {
        border-radius: 8px;
        // min-width: 200px;
        min-height: 110px;
        padding: 20px;
        padding-top: 16px;
        background: #f4f4ff;
        color: #6068e0;
        flex: 1;
        .color-3 {
            line-height: 1.5;
        }
        &.person {
            min-width: 0px;
            background: #eff8fc;
            color: #349fd8;
        }
        &.group {
            min-width: 0px;
            background: #eefaf4;
            color: #3fb979;
        }
        &.grid {
            min-width: 0px;
            background: #ebf9f9;
            color: #32b2b2;
        }
        &.cooperation {
            background: #f0fae8;
            color: #75b842;
        }

        &.total {
            background: #f1f5ff;
            color: #5782ec;
        }
        &.detail {
            cursor: pointer;
            div:not(:first-child) {
                position: relative;
                text-decoration: underline;
                &:hover {
                    color: var(--primary);
                }
                &::after {
                    content: " >";
                    // margin-left: 5px;
                    font-size: 14px;
                }
            }
        }

        &:not(:first-of-type) {
            margin-left: 24px;
        }
    }
</style>
