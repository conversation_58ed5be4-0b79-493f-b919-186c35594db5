import { config, EnvProject } from "@/config"
import {
    BuildFormConfig,
    FormRule,
    FormType,
    buildSelectSource,
} from "@/core-ui/component/form"
import { rules } from "@/core-ui/component/form/rule"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { buildBaiduFormItem } from "@/plugins/baidu-map-selector"
import { sdk } from "@/service"
import { get, isArray, includes } from "lodash"
import { ListTypes } from "uniplat-sdk"
import SalaryEditField from "./salary-edit-field.vue"
import { formatDate } from "../../../../../core-ui/helpers/tools"
import moment from "moment"
import { Vue } from "vue-property-decorator"
import { DatePickerOptions } from "element-ui/types/date-picker"
import { sysConfigService } from "@/service/sys-config"

function getCol(col = 0) {
    return {
        span: 11,
        offset: 2 - col,
    }
}
const isDdDev =
    process.env.VUE_APP_ENV === "test" &&
    [EnvProject.掇刀项目].includes(config.envProject)

const isEz = [EnvProject.鄂州项目].includes(config.envProject)

const SalaryEditField2 = Vue.extend({
    extends: SalaryEditField,
    computed: {
        isCustomer: function () {
            return true
        },
    },
    data() {
        return { showToggle: false }
    },
})

export const expiateTimePickerOptions: DatePickerOptions = {
    disabledDate(time) {
        return (
            time.getTime() < +Date.now() ||
            time.getTime() >= +moment().add("1", "month").add("1", "day")
        )
    },
    shortcuts: [
        {
            text: "一周",
            onClick: (picker) => {
                picker.$emit("pick", formatDate(+moment().add("1", "week")))
            },
        },
        {
            text: "两周",
            onClick: (picker) => {
                picker.$emit("pick", formatDate(+moment().add("2", "week")))
            },
        },
        {
            text: "一个月",
            onClick: (picker) => {
                picker.$emit("pick", formatDate(+moment().add("1", "month")))
            },
        },
        {
            text: "三个月",
            onClick: (picker) => {
                picker.$emit("pick", formatDate(+moment().add("3", "month")))
            },
        },
    ],
}

export const expiateTimeRules: FormRule[] = [
    {
        validator: (rule: any, value: any, callback: any) => {
            if (+new Date(value) < +new Date()) {
                return callback(new Error("招聘过期时间需大于当前时间"))
            } else {
                callback()
            }
        },
    },
]

const tableFilter: TableFilter[] = [
    {
        label: "申请时间",
        type: FormType.DatePicker,
        prop: "apply_time",
        option: {
            type: "daterange",
        },
    },
]
export const tableConfig = (position_id: string): TableConfig => ({
    model: sdk.core
        .model("xg_company_position_recommend")
        .list("position_recommend_list"),
    filter: tableFilter,
    defaultPageSize: 10,
    preFilter: { position_id },
    predict: {
        recommend_type: "label",
        recommend_desc: "",
        recommend_duration: "label",
        audit_status: "label",
        apply_time: "label",
        status_memo: "label",
    },
    oneTabFilter: true,
})
export const columns: TableColumn[] = [
    {
        label: "推广类型",
        prop: "recommend_type_label",
        showOverflowTip: true,
    },
    {
        label: "需求描述",
        prop: "recommend_desc",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "招聘时间/推广时长",
        prop: "recommend_duration_label",
        showOverflowTip: true,
    },
    {
        label: "状态",
        prop: "status_memo",
        showOverflowTip: true,
    },
    {
        label: "申请时间",
        prop: "apply_time_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
    },
]

export enum RecommendType {
    人才推荐服务 = 1,
    社群推广服务,
    网格推广服务,
    人力资源机构代招服务,
}

export interface Row {
    id: number
    recommend_type: RecommendType
    recommend_type_label: string
}

const intentColumn = (isPublic = false) => {
    const arr = [
        {
            label: isPublic ? "单位名称" : "企业名称",
            prop: "agent_name",
            showOverflowTip: true,
            minWidth: "200",
            align: "left",
        },
        {
            label: "统一社会信用代码",
            width: "200px",
            prop: "company_code",
            align: "left",
            minWidth: "200",
            showOverflowTip: true,
        },
        {
            label: "法人",
            prop: "legal_person",
            minWidth: "120",
            showOverflowTip: true,
        },
    ]
    if (!isPublic) {
        arr.push({
            label: "是否入驻",
            prop: "is_create_label",
            minWidth: "100",
            showOverflowTip: true,
        })
    }
    return arr as TableConfig["column"]
}

const intentPredict = {
    agent_name: "label",
    company_code: "",
    legal_person: "",
    agent_id: "id",
    is_create: "label",
}

function intentFilter(isPublic = false): TableFilter[] {
    return [
        // {
        //     label: "申请时间",
        //     type: FormType.Text,
        //     option: { clearable: false },
        //     prop: "plan_description",
        // },
        {
            label: isPublic ? "单位名称" : "企业名称",
            type: FormType.Text,
            prop: "agent_name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
    ]
}

export const age: any = {
    min: 0,
    max: 0,
}

export const experience = {
    min: 0,
    max: 0,
}

const itemStyle = {
    color: "#222",
    fontSize: "18px",
    marginLeft: "-70px",
    marginTop: "20px",
    fontWeight: "600",
}

export const createPositionFormSections = (
    id?: string,
    isAudit?: number,
    isCopy?: string
): BuildFormConfig => {
    const forms: BuildFormConfig["forms"] = [
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位基本信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "岗位名称：",
            type: FormType.Text,
            prop: "name",
            required: true,
            col: { span: 11 },
        },
        {
            label: "薪资待遇：",
            type: SalaryEditField,
            prop: "salaryEditField",
            required: true,
            rules: [
                {
                    validator(rule, value, callback) {
                        if (!value.salary) {
                            return callback(new Error("请选择薪资待遇"))
                        }
                        if (value.salary === "自定义") {
                            if (!value.salary_min || !value.salary_max) {
                                return callback(new Error("请输入自定义薪资"))
                            }
                            if (+value.salary_min > +value.salary_max) {
                                return callback(new Error("需小于最高薪资"))
                            }
                        }
                        callback()
                    },
                },
            ],
            col: {
                span: 11,
                offset: 2,
            },
        },

        {
            type: FormType.Text,
            prop: "salary_min",
            hide: true,
        },
        {
            type: FormType.Text,
            prop: "salary_max",
            hide: true,
        },
        {
            type: FormType.Select,
            prop: "salary",
            hide: true,
        },
        {
            label: "结算方式：",
            prop: "salary_type",
            type: FormType.Select,
            col: { span: 11 },
        },
        {
            label: "工作性质：",
            type: FormType.Select,
            prop: "work_type",
            required: true,
            col: { span: 11, offset: 2 },
        },
        // {
        //     label: "区域类型：",
        //     type: FormType.Select,
        //     prop: "region_type",
        //     defaultValue: id ? undefined : 1,
        //     col: { span: 11 },
        // },
        {
            label: "招聘人数：",
            type: FormType.Text,
            option: { type: "number" },
            prop: "recruit_count",
            col: { span: 11 },
            rules: [
                { required: true, message: "请输入招聘人数" },
                {
                    validator(rule, value, callback) {
                        if (+value < 1) {
                            callback(new Error("招聘人数不能小于1"))
                            return
                        }
                        if (+value > 9999) {
                            callback(new Error("招聘人数不能大于9999"))
                            return
                        }
                        if ((value + "").includes(".")) {
                            callback(new Error("请输入整数"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: "招聘过期时间：",
            type: FormType.DatePicker,
            prop: "expired_date",
            col: { span: 11, offset: 2 },
            option: {
                pickerOptions: expiateTimePickerOptions,
                disabled: !!isAudit,
            },
            rules: expiateTimeRules,
        },
        // {
        //     label: "工作地区：",
        //     type: FormType.Cascader,
        //     option: {
        //         filterable: true,
        //         elProps: { checkStrictly: true },
        //     },
        //     prop: "city",
        //     required: true,
        //     col: { span: 11, offset: 2 },
        // },
        {
            label: "职能：",
            type: FormType.Cascader,
            prop: "function_category",
            option: {
                filterable: true,
                placeholder: "请输入或选择",
            },
            col: { span: 11 },
        },
        {
            prop: "职位福利",
            useTag: "职位福利",
            label: "岗位福利",
            type: FormType.Select2,
            option: {
                multiple: true,
            },
            col: {
                span: 24,
            },
        },
        {
            prop: "职位亮点",
            useTag: "职位亮点",
            label: "职位亮点",
            type: FormType.Select2,
            option: {
                multiple: true,
            },
            col: {
                span: 24,
            },
        },
        {
            label: "岗位描述：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 4,
                resize: "vertical",
                autosize: {
                    minRows: 4,
                    maxRows: 10,
                },
            },
            col: { span: 24 },
            prop: "description",
            rules: [{ required: true, message: "请输入岗位描述" }],
        },
        {
            prop: "lat",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        {
            prop: "lng",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        isDdDev
            ? {
                  label: "所属地：",
                  type: FormType.Cascader,
                  prop: "region_code",
                  option: { elProps: { checkStrictly: true } },
                  required: true,
                  col: { span: 10 },
                  rules: [{ required: true, message: "请选择所属地" }],
              }
            : {
                  label: "",
                  type: FormType.Cascader,
                  prop: "region_code",
                  option: { elProps: { checkStrictly: true } },
                  required: true,
                  col: { span: 0 },
                  hide: true,
              },
        isDdDev
            ? {
                  label: "详细地址：",
                  type: FormType.Text,
                  prop: "address_detail",
                  required: true,
                  col: { span: 12, offset: 2 },
                  rules: [{ required: true, message: "请输入详细地址" }],
              }
            : {
                  prop: "address_detail",
                  label: "",
                  type: FormType.Text,
                  hide: true,
              },
        isDdDev
            ? buildBaiduFormItem({
                  label: "工作地：",
                  prop: "addressEditFiled",
                  col: { span: 24 },
                  options: {
                      placeholder: "请选择地址",
                  },
                  extendData: {
                      from: "create-position",
                  },
                  required: true,
                  rules: [
                      {
                          validator: (_, value, callback) => {
                              if (!get(value, "lng") || !get(value, "lat")) {
                                  callback(
                                      new Error(
                                          "地址经纬度信息缺失，请重新选择地址"
                                      )
                                  )
                                  return
                              }
                              callback()
                          },
                      },
                  ],
              })
            : buildBaiduFormItem({
                  label: "工作地：",
                  prop: "addressEditFiled",
                  col: { span: 24 },
                  options: {
                      placeholder: "请选择地址",
                  },
                  required: true,
                  rules: [
                      {
                          validator: (_, value, callback) => {
                              if (!get(value, "address_detail")) {
                                  callback(new Error("未设置工作地"))
                                  return
                              }
                              if (!get(value, "lng") || !get(value, "lat")) {
                                  callback(
                                      new Error(
                                          "地址经纬度信息缺失，请重新选择地址"
                                      )
                                  )
                                  return
                              }
                              callback()
                          },
                      },
                  ],
              }),
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "任职信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "岗位职责：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 4,
                resize: "vertical",
                autosize: {
                    minRows: 4,
                    maxRows: 10,
                },
            },
            col: { span: 24 },
            prop: "function_detail",
            rules: [{ required: true, message: "请输入岗位职责" }],
        },
        {
            label: "年龄限制：",
            type: FormType.Text,
            prop: "age_require_min",
            option: {
                type: "number",
                placeholder: "最低年龄",
            },
            col: {
                span: 6,
            },
            rules: [
                {
                    required: true,
                    message: "不能为空",
                },
                {
                    validator(rule, value, callback) {
                        age.min = +value
                        if (+age.min < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+age.min > +age.max && age.max !== "") {
                            callback(new Error("需小于最高年龄"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: "",
            type: FormType.Text,
            prop: "age_require_max",
            labelWidth: "25px",
            option: {
                type: "number",
                placeholder: "最高年龄",
            },
            col: {
                span: 5,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        age.max = +value
                        if (+age.max < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+age.min > +age.max) {
                            callback(new Error("需大于最低年龄"))
                            return
                        }
                        callback()
                    },
                    message: "需大于最低年龄",
                },
            ],
        },
        {
            label: "工作年限：",
            type: FormType.Text,
            prop: "experience_min",
            option: {
                type: "number",
                placeholder: "最低年限",
            },
            col: {
                span: 6,
                offset: 2,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        experience.min = +value
                        if (!experience.min || !experience.max) {
                            callback()
                            return
                        }
                        if (+experience.min < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+experience.min > +experience.max) {
                            callback(new Error("需小于最高年限"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: ``,
            type: FormType.Text,
            prop: "experience_max",
            labelWidth: "25px",
            option: {
                type: "number",
                placeholder: "最高年限",
            },
            col: {
                span: 5,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        experience.max = +value
                        if (!experience.min || !experience.max) {
                            callback()
                            return
                        }
                        if (+experience.min > +experience.max) {
                            callback(new Error("需大于最低年限"))
                            return
                        }
                        callback()
                    },
                    message: "需大于最低年限",
                },
            ],
        },
        {
            label: "学历要求：",
            prop: "education",
            type: FormType.Select,
            col: { span: 11, offset: 0 },
        },
        {
            label: "性别要求：",
            prop: "gender_require",
            type: FormType.Select,
            col: getCol(0),
            hide: config.projectConfig.hideGender,
        },
        {
            label: "语言种类：",
            type: FormType.Select,
            prop: "language",
            col: getCol(2),
        },
        {
            label: "外语水平：",
            type: FormType.Select,
            prop: "language_level",
            col: getCol(0),
        },
        {
            label: "户籍所在地：",
            type: FormType.Cascader,
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
            prop: "households",
            col: getCol(2),
        },
        {
            label: "专业要求：",
            type: FormType.Cascader,
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
            prop: "major",
            col: getCol(0),
        },
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位联系信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "联系人：",
            type: FormType.Text,
            prop: "contact_person",
            required: true,
            col: {
                span: 11,
            },
        },
        {
            label: "联系电话：",
            type: FormType.Text,
            prop: "contact_mobile",
            required: true,
            col: {
                span: 11,
                offset: 2,
            },
            // rules: [...rules.mobile],
        },
        // {
        //     label: "岗位来源：",
        //     type: FormType.Select,
        //     prop: "source_from_type",
        //     required: true,
        // },
        // {
        //     type: FormType.Tip,
        //     prop: "t",
        //     label: "",
        //     defaultValue: "789",
        //     option: {
        //         placeholder: "如果此岗位有站外链接，请添加站外链接的来源与地址",
        //     },
        //     itemStyle: {
        //         color: "#7998B8",
        //         marginLeft: "-63px",
        //     },
        // },
        // {
        //     label: "岗位链接：",
        //     type: FormType.Text,
        //     prop: "page_url",
        //     col: { span: 11 },
        // },
        {
            label: "联系人邮箱：",
            type: FormType.Text,
            prop: "contact_email",
            col: { span: 11 },
        },
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位投递要求",
            },
            itemStyle,
            prop: "t3",
            hide: isEz,
        },
        {
            label: "附件简历是否必填：",
            type: FormType.Radio,
            prop: "require_profile",
            required: true,
            col: { span: 11 },
            option: {
                type: "boolean",
            },
            labelWidth: "140px",
            sourceInputsParameter: buildSelectSource([
                {
                    value: "是",
                    key: "1",
                },
                {
                    value: "否",
                    key: "0",
                },
            ]),
            hide: isEz,
        },
        {
            label: "工作经历是否必填：",
            type: FormType.Radio,
            prop: "require_work",
            required: true,
            col: { span: 11, offset: 2 },
            option: {
                type: "boolean",
            },
            labelWidth: "140px",
            sourceInputsParameter: buildSelectSource([
                {
                    value: "是",
                    key: "1",
                },
                {
                    value: "否",
                    key: "0",
                },
            ]),
            hide: isEz,
        },
        {
            label: "学历信息是否必填：",
            type: FormType.Radio,
            prop: "require_education",
            required: true,
            col: { span: 11 },
            option: {
                type: "boolean",
            },
            labelWidth: "140px",
            sourceInputsParameter: buildSelectSource([
                {
                    value: "是",
                    key: "1",
                },
                {
                    value: "否",
                    key: "0",
                },
            ]),
            hide: isEz,
        },
        {
            prop: "create_type",
            type: FormType.Text,
            defaultValue: "1",
            hide: true,
        },
    ]
    if (!id || isCopy) {
        forms.unshift({
            type: FormType.Tip,
            prop: "empty",
            label: "",
            labelWidth: "0px",
            option: { placeholder: "" },
            col: { span: 11, offset: 2 },
        })
        forms.unshift({
            label: "所属企业：",
            type: FormType.IntentSearch,
            prop: "agent_id",
            required: true,
            option: {
                dialogProp: {
                    width: "1000px",
                },
                intentSearchConfig: {
                    tableConfig: () => ({
                        model: sdk.core
                            .model("xg_agent")
                            .list("intent_search_list"),
                        // preFilter: h(["project_id"]),
                        preFilter: undefined,
                        predict: intentPredict,
                        column: intentColumn(),
                        filter: intentFilter(),
                    }),
                    template: "{agent_name}",
                    valueKey: "agent_id",
                },
            },
            col: {
                span: 11,
            },
            rules: [{ required: true, message: "请选择所属企业" }],
        })
    }
    return {
        forms,
        sdkModel: "xg_company_position",
        sdkAction: id ? "update_position" : "create_position",
        id,
    }
}

export const createPositionFormSections2 = (
    id?: string,
    isAudit?: number,
    isCopy?: string
): BuildFormConfig => {
    const forms: BuildFormConfig["forms"] = [
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位基本信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "岗位名称：",
            type: FormType.Text,
            prop: "name",
            required: true,
            col: { span: 11 },
        },
        {
            label: "薪资待遇",
            type: FormType.Text,
            option: {
                type: "number",
            },
            prop: "welfare_salary",
            required: true,
            col: { span: 11, offset: 2 },
            rules: [
                {
                    validator(rule, value, callback) {
                        if (!value) {
                            callback(new Error("请输入薪资待遇"))
                        } else if (!+value || value.toString().includes(".")) {
                            callback(new Error("请输入正整数"))
                        }
                        callback()
                    },
                },
            ],
        },
        // {
        //     label: "薪资待遇：",
        //     type: SalaryEditField2,
        //     prop: "salaryEditField",
        //     required: true,
        //     rules: [
        //         {
        //             validator(rule, value, callback) {
        //                 if (!value.salary_min) {
        //                     return callback(new Error("请输入最小薪资"))
        //                 }
        //                 if (!value.salary_max) {
        //                     return callback(new Error("请输入最大薪资"))
        //                 }
        //                 if (+value.salary_min > +value.salary_max) {
        //                     return callback(new Error("需小于最高薪资"))
        //                 }

        //                 callback()
        //             },
        //         },
        //     ],
        //     col: {
        //         span: 11,
        //         offset: 2,
        //     },
        // },

        {
            type: FormType.Text,
            prop: "salary_min",
            hide: true,
        },
        {
            type: FormType.Text,
            prop: "salary_max",
            hide: true,
        },
        {
            type: FormType.Select,
            prop: "salary",
            hide: true,
        },
        {
            label: "结算方式：",
            prop: "salary_type",
            type: FormType.Select,
            col: { span: 11 },
        },
        {
            label: "工作性质：",
            type: FormType.Select,
            prop: "work_type",
            required: true,
            col: { span: 11, offset: 2 },
        },
        // {
        //     label: "区域类型：",
        //     type: FormType.Select,
        //     prop: "region_type",
        //     defaultValue: id ? undefined : 1,
        //     col: { span: 11 },
        // },
        {
            label: "招聘人数：",
            type: FormType.Text,
            option: { type: "number" },
            prop: "recruit_count",
            required: true,
            col: { span: 11 },
            rules: [
                {
                    validator(rule, value, callback) {
                        if (+value < 1) {
                            callback(new Error("招聘人数不能小于1"))
                            return
                        }
                        if (+value > 9999) {
                            callback(new Error("招聘人数不能大于9999"))
                            return
                        }
                        if ((value + "").includes(".")) {
                            callback(new Error("请输入整数"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: "招聘过期时间：",
            type: FormType.DatePicker,
            prop: "expired_date",
            col: { span: 11, offset: 2 },
            option: {
                // disabled: !!id,
                pickerOptions: expiateTimePickerOptions,
                disabled: !!isAudit,
            },
            rules: expiateTimeRules,
        },
        // {
        //     label: "工作地区：",
        //     type: FormType.Cascader,
        //     option: {
        //         filterable: true,
        //         elProps: { checkStrictly: true },
        //     },
        //     prop: "city",
        //     required: true,
        //     col: { span: 11, offset: 2 },
        // },
        {
            label: "工种：",
            type: FormType.Select,
            prop: "industry",
            option: {
                filterable: true,
            },
            col: { span: 11 },
        },
        {
            prop: "职位福利",
            useTag: "职位福利",
            label: "岗位福利",
            type: FormType.Select2,
            option: {
                multiple: true,
            },
            col: {
                span: 24,
            },
        },
        {
            prop: "职位亮点",
            useTag: "职位亮点",
            label: "职位亮点",
            type: FormType.Select2,
            option: {
                multiple: true,
            },
            col: {
                span: 24,
            },
        },
        {
            label: "岗位描述：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 4,
                resize: "vertical",
                autosize: {
                    minRows: 4,
                    maxRows: 10,
                },
            },
            col: { span: 24 },
            prop: "description",
            rules: [{ required: true, message: "请输入岗位描述" }],
        },
        {
            prop: "lat",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        {
            prop: "lng",
            label: "",
            type: FormType.Text,
            hide: true,
        },
        isDdDev
            ? {
                  label: "所属地：",
                  type: FormType.Cascader,
                  prop: "region_code",
                  option: { elProps: { checkStrictly: true } },
                  required: true,
                  col: { span: 10 },
                  rules: [{ required: true, message: "请选择所属地" }],
              }
            : {
                  label: "",
                  type: FormType.Cascader,
                  prop: "region_code",
                  option: { elProps: { checkStrictly: true } },
                  required: true,
                  col: { span: 0 },
                  hide: true,
              },
        isDdDev
            ? {
                  label: "详细地址：",
                  type: FormType.Text,
                  prop: "address_detail",
                  required: true,
                  col: { span: 12, offset: 2 },
                  rules: [{ required: true, message: "请输入详细地址" }],
              }
            : {
                  prop: "address_detail",
                  label: "",
                  type: FormType.Text,
                  hide: true,
              },
        isDdDev
            ? buildBaiduFormItem({
                  label: "工作地：",
                  prop: "addressEditFiled",
                  col: { span: 24 },
                  options: {
                      placeholder: "请选择地址",
                  } as any,
                  extendData: {
                      from: "create-position",
                  },
                  rules: [
                      {
                          validator: (_, value, callback) => {
                              if (!get(value, "address_detail")) {
                                  callback(new Error("未设置工作地"))
                                  return
                              }
                              if (!get(value, "lng") || !get(value, "lat")) {
                                  callback(
                                      new Error(
                                          "地址经纬度信息缺失，请重新选择地址"
                                      )
                                  )
                                  return
                              }
                              callback()
                          },
                      },
                  ],
              })
            : buildBaiduFormItem({
                  label: "工作地：",
                  prop: "addressEditFiled",
                  col: { span: 24 },
                  options: {
                      placeholder: "请选择地址",
                  },
                  required: true,
                  rules: [
                      {
                          validator: (_, value, callback) => {
                              if (!get(value, "address_detail")) {
                                  callback(new Error("未设置工作地"))
                                  return
                              }
                              if (!get(value, "lng") || !get(value, "lat")) {
                                  callback(
                                      new Error(
                                          "地址经纬度信息缺失，请重新选择地址"
                                      )
                                  )
                                  return
                              }
                              callback()
                          },
                      },
                  ],
              }),
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "任职信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "岗位职责：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 4,
                resize: "vertical",
                autosize: {
                    minRows: 4,
                    maxRows: 10,
                },
            },
            col: { span: 24 },
            prop: "function_detail",
            rules: [{ message: "请输入岗位职责" }],
        },
        {
            label: "年龄限制：",
            type: FormType.Text,
            prop: "age_require_min",
            option: {
                type: "number",
                placeholder: "最低年龄",
            },
            col: {
                span: 6,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        age.min = +value
                        if (+age.min < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+age.min > +age.max && age.max !== "") {
                            callback(new Error("需小于最高年龄"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: "",
            type: FormType.Text,
            prop: "age_require_max",
            labelWidth: "25px",
            option: {
                type: "number",
                placeholder: "最高年龄",
            },
            col: {
                span: 5,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        age.max = +value
                        if (+age.max < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+age.min > +age.max) {
                            callback(new Error("需大于最低年龄"))
                            return
                        }
                        callback()
                    },
                    message: "需大于最低年龄",
                },
            ],
        },
        {
            label: "工作年限：",
            type: FormType.Text,
            prop: "experience_min",
            option: {
                type: "number",
                placeholder: "最低年限",
            },
            col: {
                span: 6,
                offset: 2,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        experience.min = +value
                        if (!experience.min || !experience.max) {
                            callback()
                            return
                        }
                        if (+experience.min < 0) {
                            callback(new Error("不能小于0"))
                            return
                        }
                        if (+experience.min > +experience.max) {
                            callback(new Error("需小于最高年限"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: ``,
            type: FormType.Text,
            prop: "experience_max",
            labelWidth: "25px",
            option: {
                type: "number",
                placeholder: "最高年限",
            },
            col: {
                span: 5,
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        experience.max = +value
                        if (!experience.min || !experience.max) {
                            callback()
                            return
                        }
                        if (+experience.min > +experience.max) {
                            callback(new Error("需大于最低年限"))
                            return
                        }
                        callback()
                    },
                    message: "需大于最低年限",
                },
            ],
        },

        {
            label: "学历要求：",
            prop: "education",
            type: FormType.Select,
            col: { span: 11, offset: 0 },
        },
        {
            label: "性别要求：",
            prop: "gender_require",
            type: FormType.Select,
            col: { span: 11, offset: 2 },
            hide: config.projectConfig.hideGender,
        },
        {
            label: "语言种类：",
            type: FormType.Select,
            prop: "language",
            col: { span: 11, offset: 0 },
        },
        {
            label: "外语水平：",
            type: FormType.Select,
            prop: "language_level",
            col: { span: 11, offset: 2 },
        },
        {
            label: "户籍所在地：",
            type: FormType.Cascader,
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
            prop: "households",
            col: { span: 11, offset: 0 },
        },
        {
            label: "专业要求：",
            type: FormType.Cascader,
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
            prop: "major",
            col: { span: 11, offset: 2 },
        },
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "岗位联系信息",
            },
            itemStyle,
            prop: "t3",
        },
        {
            label: "联系人：",
            type: FormType.Text,
            prop: "contact_person",
            required: true,
            col: {
                span: 11,
            },
        },
        {
            label: "联系电话：",
            type: FormType.Text,
            prop: "contact_mobile",
            required: true,
            col: {
                span: 11,
                offset: 2,
            },
        },
        {
            label: "联系人邮箱：",
            type: FormType.Text,
            prop: "contact_email",
            col: { span: 11 },
        },
        {
            prop: "create_type",
            type: FormType.Text,
            defaultValue: "1",
            hide: true,
        },
    ]
    if (!id || isCopy) {
        forms.unshift({
            type: FormType.Tip,
            prop: "empty",
            label: "",
            labelWidth: "0px",
            option: { placeholder: "" },
            col: { span: 11, offset: 2 },
        })
        forms.unshift({
            label: "所属单位：",
            type: FormType.IntentSearch,
            prop: "agent_id",
            required: true,
            option: {
                dialogProp: {
                    width: "1000px",
                },
                intentSearchConfig: {
                    tableConfig: () => ({
                        model: sdk.core
                            .model("xg_agent")
                            .list("intent_search_list"),
                        // preFilter: h(["project_id"]),
                        preFilter: {
                            is_welfare_position: "1",
                        },
                        predict: {
                            agent_name: "",
                            company_code: "",
                            legal_person: "contact_person",
                            agent_id: "id",
                            is_create: "label",
                        },
                        column: intentColumn(true),
                        filter: intentFilter(true),
                    }),
                    template: "{agent_name}",
                    valueKey: "agent_id",
                },
            },
            col: {
                span: 11,
            },
            rules: [{ required: true, message: "请选择所属单位" }],
        })
    }
    return {
        forms,
        sdkModel: "xg_company_position",
        sdkAction: id ? "update_position_welfare" : "create_public_welfare",
        id: id ? +id : undefined,
    }
}

export async function createForm(
    id: string | string[],
    type: string,
    name: string
): Promise<BuildFormConfig> {
    const label = {
        1: "人才要求说明",
        2: "推广描述",
        3: "推广描述",
        4: "合作描述",
    }[type]
    const action = "back_recommend_apply_" + type
    const hide = !["2", "3"].includes(type)
    const isBatch = isArray(id)
    const forms: BuildFormConfig["forms"] = [
        {
            label: "服务类型：",
            type: FormType.Select,
            prop: "type1",
            defaultValue: type,
            required: true,
            sourceInputsParameter: buildSelectSource(
                // [
                //     {
                //         key: "1",
                //         value: "人才推荐服务",
                //     },
                // ]
                // {
                //     key: "2",
                //     value: "社群推广服务",
                // },
                // {
                //     key: "3",
                //     value: "网格推广服务",
                // },[]
                // [
                //     {
                //         key: "4",
                //         value: "人力资源机构撮合服务",
                //     },
                // ]
                await sysConfigService.data.position_recommend_type_mapping
            ),
        },
        { prop: "type", defaultValue: type },
        {
            label: "推广时间：",
            type: FormType.DatePicker,
            prop: "recommend_start_time",
            option: { type: "date" },
            col: {
                span: 14,
            },
            hide,
        },
        {
            label: "",
            type: FormType.DatePicker,
            labelWidth: "0px",
            prop: "recommend_end_time",
            option: { type: "date" },
            col: {
                span: 10,
            },
            hide,
        },
        {
            label: "推广区域：",
            type: FormType.MultipleCascader,
            prop: "recommend_region",
            required: true,
            option: {
                elProps: { checkStrictly: true, multiple: true },
            },
            hide,
            rules: [
                {
                    required: true,
                    message: "请选择推广区域",
                },
                {
                    validator(rule, value, callback) {
                        if (value.length > 5) {
                            callback(new Error("最多选择五项"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: label + "：",
            type: FormType.Text,
            option: { type: "textarea" },
            prop: "description",
            rules: [
                {
                    required: true,
                    message: "请输入" + label,
                },
            ],
            required: true,
        },
        {
            type: FormType.Text,
            prop: "contact_person",
            required: true,
            hide: true,
        },
        {
            type: FormType.Text,
            prop: "contact_mobile",
            required: true,
            hide: true,
        },
    ]
    if (!isBatch) {
        forms.unshift({
            label: "关联岗位：",
            prop: "",
            type: FormType.Text,
            defaultValue: name,
            option: { disabled: true },
        })
    }
    return {
        sdkModel: "xg_company_position",
        sdkAction: isBatch ? "batch_recommend_apply" : action,
        id: isBatch ? id[0] : id,
        forms,
    }
}

export function createOnlineFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_company_position",
        sdkAction: "online",
        id,
        forms: [
            {
                label: "招聘过期时间：",
                type: FormType.DatePicker,
                prop: "expired_date",
                option: {
                    pickerOptions: expiateTimePickerOptions,
                    disabled: true,
                },
                // rules: expiateTimeRules,
            },
        ],
    }
}

const tableFilter2: TableFilter[] = [
    {
        label: "投递类别",
        type: FormType.Select,
        prop: "created_from",
    },
    {
        label: "投递时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "求职者",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
        prop: "name",
    },
]

export const tableConfig2 = (position_id: string): TableConfig => ({
    model: sdk.core.model("xg_candidate_order").list("position_order_list"),
    defaultPageSize: 10,
    preFilter: { position_id },
    filter: tableFilter2,
    predict: {
        name: "",
        mobile_encode: "",
        sex: "label",
        age: "",
        education: "label",
        create_time: "label",
        created_from: "label",
        status: "",
    },
    oneTabFilter: true,
})

export const columns2: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "电话",
        prop: "mobile_encode",
        showOverflowTip: true,
        align: "left",
    },
    {
        label: "性别",
        prop: "sex_label",
        showOverflowTip: true,
    },
    {
        label: "学历",
        prop: "education_label",
        showOverflowTip: true,
    },
    {
        label: "报名时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    // {
    //     label: "年龄",
    //     prop: "age",
    //     showOverflowTip: true,
    // },
    {
        label: "投递类别",
        prop: "created_from_label",
        showOverflowTip: true,
    },
    // {
    //     label: "投递方式",
    //     prop: "投递方式",
    //     showOverflowTip: true,
    // },
    {
        label: "操作",
        prop: "h",
    },
]

export interface Row2 {
    /** 姓名 */
    name: string

    /** 手机号 */
    mobile_encode: string

    /** 性别 */
    sex: string

    /** 年龄 */
    age: string

    /** 学历 */
    education: string

    /** 报名时间 */
    create_time: string

    /** 投递类别 */
    created_from: string

    /** 状态 */
    status: string
    id: number
    v: number
}

export const predict3 = {
    create_time: "label",
    name: "system_user_references#info#name",
    mobile: "system_user_references#mobile",
    action_name: "",
    invoker_id: "",
    invoker_action_name: "",
}
export function tableConfig3(obj_id: string): TableConfig {
    return {
        model: sdk.core.model("work_flow_log@xg_project").list("detail_log"),
        defaultPageSize: 10,
        predict: predict3,
        preFilter: {
            obj_id,
            model_name: "xg_company_position",
        },
    }
}

export const columns3: TableColumn[] = [
    { label: "用户姓名", prop: "name", align: "left", showOverflowTip: true },
    {
        label: "用户手机号",
        prop: "mobile",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "动作",
        prop: "action_name",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "操作时间",
        prop: "create_time_label",
        align: "left",
        showOverflowTip: true,
    },
]
