import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import router from "@/router"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { filter, flatMap, map } from "lodash"
import { TagManagerTypes } from "uniplat-sdk"
import { VNode } from "vue"

const tableFilter: TableFilter[] = [
    {
        label: "用户信息",
        type: FormType.Text,
        prop: "name",
    },
    {
        label: "所在城市",
        type: FormType.MultipleCascader,
        prop: "permanent_province_code",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "服务内容",
        type: FormType.Select,
        prop: "serve_type",
    },
    {
        label: "就业状态",
        type: FormType.Select,
        prop: "employment_status",
        option: {
            multiple: true,
        },
    },
    {
        label: "服务时间",
        type: FormType.DatePicker,
        prop: "ddd",
        option: {
            type: "daterange",
        },
    },
    {
        label: "标签",
        type: FormType.MultipleCascader,
        prop: "label_nos",
        option: {
            filterable: true,
            collapseTags: true,
            elProps: {
                // multiple: true,
                checkStrictly: true,
            },
        },
    },
]

const rowPredict = {
    userId: "user_profile#id",
    profile_access_key: "user_profile#_access_key",
    name: "user_profile#name",
    sex: "user_profile#sex_label",
    birth_date: "user_profile#birth_date",
    mobile_hide: "user_profile#mobile_hide",
    idcard_hide: "user_profile#id_card_hide",
    region_name: "user_profile#basic_info#permanent_city#region_name",
    employment_status:
        "user_profile#user_profile_current_job_info#employment_status_label",
    province_name: "user_profile#basic_info#permanent_province#region_name",
    city_name: "user_profile#basic_info#permanent_city#region_name",
    update_time: "user_profile#update_time",
    tags: "tags",
    served_tasks: "",
}

export const taskRowPredict = {
    userId: "task_serve_target#user_profile#id",
    profile_access_key: "task_serve_target#user_profile#_access_key",
    name: "task_serve_target#user_profile#name",
    sex: "task_serve_target#user_profile#sex_label",
    birth_date: "task_serve_target#user_profile#birth_date",
    mobile_hide: "task_serve_target#user_profile#mobile_hide",
    idcard_hide: "task_serve_target#user_profile#id_card_hide",
    region_name:
        "task_serve_target#user_profile#basic_info#permanent_city#region_name",
    employment_status:
        "task_serve_target#user_profile#user_profile_current_job_info#employment_status_label",
    province_name:
        "task_serve_target#user_profile#basic_info#permanent_province#region_name",
    city_name:
        "task_serve_target#user_profile#basic_info#permanent_city#region_name",
    update_time: "task_serve_target#user_profile#update_time",
    tags: "tags",
    served_content_list: "",
}

export const columns: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        width: "70",
        showOverflowTip: true,
    },
    {
        label: "性别",
        width: "50",
        align: "center",
        prop: "sex_label",
        showOverflowTip: true,
    },
    {
        label: "身份证号",
        prop: "idcard_hide",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile_hide",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "已服务任务",
        prop: "served_tasks",
        width: "180",
        align: "center",
        render: (h, row: Row) => {
            return h(
                "div",
                map(row.served_tasks, (item, index) => {
                    const node: VNode[] = []
                    if (index) {
                        node.push(h("span", {}, "，"))
                    }
                    node.push(
                        h(
                            "span",
                            {
                                class: "primary pointer",
                                on: {
                                    click: () => {
                                        router.push({
                                            name: routesMap.groupService
                                                .serviceManageDetail.task,
                                            query: {
                                                id: item.id + "",
                                                from: routesMap.groupService
                                                    .serviceManageDetail.result
                                                    .served_target_count,
                                            },
                                        })
                                    },
                                },
                            },
                            `${item.title}`
                        )
                    )
                    return node
                })
            )
        },
    },
    {
        label: "已服务内容",
        prop: "served_content_list",
        width: "180",
        align: "center",
        render: (h, row: Row) => {
            return h(
                "div",
                map(row.served_tasks, (item, index) => {
                    const node: VNode[] = []
                    if (index) {
                        node.push(h("span", {}, "，"))
                    }
                    node.push(
                        h(
                            "span",
                            {
                                class: "primary pointer",
                                on: {
                                    click: () => {
                                        router.push({
                                            name: routesMap.groupService
                                                .serviceManageDetail.item,
                                            query: {
                                                id: item.id + "",
                                            },
                                        })
                                    },
                                },
                            },
                            `${item.title}`
                        )
                    )
                    return node
                })
            )
        },
    },
    {
        label: "当前城市",
        prop: "address",
        width: "150",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.province_name, row.city_name].filter(Boolean).join("")
        },
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "更新时间",
        prop: "update_time",
        width: "180",
        align: "center",
        render: (h, row) => {
            return h("div", [
                h("div", formatTime.day(row.update_time)),
                h("div", formatTime.default(row.update_time, "HH:mm")),
            ])
        },
    },
    {
        label: "标签",
        prop: "tags",
        minWidth: "100",
        showOverflowTip: true,
        formatter: (row: Row) => {
            return flatMap(row.tags, (item) =>
                flatMap(item, (i) => i.tagName)
            ).join("，")
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export function tableConfig(preFilter: {
    project_id?: string
    task_id?: string
    pc_id?: string
}): TableConfig {
    const model = preFilter.project_id
        ? "task_served_target_for_project"
        : preFilter.pc_id
        ? "task_served_target_for_content"
        : "task_serve_record"
    const list = preFilter.project_id
        ? "list_for_project"
        : preFilter.pc_id
        ? "list_for_content"
        : "served_target_for_object"
    const column = filter(columns, (item) => {
        if (preFilter.task_id) {
            return item.prop !== "served_tasks"
        } else {
            return item.prop !== "served_content_list"
        }
    })
    return {
        model: sdk.core.model(model).list(list),
        filter: tableFilter,
        defaultPageSize: 8,
        predict: preFilter.task_id ? taskRowPredict : rowPredict,
        preFilter,
        column,
    }
}

export interface Row {
    userId: string
    name: string
    sex_label: string
    birth_date: string
    mobile_hide: string
    idcard_hide: string
    region_name: number
    employment_status_label: string
    province_name: string
    city_name: string
    update_time: string
    tags: { [key: string]: TagManagerTypes.TagInfo[] }
    served_tasks: {
        id: number
        title: string
    }[]
    served_content_list: {
        id: number
        title: string
    }[]
    id: number
    v: number
    profile_access_key: string
}
