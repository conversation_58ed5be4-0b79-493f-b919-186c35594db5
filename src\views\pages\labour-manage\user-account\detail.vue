<template>
    <div class="core-ui-table-container" :key="refreshQueryParams" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div class="u-flex">
                <el-button
                    type="primary"
                    @click="showDelete = true"
                    v-role="'model.user_account.action.delete_user'"
                >
                    注销
                </el-button>

                <el-button
                    type="primary"
                    @click="showChange = true"
                    v-role="'model.user_account.action.replace_user_mobile'"
                >
                    更换手机号
                </el-button>
            </div>
        </div>
        <template>
            <detail-view :row="row"></detail-view>
            <list-view
                :id="row.uniplat_uid"
                v-show="row.is_verified"
            ></list-view>
            <!-- 暂时不展示 -->
            <WeiChatSubScribe
                v-if="row.unionIds && showSubMessage"
                :unionIds="row.unionIds"
            />
        </template>
        <CommonPop
            v-model="showDelete"
            :id="id"
            :labelWidth="'0'"
            title="注销"
            sdkModel="user_account"
            sdkAction="delete_user"
            :afterBuildFormSections="afterBuildFormSections"
        />
        <CommonPop
            v-model="showChange"
            :id="id"
            title="更换手机号"
            sdkModel="user_account"
            sdkAction="replace_user_mobile"
        />
    </div>
</template>

<script lang='ts'>
    import { config, EnvProject } from "@/config"
    import { buildFormSections } from "@/core-ui/component/form"
    import { ReturnPromiseType } from "@/core-ui/helpers/types"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { predict, Row } from "."
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailView from "./components/detail-view.vue"
    import ListView from "./components/list-view.vue"
    import WeiChatSubScribe from "./components/weichat-subscribe.vue"

    @Component({
        name: routesMap.labourManage.userAccountDetail,
        components: { DetailView, ListView, WeiChatSubScribe, CommonPop },
    })
    export default class UserAccountDetail extends Vue {
        private row: Row | null = null
        id = ""
        showDelete = false
        showChange = false
        showSubMessage = [EnvProject.孝感项目, EnvProject.荆州项目].includes(
            config.envProject
        )

        afterBuildFormSections(e: ReturnPromiseType<typeof buildFormSections>) {
            const d = e.forms.find((i) => i.prop === "memo")
            if (d) {
                d.itemStyle = {
                    color: "red",
                }
            }
            return e
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.labourManage.userAccountDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            const d = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: "注册居民详情",
                    to: {
                        name: routesMap.labourManage.userAccountDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.labourManage.userAccountDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string
        }

        mounted() {
            this.init()
        }

        private init() {
            const id = (this.$route.query.id as string) || this.id
            if (!id) return
            this.id = id
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("user_account")
                    .detail(this.id, "manage")
                    .query()
                    .then((r) => {
                        this.row = {
                            ...sdk.buildRow<Row>(r.row, predict),
                            ...(r.row.object_data || {}),
                        } as unknown as Row
                    })
            })
        }

        private audit() {}
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
