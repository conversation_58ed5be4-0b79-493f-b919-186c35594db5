import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    // {
    //     label: "所在城市",
    //     type: FormType.Cascader,
    //     prop: "city1",
    // },
    // {
    //     label: "工作城市",
    //     type: FormType.Cascader,
    //     prop: "city2",
    // },
    // {
    //     label: "就业状态",
    //     type: FormType.Select,
    //     prop: "status",
    // },
    // {
    //     label: "学历",
    //     type: FormType.Select,
    //     prop: "edu",
    // },
    {
        label: "姓名",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    // {
    //     label: "年龄",
    //     type: FormType.TextRange,
    //     option: {
    //         type: "number",
    //     },
    //     prop: "age",
    // },
    // {
    //     label: "标签",
    //     type: FormType.Select,
    //     prop: "tag",
    //     useTag: "福利标签组",
    //     option: {
    //         filterable: true,
    //         multiple: true,
    //         collapseTags: true,
    //     },
    // },
    // {
    //     label: "当前工作类型",
    //     type: FormType.Select,
    //     prop: "t",
    // },
    // {
    //     label: "毕业院校",
    //     type: FormType.Text,
    //     prop: "school",
    // },
    // {
    //     label: "推荐结果",
    //     type: FormType.Select,
    //     prop: "re",
    // },
]
export const tableConfig = (created_from_id: string): TableConfig => ({
    model: sdk.core.model("xg_candidate_order").list("recommend_profile_list"),
    filter: tableFilter,
    defaultPageSize: 10,
    preFilter: { created_from_id },
    predict: {
        name: "",
        sex: "label",
        age: "",
        id_card: "",
        id_card_encode: "",
        mobile: "",
        mobile_encode: "",
        user_profile_basic_id: "profile#id",
        profile_access_key: "profile#_access_key",
        province: "permanent_province#region_name",
        city: "permanent_city#region_name",
        area: "permanent_area#region_name",
        update_time: "label",
        permanent_detail: "label",
        employment_status: "employment_status_label",
        status1: "label",
        showMobile: false,
    },
    tabPages: ["全部", "未联系企业", "已联系企业"],
    oneTabFilter: true,
})
export const columns: TableColumn[] = [
    {
        prop: "select",
        type: "selection",
        selectable(row: any) {
            return row.status1_label === "待审核"
        },
    },
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "性别",
        prop: "sex_label",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "年龄",
        prop: "age",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "身份证号",
        prop: "id_card_encode",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile_encode",
        width: "180px",
        showOverflowTip: true,
    },
    {
        label: "当前城市",
        prop: "permanent_detail",
        showOverflowTip: true,
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        showOverflowTip: true,
    },
    {
        label: "更新时间",
        prop: "update_time_label",
        showOverflowTip: true,
    },
    {
        label: "标签",
        prop: "tag",
        showOverflowTip: true,
    },
    // {
    //     label: "推荐结果",
    //     prop: "result",
    //     showOverflowTip: true,
    // },
    {
        label: "联系状态",
        prop: "status1_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "90px",
        showOverflowTip: true,
    },
]

export interface Row {
    id: number
    name: string
    sex: string
    age: string
    id_card: string
    mobile: string
    region_name: string
    update_time: string
    user_profile_basic_id: string
    profile_access_key: string
}
