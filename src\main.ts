import "@/css/base.scss"
import "@/css/index.less"
import "@/installer"
import store from "@/store"
import Breadcrumb from "@/views/components/breadcrumb/index.vue"
import { Switch as ElSwitch } from "element-ui"
import moment from "moment"
import { enablePayloadEncode, set } from "uniplat-sdk/build/main/helpers/crypto"
import Vue from "vue"
import App from "./app-view.vue"
import { antiDebugger } from "./core-ui/controller/debugger"
import { getUniplatKey } from "./encrypt"
import router from "./router"
const key = getUniplatKey()
// 开启反参加密
if (process.env.VUE_APP_responseEncoder && key) {
    antiDebugger()
    set(key)
    // 开启入参加密
    process.env.VUE_APP_requestEncoder && enablePayloadEncode()
}

Vue.use(ElSwitch)
Vue.component("bread-crumb", Breadcrumb)
moment.locale("zh-cn")

Vue.config.productionTip = false

export const app = new Vue({
    router,
    store,
    render: (h) => h(App),
}).$mount("#app")
