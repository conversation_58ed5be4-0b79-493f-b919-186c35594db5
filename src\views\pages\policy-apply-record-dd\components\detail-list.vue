<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :showExpand="false"
        >
            <div slot="table" slot-scope="{ data, index }">
                <div class="u-flex u-row-right u-m-b-10">
                    <el-button
                        v-if="detail.apply_status === 1"
                        size="mini"
                        type="primary"
                        @click="batchAudit"
                    >
                        批量核查
                    </el-button>
                </div>
                <common-table
                    :data="data"
                    :columns="tableConfig.column"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="序号" slot-scope="scope">
                        {{ (index - 1) * 10 + scope.index + 1 }}
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <template v-if="scope.row.status === status.通过">
                            <el-button
                                type="text"
                                @click="cancel(scope.row)"
                                v-if="
                                    scope.row.audit_status !== 5 &&
                                    detail.apply_status >= 2 &&
                                    detail.apply_status < 4 &&
                                    detail.apply_status !== 3
                                "
                            >
                                取消
                            </el-button>
                        </template>
                        <el-button
                            v-if="+scope.row.status === 0"
                            type="text"
                            @click="audit(scope.row)"
                        >
                            核查
                        </el-button>
                        <!-- 已完善 -->
                        <el-button
                            type="text"
                            v-if="scope.row.audit_status === 2"
                            @click="toDetail(scope.row)"
                        >
                            去审核
                        </el-button>
                        <el-button
                            v-else
                            type="text"
                            @click="toDetail(scope.row)"
                        >
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <common-pop
            title="核查"
            v-model="showPop"
            sdkModel="zc_apply_personal"
            sdkAction="audit"
            :id="rowId"
            @refresh="reloadList(false)"
            :prefilters="[
                {
                    property: 'apply_company_id',
                    value: detail.apply_model_id,
                },
            ]"
        >
        </common-pop>
        <common-pop
            title="批量核查"
            v-model="showBatchPop"
            sdkModel="zc_apply_personal"
            sdkAction="batch_audit"
            :id="ids"
            :isBatch="true"
            @refresh="reloadList(false)"
            :prefilters="[
                {
                    property: 'apply_company_id',
                    value: detail.apply_model_id,
                },
            ]"
        >
        </common-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { MessageBox } from "element-ui"
    import { find, get, includes } from "lodash"
    import { Component, Prop, Vue } from "vue-property-decorator"
    enum Status {
        待核查,
        通过,
        不通过,
    }
    @Component({ components: { TableContainer, CommonTable, CommonPop } })
    export default class Template extends BaseTableController<any> {
        @Prop()
        private detail: any

        tableConfig: TableConfig | null = null
        private status = Status

        showPop = false
        showBatchPop = false
        mounted() {
            const r: any = {
                qiyexinajiuye: "company_apply_list_qyxnjy",
                jiuyeshehuobutie: "manage_company_personal_list_jysh",
                jiuyeshehuobutie_bk: "manage_company_personal_list_jysh",
            }
            return buildConfig4RemoteMeta(
                "zc_apply_personal",
                r[this.detail.no] || "manage_company_personal_list",
                {
                    useLabelWidth: true,
                    disabledOpt: true,
                    prefilters: {
                        apply_company_id: this.detail.apply_model_id,
                        // obj_id: this.obj_id,
                    },
                    predicts: {
                        status: "label",
                        audit_status: "label",
                        field_groups: "",
                    },
                }
            ).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
                tableConfig.column = [
                    {
                        type: "selection",
                        prop: "select",
                        selectable(row: any) {
                            return row.status === 0
                        },
                    },
                    ...r.columns,
                    {
                        label: "操作",
                        prop: "h",
                        fixed: "right",
                        width: "200px",
                    },
                ].map((e) => {
                    if (["完善进度", "完善状态"].includes(e.label)) {
                        return {
                            ...e,
                            formatter: (row: any) => {
                                return (
                                    find(get(row, "field_groups", []), (e) =>
                                        e.includes("/")
                                    ) || "0/0"
                                )
                            },
                            width: "200px",
                        }
                    }
                    // if (e.label === "审核状态") {
                    //     return {
                    //         ...e,
                    //         formatter: (row: any) => {
                    //             return get(row, "field_groups.10", "--")
                    //         },
                    //         width: "200px",
                    //     }
                    // }
                    return e
                })
            })
        }

        private ids: string[] = []
        handleSelectionChange(d: any) {
            this.ids = d.ids
        }

        audit(row: any) {
            this.rowId = row.id
            this.showPop = true
        }

        toDetail(row: any) {
            this.$router.push({
                name: routesMap.policyApplyRecordDD.audit,
                query: {
                    id: row._access_key + "",
                    apply_company_id: this.detail.apply_model_id,
                    from: this.$route.name,
                },
            })
        }

        batchAudit() {
            if (!this.ids.length) {
                return this.$message.warning("请选择数据")
            }
            this.showBatchPop = true
        }

        cancel(row: any, pop = true) {
            MessageBox.confirm("确定取消?", "提示", {}).then(() => {
                sdk.core
                    .model("zc_apply_personal")
                    .action("cancel")
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: row.id }],
                        prefilters: [
                            {
                                property: "apply_company_id",
                                value: this.detail.apply_model_id,
                            },
                        ],
                    })
                    .execute()
                    .then(() => {
                        this.reloadList(false)
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
