<template>
    <div class="core-ui-table-container" :key="refreshQueryParams" v-if="row">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex" v-if="row">
                <template v-if="show">
                    <el-button
                        type="primary"
                        @click="cancel"
                        plain
                        class="custom-btn back-btn"
                        v-role="[
                            'model.xg_company_position_recommend.action.audit_cancel',
                        ]"
                    >
                        撤回审核
                    </el-button>
                    <el-button
                        type="primary"
                        @click="finishRecommend"
                        plain
                        class="custom-btn back-btn"
                        v-role="[
                            'model.xg_company_position_recommend.action.finish_recommend',
                        ]"
                    >
                        完成推广
                    </el-button>
                </template>
                <el-button
                    type="primary"
                    @click="toggleshowAuditPop(row)"
                    v-if="row.audit_status === auditStatus.待审核"
                    v-role="[
                        'model.xg_company_position_recommend.action.audit',
                    ]"
                >
                    <!-- && row.apply_type === applyType.企业发起 -->
                    审核
                </el-button>
            </div>
        </div>
        <div>
            <group-detail-view
                ref="view"
                :row="row"
                v-if="row"
                :show="show"
            ></group-detail-view>
        </div>
        <audit-pop
            v-model="showAuditPop"
            title="社群推广申请审核"
            :row="row"
            @refresh="init"
        ></audit-pop>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { ApplyType, AuditStatus, predict, Row } from "."
    import { cancel_audit, finishRecommend } from "../service"
    import AuditPop from "./components/audit-pop.vue"
    import GroupDetailView from "./components/detail-view.vue"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"

    @Component({
        name: routesMap.recruit.groupDetail,
        components: { GroupDetailView, AuditPop },
    })
    export default class GroupDetail extends Vue {
        private row: Row | null = null
        private id = ""
        private auditStatus = AuditStatus
        private applyType = ApplyType
        refreshConfig = {
            fun: this.init,
            name: "refreshGroupDetail",
        }

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `岗位社群推广`,
                    to: {
                        name: routesMap.recruit.group,
                    },
                },
            ]
            if (this.from === "job-detail") {
                d = [...getCacheBreadcrumbsByRoutePath(routesMap.recruit.jobDetail)]
            }
            d = [
                ...d,
                {
                    label: "岗位社群推广详情",
                    to: {
                        name: routesMap.recruit.groupDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.groupDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string | undefined
        }

        mounted() {
            this.init()
        }

        private get show() {
            return (
                this.row &&
                ["待推荐", "待推广", "已推荐", "推广中"].includes(
                    this.row.status_memo
                )
            )
        }

        private init() {
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("xg_company_position_recommend")
                    .detail(this.id, "manage_apply_detail")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                    })
            })
        }

        private cancel() {
            cancel_audit(this.row!.id, this.init, () => {
                this.callRefresh("refreshRecruitGroupList")
            })
        }

        private finishRecommend() {
            finishRecommend(this.row!.id, {
                init: this.init,
                refresh: () => this.callRefresh("refreshRecruitGroupList"),
                content: "是否结束此次社群推广服务",
                title: "完成推荐确认",
            })
        }

        private confirm() {}

        private showAuditPop = false

        private toggleshowAuditPop(row: Row) {
            this.row = row
            this.showAuditPop = true
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
</style>
