<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>

        <div
            style="padding: 20px; margin-bottom: 20px"
            class="bg-white round-3"
        >
            <div>title:{{ title }}</div>
            <div>region_code:{{ region_code }}</div>
            <div>startTime:{{ startTime }}</div>
            <div>endTime:{{ endTime }}</div>
            <div>region_code_full:{{ region_code_full }}</div>
        </div>
        <div style="padding: 20px" class="bg-white round-3">
            <el-form :inline="true" :model="formInline" class="flex">
                <el-form-item label="审批人">
                    <el-input
                        v-model="formInline.user"
                        placeholder="审批人"
                    ></el-input>
                </el-form-item>
                <el-form-item label="活动区域">
                    <region-selector
                        :selectedValue="formInline.region_code_full"
                        @change="handleRegionChange"
                    />
                </el-form-item>
                <el-form-item label="活动区域">
                    <el-select
                        v-model="formInline.region"
                        placeholder="活动区域"
                    >
                        <el-option label="区域一" value="shanghai"></el-option>
                        <el-option label="区域二" value="beijing"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="margin-left: auto">
                    <el-button @click="onReset">重置</el-button>
                    <el-button type="primary" @click="onSubmit">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { Component, Vue } from "vue-property-decorator"
    import { updateTagItem } from "../../single-page/components/tags-view"
    import StaffInfo from "./components/StaffInfo.vue"
    import TabItem from "./components/TabItem.vue"
    import RegionSelector from "./components/RegionSelector.vue"

    @Component({ components: { StaffInfo, TabItem, RegionSelector } })
    export default class LedgerDetail extends Vue {
        private region_code = this.$route.query.region_code
        private startTime = this.$route.query.startTime
        private endTime = this.$route.query.endTime
        private title = this.$route.query.title as string
        private region_code_full = this.$route.query.region_code_full?.toString()

        private breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: `基层服务台账`,
                    to: {
                        name: routesMap.baseService.ledger,
                    },
                },
                {
                    label: this.title,
                },
            ]
            updateTagItem({
                name: routesMap.baseService.ledgerChartDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private formInline = {
            user: "",
            region: "",
            region_code_full: "",
        }

        private onReset() {
            this.formInline = {
                user: "",
                region: "",
                region_code_full: "",
            }
        }

        private onSubmit() {
            console.log(this.formInline)
            console.log(this.region_code_full)
        }

        created() {
            this.formInline.region_code_full = this.region_code_full
        }

        mounted() {
            this.setBreadcrumbs()
        }

        private handleRegionChange(value: any) {
            this.formInline.region_code_full = value.regionFullPath
            // this.region_code_full = value.regionFullPath
        }

        // private tabs = [
        //     {
        //         label: "劳动力更新情况",
        //         name: "get_grid_staff_labor_list",
        //         columns: [
        //             {
        //                 label: "劳动力信息",
        //                 prop: "name",
        //             },
        //             {
        //                 label: "户籍地",
        //                 prop: "household_village_code_remark",
        //             },
        //             {
        //                 label: "更新人",
        //                 prop: "collector_name",
        //             },
        //             {
        //                 label: "更新人",
        //                 prop: "update_time",
        //             },
        //         ],
        //     },
        //     {
        //         label: "社群情况",
        //         name: "get_grid_group_chat_members",
        //         columns: [
        //             {
        //                 label: "社群名称",
        //                 prop: "name",
        //             },
        //             {
        //                 label: "社群居民数量",
        //                 prop: "member_count",
        //             },
        //         ],
        //     },
        //     {
        //         label: "岗位推广情况",
        //         name: "xg_company_position",
        //         model_name: true,
        //         columns: [
        //             {
        //                 label: "推荐岗位",
        //                 prop: "params_json",
        //                 formatter: "json",
        //                 jsonKey: "name",
        //             },
        //             {
        //                 label: "推荐时间",
        //                 prop: "final_change_time",
        //             },
        //             {
        //                 label: "查看次数",
        //                 prop: "browse_count",
        //             },
        //         ],
        //     },
        //     {
        //         label: "政策推广情况",
        //         name: "policy_form",
        //         model_name: true,
        //         columns: [
        //             {
        //                 label: "推荐政策",
        //                 prop: "policy_name",
        //                 formatter(row: any) {
        //                     try {
        //                         const json =
        //                             typeof row.params_json === "string"
        //                                 ? JSON.parse(row.params_json)
        //                                 : row.params_json
        //                         return json.policy_name || "--"
        //                     } catch (error) {
        //                         return "--"
        //                     }
        //                 },
        //             },
        //             {
        //                 label: "推荐时间",
        //                 prop: "final_change_time",
        //             },
        //             {
        //                 label: "政策类型",
        //                 prop: "policy_type",
        //                 formatter(row: any) {
        //                     try {
        //                         const json =
        //                             typeof row.params_json === "string"
        //                                 ? JSON.parse(row.params_json)
        //                                 : row.params_json
        //                         return json.policy_type || "--"
        //                     } catch (error) {
        //                         return "--"
        //                     }
        //                 },
        //             },
        //             {
        //                 label: "查看次数",
        //                 prop: "browse_count",
        //             },
        //         ],
        //     },
        //     {
        //         label: "招聘会推广情况",
        //         name: "job_fair",
        //         model_name: true,
        //         columns: [
        //             {
        //                 label: "招聘会名称",
        //                 prop: "job_fair_name",
        //                 formatter(row: any) {
        //                     try {
        //                         const json =
        //                             typeof row.params_json === "string"
        //                                 ? JSON.parse(row.params_json)
        //                                 : row.params_json
        //                         return json.job_fair_name || "--"
        //                     } catch (error) {
        //                         return "--"
        //                     }
        //                 },
        //             },
        //             {
        //                 label: "召开时间",
        //                 prop: "job_fair_time",
        //                 formatter(row: any) {
        //                     try {
        //                         const json =
        //                             typeof row.params_json === "string"
        //                                 ? JSON.parse(row.params_json)
        //                                 : row.params_json
        //                         return json.job_fair_time || "--"
        //                     } catch (error) {
        //                         return "--"
        //                     }
        //                 },
        //             },
        //             {
        //                 label: "推荐时间",
        //                 prop: "final_change_time",
        //             },
        //             {
        //                 label: "查看次数",
        //                 prop: "browse_count",
        //             },
        //         ],
        //     },
        //     {
        //         label: "培训推广情况",
        //         name: "hb_training",
        //         model_name: true,
        //         columns: [
        //             {
        //                 label: "培训班",
        //                 prop: "train_class_name",
        //                 formatter(row: any) {
        //                     try {
        //                         const json =
        //                             typeof row.params_json === "string"
        //                                 ? JSON.parse(row.params_json)
        //                                 : row.params_json
        //                         return json.train_class_name || "--"
        //                     } catch (error) {
        //                         return "--"
        //                     }
        //                 },
        //             },
        //             {
        //                 label: "培训专业",
        //                 prop: "training_plan_major_name",
        //                 formatter(row: any) {
        //                     try {
        //                         const json =
        //                             typeof row.params_json === "string"
        //                                 ? JSON.parse(row.params_json)
        //                                 : row.params_json
        //                         return json.training_plan_major_name || "--"
        //                     } catch (error) {
        //                         return "--"
        //                     }
        //                 },
        //             },
        //             {
        //                 label: "推荐时间",
        //                 prop: "final_change_time",
        //             },
        //             {
        //                 label: "查看次数",
        //                 prop: "browse_count",
        //             },
        //         ],
        //     },
        // ]
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "./style/tail.less";
    @import "./style/custom-table.less";
</style>
