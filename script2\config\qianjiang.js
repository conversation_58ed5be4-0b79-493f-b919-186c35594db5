const defaultDomainAllInOne = {
    VUE_APP_UNIPLAT: "/api",
    VUE_APP_UNIPLAT_WEB: "/uniplat",
    VUE_APP_H5: "/h5",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
}

const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "潜江",
    VUE_APP_CITY_SAMPLE_NAME2: "潜江市",
    VUE_APP_DEFAULT_REGION_CODE: "429005000000",
    VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
    VUE_APP_DEFAULT_REGION_NAME: "潜江市",
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "潜江市人民政府",
    VUE_APP_BAIDU_KEY: "Xa338L0G79uVCEmkg7tIXOZtCLEPFfMY",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "bM8swUjSRSGrxlzf",
    VUE_APP_requestEncoder: "", // 入参加密 aes
    VUE_APP_responseEncoder: "", // 返回参加密 aes
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-qj",

    VUE_APP_BIGSCREEN_BI_PATH: "/bigScreen",
}

const { config } = require("./qianjiang_p.js")
const jzConfig = require("./jz.js")
module.exports = {
    name: "潜江项目",
    env: {
        test: {
            ...jzConfig.env.test,
            VUE_APP_CITY_SAMPLE_NAME: "潜江",
            VUE_APP_CITY_SAMPLE_NAME2: "潜江市",
            VUE_APP_DEFAULT_REGION_CODE: "429005000000",
            VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
            VUE_APP_DEFAULT_REGION_NAME: "潜江市",
            VUE_APP_BM_AUTO_COMPLETE_LOCATION: "潜江市人民政府",
        },
        pro: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_requestEncoder: "aes", // 入参加密 aes
            VUE_APP_responseEncoder: "aes", // 返回参加密 aes
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "就业管理工作台",
                VUE_APP_APP_TITLE: "智慧就业服务工作台",
                BASE_URL: "platform/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/platform-qj",
                    host: "88",
                    env: {
                        BASE_URL: "platform-qj/",
                        VUE_APP_OPERATE_URL: "/platform-qj",
                        VUE_APP_UNIPLAT_ENTRANCE: "荆州市智慧就业服务工作台",
                        VUE_APP_HEADER_TITLE: "就业管理工作台",
                        VUE_APP_APP_TITLE: "智慧就业服务工作台",
                    },
                },
                pro: {
                    env: {
                        VUE_APP_UNIPLAT:
                            "https://qianjiangsc.beikesmart.com/api",
                    }, // 潜江环境配置
                },
            },
        },
    ],
}
