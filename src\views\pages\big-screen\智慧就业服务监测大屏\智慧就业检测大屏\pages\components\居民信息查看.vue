<template>
    <div>
        <table-filter
            v-if="tableConfig2"
            ref="filter"
            :tableFilter="tableConfig2.filter"
            :metaFilters="metaFilters"
            @search="resetPageAndSearch"
            :showExpand="false"
            :defaultFilterIsExpand="true"
        />
        <div class="list-container" v-loading="loading">
            <common-table
                :emptyText="emptyText"
                :data="items"
                :columns="columns"
            >
                <div
                    slot="index"
                    class="u-flex u-row-center"
                    slot-scope="scope"
                >
                    {{ scope.index + 1 }}
                </div>
                <div slot="h" class="u-flex u-row-center" slot-scope="scope">
                    <div class="handler-btn" @click="toDetail(scope.row)">
                        详情
                    </div>
                </div>
            </common-table>

            <div class="u-flex u-row-center u-m-t-30">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="index"
                    :page-sizes="pageSizes"
                    :page-size="size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { Component, Mixins, Ref, Prop } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import {
        BaseTable,
        BaseTableController,
    } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import TableFilter from "@/core-ui/component/table/filter.vue"
    import {
        buildSelectSource,
        FormType,
        SelectOption,
    } from "@/core-ui/component/form"
    import { ListTypes, metaFilter } from "uniplat-sdk/build/main/def"
    import { sdk } from "@/service"
    import { computeAge } from "@/views/pages/labour-manage/seeker-info"
    import { cloneDeep, find, get, last, map, split } from "lodash"
    import { routesMap } from "@/router/direction"
    import { getAddress, sleep } from "@/utils"
    import { getTagLabels } from "./index"
    import {
        maskId,
        maskMobile,
        maskName,
    } from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/tools"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { config } from "@/config"

    @Component({
        components: {
            TableContainer,
            CommonTable,
            TableFilter,
        },
    })
    export default class Template extends Mixins(BaseTable, BaseItem) {
        @Prop({ default: 10 })
        private cusPageSize!: number

        protected pageSizes = [10]
        tableConfig2: TableConfig | null = null
        selectOption: SelectOption[] = []
        public metaFilters: metaFilter[] = []
        row: any | null = null
        private importConfig: any = null

        private get emptyText() {
            return this.loading ? "加载中..." : "暂无数据"
        }

        private get tableFilter(): any[] {
            let defaultValue: any
            if (
                this.currentRegion?.code &&
                this.currentRegion?.code !== config.defaultRegion.region_code
            ) {
                defaultValue = ["420000", "420900", this.currentRegion?.code]
            }
            return [
                {
                    label: "用户信息：",
                    option: { placeholder: "请输入姓名/身份证/手机号" },
                    type: FormType.Text,
                    prop: "user_info",
                    keyValueFilter: {
                        match: ListTypes.filterMatchType.exact,
                    },
                },
                {
                    label: "性别：",
                    type: FormType.Select,
                    prop: "sex",
                    sourceInputsParameter: buildSelectSource([
                        {
                            value: "全部",
                            key: "",
                        },
                        {
                            key: "1",
                            value: "男",
                        },
                        {
                            key: "2",
                            value: "女",
                        },
                    ]),
                },
                {
                    label: "户籍地：",
                    type: FormType.Cascader,
                    prop: "household_code",
                    useOtherProp: "household_province_code",
                    option: {
                        filterable: true,
                        elProps: { checkStrictly: true },
                    },
                    defaultValue,
                },
                {
                    label: "常住地：",
                    type: FormType.Cascader,
                    prop: "permanent_code",
                    useOtherProp: "household_province_code",
                    option: {
                        filterable: true,
                        elProps: { checkStrictly: true },
                    },
                },
            ]
        }

        // 重点人群标签
        private tags1: string[] = []

        created() {
            this.pageSizes = [this.cusPageSize]
        }

        async mounted() {
            this.tableConfig2 = this.createTableConfig()
            this.tableConfig2
                .model!.query({
                    pageIndex: 1,
                    item_size: 0,
                })
                .then((r) => {
                    // this.tagGroups = r.pageData.tagGroups
                    this.metaFilters = r.pageData.meta.filters

                    const t = get(
                        find(
                            this.metaFilters,
                            (e: any) => e.property === "label_nos"
                        ),
                        "ext_properties.mapping.mapping_values"
                    )
                    this.tags1 = getTagLabels(
                        find(t, (e) => e.key === "tag_fonce_group_label")
                    )
                    this.onFilterChanged()
                })
        }

        createTableConfig() {
            return {
                model: sdk.core.model("query_user_profile").list(""),
                filter: this.tableFilter,
                defaultPageSize: this.pageSizes[0],
                predict: {},
                handleFilterData: (params: any) => {
                    const birth_date = (params.birth_date || [])
                        .map((e: number) => computeAge(e))
                        .filter(Boolean)
                        .reverse()

                    let uniplat_uid: { min: string; max: string } | {} = {}
                    if (params.uniplat_uid) {
                        if (params.uniplat_uid === "0") {
                            uniplat_uid = { min: "0", max: "0" }
                        } else {
                            uniplat_uid = { min: "1", max: "" }
                        }
                    }
                    return { ...params, birth_date, uniplat_uid }
                },
            }
        }

        @Ref("filter")
        private readonly filter!: TableFilter | any

        resetPageAndSearch() {
            this.index = 1
            this.onFilterChanged()
        }

        private get type(): string {
            return (this.$route.query?.type as string) || ""
        }

        private get typeFilter(): any {
            if (this.$route.query?.labels) {
                return {
                    labels: this.$route.query?.labels,
                }
            }
            if (this.type === "人岗匹配-求职者数") {
                return {
                    labels: "tag_service_demand_label,jiuyexuqiu",
                }
            }
            if (this.type === "重点人群帮扶-人群总数") {
                return {
                    labels: "tag_fonce_group_label",
                }
            }
            return {}
        }

        protected onFilterChanged() {
            this.size = this.pageSizes[0]
            let filterData = cloneDeep(this.filter?.getFilterData() || {})
            filterData = {
                ...filterData,
                ...this.typeFilter,
            }
            const v = split(filterData.tags, ",")
            // const tags = map(v, (i) => {
            //     const tag = split(i, ":::")
            //     return {
            //         tagGroup: tag[0],
            //         tag: tag[1],
            //     }
            // })
            //     .filter((i) => i.tag)
            //     .map((i) => i.tag)
            //     .join(",")
            // filterData.tags = tags
            if (filterData.birth_date) {
                const birth_date = (filterData.birth_date || [])
                    .map((e: number) => computeAge(e))
                    .filter(Boolean)
                    .reverse()
                filterData.birth_date_start = birth_date[0]
                filterData.birth_date_end = birth_date[1]
                delete filterData.birth_date
            }
            if (filterData.household_code) {
                filterData.household_code = last(
                    split(filterData.household_code, ",").filter(Boolean)
                )
            }
            console.log("f", JSON.parse(JSON.stringify(filterData)))
            if (filterData.update_time) {
                filterData.update_time_start = filterData.update_time[0]
                filterData.update_time_end = filterData.update_time[1]
                delete filterData.update_time
            }

            if (!filterData.household_code) {
                if (this.$route.query.regionCode) {
                    filterData.household_code = this.$route.query.regionCode
                } else {
                    filterData.household_code = "420901"
                }
            }
            this.loading = true
            Promise.all([
                sdk.core
                    .domainService("xg_project", "back_api", "fetch_profile_list")
                    .post({
                        ...filterData,
                        page_index: this.index,
                        page_size: this.size,
                    })
                    .then((r: any) => {
                        this.items = r.data
                    }),
                sdk.core
                    .domainService(
                        "xg_project",
                        "back_api",
                        "fetch_profile_list_total"
                    )
                    .post(filterData)
                    .then((r: any) => {
                        this.total = this.handleCustomTotal() || +r.total_count
                    }),
            ]).finally(() => {
                this.loading = false
            })
        }

        private handleCustomTotal() {
            if (this.$route.query?.cIndex) {
                return (this.$route.query?.cIndex as any) * 1
            }
            if (this.type === "劳动力-劳动力总数") {
                if (
                    this.currentRegion?.code === undefined ||
                    this.currentRegion?.code + "" === "420900"
                ) {
                    return 2618912
                }
            }
            return 0
        }

        toDetail(row: any) {
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.personDetail,
                query: {
                    id: row._access_key || row.id,
                    type: this.type,
                    cIndex: this.$route.query?.cIndex,
                    labels: this.$route.query?.labels,
                },
            })
        }

        private readonly columns: any[] = [
            { prop: "index", label: "序号" },
            {
                prop: "name_hide",
                label: "姓名",
                formatter(row: any) {
                    return maskName(row.name_hide)
                },
            },
            {
                prop: "sex",
                label: "性别",
                formatter: (row: any) => {
                    return +row.sex === 1 ? "男" : +row.sex === 2 ? "女" : ""
                },
            },
            {
                label: "手机号",
                prop: "mobile_hide",
                formatter: (row: any) => {
                    return maskMobile(row.mobile_hide)
                },
                showOverflowTip: true,
            },
            {
                label: "身份证号",
                prop: "id_card_hide",
                formatter: (row: any) => {
                    return maskId(row.id_card_hide)
                },
                showOverflowTip: true,
            },
            {
                label: "民族",
                prop: "nation",
                formatter(row: any) {
                    return row.nation || "--"
                },
            },
            {
                label: "户口性质",
                prop: "reg_residence_property",
                formatter: (row: any) =>
                    +row.reg_residence_property === 2
                        ? "城镇"
                        : +row.reg_residence_property === 1
                        ? "农村"
                        : "--",
            },
            {
                label: "户籍地",
                prop: "household",
                render(h: any, row: any) {
                    return h(
                        "div",
                        {
                            class: "pre-line u-text-center u-line-1",
                        },
                        [
                            h(
                                "span",
                                { class: "u-line-1" },
                                getAddress(row, [
                                    "household_province_name",
                                    "household_city_name",
                                    "household_area_name",
                                    "household_countryside_name",
                                    "household_village_name",
                                ]) || "-"
                            ),
                        ]
                    )
                },
                showOverflowTip: true,
            },
            {
                label: "常住地",
                prop: "household2",
                width: "220px",
                render(h: any, row: any) {
                    return h(
                        "div",
                        {
                            class: "pre-line u-text-center u-line-1",
                        },
                        [
                            h(
                                "span",
                                { class: "u-line-1" },
                                getAddress(row, [
                                    "permanent_province_name",
                                    "permanent_city_name",
                                    "permanent_area_name",
                                ]) || "-"
                            ),
                        ]
                    )
                },
                showOverflowTip: true,
            },
            {
                label: "操作",
                prop: "h",
            },
        ]
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "./table.less";
</style>
