<template>
    <div
        class="core-ui-table-container list-view u-p-x-20 u-p-b-40"
        v-show="show"
    >
        <div class="title">推荐求职者信息 <title-tips detail></title-tips></div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            :alwaysShowPageIndex="false"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            @getData="getData"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-x-20">
                <common-table :data="data" :columns="columns">
                    <div slot="recommend_duration_label" slot-scope="scope">
                        <div v-if="getTimeRange(scope.row)">
                            {{ getTimeRange(scope.row) }}
                        </div>
                        <div>{{ scope.row.recommend_duration_label }}</div>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-flex-wrap u-row-center"
                        slot-scope="scope"
                    >
                        <el-button
                            class="u-m-r-10"
                            type="text"
                            @click="toDetail(scope.row)"
                        >
                            详情
                        </el-button>
                        <template
                            v-if="
                                scope.row.created_from === 'talent' &&
                                ['联系待标记', '沟通中'].includes(
                                    scope.row.status_memo
                                )
                            "
                        >
                            <el-button
                                type="text"
                                v-if="scope.row.status_memo === '联系待标记'"
                                @click="changeStatus(scope.row, 10)"
                            >
                                沟通中
                            </el-button>
                            <div class="u-flex">
                                <el-button
                                    type="text"
                                    @click="changeStatus(scope.row, 20)"
                                >
                                    成功入职
                                </el-button>
                                <el-button
                                    type="text"
                                    @click="changeStatus(scope.row, 30)"
                                >
                                    已淘汰/拒绝
                                </el-button>
                            </div>
                        </template>
                        <div
                            class="u-flex"
                            v-else-if="
                                scope.row.created_from === 'agent_bidding' &&
                                ['待反馈'].includes(scope.row.status4_label)
                            "
                        >
                            <el-button
                                type="text"
                                @click="changeStatus(scope.row, 20)"
                            >
                                已入职
                            </el-button>
                            <el-button
                                type="text"
                                @click="changeStatus(scope.row, 30)"
                            >
                                未入职
                            </el-button>
                        </div>
                        <!-- <div v-else>-{{ scope.row.status_memo }}</div> -->
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import { columns2, Row2 as Row, tableConfig2 } from "./detail"
    import { TableConfig } from "@/core-ui/component/table"
    import { getTimeRange } from "../../service"
    import { MessageBox } from "element-ui"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import TitleTips from "@/views/page-company/recruit/components/title-tips.vue"
    import { routesMap } from "@/router/direction"

    @Component({ components: { TableContainer, CommonTable, TitleTips } })
    export default class ListView extends BaseTableController<Row> {
        private tableConfig: TableConfig | null = null
        private columns = columns2

        @Prop()
        id!: string

        mounted() {
            this.tableConfig = tableConfig2(this.id)
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.company.recruit.jobDeliverDetail,
                query: { id: row._access_key || row.id, from: this.$route.name },
            })
        }

        private show = false

        private getData(data: any) {
            !this.show && (this.show = !!data.length)
        }

        private getTimeRange = getTimeRange

        private changeStatus(row: Row, status = 0) {
            const actionName = "update_status4"
            MessageBox.confirm("确认改变状态？", "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_candidate_order")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .addInputs_parameter({ status })
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .list-view {
        background: #fff;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
        .table {
            padding: 0;
        }
    }
    ::v-deep .filter-container {
        padding: 0;
        margin-bottom: 0;
    }
</style>
