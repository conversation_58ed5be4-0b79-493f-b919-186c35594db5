import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
const isEz = config.envProject === EnvProject.鄂州项目
export const enterpriseMange = [
    {
        path: "/enterpriseMange",
        name: routesMap.home.enterpriseMange,
        meta: {
            title: "市场主体信息管理",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/market.svg"),
            hidden: isQj,
        },
        component: layout,
        children: [
            // 市场主体信息库管理
            {
                path: "/enterprise-mange",
                redirect: "enterprise-mange/enterprise-database",
                name: routesMap.collectTaskManage.marketDatabaseManage.root,
                meta: {
                    title: "市场主体信息库管理",
                    // svgIcon: require("@/assets/icon/menu2/laborInfoBase.svg"),
                    hidden: ![
                        EnvProject.荆州项目,
                        EnvProject.黄州项目,
                        EnvProject.宜都项目,
                        EnvProject.鄂州项目,
                        EnvProject.saas项目,
                        EnvProject.洪湖项目,
                    ].includes(config.envProject),
                },
                component: RouteView,
                children: [
                    {
                        path: "enterprise-database",
                        name: routesMap.collectTaskManage.marketDatabaseManage
                            .enterpriseDatabase.list,
                        meta: {
                            title: "企业信息库",
                            role: [
                                "/tablelist/tg_enterprise/back_list",
                                "/tablelist/tg_enterprise/back_list2",
                            ],
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/enterprise-database/index.vue"
                            ),
                    },
                    {
                        path: "enterprise-database-filter",
                        name: routesMap.collectTaskManage.marketDatabaseManage
                            .enterpriseDatabase.filter,
                        meta: {
                            title: "企业信息库筛选",
                            hidden: true,
                            parentMenuName:
                                routesMap.collectTaskManage.marketDatabaseManage
                                    .enterpriseDatabase.list,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/enterprise-database/filter.vue"
                            ),
                    },
                    {
                        path: "enterprise-database-detail",
                        name: routesMap.collectTaskManage.marketDatabaseManage
                            .enterpriseDatabase.detail,
                        meta: {
                            title: "企业信息库详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.collectTaskManage.marketDatabaseManage
                                    .enterpriseDatabase.list,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/enterprise-database/detail.vue"
                            ),
                    },
                    {
                        path: "staff-database",
                        name: routesMap.collectTaskManage.marketDatabaseManage
                            .staffDatabase.list,
                        meta: {
                            title: "企业员工信息库",
                            role: "/tablelist/company_employee_information/manage",
                            hidden: !isDev,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/staff-database/index.vue"
                            ),
                    },
                    {
                        path: "staff-database-filter",
                        name: routesMap.collectTaskManage.marketDatabaseManage
                            .staffDatabase.filter,
                        meta: {
                            title: "企业员工信息库筛选",
                            hidden: true,
                            parentMenuName:
                                routesMap.collectTaskManage.marketDatabaseManage
                                    .staffDatabase.list,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/staff-database/filter.vue"
                            ),
                    },
                    {
                        path: "staff-database-detail",
                        name: routesMap.collectTaskManage.marketDatabaseManage
                            .staffDatabase.detail,
                        meta: {
                            title: "企业员工信息库详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.collectTaskManage.marketDatabaseManage
                                    .staffDatabase.list,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/staff-database/detail.vue"
                            ),
                    },
                    // {
                    //     path: "employment-demand-database",
                    //     name: routesMap.collectTaskManage.marketDatabaseManage
                    //         .employmentDemandDatabase.list,
                    //     meta: {
                    //         title: "企业用工需求库",
                    //         hidden: ![EnvProject.荆州项目, EnvProject.黄州项目].includes(config.envProject),
                    //         role: "/tablelist/tg_enterprise/back_list2",
                    //     },
                    //     component: () =>
                    //         import(
                    //             "@/views/pages/collect-task-manage/employment-demand-database/index.vue"
                    //         ),
                    // },
                    {
                        path: "labor-services-brand",
                        name: routesMap.laborInfoBaseManage.laborServicesBrand,
                        meta: {
                            title: "劳务品牌信息库",
                            hidden: ![
                                EnvProject.荆州项目,
                                EnvProject.saas项目,
                                EnvProject.黄州项目,
                            ].includes(config.envProject),
                            role: "/tablelist/labor_services_brand",
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/labor-services-brand/index.vue"
                            ),
                    },
                    {
                        path: "labor-services-brand-create-brand",
                        name: routesMap.laborInfoBaseManage
                            .laborServicesBrandCreateBrand,
                        meta: {
                            title: "劳务品牌信息库编辑",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage
                                    .laborServicesBrand,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/labor-services-brand/create-brand.vue"
                            ),
                    },
                    {
                        path: "labor-services-brand-detail",
                        name: routesMap.laborInfoBaseManage
                            .laborServicesBrandDetail,
                        meta: {
                            title: "劳务品牌信息库详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.laborInfoBaseManage
                                    .laborServicesBrand,
                        },
                        component: () =>
                            import(
                                "@/views/pages/collect-task-manage/labor-info-base-manage/labor-services-brand/detail.vue"
                            ),
                    },
                ],
            },
            // 企业用工情况调查
            {
                path: "/hr-info-manage",
                redirect: "hr-info-manage/work-info-apply",
                name: routesMap.employmentManage.hrInfoManage.hrInfoManage,
                meta: {
                    title: "企业用工情况调查",
                    // svgIcon: require("@/assets/icon/menu/hrInfoManage.svg"),
                    hidden: [EnvProject.宜都项目].includes(config.envProject),
                },
                component: RouteView,
                children: [
                    {
                        path: "staff-list",
                        name: routesMap.employmentManage.hrInfoManage.staffList,
                        meta: {
                            title: "企业员工列表",
                            hidden: [
                                EnvProject.荆州项目,
                                EnvProject.saas项目,
                                EnvProject.黄州项目,
                            ].includes(config.envProject),
                            role: "/tablelist/company_employee_information/manage",
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/staff-list/index.vue"
                            ),
                    },
                    {
                        path: "staff-list-detail",
                        name: routesMap.employmentManage.hrInfoManage
                            .staffListDetail,
                        meta: {
                            title: "企业员工列表详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .staffList,
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/staff-list/detail.vue"
                            ),
                    },
                    {
                        path: "dimission",
                        name: routesMap.labourManage.dimission,
                        meta: {
                            title: "离职人员登记表",
                            role: "/tablelist/leaving_person",
                            hidden: [
                                EnvProject.黄州项目,
                                EnvProject.宜都项目,
                            ].includes(config.envProject),
                        },
                        component: () =>
                            import(
                                "@/views/pages/labour-manage/dimission/index.vue"
                            ),
                    },
                    {
                        path: "work-info-apply",
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoApply,
                        meta: {
                            title: "用工信息填报",
                            role: ![EnvProject.掇刀项目].includes(
                                config.envProject
                            )
                                ? "/tablelist/company_task"
                                : "/tablelist/company_task/report_list",
                        },
                        component: () =>
                            ![EnvProject.掇刀项目].includes(config.envProject)
                                ? import(
                                      "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/index.vue"
                                  )
                                : import(
                                      "@/views/pages/work-info-survey/work-info-apply/index.vue"
                                  ),
                    },
                    {
                        path: "work-info-apply-add",
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoApplyAdd,
                        meta: {
                            title: "新建任务",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workInfoApply,
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/add.vue"
                            ),
                    },
                    {
                        path: "work-info-apply-detail",
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoApplyDetail,
                        meta: {
                            title: "任务详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workInfoApply,
                        },
                        component: () =>
                            ![EnvProject.掇刀项目].includes(config.envProject)
                                ? import(
                                      "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/detail.vue"
                                  )
                                : import(
                                      "@/views/pages/work-info-survey/work-info-apply/detail.vue"
                                  ),
                    },
                    {
                        path: "work-info-apply-record",
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoApplyRecord,
                        meta: {
                            title: "填报记录",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workInfoApply,
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/apply-record.vue"
                            ),
                    },
                    {
                        path: "work-info-audit",
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoAudit,
                        meta: {
                            title: "用工信息审核",
                            role: ![EnvProject.掇刀项目].includes(
                                config.envProject
                            )
                                ? "/tablelist/company_task_record/list_for_plat_yonggong"
                                : "/tablelist/company_task_record/list_for_plat_report",
                        },
                        component: () =>
                            ![EnvProject.掇刀项目].includes(config.envProject)
                                ? import(
                                      "@/views/zq-operate/employment-manage/hr-info-manage/work-info-audit/index.vue"
                                  )
                                : import(
                                      "@/views/pages/work-info-survey/work-info-audit/index.vue"
                                  ),
                    },
                    {
                        path: "work-info-audit-check",
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoAuditCheck,
                        meta: {
                            title: "审核查看",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workInfoAudit,
                        },
                        component: () =>
                            ![EnvProject.掇刀项目].includes(config.envProject)
                                ? import(
                                      "@/views/zq-operate/employment-manage/hr-info-manage/work-info-audit/audit.vue"
                                  )
                                : import(
                                      "@/views/pages/work-info-survey/work-info-audit/audit.vue"
                                  ),
                    },
                    {
                        path: "work-demand-apply",
                        name: routesMap.employmentManage.hrInfoManage
                            .workDemandApply,
                        meta: {
                            title: "用工需求填报",
                            role: "/tablelist/company_task/list_for_req",
                            hidden: [EnvProject.掇刀项目].includes(
                                config.envProject
                            ),
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/index.vue"
                            ),
                    },
                    {
                        path: "work-demand-apply-add",
                        name: routesMap.employmentManage.hrInfoManage
                            .workDemandApplyAdd,
                        meta: {
                            title: "新建任务",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workDemandApply,
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/add.vue"
                            ),
                    },
                    {
                        path: "work-demand-apply-detail",
                        name: routesMap.employmentManage.hrInfoManage
                            .workDemandApplyDetail,
                        meta: {
                            title: "任务详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workDemandApply,
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/detail.vue"
                            ),
                    },
                    {
                        path: "work-demand-apply-record",
                        name: routesMap.employmentManage.hrInfoManage
                            .workDemandApplyRecord,
                        meta: {
                            title: "填报记录",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workDemandApply,
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-apply/apply-record.vue"
                            ),
                    },
                    {
                        path: "work-demand-audit",
                        name: routesMap.employmentManage.hrInfoManage
                            .workDemandAudit,
                        meta: {
                            title: "用工需求审核",
                            role: "/tablelist/company_task_record/list_for_plat_xuqiu",
                            hidden: [EnvProject.掇刀项目].includes(
                                config.envProject
                            ),
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-audit/index.vue"
                            ),
                    },
                    {
                        path: "work-demand-audit-check",
                        name: routesMap.employmentManage.hrInfoManage
                            .workDemandAuditCheck,
                        meta: {
                            title: "审核查看",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.hrInfoManage
                                    .workDemandAudit,
                        },
                        component: () =>
                            import(
                                "@/views/zq-operate/employment-manage/hr-info-manage/work-demand-audit/audit.vue"
                            ),
                    },
                ],
            },
            // 市场主体管理
            {
                path: "/market-main",
                redirect: "market-main",
                name: routesMap.employmentManage.marketMain,
                meta: {
                    // svgIcon: require("@/assets/icon/menu/employmentManage.svg"),
                    title: "企业信息库",
                    hidden: ![
                        EnvProject.荆州项目,
                        EnvProject.黄州项目,
                        EnvProject.洪湖项目,
                    ].includes(config.envProject),
                },
                component: RouteView,
                children: [
                    {
                        path: "market-main",
                        name: routesMap.employmentManage.marketMain,
                        meta: {
                            title: "市场主体",
                            role: "/tablelist/tg_enterprise/for_operate",
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/market-main/index.vue"
                            ),
                    },
                    {
                        path: "market-main-detial",
                        name: routesMap.employmentManage.marketMainDetail,
                        meta: {
                            title: "市场主体详情",
                            hidden: true,
                            parentMenuName:
                                routesMap.employmentManage.marketMain,
                        },
                        component: () =>
                            import(
                                "@/views/pages/employment-manage/market-main/detail.vue"
                            ),
                    },
                ],
            },
            // 市场主体数据分析大屏
            {
                ...(EnvProject.孝感项目 === config.envProject
                    ? {
                          path: "https://jy.xg12333.cn:28090/#/",
                          name: routesMap.dataAcquisition.returnHome,
                          meta: {
                              title: "市场主体数据分析大屏",
                              // svgIcon: require("@/assets/icon/menu/screen.svg"),
                              role: "/redirect/xg_project/company_screen",
                          },
                      }
                    : {
                          path: "/show-big-screen",
                          name: routesMap.dataAcquisition.returnHome,
                          meta: {
                              title: "市场主体数据分析大屏",
                              // svgIcon: require("@/assets/icon/menu/screen.svg"),
                              newPage: true,
                              targetName: routesMap.bigScreen.home,
                              hidden: [EnvProject.宜都项目].includes(
                                  config.envProject
                              ),
                              role: "/redirect/xg_project/company_screen",
                          },
                      }),
            },
            {
                path: "/show-big-screen2",
                name: routesMap.dataAcquisition.empBigScreen,
                meta: {
                    title: "企业用工监测分析",
                    newPage: true,
                    targetPath:
                        `${process.env.BASE_URL}/big-screen/emp`.replace(
                            "//",
                            "/"
                        ),
                    role: "/redirect/xg_project/company_task_statistic",
                },
            },
        ],
    },
]
