<template>
    <div class="container" v-if="myPath">
        <iframe class="iframe" :src="myPath" />
    </div>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import { PassportTokenController } from "@/core-ui/service/passport/token"
    import { routesMap } from "@/router/direction"
    import { get } from "lodash"
    import { Component, Vue } from "vue-property-decorator"

    const baseUrlObj: any = {
        [EnvProject.荆州项目]: {
            test: "https://admin.beikesmart.com/iframe/jingzhou",
            pro: "https://admin.jzjycy.com/iframe/jingzhou",
        },
    }
    @Component({
        name: routesMap.live.job,
        components: {},
    })
    export default class Index extends Vue {
        private get srcPath() {
            const base = get(
                baseUrlObj[config.envProject],
                process.env.VUE_APP_ENV,
                ""
            )

            return `${base}/recruitLive`
        }

        get myPath() {
            return `${this.srcPath}?token=${PassportTokenController.hasToken()}`
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .container {
        margin-left: -30px;
        margin-right: -30px;
        margin-bottom: -20px;
        height: calc(100vh - @tag-view-height - @banner-height - 2px);
        .iframe {
            width: 100%;
            height: 100%;
            border: none;
            overflow: hidden;
        }
    }
</style>
