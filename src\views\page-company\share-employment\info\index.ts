import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { map } from "lodash"
import { ListTypes } from "uniplat-sdk"
const tableFilter: TableFilter[] = [
    {
        label: "企业名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "发布时间",
        type: FormType.DatePicker,
        prop: "update_time",
        option: {
            type: "datetimerange",
        },
    },
    {
        label: "所在地",
        type: FormType.Text,
        prop: "address",
    },
]

export const predict = {
    agent_name: "agentRef#agent_name",
    person_num: "",
    contact_person: "",
    apply_description: "",
    region: "label",
    address: "",
    update_time: "label",
    contact_number: "",
    mobile_hide: "",
    expired_date: "label",
    address_detail: "agentRef#address_detail_label",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("share_employee_apply").list("list_for_all"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict,
        tabPages: ["人工富余", "人工短缺"],
        oneTabFilter: true,
    }
}

export interface Row {
    agent_name: string
    person_num: number
    contact_person: string
    apply_description: string
    region: number
    region_label: string
    address: string
    update_time: string
    id: number
    v: number
    contact_number: string
    mobile_hide: string
    expired_date: string
    expired_date_label: string
    address_detail: string
    address_detail_label: string
}

export const columns: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "涉及人数",
        prop: "person_num",
        showOverflowTip: true,
    },
    {
        label: "联系人",
        prop: "contact_person",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "mobile_hide",
        showOverflowTip: true,
    },
    {
        label: "有效期",
        prop: "expired_date_label",
        formatter: (row) => formatTime.day(row.expired_date),
        showOverflowTip: true,
    },
    {
        label: "描述",
        prop: "apply_description",
        showOverflowTip: true,
    },
    {
        label: "所在地",
        prop: "address_detail_label",
        showOverflowTip: true,
    },
    {
        label: "发布时间",
        prop: "update_time_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
    },
]
