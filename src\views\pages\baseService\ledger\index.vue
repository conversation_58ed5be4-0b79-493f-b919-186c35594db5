<template>
    <div style="padding-block: 16px">
        <!-- 所在区域 -->
        <section>
            <div class="flex items-center gap-20">
                <div class="text-18 font-600">所在区域</div>
                <div style="width: max-content">
                    <RegionTree
                        :data="regionTreeData"
                        @change="regionTreeChange"
                    ></RegionTree>
                </div>
            </div>
            <StatisticsCard :region_code="region_code" />
        </section>
        <!-- 区域服务情况 -->
        <section style="margin-top: 42px">
            <div class="text-18 font-600">
                {{ region_level === 5 ? "人社专员服务信息" : "区域服务情况" }}
            </div>
            <div
                style="margin-top: 20px; padding: 20px"
                class="bg-white round-3"
            >
                <ServiceDataTable
                    :region_level="region_level"
                    :region_code="region_code"
                ></ServiceDataTable>
            </div>
        </section>
        <!-- 服务情况分析 -->
        <section style="margin-top: 42px">
            <div class="flex items-center justify-between">
                <div class="text-18 font-600">服务情况分析</div>
                <DateRangeGroup @change="handleDateChange" />
            </div>

            <div style="margin-top: 20px" class="grid grid-cols-2 gap-20">
                <AnalysisCard
                    v-for="item in analysisData"
                    :key="item.name"
                    :title="item.title"
                    :region_code="region_code"
                    :name="item.name"
                    :lineColor="item.lineColor"
                    :startTime="startTime"
                    :endTime="endTime"
                    :all_flag="allFlag"
                    @view-more="viewMore(item)"
                />
            </div>
        </section>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"
    import { getLastRegionCode } from "../../collect-task-manage/components/region-tool"
    import StatisticsCard from "./components/StatisticsCard.vue"
    import RegionTree from "@/views/pages/collect-task-manage/labor-info-base-manage/components/region-tree.vue"
    import ServiceDataTable from "./components/ServiceDataTable.vue"
    import LineChart from "./components/LineChart.vue"
    import AnalysisCard from "./components/AnalysisCard.vue"
    import DateRangeGroup from "./components/DateRangeGroup.vue"
    import { routesMap } from "@/router/direction"

    @Component({
        components: {
            StatisticsCard,
            RegionTree,
            ServiceDataTable,
            LineChart,
            AnalysisCard,
            DateRangeGroup,
        },
    })
    export default class LedgerIndex extends Vue {
        // 当前区域
        private region_code = getLastRegionCode()
        private region_code_full = []
        // 当前区域等级
        private region_level = ""
        // 时间范围
        private startTime = ""
        private endTime = ""
        private allFlag = "0"

        // 区域树
        private regionTreeData: any = {
            level: 5,
            type: "scene",
            treeInfo: {
                manage_region_full_path: "",
            },
        }

        // 区域树变化
        private regionTreeChange(r: any) {
            this.region_code_full = r.values

            this.region_level = r.regionGrade
            if (r.values && r.values.length > 0) {
                const codes = r.values[0].split(",")
                this.region_code = codes[codes.length - 1]
            } else {
                this.region_code = ""
            }
        }

        private handleDateChange({
            startTime,
            endTime,
        }: {
            startTime: string
            endTime: string
        }) {
            this.startTime = startTime
            this.endTime = endTime
            if (startTime === "" && endTime === "") {
                this.allFlag = "1"
            } else {
                this.allFlag = "0"
            }
        }

        private analysisData = [
            {
                title: "岗位推广情况",
                name: "xg_company_position",
                lineColor: "#ECC957",
            },
            {
                title: "政策推广情况",
                name: "policy_form",
                lineColor: "#A1C452",
            },
            {
                title: "招聘会推广情况",
                name: "job_fair",
                lineColor: "#57BDEC",
            },
            {
                title: "培训推广情况",
                name: "hb_training",
                lineColor: "#F3A162",
            },
        ]

        private viewMore(item: any) {
            this.$router.push({
                name: routesMap.baseService.ledgerChartDetail,
                query: {
                    name: item.name,
                    region_code: this.region_code,
                    startTime: this.startTime,
                    endTime: this.endTime,
                    title: item.title,
                    region_code_full: this.region_code_full,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "./style/tail.less";
    @import "./style/custom-tabs.less";
</style>
