{"name": "bijie-bussiness-order-portal", "version": "0.1.0", "private": true, "workspaces": ["src/plugins/wangeditor", "src/plugins/baidu-map-selector"], "scripts": {"serve": "node ./script2/run.js", "build": "node ./script2/build.js", "deploy": "node ./script2/build.js -b", "serve-base": "vue-cli-service serve", "build-base": "bash -c 'cross-env VUE_APP_APP_NAME=$1 vue-cli-service build --mode $2' --", "lint": "vue-cli-service lint", "up-sdk": "npm install uniplat-sdk@latest"}, "dependencies": {"@rongcloud/engine": "^5.12.1", "@rongcloud/imlib-next": "^5.12.1", "@rongcloud/plugin-call": "^5.2.11", "@rongcloud/plugin-rtc": "^5.7.3-enterprise.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "@wangeditor/plugin-upload-attachment": "^1.1.0", "axios": "^0.27.2", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "echarts": "^5.4.3", "element-ui": "^2.15.13", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "moment": "^2.29.3", "path-browserify": "^1.0.1", "postcss-pxtorem": "^6.0.0", "qiniu-js": "^3.4.2", "theme-vue": "0.0.8", "uniplat-sdk": "^0.1.739-private", "vue": "^2.6.14", "vue-baidu-map": "^0.21.22", "vue-class-component": "^7.2.3", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.1", "vuex": "^3.6.0", "vuex-class": "^0.3.2", "xlsx": "^0.16.9", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.14.178", "@types/path-browserify": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/eslint-config-typescript": "^9.1.0", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^5.0.2", "cross-env": "^7.0.3", "cross-spawn": "^7.0.3", "dotenv": "^16.4.1", "enquirer": "^2.3.6", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.0.0", "less-loader": "^8.0.0", "node-sass": "^4.11.0", "sass-loader": "^7.1.0", "terser-webpack-plugin": "^5.3.3", "thread-loader": "^3.0.4", "typescript": "~4.5.5", "vue-template-compiler": "^2.6.14", "yargs": "^17.7.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <= 11"]}