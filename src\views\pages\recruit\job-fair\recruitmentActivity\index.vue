<template>
    <div v-if="tableConfig">
        <div class="core-ui-custom-header">
            <div class="title u-flex-1">
                {{ $route.meta.title }}
            </div>
            <el-button type="primary" plain @click="showMessage">
                说明
            </el-button>
            <el-button type="primary" @click="exportToExcel"> 导出 </el-button>
        </div>
        <table-filter
            ref="filter"
            :filedWidth="220"
            :tableFilter="tableConfig.filter"
            @search="search"
        />

        <common-table
            class="u-p-20"
            v-loading="l"
            :data="data"
            :columns="tableConfig.column"
        >
            <div slot="其他" slot-scope="scope">
                <span
                    :class="scope.row.其他 ? 'primary pointer' : ''"
                    @click="checkInfo(scope.row)"
                >
                    {{ scope.row.其他 || "-" }}
                </span>
            </div>
        </common-table>
        <OtherInfoPop v-model="otherInfoVisible" :info="otherInfo" />
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { tableDataToArray } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { ExcelGenerator } from "@/core-ui/component/table/excel-generator"
    import TableFilter from "@/core-ui/component/table/filter.vue"
    import { formatDate } from "@/core-ui/helpers/tools"
    import { routesMap } from "@/router/direction"
    import { MessageBox } from "element-ui"
    import { cloneDeep, find, flatMap, forEach, map, split } from "lodash"
    import moment from "moment"
    import { Component, Ref, Vue } from "vue-property-decorator"
    import { Row, tableConfig } from "."
    import OtherInfoPop from "./components/other-info-pop.vue"

    @Component({
        name: routesMap.recruit.DataResourceStatistics,
        components: { TableContainer, CommonTable, TableFilter, OtherInfoPop },
    })
    export default class DataResourcesStatisticsIndex extends Vue {
        data: Row[] = []
        refreshConfig = {
            fun: this.search,
            name: routesMap.recruit.DataResourceStatistics,
        }

        @Ref()
        private filter!: TableFilter

        private l = false

        tableConfig: TableConfig | null = null

        column: TableColumn[] = []

        private otherInfoVisible = false
        private otherInfo: {
            title: string
            activity_type_remark: number
        }[] = []

        async mounted() {
            const c = await tableConfig()
            const t = find(c.filter, { prop: "time" })
            if (t) {
                t.defaultValue = [
                    formatDate(+moment().startOf("month")),
                    formatDate(+moment().endOf("month")),
                ]
            }
            this.tableConfig = c
            this.column = [...this.tableConfig.column!]
            this.$nextTick(() => {
                this.search()
            })
        }

        buildDataAndColumn(res: Row[]) {
            if (!res.length) {
                return
            }

            const activityColumn: TableColumn[] = []
            forEach(res[0].activity_list, (i) => {
                activityColumn.push({
                    label: i.key,
                    prop: i.key,
                    minWidth: "140",
                    showOverflowTip: true,
                })
            })
            const c = this.column.find((i) => i.label === "专场招聘会数量（场）")
            if (c) {
                c.children = [
                    {
                        label: "合计",
                        prop: "activity_type_account",
                        minWidth: "100",
                        showOverflowTip: true,
                    },
                    ...activityColumn,
                ]
            }
            console.log(this.column)
            this.data = map(res, (i) => {
                const activityData: any = {}
                forEach(activityColumn, (a) => {
                    activityData[a.prop] = find(i.activity_list, {
                        key: a.prop,
                    })?.value
                })
                return { ...i, ...activityData }
            })
        }

        searchData: any = {}

        search() {
            const data = this.filter.getFilterData()
            this.searchData = this.tableConfig!.handleFilterData!(data!)
            this.l = true
            if (!this.searchData.start_date || !this.searchData.end_date) {
                return this.$message.error("请先选择时间")
            }
            this.tableConfig!.domainService!.post<{ result: Row[] }>(
                this.searchData
            ).then((r) => {
                this.buildDataAndColumn(r.result)
                this.l = false
            })
        }

        showMessage() {
            const h = this.$createElement
            const msg = `地区：荆州市合计所有区的数据，市辖区统计所属区域为 荆州市的数据，其他区统计所属区域为各个区及其所有下级区域的数据\n招聘形式：根据招聘会类型统计\n参与企业：统计每个招聘会审核通过的报名企业，企业参加多个招聘会则记多次\n提供岗位：统计每个招聘会审核通过的报名岗位，岗位参加多个招聘会则记多次\n进场求职人数：取数据填报数据\n达成就业意向：取数据填报数据\n发放宣传资料：取数据填报数据\n网络参与人数：取数据填报数据\n开展职业指导人次：取数据填报数据`
            MessageBox.alert("", "说明", {
                message: h(
                    "div",
                    {
                        class: "pre-line lh12",
                    },
                    split(msg, `\n`).map((i) => h("p", { class: "u-m-b-4" }, i))
                ),
            })
        }

        exportToExcel() {
            const first: string[] = []
            const second: string[] = []
            forEach(this.column, (i) => {
                first.push(i.label!)
                if (i.children) {
                    forEach(i.children, (j, k) => {
                        k && first.push("")
                        second.push(j.label!)
                    })
                } else {
                    second.push("")
                }
            })
            const column = flatMap(
                this.tableConfig!.column,
                (i) => i.children || [i]
            )
            ExcelGenerator.execute({
                primaryRows: [],
                columns: first,
                childColumns: second,
                rows: tableDataToArray(this.data, column, this.$createElement),
                fileName: `${this.$route.meta!.title}${
                    this.searchData.start_date
                }至${this.searchData.end_date}`,
            })
        }

        private checkInfo(data: Row) {
            if (!data.其他) {
                return
            }
            this.otherInfo = cloneDeep(data.other_job_fair).map((i) => {
                return {
                    ...i,
                    type: "其他",
                }
            })
            this.otherInfoVisible = true
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
</style>
