<template>
    <div class="content">
        <div class="core-ui-custom-header">
            <div class="title">{{ title }}</div>
            <div class="u-flex u-row-right"></div>
        </div>
        <div class="content1">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="area-select">所在区域：</div>
                    <RegionTree
                        v-show="isShowCascader"
                        :data="regionTreeData"
                        @change="regionTreeChange"
                        @showCascader="showCascader"
                        :maxLevel="3"
                    ></RegionTree>

                    <!-- <div class="area-region-name">{{ regionName }}</div> -->
                </div>

                <div>
                    <el-button
                        type="primary"
                        @click="importQjExcel"
                        v-role="[
                            'model.tg_enterprise.action.import_te_enterprise',
                        ]"
                    >
                        导入
                    </el-button>
                    <el-button type="primary" @click="toSearch">
                        信息查询
                    </el-button>
                </div>
            </div>
            <div class="info-items" v-for="(item, index) in data" :key="index">
                <div class="info-label">{{ item.label }}</div>
                <div class="info-box d-flex">
                    <div
                        class="left-box"
                        v-for="(data, subIndex) in item.leftBox"
                        :key="'left' + subIndex"
                    >
                        <div
                            class="item"
                            :class="{ pointer: !!data.filters }"
                            @click="toDetailList(data)"
                        >
                            <div
                                class="item-label"
                                :class="{ bigger: item.rightBox.length > 1 }"
                            >
                                {{ data.value }}
                            </div>
                            <div>{{ data.label }}</div>
                        </div>
                    </div>
                    <div class="right-box">
                        <div
                            class="right-box-line"
                            v-for="(line, lineIndex) in item.rightBox"
                            :key="'line' + lineIndex"
                        >
                            <template v-for="(subItem, subItemIndex) in line">
                                <div
                                    class="item pointer"
                                    v-if="!subItem.hide"
                                    :key="'right' + subItemIndex"
                                    :class="{
                                        pointer: !!subItem.filters,
                                    }"
                                    @click="toDetailList(subItem)"
                                >
                                    <div class="item-label">
                                        {{ subItem.value }}
                                    </div>
                                    <div>{{ subItem.label }}</div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="content2" v-show="showList">
            <table-container
                v-if="tableConfig"
                filedWidth="200"
                :showPageIndex="false"
                ref="table"
                v-model="tableConfig"
                class="container"
            >
                <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                    <common-table :data="data" :columns="columns">
                    </common-table>
                </div>
            </table-container>
        </div>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入企业信息库"
            :importConfig="importConfig"
            @refresh="reloadList"
        />
    </div>
</template>

<script lang='ts'>
    import { sdk } from "@/service"
    import { Component, Prop } from "vue-property-decorator"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import RegionTree from "../labor-info-base-manage/components/region-tree.vue"
    import {
        buildRegionObjParams,
        getRegionCode,
        getRegionKey,
        getRegionKeyByIndex,
    } from "../components/region-tool"
    import { config, EnvProject } from "@/config"
    import { routesMap } from "@/router/direction"
    import { detailListTitieMapping } from "."
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"

    interface Row {
        id: number
        label: string
        showDetail?: number
        filters?: {
            list_name: string
        }
    }

    @Component({
        components: { TableContainer, CommonTable, RegionTree, ExcelImport },
    })
    export default class CommonList extends BaseTableController<Row> {
        @Prop()
        private title!: string

        @Prop()
        private list_name!: string

        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []

        private showList = false

        private firstData: any = []
        private dataInfo: any = []

        private data: any[] = []
        private region_code = getRegionCode()
        private isQj = [EnvProject.潜江项目].includes(config.envProject)
        private showImportPop = false
        private importConfig: any = null

        private regionTreeData: any = {
            level: 3,
            type: "region_code",
            treeInfo: {
                manage_region_full_path: getRegionCode(),
            },
        }

        private regionKey = getRegionKey()

        private itemWidth = 0

        private isShowCascader = true
        private regionName = ""

        created() {
            this.getListData({})
            this.init()
            this.importConfig = {
                templateUrl:
                    window.location.origin +
                    `/${
                        process.env.BASE_URL === "/" ? "" : process.env.BASE_URL
                    }/file/潜江/潜江企业信息库导入模板.xlsx`.replace("//", "/"),
                modelName: "tg_enterprise",
                actionName: "import_te_enterprise",
                bigActionImportParams: {
                    dataDetails: [],
                    inputs_parameters: [],
                    prefilters: [],
                    selected_list: [],
                    tagInfos: [],
                    tagInfosForList: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
        }

        private showCascader(flag: boolean, regionName: string) {
            // this.isShowCascader = flag
            // this.regionName = regionName
            return { flag, regionName }
        }

        private getListData(detail: any) {
            this.data = [
                {
                    label: "企业总体情况",
                    leftBox: [
                        {
                            label: "市场主体总数",
                            value: detail["市场主体总数"]?.count || 0,
                            showDetail: detail["市场主体总数"]?.show_detail,
                            filters: detail["市场主体总数"]?.filters,
                        },
                    ],
                    rightBox: [
                        [
                            {
                                label: "企业主体",
                                value: detail["企业数"]?.count || 0,
                                showDetail: detail["企业数"]?.show_detail,
                                filters: detail["企业数"]?.filters,
                            },
                            {
                                label: "个体工商户",
                                value: detail["个体工商户数量"]?.count || 0,
                                showDetail: detail["个体工商户数量"]?.show_detail,
                                filters: detail["个体工商户数量"]?.filters,
                            },
                            {
                                label: "其他",
                                value: detail["其他"]?.count || 0,
                                showDetail: detail["其他"]?.show_detail,
                                filters: detail["其他"]?.filters,
                            },
                        ],
                        [
                            {
                                label: "第一产业",
                                value: detail["第一产业"]?.count || 0,
                                showDetail: detail["第一产业"]?.show_detail,
                                filters: detail["第一产业"]?.filters,
                            },
                            {
                                label: "第二产业",
                                value: detail["第二产业"]?.count || 0,
                                showDetail: detail["第二产业"]?.show_detail,
                                filters: detail["第二产业"]?.filters,
                            },
                            {
                                label: "第三产业",
                                value: detail["第三产业"]?.count || 0,
                                showDetail: detail["第三产业"]?.show_detail,
                                filters: detail["第三产业"]?.filters,
                            },
                            {
                                label:
                                    config.envProject === EnvProject.黄州项目
                                        ? "四上企业"
                                        : "规模以上企业",
                                value: detail["规上企业"]?.count || 0,
                                showDetail: detail["规上企业"]?.show_detail,
                                filters: detail["规上企业"]?.filters,
                            },
                        ],
                    ],
                },
                {
                    label: "企业入驻情况",
                    hidden: [
                        EnvProject.宜都项目,
                        EnvProject.潜江项目,
                        EnvProject.洪湖项目,
                    ].includes(config.envProject),
                    leftBox: [
                        {
                            label: "入驻市场总数",
                            value: detail["入驻市场总数"]?.count || 0,
                            showDetail: detail["入驻市场总数"]?.show_detail,
                            filters: detail["入驻市场总数"]?.filters,
                        },
                    ],
                    rightBox: [
                        [
                            {
                                label: "入驻招聘企业",
                                value: detail["入驻招聘企业"]?.count || 0,
                                showDetail: detail["入驻招聘企业"]?.show_detail,
                                filters: detail["入驻招聘企业"]?.filters,
                            },
                            {
                                label: "入驻人力机构",
                                value: detail["入驻人力机构"]?.count || 0,
                                showDetail: detail["入驻人力机构"]?.show_detail,
                                filters: detail["入驻人力机构"]?.filters,
                                hide: [EnvProject.黄州项目].includes(
                                    config.envProject
                                ),
                            },
                        ],
                    ],
                },
                {
                    label: "近1年企业招聘需求",
                    hidden: [
                        EnvProject.宜都项目,
                        EnvProject.潜江项目,
                        EnvProject.洪湖项目,
                    ].includes(config.envProject),
                    leftBox: [
                        {
                            label: "岗位总数",
                            value: detail["岗位总数"]?.count || 0,
                            showDetail: detail["岗位总数"]?.show_detail,
                            filters: detail["岗位总数"]?.filters,
                        },
                    ],
                    rightBox: [
                        [
                            {
                                label: "第三方平台岗位",
                                value: detail["三方岗位数"]?.count || 0,
                                showDetail: detail["三方岗位数"]?.show_detail,
                                filters: detail["三方岗位数"]?.filters,
                            },
                            {
                                label: "本平台岗位",
                                value: detail["本平台岗位数"]?.count || 0,
                                showDetail: detail["本平台岗位数"]?.show_detail,
                                filters: detail["本平台岗位数"]?.filters,
                            },
                        ],
                    ],
                },
                {
                    label: "企业用工情况",
                    hidden: [
                        EnvProject.宜都项目,
                        EnvProject.鄂州项目,
                        EnvProject.潜江项目,
                        EnvProject.洪湖项目,
                    ].includes(config.envProject),
                    leftBox: [],
                    rightBox: [
                        [
                            {
                                label: "近七天新增用工信息数",
                                value: detail["新增用工信息"]?.count || 0,
                                showDetail: detail["新增用工信息"]?.show_detail,
                                filters: detail["新增用工信息"]?.filters,
                            },
                            {
                                label: "近七天新增用工需求数",
                                value: detail["新增用工需求"]?.count || 0,
                                showDetail: detail["新增用工需求"]?.show_detail,
                                filters: detail["新增用工需求"]?.filters,
                            },
                        ],
                    ],
                },
            ].filter((i) => !i.hidden)
        }

        private init() {
            sdk.getDomainService(
                "getTgStaticsv2",
                "tg_enterprise_info_api",
                "xg_project"
            )
                .post(buildRegionObjParams(this.region_code || ""))
                .then((res: any) => {
                    this.getListData(res.couts)

                    let tableData: any = {
                        // district: "下级区域",
                        region_name: "下级区域",
                        个体户数量: "个体工商户",
                        企业数量: "企业主体",
                        入驻数量: "入驻企业",
                        // 规上企业: "规模以上企业",
                        postion_count: "招聘需求",
                    }
                    if ([EnvProject.潜江项目].includes(config.envProject)) {
                        tableData = {
                            region_name: "下级区域",
                            个体户数量: "个体工商户",
                            企业数量: "企业主体",
                        }
                    }
                    const query: any = {
                        request: () => {
                            return new Promise((resolve) => {
                                const columns: any[] = []
                                Object.keys(tableData).forEach((i: string) => {
                                    columns.push({
                                        label: tableData[i],
                                        prop: i,
                                        showOverflowTip: true,
                                    })
                                })
                                this.columns = columns
                                const list = res.list || []
                                this.showList = !!list.length
                                const sortByList = _.sortBy(list, (i) => {
                                    return i.region_name ? -1 : 1
                                })
                                sortByList.forEach((i) => {
                                    if (!i.region_name) {
                                        i.region_name = "其他"
                                    }
                                })
                                resolve({
                                    data: sortByList,
                                    total_count: 99,
                                })
                            })
                        },
                    }

                    this.tableConfig = null

                    this.$nextTick(() => {
                        this.tableConfig = {
                            domainService: query,
                            defaultPageSize: 99,
                        }
                    })

                    console.log(res)
                })
        }

        private toSearch() {
            this.$emit("toSearch")
        }

        private regionTreeChange(r: any) {
            this.$emit("regionTreeChange", r)

            const regionCode = (getRegionCode() || "").split(",")
            const values = r.values[0].split(",")
            const preRegionCode = []
            if (regionCode[0]) {
                if (regionCode[0] && values[0] !== regionCode[0]) {
                    preRegionCode.push(regionCode[0])
                    if (regionCode[1] && values[0] !== regionCode[1]) {
                        preRegionCode.push(regionCode[1])
                        if (regionCode[2] && values[0] !== regionCode[2]) {
                            preRegionCode.push(regionCode[2])
                            if (regionCode[3] && values[0] !== regionCode[3]) {
                                preRegionCode.push(regionCode[3])
                                if (regionCode[4] && values[0] !== regionCode[4]) {
                                    preRegionCode.push(regionCode[4])
                                }
                            }
                        }
                    }
                }
            }

            values.unshift(...preRegionCode)

            if (r.values && r.values.length > 0) {
                this.regionKey =
                    getRegionKeyByIndex(r.regionGrade - 1) || this.regionKey
                const codes = values
                this.region_code = codes.join(",")
                this.init()
            }
        }

        private toDetailList(data: Row) {
            const title = detailListTitieMapping.get(data.label)
            if (data.filters) {
                this.$router.push({
                    name: routesMap.collectTaskManage.marketDatabaseManage
                        .enterpriseDatabase.detailList,
                    query: {
                        filters: JSON.stringify(data.filters || ({} as any)),
                        region_code: JSON.stringify(
                            buildRegionObjParams(this.region_code || "")
                        ),
                        show_detail: data.showDetail + "",
                        title,
                        from: this.$route.name,
                    },
                })
            }
        }

        importQjExcel() {
            this.showImportPop = true
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        overflow: auto;
    }
    .content1 {
        background-color: #fff;
        padding: 20px;

        .area-select {
            width: 100px;
            color: #222222;
            font-size: 18px;
            word-break: keep-all;
        }

        .area-region-name {
            line-height: 18px;
            font-size: 18px;
            vertical-align: middle;
        }

        .info-label {
            height: 50px;
            font-weight: 600;
            font-size: 18px;
            color: #000000;
            line-height: 50px;
        }

        .info-box {
            width: 100%;
            gap: 20px;

            .left-box {
                width: 22%;
                background: #f4f1ff;
                border-radius: 8px;

                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }

            .right-box {
                display: flex;
                flex-direction: column;
                gap: 20px;
                flex: 1;

                .right-box-line {
                    display: flex;
                    flex: 1;
                    gap: 20px;

                    .item {
                        background: #f1f5ff;
                    }

                    &:nth-child(2) {
                        .item {
                            background-color: #ebf9f9;

                            .item-label {
                                color: #32b2b2;
                            }
                        }
                    }
                }
            }

            .item {
                flex: 1;
                border-radius: 8px;
                height: 90px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 10px;
                color: #333333;
                &.pointer {
                    cursor: pointer;
                    div:not(:first-child) {
                        position: relative;
                        text-decoration: underline;
                        &:hover {
                            color: var(--primary);
                        }
                        &::after {
                            content: " >";
                            // margin-left: 5px;
                            font-size: 14px;
                        }
                    }
                }

                .item-label {
                    font-weight: 600;
                    font-size: 30px;
                    color: #5782ec;
                    line-height: 30px;
                    text-align: center;
                    font-style: normal;

                    &.bigger {
                        font-size: 40px;
                        line-height: 40px;
                    }
                }
            }
        }
    }

    .content2 {
        background-color: #fff;
        margin-top: 20px;
    }
</style>
