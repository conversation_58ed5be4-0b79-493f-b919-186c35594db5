<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="1200px"
    >
        <div>
            <table-container
                v-if="tableConfig"
                showTableFilter
                filedWidth="200"
                ref="table"
                v-model="tableConfig"
                :useTab="true"
                highlight-current-row
                class="container"
            >
                <div slot="table" slot-scope="{ data }" class="u-p-20">
                    <common-table :data="data" :columns="columns">
                        <template slot="listRadio" slot-scope="scope">
                            <el-radio v-model="agent_id" :label="scope.row.id">
                            </el-radio>
                        </template>
                    </common-table>
                </div>
            </table-container>

            <div class="u-flex u-row-center u-m-t-40">
                <el-button
                    @click="close"
                    class="btn u-m-r-30"
                    type="primary"
                    plain
                >
                    取消
                </el-button>
                <el-button
                    class="btn"
                    type="primary"
                    :loading="loading"
                    @click="confirm"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Mixins } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { pageLoading } from "@/views/controller"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { buildConfig4RemoteMeta } from "@/views/pages/collect-task-manage/components/build-table"
    import { sdk } from "@/service"
    import { buildFormSections } from "@/core-ui/component/form"

    @Component({
        components: { TableContainer, CommonTable },
    })
    export default class LaborInfoBase extends Mixins(
        BaseTableController,
        DialogController
    ) {
        tableConfig: TableConfig | null = null

        private columns: TableColumn[] = []

        private agent_id = null

        private get title() {
            return "企业列表"
        }

        onOpen() {
            this.init()
        }

        private init() {
            buildFormSections({
                action: this.action(),
                forms: [],
                prefilters: [
                    {
                        property: "job_fair_id",
                        value: this.$route.query.id,
                    },
                ],
            }).then((r) => {
                console.log(r)
            })

            pageLoading(() => {
                return buildConfig4RemoteMeta("xg_agent", "intent_search_list", {
                    disabledOpt: true,
                    useLabelWidth: true,
                    addSelector: true,
                    isSingleSelector: true,
                }).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private action() {
            return sdk.core
                .model("job_fair_agent_apply")
                .action("add_agent")
                .updateInitialParams({
                    selected_list: [],
                    prefilters: [
                        {
                            property: "job_fair_id",
                            value: this.$route.query.id,
                        },
                    ],
                })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = r.columns
        }

        private confirm() {
            if (!this.agent_id) {
                return this.$message.warning("请选择企业")
            }
            const data = {
                agent_id: this.agent_id,
            }

            this.loading = true
            this.action()
                .addInputs_parameter(data)
                .execute()
                .then(() => {
                    this.close()
                    this.$emit("refresh")
                })
                .finally(() => {
                    this.loading = false
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        // background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }

        /deep/ .el-radio__label {
            display: none;
        }
    }
</style>
