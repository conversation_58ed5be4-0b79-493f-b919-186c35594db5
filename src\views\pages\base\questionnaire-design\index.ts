import { BuildFormConfig, FileType, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
const tableFilter: TableFilter[] = [
    {
        label: "问卷标题",
        type: FormType.Text,
        prop: "title",
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("general_questionnaire").list(),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: {
            title: "",
            deadline: "",
            type: "label",
            target_type: "label",
            share_image: "",
            share_h5_link: "",
            is_using: "is_using",
        },
    }
}

export interface Row {
    /** 问卷名称 */
    title: string
    /** 答题截止时间 */
    deadline: string
    /** 问卷类型 */
    type: Type
    /** 问卷类型[文本] */
    type_label: string
    /** 问卷对象类型 */
    target_type: TargetType
    /** 问卷对象类型[文本] */
    target_type_label: string
    /** 小程序分享图片 */
    share_image: string
    /** h5地址 */
    share_h5_link: string
    id: number
    v: number
}

const enum Type {
    问卷模板 = 0,
    任务问卷 = 1,
    独立问卷 = 2,
}

const enum TargetType {
    个人用户 = 0,
    企业用户 = 1,
}

export const columns: TableColumn[] = [
    {
        label: "问卷",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "答题截止时间",
        prop: "deadline",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.deadline)
        },
    },
    {
        label: "问卷类型",
        prop: "type_label",
        showOverflowTip: true,
    },
    {
        label: "问卷对象类型",
        prop: "target_type_label",
        showOverflowTip: true,
    },
    {
        label: "小程序分享图片",
        prop: "share_image",
        showOverflowTip: true,
        render: (h, row) => {
            return h("img", {
                attrs: {
                    src: sdk.buildImage(row?.share_image || ""),
                },
                style: {
                    width: "100px",
                    height: "100px",
                },
            })
        },
    },
    {
        label: "h5地址",
        prop: "share_h5_link",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "160px",
        showOverflowTip: true,
    },
]

const intentPredict = {
    title: "title",
    description: "",
    id: "",
}

const intentColumn: TableConfig["column"] = [
    {
        label: "ID",
        prop: "id",
        showOverflowTip: true,
    },
    {
        label: "问卷标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "备注",
        prop: "description",
        showOverflowTip: true,
    },
]

export const intentFilter: TableFilter[] = [
    {
        label: "问卷标题",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
]

export function createFormConfig(id: string): BuildFormConfig {
    const forms: BuildFormConfig["forms"] = [
        {
            label: "问卷标题",
            type: FormType.Text,
            prop: "title",
            required: true,
        },
        {
            label: "问卷是否启用",
            type: FormType.Switch,
            prop: "is_using",
            required: true,
            hide: !!id,
        },
        {
            label: "描述",
            type: FormType.Text,
            prop: "description",
        },
        {
            label: "答题截止时间",
            type: FormType.DatePicker,
            prop: "deadline",
            required: true,
        },
        {
            label: "问卷类型",
            type: FormType.Select,
            prop: "type",
            required: true,
            hide: !!id,
        },
        {
            label: "问卷对象类型",
            type: FormType.Select,
            prop: "target_type",
            required: true,
        },
        {
            label: "小程序分享图片",
            type: FormType.MyUpload,
            prop: "share_image",
            required: true,
            option: {
                fileType: [FileType.Image],
                listType: "picture-card",
                placeholder: "支持上传jpg、png等图片格式,最多支持三张",
                limit: 1,
                limitSize: 3072,
            },
        },
        {
            label: "问卷模版",
            type: FormType.IntentSearch,
            prop: "questionnaire_id",
            option: {
                dialogProp: {
                    width: "800px",
                },
                intentSearchConfig: {
                    tableConfig: () => ({
                        model: sdk.core
                            .model("general_questionnaire")
                            .list("intent_search_list"),
                        preFilter: {
                            is_using: "1",
                            type: "0",
                        },
                        predict: intentPredict,
                        column: intentColumn,
                        filter: intentFilter,
                    }),
                    template: "{title}",
                    valueKey: "id",
                },
            },
            hide: !!id,
        },
    ]
    return {
        sdkModel: "general_questionnaire",
        sdkAction: id ? "update_base_info" : "insert",
        id: +id,
        forms,
    }
}
