const defaultDomainAllInOne = {
    VUE_APP_UNIPLAT: "/api",
    VUE_APP_UNIPLAT_WEB: "/uniplat",
    VUE_APP_H5: "/h5",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
    // 公招网url
    VUE_APP_GZW_URL: "/",
    // 启用升级页面
    VUE_APP_DISABLE_UPGRADE: "",
}
const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "咸丰",
    VUE_APP_CITY_SAMPLE_NAME2: "咸丰县",
    VUE_APP_DEFAULT_REGION_CODE: "422826000000",
    VUE_APP_DEFAULT_REGION_NAME: "咸丰县",
    VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "咸丰县人民政府",
    VUE_APP_BAIDU_KEY: "Xa338L0G79uVCEmkg7tIXOZtCLEPFfMY",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "PLNBSP96NG248ZFQ",
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-xianfeng",
    VUE_APP_AES_ENCRYPT_KEY_PATH_BK: "./bk-key-xg",
    VUE_APP_MINI_PROGRAM_NAME: "咸丰就业",
    VUE_APP_BIGSCREEN_BI_PATH: "/bigScreen",
}

const { config } = require("./xianfeng_p.js")

module.exports = {
    name: "咸丰项目",
    env: {
        test: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_UNIPLAT: "http://jzpes-h5-test.teammix.com:8800/api",
            // VUE_APP_UNIPLAT: "http://jzpes.test-api.qqxb.jinsehuaqin.com:8800",
            // 共享用工局方端
            VUE_APP_BK_JOB_SHARE_ADMIN:
                "http://jzpes.admin-test.qqxb.jinsehuaqin.com:8800",
            // 共享用工企业端
            VUE_APP_BK_ENT_ADMIN_PATH:
                "http://jzpes.com-test.qqxb.jinsehuaqin.com:8800",
            VUE_APP_requestEncoder: "", // 入参加密 aes
            VUE_APP_responseEncoder: "", // 返回参加密 aes
            // 培训H5
            VUE_APP_BK_PEIXUN_H5_URL:
                "http://jzpes.tra-test.qqxb.jinsehuaqin.com:8800",
        },
        pro: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_UNIPLAT: "https://www.xfzhhjy.cn/api",
            // 共享用工局方端
            // VUE_APP_BK_JOB_SHARE_ADMIN: "https://admin.jzjycy.com",
            // 共享用工企业端
            // VUE_APP_BK_ENT_ADMIN_PATH: "https://hr.jzjycy.com",
            VUE_APP_requestEncoder: "aes", // 入参加密 aes
            VUE_APP_responseEncoder: "aes", // 返回参加密 aes
            // 培训H5
            // VUE_APP_BK_PEIXUN_H5_URL: "https://tra.jzjycy.com",
            VUE_APP_BK_LINGGONG_ADMIN: "https://admin.xfzhhjy.cn",
            VUE_APP_BK_LINGGONG_JIGOU_ADMIN: "https://yz.ddggjy.com",
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "就业管理工作台",
                BASE_URL: "platform/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                // 一般用于master分支
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/platform2",
                    host: "88",
                    env: {
                        VUE_APP_UNIPLAT_ENTRANCE: "荆州市智慧就业服务工作台",
                    },
                },
                pro: {
                    path: "/mnt/xianfeng/web/platform/production-pro",
                    host: "qqxb-ez",
                    env: {},
                },
            },
        },
        // 企业端
        {
            name: "企业端",
            env: {
                VUE_APP_HEADER_TITLE: "企业智慧就业服务平台",
                VUE_APP_UNIPLAT_ENTRANCE: "企业智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org",
                BASE_URL: "org/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["企业端"],
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/org",
                    host: "88",
                    env: {},
                },
                pro: {
                    path: "/mnt/jingzhou/front-web/org",
                    host: "qqxb-org",
                    env: {},
                },
            },
        },
        // 机构端
        {
            name: "机构端",
            env: {
                VUE_APP_HEADER_TITLE: "人资机构就业服务平台",
                VUE_APP_UNIPLAT_ENTRANCE: "人力资源机构智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org_hr",
                BASE_URL: "hr/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["机构端"],
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/hr",
                    host: "88",
                    env: {},
                },
                pro: {
                    path: "/mnt/jingzhou/front-web/hr",
                    host: "qqxb-org",
                    env: {},
                },
            },
        },
        {
            name: "培训机构端",
            env: {
                // VUE_APP_HEADER_TITLE: "荆州培训机构管理系统",
                // VUE_APP_UNIPLAT_ENTRANCE: "居民就业服务客户端",
                VUE_APP_APP_NAME: "xg_project_train",
                VUE_APP_HEADER_TITLE: "培训机构管理系统",
                VUE_APP_UNIPLAT_ENTRANCE: "培训机构服务平台",
                // VUE_APP_APP_NAME: "xg_project_org",
                BASE_URL: "train/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["培训机构端"],
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/train",
                    host: "88",
                    env: {},
                },
                pro: {
                    path: "/mnt/jingzhou/front-web/train",
                    host: "jz-en",
                    env: {},
                },
            },
        },
        {
            name: "网格员端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "居民就业服务客户端",
                VUE_APP_HEADER_TITLE: "劳动力资源信息采集平台",
                VUE_APP_APP_TITLE: "劳动力资源信息采集平台",
                BASE_URL: "grid/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["网格员端"],
                VUE_APP_APP_NAME: "xg_project_grid",
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/grid",
                    host: "88",
                    env: {},
                },
            },
        },
    ],
}
