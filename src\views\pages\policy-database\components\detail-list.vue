<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
        >
            <div slot="btn-right" class="u-m-l-20">
                <el-button @click="showImportPop = true" type="primary">
                    导入
                </el-button>
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns"> </common-table>
            </div>
        </table-container>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入政策办理情况"
            placeholder="请点击「确定」上传"
            :customTemplate="true"
            :importConfig="importConfig"
            @refresh="reloadList"
            @exportTemplate="exportTemplate"
            projectName="warning_platform"
            :beforeConfirm="beforeConfirm"
        >
            <div slot="top" class="u-p-x-20 u-m-b-20 u-flex">
                <div>数据跨度结束时间：</div>
                <el-date-picker
                    v-model="
                        importConfig.bigActionImportParams.inputs_parameters[1]
                            .value
                    "
                    value-format="yyyy-MM-dd"
                ></el-date-picker>
            </div>
        </excel-import>
    </div>
</template>

<script lang='ts'>
    import { Component, Prop } from "vue-property-decorator"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import {
        TableColumn,
        TableConfig,
        TableFilter,
    } from "@/core-ui/component/table"
    import { sdk } from "@/service"
    import { buildSelectSource, FormType } from "@/core-ui/component/form"
    import { find, isArray, map } from "lodash"
    import moment from "moment"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { ExcelGenerator } from "@/core-ui/component/table/excel-generator"

    @Component({ components: { CommonTable, TableContainer, ExcelImport } })
    export default class DetailList extends BaseTableController<any> {
        @Prop()
        detail!: any

        private showImportPop = false
        private importConfig: any = null
        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []
        private filters: TableFilter[] = []

        mounted() {
            this.init()
        }

        private async init() {
            await this.buildColumns()
            const buildParams = this.buildParams
            this.tableConfig = {
                domainService: sdk.getDomainService(
                    "get_policy",
                    "back_api",
                    "warning_platform"
                ),
                filter: this.filters,
                preFilter: {
                    policy_id: this.detail.id,
                },
                defaultPageSize: 10,
                handleFilterData(params) {
                    return buildParams(params)
                },
            }
            this.importConfig = {
                // templateUrl: window.location.origin + "/file/离职人员导入模板.xls",
                modelName: "policy_conditions_object",
                actionName: "import_policy_object",
                prefilters: [
                    {
                        property: "policy_id",
                        value: this.detail.id,
                        prefilters: null,
                        relationalOperator: null,
                    },
                ],
                bigActionImportParams: {
                    inputs_parameters: [
                        {
                            property: "policy_id",
                            value: this.detail.id,
                        },
                        {
                            property: "collect_endTime",
                            value: "",
                        },
                    ],
                    selected_list: [
                        // {
                        //     id: this.taskId,
                        //     v: 0,
                        // },
                    ],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
        }

        private beforeConfirm() {
            if (
                this.importConfig.bigActionImportParams.inputs_parameters[1].value
            ) {
                return Promise.resolve(true)
            }
            this.$message.warning("请填写数据跨度结束时间")
            return Promise.resolve(false)
        }

        private buildParams(params: any) {
            const pList = map(params, (e, k) => {
                if (!e) return undefined
                const i: any = find(this.filters, (i) => i.prop === k)
                const dateType = i?.option?.origin_type || i?.option?.type || ""
                let info_value: any = e
                if (["month", "quarter"].includes(dateType)) {
                    info_value[0] = moment(info_value[0]).format("YYYY-MM-DD")
                    info_value[1] =
                        moment(info_value[1]).format("YYYY-MM") +
                        "-" +
                        moment(info_value[1]).endOf("month").format("DD")
                }
                if (["year"].includes(dateType)) {
                    info_value = [
                        moment(info_value).format("YYYY-MM-DD"),
                        moment(info_value).format("YYYY") + "-12-31",
                    ]
                }
                if (["date"].includes(dateType)) {
                    info_value = [
                        moment(info_value[0]).format("YYYY-MM-DD"),
                        moment(info_value[1]).format("YYYY-MM-DD"),
                    ]
                }
                if (isArray(info_value)) {
                    info_value = info_value.join(",")
                }
                return {
                    info_key: k,
                    info_value,
                    date_type: dateType,
                    filter_type: i?.option?.filter_type,
                    info_type: dateType,
                }
            }).filter(Boolean)

            return {
                filter: pList,
            }
        }

        private async buildColumns() {
            const data: any = await sdk
                .getDomainService("get_title", "back_api", "warning_platform")
                .post({ policy_id: this.detail.id })

            const i: any[] = data?.filter || []
            this.filters = i.map((e) => {
                let type = FormType.Select
                let timeType = ""
                let timeFormat = ""
                let sourceInputsParameter: any
                if (["date", "month", "quarter", "year"].includes(e.info_type)) {
                    type = FormType.DatePicker
                    timeType = (
                        {
                            date: "daterange",
                            month: "monthrange",
                            quarter: "monthrange",
                            year: "year",
                        } as any
                    )[e.info_type]
                    timeFormat = (
                        {
                            date: undefined,
                            month: "yyyy-MM",
                            quarter: "yyyy-MM",
                            year: "yyyy",
                        } as any
                    )[e.info_type]
                } else {
                    if (e.info_type === "text") {
                        type = FormType.Text
                        timeType = "2"
                    }
                    sourceInputsParameter = buildSelectSource(
                        map(e.mapping, (i) => ({
                            key: i.map_value,
                            value: i.map_key,
                        }))
                    )
                }
                return {
                    label: e.label || e.info_key,
                    type,
                    option: timeType
                        ? {
                              type: timeType,
                              filter_type: e.filter_type,
                              format: timeFormat,
                              origin_type: e.info_type,
                          }
                        : undefined,
                    prop: e.info_key,
                    sourceInputsParameter,
                }
            }) as any
            this.columns = (data?.title || []).map((e: any) => {
                return {
                    label: e.info_key,
                    prop: e.info_key,
                    require: e.require,
                }
            })
        }

        exportTemplate() {
            ExcelGenerator.execute({
                fileName: "政策办理情况导入模板",
                rows: [],
                columns: this.columns.map((i: any, index) => ({
                    v: i.label.replace("*", ""),
                    s: {
                        font: {
                            color: { rgb: i.require ? "ff0000" : "00f0000" },
                        },
                    },
                })) as any,
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
