<template>
    <div class="h-100 d-flex" :class="themeClassName">
        <div class="left h-100 u-flex-none u-rela" :style="leftStyle">
            <img class="banner" :style="bannerStyle" :src="bannerImg" />
            <img :style="bottomStyle" :src="bottomImg" v-if="bottomImg" />
            <img
                :src="img.bgImg"
                :style="img.bgImgStyle"
                v-for="(img, index) in extraImgs"
                :key="index"
            />
        </div>
        <div class="right u-flex-1 h-100" :style="rightStyle">
            <div
                class="container container-box"
                :class="{
                    'has-security': showSecurityCardLogin,
                }"
            >
                <el-tabs v-model="currentTab" :stretch="true">
                    <!-- <el-tab-pane label="企业微信登录" name="mp-work">
                        <mp-work-login
                            @loginSuccess="loginSuccess"
                        ></mp-work-login>
                    </el-tab-pane> -->
                    <el-tab-pane
                        v-if="showSecurityCardLogin"
                        label="扫码登录"
                        name="SecurityCardLogin"
                    >
                        <SecurityCardLogin
                            @loginSuccess="loginSuccess"
                            @goApplyCompany="goApplyCompany"
                        />
                    </el-tab-pane>
                    <el-tab-pane
                        :label="showForgetPassword ? '忘记密码' : '密码登录'"
                        name="password"
                    >
                        <forget-password
                            v-if="showForgetPassword"
                            @setMobile="mobile = $event"
                            :mobile="mobile"
                            @showForgetPassword="showForgetPassword = $event"
                        ></forget-password>
                        <password-login-form
                            v-else
                            @setMobile="mobile = $event"
                            :mobile="mobile"
                            @showForgetPassword="showForgetPassword = $event"
                            @loginSuccess="loginSuccess"
                            @goApplyCompany="goApplyCompany"
                        ></password-login-form>
                    </el-tab-pane>
                    <el-tab-pane
                        v-if="!showForgetPassword && !config.isDh"
                        label="验证码登录"
                        name="mobile"
                    >
                        <mobile-login-form
                            v-if="!useCaptchaMobile"
                            @setMobile="mobile = $event"
                            :mobile="mobile"
                            @loginSuccess="loginSuccess"
                            @goApplyCompany="goApplyCompany"
                        ></mobile-login-form>

                        <captcha-mobile-login-form
                            v-if="useCaptchaMobile"
                            @setMobile="mobile = $event"
                            :mobile="mobile"
                            @loginSuccess="loginSuccess"
                            @goApplyCompany="goApplyCompany"
                        ></captcha-mobile-login-form>
                    </el-tab-pane>
                </el-tabs>
                <div id="captcha-div"></div>
            </div>
        </div>
        <select-company
            @close="close"
            @change="change"
            :showSelect="false"
            :title="switchTitle"
            v-model="showSelectCompanyDialog"
        />
    </div>
</template>

<script lang="ts">
    import { config, EnvApplication, EnvProject } from "@/config"
    import { currentAppRoute } from "@/router"
    import { routesMap } from "@/router/direction"
    import { sceneKey, userService } from "@/service/service-user"
    import { loadingController } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import MobileLoginForm from "./components/login-form/mobile-login-form.vue"
    import CaptchaMobileLoginForm from "./components/login-form/captcha-mobile-login-form.vue"
    import MpWorkLogin from "./components/login-form/mp-work-login.vue"
    import PasswordLoginForm from "./components/login-form/password-login-form.vue"
    import SecurityCardLogin from "./components/login-form/security-card-login.vue"
    import ForgetPassword from "./components/password/forget-password.vue"
    import SelectCompany from "./components/select-compony.vue"

    @Component({
        components: {
            MobileLoginForm,
            CaptchaMobileLoginForm,
            PasswordLoginForm,
            ForgetPassword,
            MpWorkLogin,
            SelectCompany,
            SecurityCardLogin,
        },
    })
    export default class LoginIndex extends Vue {
        private showForgetPassword = false
        currentTab =
            config.envProject === EnvProject.孝感项目
                ? "SecurityCardLogin"
                : "password"

        switchTitle = [EnvApplication.局方端].includes(config.envApplication)
            ? "管理的组织机构切换"
            : "选择企业"

        private bannerImg = config.projectConfig.bannerImg

        private bannerStyle = config.projectConfig.bannerStyle || {}

        private bottomImg = config.projectConfig.bottomImg || ""
        private bottomStyle = config.projectConfig.bottomStyle || {}

        private extraImgs = config.projectConfig.extraImgs || []

        private title = config.appTitle
        private leftStyle = config.projectConfig.leftStyle || {}

        private rightStyle = config.projectConfig.rightStyle || {}
        private themeClassName = config.projectConfig.themeClass || ""
        private mobile = ""
        private config = config
        private showSecurityCardLogin = [EnvProject.孝感项目].includes(
            config.envProject
        )

        private showSelectCompanyDialog = false

        private useCaptchaMobile = sessionStorage.getItem("useCaptchaMobile")

        private loginSuccess() {
            loadingController(async () => {
                const oldKey = userService.getAgentIdCache()
                if (oldKey) {
                    await this.goNext()
                } else {
                    await userService.getAgentList().then(async (r1) => {
                        if (r1.length > 1) {
                            this.showSelectCompanyDialog = true
                            setTimeout(() => {
                                window.localStorage.removeItem("currentAgentIdKey")
                            }, 500)
                        } else {
                            await this.goNext()
                        }
                    })
                }
            })
        }

        private goNext() {
            return userService.setup().then((r) => {
                if (r) {
                    return userService.login2Redirect()
                } else if (!sceneKey) {
                    return this.$router.push({ name: currentAppRoute[0].name })
                }
            })
        }

        private change() {
            // this.$router.push({ name: routesMap.home.page })
        }

        private close() {
            const oldKey = userService.getAgentIdCache()
            if (!oldKey) {
                userService.logout()
            }
        }

        private goApplyCompany() {
            this.$router.push({ name: routesMap.apply })
        }

        mounted() {}
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .left {
        width: 50%;
        background: linear-gradient(360deg, #457cee 0%, #305dcc 100%);
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }
    .right {
        display: flex;
        align-items: center;
        justify-content: center;
        .container {
            margin-bottom: 10%;
            position: relative;

            .title {
                font-size: 24px;
                font-weight: 600;
                color: #000000;
                line-height: 33px;
                margin-bottom: 29px;
                text-align: center;
            }
        }
    }
    .has-security {
        .el-tabs {
            /deep/.el-tabs__header {
                width: 100% !important;
            }
        }
    }
    .el-tabs {
        /deep/.el-tabs__header {
            width: 300px;
            margin: 0 auto;
            margin-bottom: 40px;
            .el-tabs__nav-wrap::after {
                display: none;
            }
            .el-tabs__item {
                font-size: 22px;
                font-weight: 600;
            }
        }
    }
    .xg-project-org {
        .container-box {
            background: #fafcff;
            box-shadow: 0px 5px 10px 1px rgba(211, 223, 240, 0.5);
            border-radius: 5px 5px 5px 5px;
            padding: 30px 40px;
        }
        .container {
            height: auto;
            margin-bottom: 0;
        }
        .forgive {
            padding-bottom: 30px;
        }
        /deep/.el-tabs .el-tabs__item {
            font-size: 18px;
            font-weight: bold;
        }
    }

    #captcha-div {
        position: absolute;
        padding-left: 40px;
        width: 400px;
        left: 0px;
        top: 0px;
        background-color: #fff;
        z-index: 9;

        /deep/ #tianai-captcha-parent {
            width: 331px;
            height: 284px;
            padding: 16px;
            padding-top: 16px;
        }
    }
</style>
