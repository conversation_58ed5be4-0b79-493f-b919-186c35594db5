
<template>
    <div v-if="tableConfig">
        <table-container
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            @setTotal="$emit('setTotal', $event)"
            class="container"
        >
            <div slot="title" v-if="!inTaskMessage">
                <breadcrumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div slot="table" slot-scope="{ data }">
                <common-table
                    :data="data"
                    :tableConfig="tableConfig"
                    :columns="tableConfig.column"
                >
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="viewDetail(scope.row)">
                            查看详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Breadcrumb from "@/views/components/breadcrumb/index.vue"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { omit } from "lodash"
    import { Component, Prop } from "vue-property-decorator"
    import { Row, tableConfig } from "."

    @Component({
        name: routesMap.groupService.serviceManageDetail.result.served_target_count,
        components: { TableContainer, CommonTable, Breadcrumb },
    })
    export default class TaskObjectServedList extends BaseTableController<Row> {
        @Prop({ default: false })
        private inTaskMessage!: boolean

        @Prop({ default: "" })
        private fromProp!: string

        private tableConfig: TableConfig | null = null
        private get from() {
            return this.$route.query.from as "" | "task" | "project" | "pc"
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "已服务对象",
                    to: {
                        name: routesMap.groupService.serviceManageDetail.result
                            .served_target_count,
                        query: this.$route.query,
                    },
                },
            ]
            if (this.from) {
                d.unshift(...getCacheBreadcrumbsByRoutePath(this.from))
            }
            updateTagItem({
                name: routesMap.groupService.serviceManageDetail.result
                    .served_target_count,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        refreshConfig = {
            name: routesMap.groupService.serviceManageDetail.result
                .served_target_count,
            fun: this.refresh,
        }

        refresh() {
            this.tableConfig = tableConfig(omit(this.$route.query, "from"))
            this.setBreadcrumbs()
        }

        mounted() {
            this.refresh()
        }

        viewDetail(row: Row) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: row.profile_access_key || row.userId,
                    from:
                        this.fromProp ||
                        routesMap.groupService.serviceManageDetail.result
                            .served_target_count,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
