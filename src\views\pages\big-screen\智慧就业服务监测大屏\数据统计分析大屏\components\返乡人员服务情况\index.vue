<template>
    <Card label="返乡人员服务情况">
        <Items :items="items" @toRoute="toRoute"></Items>
    </Card>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import Card from "../../common/card.vue"
    import Items from "../../common/items.vue"

    @Component({ components: { Card, Items } })
    export default class Template extends Vue {
        @Prop({ default: {} })
        private info!: any

        @Watch("info", { immediate: true })
        private onInfoChange() {
            if (!this.info) {
                return
            }
            this.items = this.buildInfo()
        }

        private items = this.buildInfo()

        private buildInfo() {
            return [
                {
                    label: "返乡总人数",
                    value: this.info["返乡总人数"] || 0,
                    unit: "人",
                    isRoute: true,
                },
                {
                    label: "返乡人员服务人次",
                    value: this.info["返乡人员服务人次"] || 0,
                    unit: "次",
                },
                {
                    label: "返乡人员投递人次",
                    value: this.info["返乡人员投递人次"] || 0,
                    unit: "次",
                },
            ]
        }

        private toRoute(label: string) {
            this.$router.push({
                name: routesMap.bigScreen.dataStatisticsAnalysis.list1,
                query: {
                    type: "返乡总人数",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 909px;
        height: 139px;
    }
</style>
