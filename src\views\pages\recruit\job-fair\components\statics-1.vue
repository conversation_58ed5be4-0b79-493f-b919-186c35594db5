<template>
    <div>
        <div class="content">
            <div class="title">报名统计</div>
            <div class="list">
                <div class="item line1" v-for="item in list1" :key="item.title">
                    <div class="u-font-32 num">
                        {{ data[item.value] || 0 }}
                    </div>
                    <div class="u-font-16 label u-m-t-16">{{ item.label }}</div>
                </div>
            </div>
        </div>
        <div class="content u-m-t-24">
            <div class="title">实况统计</div>
            <div class="list">
                <div
                    class="item item2 line1"
                    v-for="item in list2"
                    :key="item.title"
                >
                    <div class="u-font-32 num">
                        {{ data[item.value] || 0 }}
                    </div>
                    <div class="u-font-16 label u-m-t-16">{{ item.label }}</div>
                </div>
            </div>
        </div>
        <div class="content u-m-t-24">
            <div class="title">浏览量统计</div>
            <div class="list">
                <div
                    class="item item3 line1"
                    v-for="item in list3"
                    :key="item.title"
                >
                    <div class="u-font-32 num">
                        {{ data[item.value] || 0 }}
                    </div>
                    <div class="u-font-16 label u-m-t-16">{{ item.label }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { config, EnvProject } from "@/config"
    import { sdk } from "@/service"
    import { get } from "lodash"
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: {} })
    export default class Statics1 extends Vue {
        private get list1() {
            return [
                {
                    label: "招聘会创建场次",
                    value: "create_count",
                },
                {
                    label: "报名企业数量",
                    value: "agent_apply_count",
                },
                {
                    label: "报名岗位数量",
                    value: "position_count",
                },
                {
                    label: "预计招聘人数",
                    value: "person_expected_count",
                },
                {
                    label: "报名人数",
                    value: "person_apply_count",
                },
            ]
        }

        private get list2() {
            return [
                {
                    label: "招聘会举办总场数",
                    value: "publish_count",
                },
                {
                    label: "到场企业数量",
                    value: "agent_sign_count",
                },
                {
                    label: "到场岗位数量",
                    value: "position_join_count",
                },
                {
                    label: "到场人数",
                    value: "person_join_count",
                },
                {
                    label: "投递人数",
                    value: "person_recruit_count",
                },
            ]
        }

        private get list3() {
            return [
                {
                    label: "活动浏览量",
                    value: "activity_browse_count",
                },
                {
                    label: "企业浏览量",
                    value: "agent_browse_count",
                },
                {
                    label: "岗位浏览量",
                    value: "position_browse_count",
                },
            ]
        }

        @Prop()
        private filterData!: any

        private data: any = {}

        mounted() {
            sdk.getDomainService(
                "get_job_fair_summaryV2",
                "anonymous/job_fair_api",
                "xg_project"
            )
                .post(undefined, { ...this.filterData })
                .then((r: any) => {
                    if (
                        EnvProject.孝感项目 === config.envProject ||
                        process.env.NODE_ENV === "development"
                    ) {
                        Object.keys(r.result).forEach((key) => {
                            r.result[key] = r.result[key].count || 0
                        })
                    }
                    this.data = r.result
                    this.$emit(
                        "initFilter",
                        get(r, "mapping.xiaogan_region_mapping.mapping_values")
                    )
                })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        background: transparent;
    }
    .item {
        height: 110px;
        background: #f4f4ff;
        border-radius: 8px;
        text-align: center;
        padding-top: 20px;
        &:nth-child(1) {
            background: #6068e0;
            .num {
                color: #fff !important;
            }
            .label {
                color: #fff;
            }
        }
        &.item2 {
            background: #ebf9f9;
            .num {
                color: #32b2b2;
            }
            &:nth-child(1) {
                background: #32b2b2;
            }
        }
        &.item3 {
            background: #edfaf1;
            .num {
                color: #3cc167;
            }
            &:nth-child(1) {
                background: #3cc167;
            }
        }

        .num {
            height: 32px;
            font-size: 32px;
            font-weight: 600;
            color: #6068e0;
        }
        .label {
            font-size: 16px;
            color: #333333;
        }
    }
    .list {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 19px;
    }
</style>
