<template>
    <Card label="春风行动招聘会列表">
        <div class="inner-card">
            <seamless
                :items="meetings"
                :scroll="true"
                :rows="5"
                :top="0"
                :lineHeight="74"
                boxId="#jiu-ye-top10"
            >
                <template slot-scope="{ slotScope }">
                    <div
                        class="meeting-item"
                        :class="{ lineHeight: slotScope.isToDay }"
                    >
                        <div>
                            <span
                                class="meeting-name"
                                :title="slotScope.name"
                                >{{ slotScope.name }}</span
                            >
                        </div>
                        <div class="meeting-info">
                            <span class="meeting-status">{{
                                slotScope.status
                            }}</span>
                            <span class="meeting-date">{{
                                slotScope.date
                            }}</span>
                        </div>
                    </div>
                </template>
            </seamless>
        </div>
        <!-- <div ref="meeting" class="meeting-list">
            <div
                v-for="(meeting, index) in meetings"
                :key="index"
                class="meeting-item"
                :class="{ lineHeight: meeting.isToDay }"
            >
                <div>
                    <span class="meeting-name" :title="meeting.name">{{
                        meeting.name
                    }}</span>
                </div>
                <div class="meeting-info">
                    <span class="meeting-status">{{ meeting.status }}</span>
                    <span class="meeting-date">{{ meeting.date }}</span>
                </div>
            </div>
        </div> -->
    </Card>
</template>

<script lang="ts">
    import { getJobFairDataV2 } from "@/views/pages/big-screen/招聘会大屏/service"
    import { loopTimer } from "@/views/pages/big-screen/招聘会大屏/tools/looptimer"
    import moment from "moment"
    import { Component, Ref, Vue } from "vue-property-decorator"
    import Card from "../../common/card.vue"
    import Seamless from "@/views/pages/big-screen/common/sesmless-scroll/table3.vue"

    @Component({ components: { Card, Seamless } })
    export default class Template extends Vue {
        @Ref()
        private readonly meeting!: any

        private meetings: any[] = []

        private scrollTimer: number | null = null
        private isScrolling = false

        created() {
            this.init()
        }

        private init() {
            getJobFairDataV2("position_top").then((r) => {
                this.meetings = r.map((i) => {
                    return {
                        name: i.item_key_id,
                        status: i.item_key_label.split("##")[0] || "",
                        date: i.item_key_label.split("##")[1] || "",
                        isToDay: this.isToDay(
                            i.item_key_label.split("##")[1] || ""
                        ),
                    }
                })

                setTimeout(() => {
                    this.startScroll()
                })
            })
        }

        beforeDestroy() {
            this.stopScroll()
        }

        private stopScroll() {
            if (this.scrollTimer) {
                clearTimeout(this.scrollTimer)
                this.scrollTimer = null
            }
            this.isScrolling = false
        }

        private isToDay(date: string) {
            return moment().format("YYYY.M.D") + "" === date
        }

        private startScroll() {
            if (this.isScrolling) return

            const scrollElement = this.$refs.meeting as HTMLElement
            if (!scrollElement) return

            const step = 1
            const scrollInterval = 50 // 滚动间隔时间
            const pauseAtBottomTime = 1000 // 到底停顿时间

            this.isScrolling = true

            const scroll = () => {
                if (!this.isScrolling) return

                const isBottom =
                    Math.ceil(
                        scrollElement.scrollTop + scrollElement.clientHeight
                    ) >= scrollElement.scrollHeight

                if (isBottom) {
                    // 到达底部，暂停滚动
                    this.stopScroll()

                    // 等待指定时间后回到顶部并重新开始
                    setTimeout(() => {
                        if (scrollElement) {
                            scrollElement.scrollTop = 0
                            this.startScroll()
                        }
                    }, pauseAtBottomTime)
                } else {
                    scrollElement.scrollTop += step
                    this.scrollTimer = setTimeout(scroll, scrollInterval)
                }
            }

            this.scrollTimer = setTimeout(scroll, scrollInterval)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 425px;
        height: 361px;
    }

    .inner-card {
        width: 385px;
        height: 336px;
        overflow: hidden;
    }

    .title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #fff;
        padding: 0 10px;
    }

    .meeting-list {
        display: flex;
        flex-direction: column;
        gap: 0px;
        height: 300px;
        overflow-y: auto;

        &::-webkit-scrollbar {
            scrollbar-width: none;
            display: none; /* 对于 Chrome, Safari 和 Opera */
        }
    }

    .meeting-item {
        width: 100%;
        padding: 16px;
        padding-top: 7px;
        padding-bottom: 7px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 8px;
        height: 74px;
        border-bottom: 1px solid rgba(32, 127, 209, 0.3);

        // &:nth-child(2n - 1) {
        //     background: rgba(20, 84, 220, 0.5);
        // }

        &.lineHeight {
            background: rgba(20, 84, 220, 0.5);
        }
    }

    .meeting-info {
        display: flex;
        justify-content: space-between;
    }

    .meeting-name {
        font-size: 16px;
        line-height: 19px;
        color: #bdd8ff;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .meeting-status {
        font-size: 14px;
        color: #93bfff;
    }

    .meeting-date {
        color: #93bfff;
        font-size: 14px;
        text-align: right;
    }
</style>
