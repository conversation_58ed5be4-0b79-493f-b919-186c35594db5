import { config, EnvProject } from "@/config"
import { BuildFormConfig, FileType, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { desensitization } from "@/utils/tools"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"

function tableFilter(isPublic = false): TableFilter[] {
    return [
        {
            label: "姓名",
            type: FormType.Text,
            prop: "name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "电话",
            type: FormType.Text,
            prop: "mobile",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "学历",
            type: FormType.Select,
            prop: "education",
        },
        {
            label: "身份证",
            type: FormType.Text,
            prop: "id_card",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "岗位名称",
            type: FormType.Text,
            prop: "position.name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: isPublic ? "单位名称" : "企业名称",
            type: FormType.Text,
            prop: "agent_name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: isPublic ? "单位所在地" : "企业所在地",
            type: FormType.Cascader,
            prop: "province_code",
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
        },
        {
            label: "管理区域",
            type: FormType.Cascader,
            prop: "mgt_province_region_code",
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
            hide: isPublic,
        },
        {
            label: "投递来源",
            type: FormType.Select,
            prop: "created_from",
        },
        {
            label: "投递平台",
            type: FormType.Select,
            prop: "client",
        },
        {
            label: "资格审核",
            type: FormType.Select,
            prop: "qualification_audit_status",
            hide: !isPublic,
        },
        {
            label: "投递时间",
            type: FormType.DatePicker,
            option: {
                type: "datetimerange",
            },
            prop: "create_time",
        },
        {
            label: "是否联系企业",
            type: FormType.Select,
            prop: "is_contact",
            hide:
                isPublic ||
                ![EnvProject.黄州项目, EnvProject.孝感项目].includes(
                    config.envProject
                ),
        },
        {
            label: "企业是否查阅简历",
            type: FormType.Select,
            prop: "company_view",
            hide:
                isPublic ||
                ![EnvProject.黄州项目, EnvProject.孝感项目].includes(
                    config.envProject
                ),
        },
    ]
}

export const predict = {
    name: "",
    position_name: "position#name",
    position_id: "position#id",
    position_access_key: "position#_access_key",
    agent_name: "position#agent#agent_name",
    region_name: "position#area#region_name",
    create_time: "label",
    status: "label",
    status_memo: "label",
    created_from: "label",
    contact_status: "label",
    qualification_audit_status: "label",
    client: "label",
    interview_time: "label",
    interview_address: "label",
    position_type: "position#position_type_label",
    tags: "tags",
    // 信息
    mobile_encode: "",
    mobile: "",
    id_card: "",
    id_card_encode: "",
    sex: "label",
    age: "",
    education: "label",
    employment_status: "label",
    household_province: "household_province#region_name",
    household_city: "household_city#region_name",
    household_area: "household_area#region_name",
    household_detail: "household_address_detail",
    permanent_province: "permanent_province#region_name",
    permanent_city: "permanent_city#region_name",
    permanent_area: "permanent_area#region_name",
    permanent_detail: "permanent_detail",
    // profile
    profile_id: "profile#id",
    profile_name_hide: "profile#name_hide",
    p_mobile_encode: "profile#mobile_hide",
    p_mobile: "profile#mobile",
    p_id_card: "profile#id_card",
    p_id_card_encode: "profile#id_card_hide",
    p_sex: "profile#sex_label",
    p_age: "profile#getAge",
    reg_residence_property: "profile#basic_info#reg_residence_property",
    nation: "profile#basic_info#nation",
    p_education: "profile#basic_info#education_label",
    p_employment_status:
        "profile#user_profile_current_job_info#employment_status_label",
    job_willing_industry: "profile#job_willingness#job_willing_industry_label",
    profile_created_from: "profile#created_from_label",
    avatar: "profile#user#avatar",
    political_outlook: "profile#basic_info#political_outlook_label",
    p_household_province: "profile#user#household_province_name",
    p_household_city: "profile#user#household_city_name",
    p_household_area: "profile#user#household_area_name",
    p_household_detail: "profile#basic_info#household_detail",
    p_permanent_province: "profile#user#permanent_province_name",
    p_permanent_city: "profile#user#permanent_city_name",
    p_permanent_area: "profile#user#permanent_area_name",
    p_permanent_detail: "profile#basic_info#permanent_detail",
    job_years: "profile#user_profile_current_job_info#job_years",
    self_evaluation: "profile#basic_info#self_evaluation",
    province_region_name: "position#agent#province#region_name",
    city_region_name: "position#agent#city#region_name",
    area_region_name: "position#agent#area#region_name",
    agent_mgt_province_region_name: "position#agent#mgt_province#region_name",
    agent_mgt_city_region_name: "position#agent#mgt_city#region_name",
    agent_mgt_area_region_name: "position#agent#mgt_area#region_name",
    is_create: "position#agent#is_create_label",
    contact_person: "position#agent#contact_person",
    contact_mobile: "position#agent#contact_mobile",
    is_contact: "label",
    company_view: "label",
    profile_access_key: "profile#_access_key",
}

export enum PositionType {
    普通岗位 = 1,
    公益岗位,
}
export enum Status {
    待处理 = 0,
    待面试 = 10,
    待入职 = 15,
    已入职 = 20,
    不合适 = 30,
    已过期 = 40,
}

export function tableConfig(list = "manage"): TableConfig {
    return {
        model: sdk.core.model("xg_candidate_order").list(list),
        filter: tableFilter(list !== "manage"),
        defaultPageSize: 10,
        predict: predict,
        column: columns(list !== "manage"),
        oneTabFilter: true,
        tabPages:
            list === "manage"
                ? [
                      "全部",
                      "待处理",
                      "待面试",
                      "通过初筛",
                      "已入职",
                      "不合适",
                      "已过期",
                  ]
                : [],
    }
}

export function columns(isPublic = false): TableColumn[] {
    return [
        {
            prop: "select",
            width: "58",
            type: "selection",
            hide: isPublic,
            selectable: (row: any) => {
                return row.status < Status.已入职
            },
        },
        {
            label: "姓名",
            prop: "name",
            width: "120px",
            showOverflowTip: true,
        },
        {
            label: "手机号",
            prop: "mobile",
            formatter(row) {
                return desensitization(
                    row.profile_id ? row.p_mobile : row.mobile
                )
            },
            minWidth: "140px",
            showOverflowTip: true,
        },
        {
            label: "年龄",
            prop: "age",
            minWidth: "100px",
            formatter(row) {
                const age = row.profile_id ? row.p_age : row.age
                return age ? age + "岁" : ""
            },
            showOverflowTip: true,
        },
        {
            label: "学历",
            prop: "education_label",
            minWidth: "100px",
            formatter(row) {
                return row.profile_id
                    ? row.p_education_label
                    : row.education_label
            },
            showOverflowTip: true,
        },
        {
            label: "投递信息",
            prop: "position_name",
            align: "left",
            showOverflowTip: true,
            minWidth: "180px",
        },
        {
            label: "投递时间",
            prop: "create_time_label",
            showOverflowTip: true,
            minWidth: "150px",
        },
        {
            label: "求职意向",
            prop: "job_willing_industry_label",
            showOverflowTip: true,
            minWidth: "140px",
        },
        {
            label: "投递来源",
            prop: "agent_name1",
            width: "140px",
            align: "left",
            render(h, row) {
                return h("div", {}, [
                    h("div", {}, row.created_from_label),
                    // h("div", { class: "color-9" }, row.client),
                ])
            },
            showOverflowTip: true,
        },
        {
            label: "投递平台",
            prop: "client_label",
            align: "left",
            minWidth: "140px",
            showOverflowTip: true,
        },
        // { label: "投递方式", prop: "created_from_label", showOverflowTip: true },
        {
            label: isPublic ? "所属单位" : "所属企业",
            prop: "agent_name",
            showOverflowTip: true,
            minWidth: "180px",
        },
        {
            label: isPublic ? "单位所在地" : "企业所在地",
            prop: "region_name",
            showOverflowTip: true,
            minWidth: "180px",
            render(h, row) {
                return h(
                    "span",
                    {},
                    getAddress(row, [
                        "province_region_name",
                        "city_region_name",
                        "area_region_name",
                    ]) || "-"
                )
            },
        },
        {
            label: "管理区域",
            prop: "mgt_region_name",
            showOverflowTip: true,
            minWidth: "180px",
            render(h, row) {
                return h(
                    "span",
                    {},
                    getAddress(row, [
                        "agent_mgt_province_region_name",
                        "agent_mgt_city_region_name",
                        "agent_mgt_area_region_name",
                    ]) || "-"
                )
            },
        },
        {
            label: isPublic ? "单位是否入驻" : "企业是否入驻",
            prop: "is_create_label",
            showOverflowTip: true,
            width: "110px",
        },
        {
            label: isPublic ? "单位联系方式" : "企业联系方式",
            prop: "contact_person",
            showOverflowTip: true,
            render(h, row) {
                return h("div", { class: "u-flex-col" }, [
                    h("span", row.contact_person),
                    renDesensitizationView(h, {
                        value: row.contact_mobile,
                    }),
                ])
            },
            minWidth: "150px",
        },
        {
            label: "投递状态",
            prop: "status_label",
            align: "left",
            minWidth: "100px",
            render(h, row) {
                return h("div", {}, [
                    h("div", {}, row.status_label),
                    // h(
                    //     "div",
                    //     { class: "color-9" },
                    //     row.status_memo_label
                    // ),
                ])
            },
            showOverflowTip: true,
        },
        {
            label: "是否联系企业",
            prop: "is_contact_label",
            showOverflowTip: true,
            minWidth: "120px",
            hide:
                isPublic ||
                ![EnvProject.黄州项目, EnvProject.孝感项目].includes(
                    config.envProject
                ),
        },
        {
            label: "企业是否查阅简历",
            prop: "company_view_label",
            minWidth: "140px",
            showOverflowTip: true,
            hide:
                isPublic ||
                ![EnvProject.黄州项目, EnvProject.孝感项目].includes(
                    config.envProject
                ),
        },
        {
            label: "操作",
            prop: "h",
            width: "120px",
            showOverflowTip: true,
            fixed: "right",
        },
    ]
}

export function createFeedbackFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_candidate_order",
        sdkAction: "company_contact",
        id,
        forms: [
            {
                label: "联系结果",
                type: FormType.Select,
                prop: "contact_type",
                required: true,
            },
            {
                label: "备注",
                type: FormType.Text,
                prop: "remark",
                option: {
                    type: "textarea",
                    rows: 3,
                },
            },
            {
                label: "上传附件",
                type: FormType.MyUpload,
                prop: "file_url",
                option: {
                    fileType: [
                        FileType.Doc,
                        FileType.Excel,
                        FileType.Ppt,
                        FileType.Text,
                        FileType.Image,
                    ],
                    drag: true,
                    placeholder: "",
                },
            },
        ],
    }
}
