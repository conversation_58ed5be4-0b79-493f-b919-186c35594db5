<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        :width="formWidth"
    >
        <div :style="style">
            <form-builder
                ref="formBuilder"
                :label-position="labelPosition"
                :labelWidth="labelWidth"
                v-if="showForm"
            ></form-builder>
        </div>
        <div class="u-flex u-row-center">
            <el-button type="primary" plain @click="close">取消</el-button>
            <el-button
                :loading="loading"
                type="primary"
                class="u-m-l-30"
                @click="confirm"
            >
                确定
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FormController,
        FormItem,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { ReturnPromiseType } from "@/core-ui/helpers/types"
    import { sdk } from "@/service"
    import { isArray, map, split } from "lodash"
    import { Component, Mixins, Prop } from "vue-property-decorator"

    @Component({ components: { FormBuilder } })
    export default class CommonPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop()
        private id!: string | string

        @Prop({ default: "90px" })
        private labelWidth!: string

        @Prop({ default: "600px" })
        private formWidth!: string

        @Prop({ default: "" })
        private title!: string

        @Prop({ default: "left" })
        private labelPosition!: string

        @Prop()
        private sdkModel!: string

        /** edit|create */
        @Prop()
        private sdkAction!: string

        @Prop({ default: true })
        private buildFull!: boolean

        @Prop({ default: false })
        private isBatch!: boolean

        @Prop({ default: true })
        private resetForm!: boolean

        @Prop({ default: "20" })
        private padding!: string

        @Prop({ default: undefined })
        private prefilters!: any

        @Prop({ default: null })
        private p!: null | ((model: any) => Promise<any>)

        @Prop()
        private afterBuildFormSections?: (
            e: ReturnPromiseType<typeof buildFormSections>
        ) => ReturnPromiseType<typeof buildFormSections>

        @Prop()
        private submiteDataHandler?: (data: any) => any

        private showForm = false

        private get style() {
            return {
                padding: `0 ${this.padding}px`,
            }
        }

        private get computeAction() {
            if (this.sdkAction.includes("|")) {
                return split(this.sdkAction, "|")[this.id ? 0 : 1]
            }
            return this.sdkAction
        }

        @Prop({ default: () => [] })
        private forms!: FormItem[]

        onOpen() {
            this.init()
        }

        onClosing() {
            this.showForm = !this.resetForm
        }

        private get selected_list() {
            let selected_list: any = this.id ? [{ id: this.id, v: 0 }] : []
            if (isArray(this.id)) {
                selected_list = map(this.id, (e) => ({
                    id: e,
                    v: 0,
                }))
            }
            return selected_list
        }

        private get action() {
            return sdk.core
                .model(this.sdkModel)
                .action(this.computeAction)
                .updateInitialParams({
                    selected_list: this.selected_list,
                    prefilters: this.prefilters,
                })
        }

        private init() {
            this.showForm = true
            return buildFormSections({
                sdkModel: this.sdkModel,
                sdkAction: this.computeAction,
                forms: this.forms,
                needSourceData: true,
                select_list: this.selected_list,
                prefilters: this.prefilters,
            }).then((r) => {
                if (this.afterBuildFormSections) {
                    r = this.afterBuildFormSections(r)
                }
                if (this.buildFull) {
                    this.buildFormFull(r)
                } else {
                    this.buildForm(r.forms)
                }
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private async submit(data: any) {
            this.loading = true
            if (this.submiteDataHandler) {
                data = this.submiteDataHandler(data)
            }
            const model = sdk.core
                .model(this.sdkModel)
                .action(this.computeAction)
                .addInputs_parameter(data)
                .updateInitialParams({
                    selected_list: this.selected_list,
                    prefilters: this.prefilters,
                })
            if (this.p) {
                return this.p(model)
                    .then((r) => {
                        this.close()
                        this.$emit("refresh", r)
                    })
                    .catch((e) => {
                        this.$emit("error", e)
                    })
                    .finally(() => {
                        this.loading = false
                    })
            }
            if (!this.isBatch) {
                this.loading = true
                return model
                    .execute({})
                    .then((r) => {
                        this.close()
                        console.log("is")
                        this.$emit("refresh", r)
                    })
                    .catch((e) => {
                        this.$emit("error", e)
                    })
                    .finally(() => {
                        this.loading = false
                    })
            } else {
                let errorList: any = []
                this.loading = true
                return model
                    .executeEach({
                        inputs_parameters: [],
                        selected_list: this.selected_list,
                        prefilters: this.prefilters,
                    } as any)(
                        () => {},
                        (errorRow) => {
                            console.log(JSON.parse(JSON.stringify(errorRow)))
                            errorList = errorRow
                        }
                    )
                    .awaiting.then(() => {
                        if (errorList.length) {
                            this.$message.error(
                                `${errorList.map(
                                    (i: any) => `id:${i.id}: ${i.error}`
                                )}`
                            )
                        }
                    })
                    .then(() => {
                        this.$message.success("操作成功")
                        this.$emit("refresh")
                        this.close()
                    })
                    .finally(() => {
                        this.loading = false
                    })
            }
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
