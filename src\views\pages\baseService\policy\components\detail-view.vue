<template>
    <div class="detail-container">
        <div class="content u-flex-none">
            <div class="title">申请基本信息</div>
            <DetailRowCol
                class="u-m-t-20"
                :labelStyle="{
                    width: '140px',
                    'text-align': 'right',
                    'padding-right': '10px',
                }"
                :list="basicList"
            />
        </div>
        <CommonPop
            labelWidth="140px"
            :id="row.id"
            v-model="showTimeEdit"
            title="修改推广时间"
            :sdkModel="ModelConfig.model"
            :sdkAction="'update_recommend_time'"
            @refresh="$emit('refresh')"
        />
        <CommonPop
            :id="row.id"
            v-model="showRegionEdit"
            title="修改推广区域"
            :sdkModel="ModelConfig.model"
            :sdkAction="'update_region_code'"
            @refresh="$emit('refresh')"
        />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { ModelConfig, Row } from ".."

    @Component({ components: { DetailRowCol, CommonPop } })
    export default class DetailView extends Vue {
        @Prop()
        private row!: Row

        @Prop({ default: false })
        private canEdit!: boolean

        showTimeEdit = false
        showRegionEdit = false
        ModelConfig = ModelConfig

        private get basicList() {
            const h = this.$createElement
            const d: ColItem[] = [
                {
                    label: "推广政策：",
                    vNode: h(
                        "span",
                        {
                            on: {
                                click: () => {
                                    this.$router.push({
                                        name: routesMap.publishPolicy.policyDetail,
                                        query: {
                                            id: this.row.policy_access_key,
                                        },
                                    })
                                },
                            },
                            class: "pointer primary",
                        },
                        this.row.policy_name
                    ),
                },
                {
                    label: "服务对象：",
                    value: this.row.policy_serve_target_type_label,
                },
                {
                    label: "推广状态：",
                    value: this.row.status_memo,
                },
                {
                    label: "推广时间段：",

                    vNode: h("div", { class: "u-flex-defult u-flex-wrap" }, [
                        h(
                            "span",
                            { class: "u-m-r-20" },
                            this.row.start_time_label +
                                " ~ " +
                                this.row.end_time_label
                        ),
                        this.canEdit &&
                            h(
                                "span",
                                {
                                    class: "pointer primary ",
                                    on: {
                                        click: () => {
                                            this.showTimeEdit = true
                                        },
                                    },
                                },
                                "编辑"
                            ),
                    ]),
                },
                {
                    label: "推广区域：",
                    vNode: [
                        h("div", { class: "u-flex-defult u-flex-wrap" }, [
                            h(
                                "span",
                                { class: "u-m-r-20" },
                                this.row.recommend_region || "-"
                            ),
                            this.canEdit &&
                                h(
                                    "span",
                                    {
                                        class: "pointer primary ",
                                        on: {
                                            click: () => {
                                                this.showRegionEdit = true
                                            },
                                        },
                                    },
                                    "编辑"
                                ),
                        ]),
                    ],
                    span: 24,
                },
            ]
            return d
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .detail-container {
        background: #fff;
        .content {
            padding: 20px;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
        }
    }
</style>
