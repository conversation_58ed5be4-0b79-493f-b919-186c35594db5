<template>
    <container
        :title="title"
        btnText="导出"
        :showBtnIcon="false"
        @clickRightBtn="toExport()"
        @monthValueChange="monthValueChange"
        filterDateType="year"
    >
        <div
            class="home-container policy-company-content"
            v-if="pageKey && list && list2"
            :key="pageKey"
        >
            <div class="content-container">
                <div class="left">
                    <Card1 :pData="list" indicator_type="001"></Card1>
                    <Card2 :pData="list" indicator_type="002"></Card2>
                    <Card3
                        :pData="list"
                        :list2="list2"
                        indicator_type="003"
                    ></Card3>
                </div>
                <div class="middle">
                    <Card4
                        :pData="list2"
                        :score="total_calc_score"
                        indicator_type=""
                    ></Card4>
                    <Card5 :pData="list2" indicator_type=""></Card5>
                </div>
                <div class="right">
                    <Card6 :pData="list" indicator_type="004"></Card6>
                    <Card7 :pData="list" indicator_type="005"></Card7>
                    <Card8 :pData="list" indicator_type="006"></Card8>
                </div>
            </div>
        </div>
    </container>
</template>

<script lang="ts">
    import { config } from "@/config"
    import { routesMap } from "@/router/direction"
    import { Component } from "vue-property-decorator"
    import "@/views/pages/big-screen/common/time-range.less"
    import "@/views/pages/big-screen/common/style/style.less"
    import Container from "@/views/pages/big-screen/潜江政策大屏/common/container.vue"
    import moment from "moment"
    import BasePage from "../../潜江政策大屏"
    import { updateBigScreenType } from "../../common/rem"
    import { loopTimer } from "../../招聘会大屏/tools/looptimer"
    import Card1 from "./components/城镇新增就业.vue"
    import Card2 from "./components/新增高校毕业生就业创业人数.vue"
    import Card3 from "./components/离校未就业高校毕业生帮扶就业率.vue"
    import Card4 from "./components/指数得分.vue"
    import Card5 from "./components/完成量进度.vue"
    import Card6 from "./components/就业创业推进乡村振兴工作情况.vue"
    import Card7 from "./components/中央就业补助资金执行率.vue"
    import Card8 from "./components/就业创业政策落实情况.vue"
    import { exportExcel } from "./index"

    @Component({
        name: routesMap.employmentTask.screen,
        components: {
            Container,
            Card1,
            Card2,
            Card3,
            Card4,
            Card5,
            Card6,
            Card7,
            Card8,
        },
    })
    export default class Index extends BasePage {
        private curTime = ""
        private list: any = null
        private list2: any = null

        private get title() {
            return "潜江市就业指标预警分析大屏"
        }

        created() {
            updateBigScreenType("招聘会大屏")
            document.title = this.title
            this.getCurTime()
        }

        @loopTimer(2000)
        private getCurTime() {
            this.curTime = moment().format("YYYY年MM月DD日 HH:mm")
        }

        destroyed() {
            document.title = config.appTitle
        }

        private toExport() {
            if (this.list && this.list2) {
                exportExcel(this.list, this.list2)
            }
        }

        private total_calc_score = 0

        initData() {
            this.list = null
            this.list2 = null
            this.query("indicator_record", "dashboard_policy_profile", {
                project: "warning_platform",
                indicator_year: this.pageTime,
            }).then((r: any) => {
                this.list = r.list
            })
            this.query("indicator_total", "dashboard_policy_profile", {
                project: "warning_platform",
                indicator_year: this.pageTime,
            }).then((r: any) => {
                this.list2 = r.list
                this.total_calc_score = r.total_calc_score || 0
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content-container {
        height: 940px;
        margin-top: 10px;
        margin-left: 20px;
        margin-right: 20px;
        display: flex;
    }

    .container {
        background-image: url("@/views/pages/big-screen/潜江政策大屏/assets/bg.webp");
        .banner {
            .header-title {
                transform: translateY(calc(-50% - 3px));
            }
        }
    }

    .left,
    .right {
        width: calc(560 * 100vw / (560 + 560 + 700));
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .middle {
        width: calc(700 * 100vw / (560 + 560 + 700));
        margin-left: 30px;
        margin-right: 30px;
    }

    .nav-box {
        position: absolute;
        right: 20px;
        bottom: 10px;
    }

    .time-box {
        position: absolute;
        bottom: 25px;
        left: 20px;
        font-size: 16px;
        color: #89b9ff;
    }

    .update-time-box {
        position: absolute;
        bottom: 25px;
        right: 20px;
        font-size: 16px;
        color: #89b9ff;
    }

    .back {
        // position: absolute;
        // top: 16px;
        // right: 50px;
        font-size: 16px;
        color: #89b9ff;
        // background-image: url("./../assets/back.svg");
        padding-left: 26px;
        background-repeat: no-repeat;
        cursor: pointer;
        user-select: none;
        transform: scale(0.9);
        position: relative;
        right: 5px;
        top: 5px;
    }
    /deep/ .empty-pie {
        font-size: 13px;
        text-align: center;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #89b9ff;
        opacity: 0.75;
    }
</style>

<style >
    .app-container {
        min-width: 1060px !important;
    }

    .el-message {
        display: none !important;
    }

    .el-notification {
        display: none !important;
    }
</style>
