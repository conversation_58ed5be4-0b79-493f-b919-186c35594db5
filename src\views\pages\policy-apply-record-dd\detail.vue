<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex" v-if="row">
                <div class="u-flex">
                    <div class="font-family-medium color-2">状态：</div>
                    <div
                        class="font-family-medium"
                        :style="{ color: statusColor[row.status] }"
                    >
                        {{ row.task_name }}
                    </div>
                </div>
                <template v-if="isCompany">
                    <el-button
                        type="primary"
                        plain
                        class="custom-btn batch-btn"
                        @click="check_finish"
                        v-if="row.apply_status === 1"
                        v-role="[
                            'model.zc_apply_instance.action.audit_personal',
                        ]"
                    >
                        完成核查
                    </el-button>
                    <el-button
                        type="primary"
                        plain
                        class="custom-btn batch-btn"
                        @click="audit"
                        v-if="row.apply_status === 3"
                        v-role="['model.zc_apply_instance.action.audit']"
                    >
                        初审通过
                    </el-button>
                    <el-button
                        type="primary"
                        plain
                        class="custom-btn batch-btn"
                        @click="audit2"
                        v-if="row.apply_status === 5"
                        v-role="['model.zc_apply_instance.action.final_audit']"
                    >
                        终审
                    </el-button>
                </template>
                <template v-if="isPerson">
                    <template v-if="row.no === 'linghuojiuyeshebao'">
                        <el-button
                            type="primary"
                            plain
                            class="custom-btn batch-btn"
                            @click="showAuditPop4 = true"
                            v-if="row.task_name === '待街道审核'"
                            v-role="[
                                'model.zc_apply_instance.action.street_audit_personal',
                            ]"
                        >
                            街道审核
                        </el-button>
                        <el-button
                            type="primary"
                            plain
                            class="custom-btn batch-btn"
                            @click="showAuditPop5 = true"
                            v-if="row.task_name === '待区县审核'"
                            v-role="[
                                'model.zc_apply_instance.action.area_audit_personal',
                            ]"
                        >
                            区县审核
                        </el-button>
                    </template>
                    <template v-else>
                        <el-button
                            type="primary"
                            plain
                            class="custom-btn batch-btn"
                            @click="showAuditPop = true"
                            v-if="row.apply_status === 3"
                            v-role="[
                                'model.zc_apply_instance.action.audit_personal',
                            ]"
                        >
                            审核
                        </el-button>
                    </template>
                </template>
                <!-- <el-button
                    type="primary"
                    plain
                    class="custom-btn batch-btn"
                    @click="prev"
                    v-if="canPrev"
                >
                    上一条
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn batch-btn"
                    @click="next('')"
                    v-if="canNext"
                >
                    下一条
                </el-button> -->
            </div>
        </div>
        <div class="detail-container" v-if="row">
            <div class="content u-flex-1 u-p-x-20 u-p-t-20">
                <template v-for="item in formList">
                    <div class="title" :key="item.title">{{ item.title }}</div>
                    <div class="u-p-20" :key="item.title + 'detail'">
                        <detail-row-col
                            :labelStyle="labelStyle"
                            :list="item.list"
                        ></detail-row-col>
                    </div>
                </template>
                <!-- 首次来鄂 居民 -->
                <template v-if="isPerson">
                    <div class="title u-flex u-row-between">
                        <div>附件信息</div>
                        <el-button @click="download" type="text"
                            >下载附件</el-button
                        >
                    </div>
                    <DetailFileList
                        class="u-p-y-20"
                        :detail="row"
                    ></DetailFileList>
                </template>
                <!-- 首次来鄂 企业 -->
                <template v-if="isCompany">
                    <div class="title">员工申请列表</div>
                    <DetailList
                        v-if="row"
                        ref="detailList"
                        class="u-p-y-20"
                        :detail="row"
                    ></DetailList>
                </template>
            </div>
        </div>
        <!-- <div
            class="detail-container u-flex u-row-center u-m-t-24 u-p-40"
            v-if="showAuditBtn"
        >
            <el-button
                type="primary"
                plain
                class="custom-btn batch-btn"
                @click="showPop = true"
            >
                不通过
            </el-button>
            <el-button
                type="primary"
                class="custom-btn batch-btn"
                @click="toOperate"
            >
                通过
            </el-button>
        </div> -->
        <remark-pop
            v-model="showPop"
            v-if="row"
            :id="row.id"
            @refresh="init"
        ></remark-pop>
        <CommonPop
            v-if="row"
            v-model="showAuditPop"
            title="审核"
            :labelWidth="'120px'"
            sdkModel="zc_apply_instance"
            sdkAction="audit_personal"
            :id="row.id"
            @refresh="init('')"
        />
        <CommonPop
            v-if="row"
            v-model="showAuditPop2"
            title="通过初审"
            :labelWidth="'120px'"
            sdkModel="zc_apply_instance"
            sdkAction="audit"
            :id="row.id"
            @refresh="init('')"
        />
        <CommonPop
            v-if="row"
            v-model="showAuditPop3"
            title="通过终审"
            :labelWidth="'120px'"
            sdkModel="zc_apply_instance"
            sdkAction="final_audit"
            :id="row.id"
            @refresh="init('')"
        />
        <CommonPop
            v-if="row"
            v-model="showAuditPop4"
            title="街道审核"
            :labelWidth="'120px'"
            sdkModel="zc_apply_instance"
            sdkAction="street_audit_personal"
            :id="row.id"
            :afterBuildFormSections="afterBuildFormSections1"
            @refresh="init('')"
        />
        <CommonPop
            v-if="row"
            v-model="showAuditPop5"
            title="区县审核"
            :labelWidth="'120px'"
            sdkModel="zc_apply_instance"
            sdkAction="area_audit_personal"
            :id="row.id"
            @refresh="init('')"
        />
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { ColItem } from "@/views/components/detail-row-col"
    import { pageLoading } from "@/views/controller"
    import { Component, Ref, Vue } from "vue-property-decorator"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../single-page/components/tags-view"
    import {
        batchDownload,
        detailPredict,
        DetailRow,
        queryParamsKey,
        ServeTargetType,
        serviceTypeMapping,
        Status,
    } from "./index"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { getAddress, sleep } from "@/utils"
    import { downLoadFile } from "@/utils/tools"
    import { getShowBtn4Page } from "../collect-task-manage/components/build-table"
    import FormView from "./components/form-view.vue"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import RemarkPop from "./components/remark-pop.vue"
    import { MessageBox } from "element-ui"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { config, EnvProject } from "@/config"
    import { createFormList } from "./detail"
    import DetailFileList from "./components/detail-file-list.vue"
    import DetailList from "./components/detail-list.vue"
    import PreviewPop from "@/views/components/preview-pop/index.vue"
    import { map } from "lodash"
    import { FormType } from "@/core-ui/component/form"

    const statusColor = {
        [Status.待提交]: "#FF8B16",
        [Status.已提交审核]: "#5782EC",
        [Status.通过]: "#888888",
        [Status.不通过]: "#E04B2D",
    }

    @Component({
        name: routesMap.policyApplyRecordDD.detail,
        components: {
            DetailRowCol,
            FormView,
            RemarkPop,
            CommonPop,
            DetailFileList,
            DetailList,
            PreviewPop,
        },
    })
    export default class PolicyApplyRecordDetail extends Vue {
        breadcrumbs: BreadcrumbItem[] = []
        isJz = config.envProject === EnvProject.荆州项目
        private showImport = false
        private showAuditPop = false
        showAuditPop4 = false
        showAuditPop5 = false

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from as string),
                {
                    label: "申报内容",
                    to: {
                        name: routesMap.policyApplyRecordDD.detail,
                        query: {
                            id: this.id,
                            serviceType: this.$route.query.serviceType,
                            rowId: this.$route.query.rowId,
                            from: this.from,
                        },
                    },
                },
            ]

            updateTagItem({
                name: routesMap.policyApplyRecordDD.detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private get isPerson() {
            return this.row?.apply_type_serve_type === ServeTargetType.居民
        }

        private get isCompany() {
            return this.row?.apply_type_serve_type === ServeTargetType.企业
        }

        private id = ""
        private row: any | null = null
        private showAuditBtn = false
        private showPop = false
        private rowId = ""
        private rowIds: { id: string; key: string }[] = []
        private canPrev = true
        private canNext = true
        private currentIndex = 0
        private currentRowId = ""
        private rows: any
        private pageIndex = 1
        private queryParams: { filters: [] } | any = {}
        private policyName = ""

        private statusColor = statusColor
        private showChangePop = false

        private get attachments() {
            return this.row?.attachments.split(",").filter(Boolean) || []
        }

        private get labelStyle() {
            return { width: "126px" }
            // if (+this.$route.query.serviceType === ServeTargetType.居民) {
            //     return { width: "86px" }
            // } else {
            //     return { width: "106px" }
            // }
        }

        refreshConfig = {
            name: routesMap.policyApplyRecordDD.detail,
            fun: this.refreshInit,
        }

        mounted() {
            this.initList()
        }

        @Ref()
        private detailList: any

        private refreshInit(e: any) {
            if (e && e[0] === "true") {
                this.refreshDetailList()
            } else {
                this.init()
            }
        }

        private refreshDetailList() {
            const fn = this.detailList?.refreshList
            console.log("refreshDetailList")
            fn && fn()
        }

        beforeDestroy() {
            sessionStorage.removeItem(queryParamsKey)
        }

        initList() {
            const storageData = JSON.parse(
                sessionStorage.getItem(queryParamsKey) as string
            )
            this.pageIndex = storageData?.pageIndex || 1
            this.queryParams = storageData?.queryParams || {}
            this.policyName = storageData?.policyName || ""
            this.rowId = this.$route.query.rowId as string
            // this.getList()
            this.init()
        }

        async init(id?: string) {
            console.log("init", id)
            await sleep(30)
            if (this.$route.name === routesMap.policyApplyRecordDD.detail) {
                this.id = id || (this.$route.query.id as string)
                this.row = null
            }
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("zc_apply_instance")
                    .detail(this.id, "")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, detailPredict)

                        this.showAuditBtn = getShowBtn4Page(r, "audit")
                        this.currentRowId = this.row?.id + ""
                        if (this.isCompany) {
                            return sdk.core
                                .model("zc_apply_instance")
                                .detail(this.id, "manage_company_detail")
                                .query()
                                .then((r) => {
                                    this.row = {
                                        ...this.row,
                                        ...sdk.buildRow(r.row, detailPredict),
                                    }
                                })
                        }
                        if (this.isPerson) {
                            return sdk.core
                                .model("zc_apply_instance")
                                .detail(this.id, "personal_detail")
                                .query()
                                .then((r) => {
                                    this.row = {
                                        ...this.row,
                                        ...sdk.buildRow(r.row, detailPredict),
                                    }
                                })
                        }
                    })
                    .then(async () => {
                        console.log("row", JSON.parse(JSON.stringify(this.row)))
                        this.formList = await createFormList({
                            row: this.row,
                            h: this.$createElement,
                            type: this.row.no,
                        })
                    })
            })
        }

        private getList(next?: boolean, type?: string) {
            const model = sdk.core.model("zc_apply_instance").list("apply_manage")
            if (this.policyName) {
                this.queryParams.filters = [
                    {
                        property: "policy_name",
                        value: this.policyName,
                    },
                ]
            }
            return model
                .query({
                    pageIndex: 1,
                    item_size: 10,
                })
                .then(() => {
                    model
                        .queryTab2(
                            serviceTypeMapping.get(
                                +this.$route.query.serviceType
                            ) || "",
                            {
                                pageIndex: this.pageIndex,
                                item_size: 10,
                                ...this.queryParams,
                            }
                        )
                        .then((r) => {
                            this.rows = r
                            if (this.rows) {
                                this.rowIds =
                                    this.rows?.rows.map((item: any) => {
                                        return {
                                            id: item.id.value,
                                            key: item._access_key.value,
                                        }
                                    }) || []

                                if (next) {
                                    this.currentIndex = 0
                                    if (type === "prev") {
                                        this.currentIndex = this.rowIds.length - 1
                                    }
                                    this.updateQueryValue(
                                        this.rowIds[0]?.key,
                                        this.rowIds[0]?.id
                                    )
                                } else {
                                    this.currentIndex = this.rowIds.findIndex(
                                        (i) => i.id === this.$route.query.rowId
                                    )
                                }

                                if (
                                    (type === "prev" || !next) &&
                                    this.rows &&
                                    !this.rows.item_index &&
                                    !this.currentIndex
                                ) {
                                    this.canPrev = false
                                } else {
                                    this.canPrev = true
                                }
                                if (
                                    (!type || !next) &&
                                    this.rows &&
                                    this.pageIndex >=
                                        Math.ceil(
                                            this.rows.record_count /
                                                this.rows.item_size
                                        ) &&
                                    this.currentIndex >= this.rowIds.length - 1
                                ) {
                                    this.canNext = false
                                } else {
                                    this.canNext = true
                                }
                                // 下一条翻页
                                const id = this.rowIds[this.currentIndex]?.key
                                if (id) {
                                    // 下一条有数据
                                    this.init(id)
                                } else {
                                    this.init()
                                }
                            }
                        })
                })
        }

        private downLoadFile(fileName: string) {
            downLoadFile(sdk.buildImage(fileName || ""), `${fileName}`)
        }

        private toOperate() {
            MessageBox.confirm(`请确认是否通过？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("policy_form_apply")
                        .action("audit")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.row?.id + "" }],
                        })
                        .addInputs_parameter({
                            status: "1",
                        })
                        .execute()
                        .then(() => {
                            this.init()
                        })
                })
            })
        }

        private prev() {
            this.next("prev")
        }

        private next(type?: string) {
            this.currentIndex = this.rowIds.findIndex(
                (i) => i.id === this.currentRowId
            )
            const i = type === "prev" ? -1 : 1
            // 翻页
            if (
                (type === "prev" && !this.currentIndex) ||
                (!type && this.currentIndex + 1 >= this.rowIds.length)
            ) {
                this.pageIndex += i
                return this.getList(true, type)
            }
            this.currentIndex += i
            const key = this.rowIds[this.currentIndex]?.key
            const rowId = this.rowIds[this.currentIndex]?.id
            if (key) {
                // 下一条有数据
                this.init(key)
            } else {
                // 下一条没数据
                // 最后一页
                if (type === "prev") {
                    this.currentIndex += 1
                } else {
                    this.currentIndex -= 1
                }

                this.init(this.rowIds[this.currentIndex]?.key)
            }
            this.updateQueryValue(key, rowId)
            if (
                type === "prev" &&
                this.rows &&
                !this.rows.item_index &&
                !this.currentIndex
            ) {
                this.canPrev = false
            } else {
                this.canPrev = true
            }
            // 点下一条时，判断是否是最后一条数据
            if (
                !type &&
                this.rows &&
                this.pageIndex >=
                    Math.ceil(this.rows.record_count / this.rows.item_size) &&
                this.currentIndex >= this.rowIds.length - 1
            ) {
                this.canNext = false
            } else {
                this.canNext = true
            }
        }

        private updateQueryValue(key: string, rowId: string) {
            const newQuery = { ...this.$route.query }
            newQuery.id = key
            newQuery.rowId = rowId
            this.$router.push({ path: this.$route.path, query: newQuery })
        }

        private toDetail() {
            if (+this.$route.query.serviceType === ServeTargetType.居民) {
                if (this.row?.profile_access_key) {
                    return this.$router.push({
                        name: routesMap.labourManage.seekerDetail,
                        query: {
                            id: this.row?.profile_access_key,
                            from: this.$route.name,
                        },
                    })
                }
            }
            if (this.row?.agent_access_key) {
                this.$router.push({
                    name: routesMap.employmentManage.companyManageDetail,
                    query: {
                        id: this.row?.agent_access_key,
                        from: this.$route.name,
                    },
                })
            }
        }

        private formList: any = []

        private check_finish() {
            MessageBox.confirm(`请确认是否完成核查？`, "提示").then(() => {
                return sdk.core
                    .model("zc_apply_instance")
                    .action("check_finish")
                    .updateInitialParams({
                        selected_list: [
                            {
                                v: 0,
                                id: this.row.id,
                            },
                        ],
                    })
                    .execute()
                    .then(() => {
                        this.init()
                    })
            })
        }

        private showAuditPop2 = false
        private audit() {
            this.showAuditPop2 = true
        }

        showAuditPop3 = false
        private audit2() {
            this.showAuditPop3 = true
        }

        download() {
            batchDownload(this.row!.apply_model_id)
        }

        afterBuildFormSections1(r: any) {
            r.forms = map(r.forms, (e) => {
                // 初次享受年月等 改成年月选择器
                if (
                    ["share_date", "the_begin_date", "the_end_date"].includes(
                        e.prop
                    )
                ) {
                    return {
                        ...e,
                        option: {
                            type: "month",
                        },
                    }
                }
                if (["share_month_number"].includes(e.prop)) {
                    return {
                        ...e,
                        type: FormType.InputNumber,
                        option: {
                            type: "number",
                        },
                    }
                }
                return { ...e }
            })
            return r
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
    .detail-container {
        background: #fff;
        .content {
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
            .label {
                width: 86px;
                color: #555;
            }
        }
        .content-row {
            color: #333;
        }
        /deep/.attachment-template {
            width: 50%;
        }
    }

    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
        margin-left: 30px;
    }
    .attachments {
        background-color: #fbfbfb;
        padding: 10px;
        color: #4273d9;
        margin-top: -10px;
        .item {
            cursor: pointer;
            &:not(:last-child) {
                margin-bottom: 18px;
            }
        }
    }
</style>
