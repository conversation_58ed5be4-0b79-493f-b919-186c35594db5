import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"

const tableFilter: TableFilter[] = [
    {
        label: "使用记录名称",
        type: FormType.Text,
        prop: "name",
    },
]

export const predict = {}
export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("policy_info").list("list_operate"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
    }
}

export const columns: TableColumn[] = [
    { label: "使用记录名称", prop: "id", align: "left", showOverflowTip: true },
]

export const detailPredict = {
    name: "table_display",
    collect_gap: "",
    note: "",
    data_count: "",
    modelName: ""
}
export function getConditions(modeName: string) {
    return sdk.core
        .model(modeName)
        .list("back_list")
        .query({ item_size: 99, pageIndex: 1 })
        .then((r) => {
            return sdk.buildRows(r.pageData.rows, {
                info_key: "",
                info_value: "",
                policy_id: "",
                seq_number: "",
            })
        })
}
