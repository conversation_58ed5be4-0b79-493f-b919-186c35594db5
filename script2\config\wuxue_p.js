const config = {
    局方端: {
        bannerImg: `/img/xiaogan/banner-show.png`,
        bannerStyle: {
            maxWidth: "600px",
            minWidth: "580px",
            marginTop: "50px",
        },
        headerLogo: `/img/hr/logo-show.png`,
        headerLogoStyle: {
            height: "30px",
            width: "32px",
        },
        theme: "#5782EC",
    },
    企业端: {
        bannerImg: `/img/company/logo-bg-show.png`,
        bannerStyle: {
            width: "100%",
        },
        headerLogo: `/img/hr/logo-show.png`,
        headerLogoStyle: {
            height: "26px",
            width: "26px",
        },
        leftStyle: {
            width: "492px",
            maxWidth: "492px",
            background: "#598BFF",
            alignItems: "flex-end",
        },
        rightStyle: {
            background: "#F5F9FF",
        },
        themeClass: "xg-project-org",
        theme: "#5782EC",
    },
    机构端: {
        bannerImg: `/img/hr/banner-show.png`,
        bannerStyle: {
            maxWidth: "600px",
            minWidth: "580px",
            marginTop: "50px",
            // height: "100%",
        },
        headerLogo: `/img/hr/logo-show.png`,
        headerLogoStyle: {
            height: "26px",
            width: "26px",
        },
        leftStyle: {
            background: "linear-gradient(360deg, #43ABFF 0%, #0169BE 100%)",
        },
        theme: "#0169BE",
    },
}

function stringifyValue(config) {
    Object.keys(config).forEach((i) => {
        config[i] = JSON.stringify(config[i])
    })
    return config
}

module.exports = {
    config: { ...stringifyValue(config) },
}
