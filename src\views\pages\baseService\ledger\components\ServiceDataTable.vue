<template>
    <div>
        <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            size="small"
            style="width: 100%"
        >
            <el-table-column
                v-for="column in columns"
                :key="column.prop"
                :prop="column.prop"
                :label="column.label"
                align="center"
            >
            </el-table-column>
            <el-table-column
                v-if="region_level === 5"
                fixed="right"
                label="操作"
                width="100"
            >
                <template slot-scope="scope">
                    <el-button
                        @click="handleDetail(scope.row)"
                        type="text"
                        size="small"
                        >详情</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            style="margin-top: 20px; text-align: center"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"

    @Component
    export default class ServiceDataTable extends Vue {
        @Prop({ type: String, default: "" }) private region_code!: string
        @Prop({ type: [String, Number], default: "" })
        private region_level!: string | number

        private tableData: any[] = []
        private total = 0
        private page = 1
        private page_size = 10
        private loading = false

        get columns() {
            if (this.region_level === 5) {
                return this.villageServiceColumns
            }
            return this.serviceColumns
        }

        // 非村级
        private serviceColumns = [
            { prop: "region_name", label: "区划" },
            { prop: "grid_total", label: "人社专员数量" },
            { prop: "equipment_total", label: "设备数量" },
            { prop: "shequn_total", label: "社群数量" },
            { prop: "shequnjumin_total", label: "社群居民数量" },
            { prop: "labor_force_count", label: "劳动力数量" },
            { prop: "policy_count", label: "政策推广次数" },
            { prop: "position_count", label: "岗位推广次数" },
            { prop: "job_fair_count", label: "招聘会推广次数" },
            { prop: "hb_training_count", label: "培训推广次数" },
        ]

        // 村级
        private villageServiceColumns = [
            { prop: "real_name", label: "人社专员" },
            { prop: "shequn_total", label: "社群数量" },
            { prop: "shequnjumin_total", label: "社群居民数量" },
            { prop: "update_labor_force_count", label: "劳动力更新数量" },
            { prop: "xg_company_position_count", label: "岗位推广数量" },
            { prop: "policy_form_count", label: "政策推广数量" },
            { prop: "job_fair_count", label: "招聘会推广数量" },
            { prop: "hb_training_count", label: "培训推广数量" },
        ]

        @Watch("region_code")
        onRegionCodeChange() {
            this.page = 1
            this.fetchData()
        }

        mounted() {
            this.fetchData()
        }

        private fetchData() {
            const params: {
                item_size: number
                item_index: number
                region_code?: string
            } = {
                item_size: this.page_size,
                item_index: this.page,
            }
            if (this.region_code) {
                params.region_code = this.region_code
            }
            this.loading = true
            const request =
                this.region_level === 5
                    ? sdk.core
                          .domainService(
                              "xg_project",
                              "g_service_register",
                              "service_region_dashboard_detail_list"
                          )
                          .post({
                              ...params,
                              name: "grid",
                              start_time: "",
                              end_time: "",
                          })
                    : sdk.core
                          .domainService(
                              "xg_project",
                              "g_service_register",
                              "service_region_dashboard_list"
                          )
                          .post(params)

            request
                .then((res: any) => {
                    this.tableData = res.list || []
                    this.total = Number(res.total) || 0
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private handleSizeChange(val: number) {
            this.page_size = val
            this.page = 1
            this.fetchData()
        }

        private handleCurrentChange(val: number) {
            this.page = val
            this.fetchData()
        }

        private handleDetail(row: any) {
            this.$router.push({
                name: routesMap.baseService.ledgerDetail,
                query: {
                    region_code: this.region_code,
                    uniplat_uid: row.uniplat_uid,
                    id: row.id,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "../style/custom-table.less";
</style>
