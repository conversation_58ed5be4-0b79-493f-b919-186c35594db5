const jzConfig = require("./config/jz.js")
const xgConfig = require("./config/xg.js")
const yanshiConfig = require("./config/yanshi.js")
const qianJiangConfig = require("./config/qianjiang.js")
const huangzhou = require("./config/huangzhou.js")
const wuxue = require("./config/wuxue.js")
const wuhanConfig = require("./config/wuhan.js")
const shiyanConfig = require("./config/shiyan.js")
const ezhouConfig = require("./config/ezhou.js")
const yiduConfig = require("./config/yidu.js")
const duodao = require("./config/duodao.js")
const configEnv = [
    // 荆州项目
    jzConfig,
    // 孝感项目
    xgConfig,
    // 演示项目
    yanshiConfig,
    qianJiangConfig,
    huangzhou,
    wuxue,
    wuhanConfig,
    shiyanConfig,
    ezhouConfig,
    yiduConfig,
    duodao,
]
module.exports = {
    configEnv,
}
