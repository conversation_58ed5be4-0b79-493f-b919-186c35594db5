export enum EnvProject {
    孝感项目 = "孝感项目",
    荆州项目 = "荆州项目",
    演示项目 = "演示项目",
    潜江项目 = "潜江项目",
    武汉数采项目 = "武汉数采项目",
    saas项目 = "saas项目",
    黄州项目 = "黄州项目",
    十堰项目 = "十堰项目",
    武穴项目 = "武穴项目",
    红安项目 = "红安项目",
    鄂州项目 = "鄂州项目",
    宜都项目 = "宜都项目",
    掇刀项目 = "掇刀项目",
    咸丰项目 = "咸丰项目",
    洪湖项目 = "洪湖项目",
    利川项目 = "利川项目",
}

export enum EnvApplication {
    局方端 = "局方端",
    企业端 = "企业端",
    机构端 = "机构端",
    培训机构端 = "培训机构端",
    网格员端 = "网格员端",
}

export enum DeployEnv {
    正式线 = "pro",
    测试线 = "test",
}

export enum NodeEnv {
    development = "development",
    production = "production",
}
const envApplication = process.env.VUE_APP_ENV_APPLICATION as EnvApplication
export const config = {
    isPro: process.env.VUE_APP_ENV === DeployEnv.正式线,
    isBuild: process.env.NODE_ENV === NodeEnv.production,
    uniplatApi: process.env.VUE_APP_UNIPLAT,
    uniplatWeb: process.env.VUE_APP_UNIPLAT_WEB,
    uniplatSocketUrl: process.env.VUE_APP_UNIPLAT_WEBSOCKET_URI,
    uniplatEntrance: process.env.VUE_APP_UNIPLAT_ENTRANCE,
    h5: process.env.VUE_APP_H5,
    operateUrl: process.env.VUE_APP_OPERATE_URL,
    orgUrl: process.env.VUE_APP_ENTERPRISE_URL,
    hrUrl: process.env.VUE_APP_HR_URL,
    workMpAgentId: process.env.VUE_APP_WORK_MP_AGENT_ID,
    isOrg: envApplication === EnvApplication.企业端,
    isHr: envApplication === EnvApplication.机构端,
    isOperate: envApplication === EnvApplication.局方端,
    isGrid: envApplication === EnvApplication.网格员端,
    envProject: process.env.VUE_APP_ENV_PROJECT as EnvProject,
    envApplication,
    clientId: process.env.VUE_APP_APP_NAME,
    defaultRegion: {
        region_code: process.env.VUE_APP_DEFAULT_REGION_CODE,
        region_name: process.env.VUE_APP_DEFAULT_REGION_NAME,
        region_code_max_level:
            process.env.VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL || 3,
    },
    headerTitle:
        process.env.VUE_APP_HEADER_TITLE || process.env.VUE_APP_APP_TITLE || "",
    appTitle:
        process.env.VUE_APP_APP_TITLE || process.env.VUE_APP_HEADER_TITLE || "",
    citySampleName: process.env.VUE_APP_CITY_SAMPLE_NAME || "",
    citySampleName2: process.env.VUE_APP_CITY_SAMPLE_NAME2 || "",
    projectConfig: JSON.parse(process.env.VUE_APP_CUS_PROJECT_STYLE_CONFIG),
    miniProgramName: process.env.VUE_APP_MINI_PROGRAM_NAME || "",
}
