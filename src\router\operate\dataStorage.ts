import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const dataStorage = [
    {
        path: "/dataStorage",
        name: routesMap.home.dataStorage,
        meta: {
            title: isQj ? "数据仓库管理" : "数据仓库及核查工具",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/dataStorage.svg"),
            hidden: [
                EnvProject.荆州项目,
                EnvProject.黄州项目,
                EnvProject.宜都项目,
                EnvProject.鄂州项目, // 11.27隐藏
                EnvProject.掇刀项目,
                EnvProject.saas项目,
                EnvProject.潜江项目,
            ].includes(config.envProject),
        },
        component: layout,
        children: [
            // {
            //     path: "resource-manage",
            //     name: routesMap.dataStorage.resourceManage,
            //     meta: {
            //         title: "数据资源库列表",
            //         role: "/tablelist/xg_resource_repository/manage",
            //     },
            //     component: () =>
            //         import(
            //             "@/views/pages/data-storage/data-resource-manage/index.vue"
            //         ),
            // },
            {
                path: "resource-manage",
                name: routesMap.dataStorage.collectManage,
                meta: {
                    title: "数据表管理",
                    role: "/tablelist/dataset_register",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-collect-manage/index.vue"
                    ),
            },
            {
                path: "collect-detail",
                name: routesMap.dataStorage.collectDetailManage,
                meta: {
                    title: "数据表详情",
                    parentMenuName: routesMap.dataStorage.collectManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-collect-manage/detail.vue"
                    ),
            },
            {
                path: "resource-manage-add",
                name: routesMap.dataStorage.resourceManageAdd,
                meta: {
                    title: "创建/修改资源库",
                    parentMenuName: routesMap.dataStorage.resourceManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-resource-manage/add.vue"
                    ),
            },
            {
                path: "resource-manage-detail",
                name: routesMap.dataStorage.resourceManageDetail,
                meta: {
                    title: "数据资源库详情",
                    parentMenuName: routesMap.dataStorage.resourceManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-resource-manage/detail.vue"
                    ),
            },
            {
                path: "resource-manage-add-item",
                name: routesMap.dataStorage.resourceManageAddItem,
                meta: {
                    title: "新增/修改数据项",
                    parentMenuName: routesMap.dataStorage.resourceManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-resource-manage/add-item.vue"
                    ),
            },
            {
                path: "comparison-manage",
                name: routesMap.dataStorage.comparisonManage,
                meta: {
                    title: "比对/查询功能列表",
                    role: "/tablelist/xg_resource_function/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-comparison-manage/index.vue"
                    ),
            },
            {
                path: "comparison-manage-add",
                name: routesMap.dataStorage.comparisonManageAdd,
                meta: {
                    title: "新增功能",
                    parentMenuName: routesMap.dataStorage.comparisonManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-comparison-manage/add.vue"
                    ),
            },
            {
                path: "comparison-manage-detail",
                name: routesMap.dataStorage.comparisonManageDetail,
                meta: {
                    title: "比对/查询功能详情",
                    parentMenuName: routesMap.dataStorage.comparisonManage,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-comparison-manage/detail.vue"
                    ),
            },
            // 暂时不要
            // {
            //     path: "data-apply-form",
            //     name: routesMap.dataStorage.applyForm,
            //     meta: {
            //         title: "数据使用申请单",
            //     },
            //     component: () =>
            //         import(
            //             "@/views/pages/data-storage/data-apply-form/index.vue"
            //         ),
            // },
            {
                path: "data-apply-add",
                name: routesMap.dataStorage.applyFormAdd,
                meta: {
                    title: "新建数据使用申请单",
                    parentMenuName: routesMap.dataStorage.applyForm,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-apply-form/add.vue"
                    ),
            },
            {
                path: "data-apply-detail",
                name: routesMap.dataStorage.applyFormDetail,
                meta: {
                    title: "失业补贴人员比对申请",
                    parentMenuName: routesMap.dataStorage.applyForm,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/data-apply-form/detail.vue"
                    ),
            },
            {
                path: "query-usage-record",
                name: routesMap.dataStorage.queryUsageRecord,
                meta: {
                    title: "比对/查询使用记录",
                    role: "/tablelist/xg_resource_function_use/manage",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/query-usage-record/index.vue"
                    ),
            },
            {
                path: "query-usage-record-detail",
                name: routesMap.dataStorage.queryUsageRecordDetail,
                meta: {
                    title: "使用记录详情",
                    parentMenuName: routesMap.dataStorage.queryUsageRecord,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/query-usage-record/detail.vue"
                    ),
            },
            {
                path: "person-compare-manage",
                name: routesMap.dataStorage.personDataCompare,
                meta: {
                    title: "数据比对功能列表",
                    role: "/tablelist/data_comparison/data_comparison_list",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/person-data-compare/function-manage/index.vue"
                    ),
            },
            {
                path: "person-compare-detail",
                name: routesMap.dataStorage.personDataCompareDetail,
                meta: {
                    title: "数据比对功能详情",
                    parentMenuName: routesMap.dataStorage.personDataCompare,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/person-data-compare/function-manage/detail.vue"
                    ),
            },
            {
                path: "person-compare-task-manage",
                name: routesMap.dataStorage.personDataCompareTask,
                meta: {
                    title: "数据比对任务列表",
                    role: "/tablelist/data_comparison_task/data_comparison_task_list",
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/person-data-compare/task-manage/index.vue"
                    ),
            },
            {
                path: "person-compare-task-manage-detail",
                name: routesMap.dataStorage.personDataCompareTaskDetail,
                meta: {
                    title: "数据比对任务详情",
                    parentMenuName: routesMap.dataStorage.personDataCompareTask,
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/data-storage/person-data-compare/task-manage/detail.vue"
                    ),
            },
        ],
    },
]
