<template>
    <div class="content">
        <Header :title="title"></Header>
        <Items :items="items" @toRoute="toRoute"></Items>
        <div class="smaller-title">政策找人帮扶人次</div>
        <Tables
            :titles="titles"
            :data="tableData"
            :disabledClickItems="disabledClickItems"
            @click="toPerson"
            :loading="loading"
        ></Tables>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import Header from "../../common/header.vue"
    import Items from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/items.vue"
    import Tables from "../../common/tables.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"

    @Component({ components: { Header, Items, Tables } })
    export default class Template extends BaseItem {
        private items = [
            {
                label: "政策总数",
                value: 81,
            },
            {
                label: "政策找人人数",
                value: 0,
                isRoute: true,
            },
            {
                label: "政策找人服务次数",
                value: 0,
                isRoute: true,
            },
            {
                label: "政策找人办结数",
                value: 0,
                isRoute: true,
            },
        ]

        private disabledClickItems = ["政策名称"]

        private titles = ["政策名称", "政策找人数", "服务人次数", "办结数"]

        private title = "政策"

        private tableData: any[][] = []

        private loading = false

        private list: any[] = []

        protected refresh() {
            this.getTableData()
        }

        private toRoute(label: string, value: number) {
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list1,
                query: {
                    type: `${this.title}_${label}`,
                    form_id__in: this.list.map((i) => i.id).join(","),
                    cIndex: value + "",
                },
            })
        }

        private toPerson(title: string, item: string, idx: number, itemSelf: any) {
            const form_id = this.list[idx].id
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list1,
                query: {
                    type: `${this.title}_${item}_${title}`,
                    form_id,
                    cIndex: itemSelf + "",
                },
            })
        }

        getTableData() {
            this.loading = true
            const model = sdk.core.model("policy_form").list("manage_list")
            model.clearPrefilter()
            model.addPrefilter({
                policy_name__in: "一次性创业补贴,灵活就业社保补贴",
            })
            return model.query({ item_size: 999, pageIndex: 1 }).then((r) => {
                return r.getList("居民", 1).then((r) => {
                    this.loading = false
                    const l = sdk.buildRows(r.rows, {
                        id: "",
                        policy_name: "",
                        policy_person_count: "",
                        policy_person_sum: "",
                        policy_person_finish_count: "",
                    })
                    this.list = l
                    this.tableData = l.map((e: any) => {
                        return [
                            e.policy_name || "",
                            e.policy_person_count,
                            e.policy_person_sum,
                            e.policy_person_finish_count,
                        ]
                    })
                    const total1 = this.tableData.reduce((sum, item: any) => {
                        return sum + +item[1]
                    }, 0)
                    const total2 = this.tableData.reduce((sum, item: any) => {
                        return sum + +item[2]
                    }, 0)
                    const total3 = this.tableData.reduce((sum, item: any) => {
                        return sum + +item[3]
                    }, 0)

                    this.items = [
                        {
                            label: "政策总数",
                            value: 81,
                        },
                        {
                            label: "政策找人人数",
                            value: total1 || 0,
                            isRoute: true,
                        },
                        {
                            label: "政策找人服务次数",
                            value: total2 || 0,
                            isRoute: true,
                        },
                        {
                            label: "政策找人办结数",
                            value: total3 || 0,
                            isRoute: true,
                        },
                    ]
                })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 613px;
        height: 294px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;

        .smaller-title {
            font-size: 15px;
            color: #89b9ff;
            line-height: 18px;
            text-align: left;
            margin-bottom: 4px;
            padding-top: 7px;
            padding-left: 20px;
        }

        /deep/ .table-box {
            .line-box {
                height: 106px;
            }

            .line {
                .row {
                    width: 25%;
                }
            }
        }
    }
    /deep/.empty-box .empty-img {
        width: 94px;
        height: 80px;
    }
</style>
