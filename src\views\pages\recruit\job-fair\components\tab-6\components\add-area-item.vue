<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="500px"
    >
        <div class="u-p-x-20" v-loading="formLoading">
            <form-builder ref="formBuilder" labelWidth="80px"></form-builder>
            <div class="u-flex u-row-center u-m-t-40">
                <el-button
                    @click="close"
                    class="btn custom-btn u-m-r-30"
                    type="primary"
                    plain
                >
                    取消
                </el-button>
                <el-button
                    class="btn custom-btn"
                    type="primary"
                    :loading="loading"
                    @click="confirm"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { Component, Mixins, Prop } from "vue-property-decorator"

    @Component({ components: { FormBuilder } })
    export default class AddareaItem extends Mixins(
        DialogController,
        FormController
    ) {
        onOpen() {
            this.init()
        }

        @Prop()
        private id?: number

        @Prop()
        private job_fair_id!: number

        private get title() {
            return `${this.id ? "修改" : "新增"}展区`
        }

        private action() {
            return sdk.core
                .model("job_fair_booth_area")
                .action(this.id ? "update_area" : "add_area")
                .updateInitialParams({
                    selected_list: this.id ? [{ id: this.id, v: 0 }] : [],
                    prefilters: [
                        { property: "job_fair_id", value: this.job_fair_id },
                    ],
                })
        }

        private init() {
            this.formLoading = true
            return buildFormSections({
                action: this.action(),
                forms: [],
            })
                .then((r) => {
                    this.buildForm(r.forms)
                })
                .finally(() => {
                    this.formLoading = false
                })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.loading = true
                    this.action()
                        .addInputs_parameter(data)
                        .execute()
                        .then(() => {
                            this.close()
                            this.$emit("refresh")
                        })
                        .finally(() => {
                            this.loading = false
                        })
                }
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .btn {
        width: 100px;
        height: 36px;
    }
</style>
