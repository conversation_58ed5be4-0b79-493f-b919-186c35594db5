import { sdk } from "."
const userInfoPredict = {
    id: "",
    id_card_openid: "",
    uniplat_version: "",
    // 基本信息
    name: "",
    sex: "",
    id_card: "",
    mobile: "",
    nation: "basic_info#nation",
    reg_residence_property: "basic_info#reg_residence_property",
    political_outlook: "basic_info#political_outlook",
    hometown: "basic_info#hometown_address",
    household: "basic_info#household_address_full_name",
    permanent_province_code: "basic_info#permanent_area_code",
    permanent_city_code: "basic_info#permanent_city_code",
    permanent_area_code: "basic_info#permanent_province_code",
    // 最高学历信息
    education: "basic_info#education_label",
    graduate_school:
        "id_card_openid#user_profile_education_info.graduation_school",
    study_speciality: "id_card_openid#user_profile_education_info.major",
    graduate_date: "id_card_openid#user_profile_education_info.graduation_time",
    start_time: "id_card_openid#user_profile_education_info.start_time",
    // 培训信息
    is_train_job: "profile_current_train_info#is_train_job",
    train_start_datetime: "profile_current_train_info#train_start_datetime",
    train_end_datetime: "profile_current_train_info#train_end_datetime",
    househtrain_specialityold: "profile_current_train_info#train_speciality",
    train_speciality_detail:
        "profile_current_train_info#train_speciality_detail_label",
    qualification_level: "profile_current_train_info#qualification_level",
    is_training_willingness:
        "profile_current_train_info#is_training_willingness_label",
    training_willingness_work_type:
        "profile_current_train_info#training_willingness_work_type",
    is_tran_flexible_employment:
        "profile_current_train_info#is_tran_flexible_employment_label",
    flexible_employment_month:
        "profile_current_train_info#flexible_employment_month",
    // 社保信息
    social_area_type:
        "id_card_openid#user_profile_socialfund_info.social_area_type",
    social_payment_type:
        "id_card_openid#user_profile_socialfund_info.social_payment_type",
    social_actual_payer:
        "id_card_openid#user_profile_socialfund_info.social_actual_payer",
    social_security_number:
        "id_card_openid#user_profile_socialfund_info.social_security_number",
    social_remark: "id_card_openid#user_profile_socialfund_info.social_remark",
    // 公积金信息
    fund_area_type:
        "id_card_openid#user_profile_socialfund_info.fund_area_type",
    fund_payment_type:
        "id_card_openid#user_profile_socialfund_info.fund_payment_type",
    fund_actual_payer:
        "id_card_openid#user_profile_socialfund_info.fund_actual_payer",
    fund_security_number:
        "id_card_openid#user_profile_socialfund_info.fund_security_number",
    fund_remark: "id_card_openid#user_profile_socialfund_info.fund_remark",

    // 就业信息
    job_id: "id_card_openid#user_profile_current_job_info.id",
    employment_status:
        "id_card_openid#user_profile_current_job_info.employment_status_label",
    employment_type:
        "id_card_openid#user_profile_current_job_info.employment_type_label",
    work_province_name:
        "id_card_openid#user_profile_current_job_info.work_province_id#sys_region_joint.name",
    work_city_name:
        "id_card_openid#user_profile_current_job_info.work_city_id#sys_region_joint.name",
    work_county_name:
        "id_card_openid#user_profile_current_job_info.work_county_id#sys_region_joint.name",
    company_name: "id_card_openid#user_profile_current_job_info.company_name",
    job_industry:
        "id_card_openid#user_profile_current_job_info.job_industry_label",
    job_type_work: "user_profile_current_job_info#job_type_work_label",
    salary: "id_card_openid#user_profile_current_job_info.salary",
    is_job_willing: "job_willingness#is_job_willing_label",
    job_willing_salary: "job_willingness#job_willing_salary",
    job_willing_industry: "job_willingness#job_willing_industry_label",
    job_willing_type_work: "job_willingness#job_willing_type_work_display",
    job_willing_province_id: "job_willingness#job_willing_province_id_label",
    job_willing_city_id: "job_willingness#job_willing_city_id_label",
    job_willing_county_id: "job_willingness#job_willing_county_id_label",
    job_willing_province_id_two:
        "job_willingness#job_willing_province_id_two_label",
    job_willing_city_id_two: "job_willingness#job_willing_city_id_two_label",
    job_willing_county_id_two:
        "job_willingness#job_willing_county_id_two_label",
    job_willing_province_id_three:
        "job_willingness#job_willing_province_id_three_label",
    job_willing_city_id_three:
        "job_willingness#job_willing_city_id_three_label",
    job_willing_county_id_three:
        "job_willingness#job_willing_county_id_three_label",
    is_start_job: "user_profile_current_job_info#is_start_job_label",
    start_job_industry:
        "user_profile_current_job_info#start_job_industry_label",
    start_job_type_work:
        "user_profile_current_job_info#start_job_type_work_display",
    file_url: "basic_info#file_url",
    recruit_position_count: "",
}
export interface CustomProfile {
    id_card: string
    nation: string
    mobile: string
    name: string
}
class ProfileService {
    private loaded = false
    public data: CustomProfile | null = null
    public setup() {
        if (this.loaded) return Promise.resolve(this.data)
        return sdk.core
            .model("user_profile_basic")
            .detail("get_key_value", "client_detail")
            .queryKeyCustom()
            .then((r) => {
                this.loaded = true
                this.data = sdk.buildRow(r.row, userInfoPredict)
                return this.data
            })
    }
}
export const profileService = new ProfileService()
