import { TableColumn, TableConfig } from "@/core-ui/component/table"

export interface Row {
    /** 关联单位 */
    name: string
    tool_name: string
    org_name: string

    /** 核查需求简述 */
    require_note: string

    /** 结果描述 */
    result_note: number

    /** 是否有异常数据 */
    is_exception: number

    /** 创建人 */
    real_name: string

    /** 创建时间 */
    create_time: string

    /** 任务状态 */
    status: Status

    /** 任务状态[文本] */
    status_label: string
    _access_key: string
    id: number
    v: number
}

export interface DetailRow {
    /** 是否删除 */
    is_del: number

    /** 创建时间 */
    create_time: string

    /** 更新人 */
    real_name: string

    /** 核查任务名称 */
    name: string

    /** 更新时间 */
    update_time: string

    /** 最后变更时间 */
    final_change_time: string

    /** 核查需求简述 */
    require_note: string

    /** 结果描述 */
    result_note: number

    /** 任务状态 */
    status: Status

    /** 任务状态[文本] */
    status_label: string

    /** 是否有异常数据 */
    is_exception: number

    /** 核查数据文件路径 */
    data_file_path: string
    id: number
    v: number
}

export const columns: TableColumn[] = [
    {
        label: "核查任务名称",
        prop: "name",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "核查模板",
        prop: "tool_name",
        showOverflowTip: true,
    },
    {
        label: "关联单位",
        prop: "org_name",
        showOverflowTip: true,
    },
    {
        label: "创建人",
        prop: "real_name",
        showOverflowTip: true,
        minWidth: "110px",
    },
    {
        label: "创建时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "任务状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "结果简述",
        prop: "result_note",
        showOverflowTip: true,
        minWidth: "150px",
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        showOverflowTip: true,
    },
]

export const columns1: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "身份证",
        prop: "id_card_hide",
        showOverflowTip: true,
    },
    {
        label: "核查结果",
        prop: "check_result_label",
        showOverflowTip: true,
    },
    {
        label: "核查说明",
        prop: "check_result_note",
        showOverflowTip: true,
        minWidth: "120px",
    },
]

export const columns2: TableColumn[] = [
    {
        label: "导出时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "导出人",
        prop: "real_name",
        showOverflowTip: true,
    },
    {
        label: "导出人所属组织",
        prop: "org_name",
        showOverflowTip: true,
    },
    {
        label: "导出结果",
        prop: "download_status_label",
        showOverflowTip: true,
    },
    // {
    //     label: "导出内容",
    //     prop: "parent_display_name",
    //     showOverflowTip: true,
    // },
    {
        label: "说明",
        prop: "download_note",
        showOverflowTip: true,
        minWidth: "120px",
    },
]

const enum Status {
    启用 = 0,
    禁用 = 1,
}

export const enum CheckResult {
	符合条件 = 0,
	不符合条件 = 1
}

export interface DetailListRow {
    /** 关联单位 */
    name: string

    /** 身份证号 */
    id_card_hide: string

    /** 核查需求简述 */
    require_note: string

    /** 结果描述 */
    result_note: number

    /** 是否有异常数据 */
    is_exception: number

    /** 创建人 */
    real_name: string

    /** 创建时间 */
    create_time: string

    /** 任务状态 */
    status: Status

    /** 任务状态[文本] */
    status_label: string

    /** 核查结果 */
    check_result: CheckResult

    /** 核查结果[文本] */
    check_result_label: string

    /** 核查说明 */
    check_result_note: string
    id: number
    v: number
}

export const intentPredict = {
    id: "",
    name: "",
    note: "",
    source_count: "",
    task_count: "",
}

export const intentColumn: TableConfig["column"] = [
    {
        label: "核查工具名称",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "描述",
        prop: "note",
        showOverflowTip: true,
    },
    {
        label: "关联数据库",
        prop: "source_count",
        showOverflowTip: true,
    },
    {
        label: "下属核查任务",
        prop: "task_count",
        showOverflowTip: true,
    },
]
