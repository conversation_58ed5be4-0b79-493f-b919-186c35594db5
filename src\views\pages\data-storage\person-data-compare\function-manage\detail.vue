<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button
                    type="primary"
                    @click="showAddComparePop = true"
                    v-if="showEdit"
                >
                    新建比对任务
                </el-button>
            </div>
        </div>
        <div class="detail-top-box u-flex u-row-between">
            <detail-row-col :list="items" class="u-p-x-20" labelStyle="120px">
            </detail-row-col>
            <div class="u-flex u-row-right u-m-r-20 u-m-l-10">
                <el-button
                    type="primary"
                    plain
                    @click="toOperate('on_shelf')"
                    v-if="shoeOperateOn"
                >
                    启用
                </el-button>
                <el-button
                    type="primary"
                    plain
                    @click="toOperate('off_shelf')"
                    v-if="shoeOperateOff"
                >
                    禁用
                </el-button>
                <el-button
                    type="primary"
                    @click="showAddPop = true"
                    v-if="showEdit"
                >
                    修改
                </el-button>
            </div>
        </div>
        <div v-if="row">
            <DetailTable ref="detailTable" :detailId="detailId" :showCompare="showCompare" />
        </div>
        <AddPop :id="detailId" v-model="showAddPop" />
        <AddComparePop :id="detailId" v-model="showAddComparePop" />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../../single-page/components/tags-view"
    import DetailTable from "./components/detail-table.vue"
    import { pageLoading } from "@/views/controller"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { MessageBox } from "element-ui"
    import { DetailRow, Status } from "./index"
    import AddPop from "./components/add-pop.vue"
    import AddComparePop from "./components/add-compare-pop.vue"
    import { getShowBtn4Page } from "@/views/pages/collect-task-manage/components/build-table"

    @Component({
        name: routesMap.dataStorage.personDataCompareDetail,
        components: { DetailTable, DetailRowCol, AddPop, AddComparePop },
    })
    export default class GeneralDetail extends Vue {
        private breadcrumbs: BreadcrumbItem[] = []

        private showList = false

        private items: ColItem[] = []

        private showAddPop = false
        private showAddComparePop = false
        private id = ""

        refreshConfig = {
            fun: this.init,
            name: routesMap.dataStorage.personDataCompareDetail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "数据比对功能详情",
                    to: {
                        name: routesMap.dataStorage.personDataCompareDetail,
                        query: {
                            id: this.$route.query.id || this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.dataStorage.personDataCompareDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private detailId = ""

        private row: DetailRow | null = null

        private shoeOperateOn = false
        private shoeOperateOff = false
        private showEdit = false
        private showCompare = false

        private get actionName() {
            return this.row?.status === Status.禁用 ? "启用" : "禁用"
        }

        private get isBan() {
            return this.row?.status === Status.禁用
        }

        created() {
            this.init()
        }

        private init() {
            if (this.$route.query.id) {
                this.id = this.$route.query.id as string
            }
            if (!this.id) return
            this.row = null
            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("data_comparison")
                    .detail(this.id, "data_comparison_list_detail")
                    .query()
                    .then((res) => {
                        this.row = sdk.buildRow(res.row, {
                            id: "",
                            status: 0,
                        })
                        this.shoeOperateOn = this.getShowBtn4Page(res, "on_shelf")
                        this.shoeOperateOff = this.getShowBtn4Page(res, "off_shelf")
                        this.showEdit = this.getShowBtn4Page(
                            res,
                            "update_data_comparison"
                        )
                        this.showCompare = getShowBtn4Page(res, "add_db_column")
                        this.detailId = this.row?.id + "" || ""
                        this.items = res.meta.sections[0].field_groups.map(
                            (item) => {
                                return {
                                    label: item.label + "：",
                                    value: item.template,
                                    hide: !item.visible,
                                    span: 12,
                                }
                            }
                        )
                    })
            })
        }

        private toAdd() {
            ;(this.$refs.detailTable as any).toAdd()
        }

        private refreshList() {
            ;(this.$refs.detailTable as any).refresh()
        }

        private publish(status: "1" | "0") {
            const label = status === "1" ? "发布" : "取消发布"
            MessageBox.confirm(`确认${label}？`, `${label}`).then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_indicator_group_ref")
                        .action(`change_status_${status}`)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.detailId }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("操作成功")
                            this.callRefresh(
                                routesMap.dataStorage.personDataCompareDetail
                            )
                        })
                })
            })
        }

        private toOperate(actionName: string) {
            MessageBox.confirm(`确认${this.actionName}？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("data_comparison")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.detailId }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            this.init()
                        })
                })
            })
        }

        private getShowBtn4Page(r: any, key: string) {
            if (
                r.meta.sections &&
                r.meta.sections.length > 0 &&
                r.meta.sections[0].actions?.length
            ) {
                const res = !!r.meta.sections[0].actions.find(
                    (i: any) => i.action_name === key
                )
                if (res) {
                    return true
                }
            }

            return false
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
    }
</style>
