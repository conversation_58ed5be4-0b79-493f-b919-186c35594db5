<template>
    <div
        class="container-box core-ui-table-container"
        :key="refreshQueryParams"
        v-if="row"
    >
        <div class="core-ui-custom-header">
            <div
                class="title"
                :class="{
                    agent: isAgent,
                }"
            >
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    type="primary"
                    @click="show = true"
                    v-if="!isPublish && !isAgent"
                >
                    公示
                </el-button>

                <el-button
                    v-if="!isPublish && isAgent"
                    @click="showImportPop = true"
                >
                    导入学员
                </el-button>
                <el-button type="primary" @click="exportExcel">
                    导出授信用账户
                </el-button>
            </div>
        </div>
        <div class="contain-box">
            <detail-view
                :detail="row"
                :isAgent="isAgent"
                :isPublish="isPublish"
                ref="detailView"
                :key="row._access_key"
            ></detail-view>
        </div>
        <show-pop v-model="show" :id="row.id" @refresh="init"></show-pop>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入学员"
            placeholder="请点击「确定」将学员上传"
            :importConfig="importConfig"
            @refresh="refresh"
        />
    </div>
</template>

<script lang='ts'>
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { Component, Prop, Ref, Vue } from "vue-property-decorator"
    import { predict } from "."
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailView from "./components/detail-view.vue"
    import ShowPop from "./components/show-pop.vue"

    @Component({
        name: routesMap.credit.accreditDetail,
        components: { DetailView, ShowPop, ExcelImport },
    })
    export default class CreditDetail extends Vue {
        private id = ""
        private row: any = null
        private showPop = false
        private show = false
        private showImportPop = false
        private importConfig: any = null

        private get isPublish(): boolean {
            return this.row.public_begin_time
        }

        // 是否是培训机构端
        @Prop({ default: false })
        private isAgent!: boolean

        @Ref()
        private detailView!: DetailView

        refreshConfig = {
            fun: this.init,
            name: routesMap.credit.accreditDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: "信用账户班级详情",
                },
            ]
            updateTagItem({
                name: routesMap.credit.accreditDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        mounted() {
            this.init()
        }

        private init() {
            this.row = null
            this.id = this.$route.query.id as string
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("jz_training_class_info")
                    .detail(this.id, this.isAgent ? "agent_detail" : "")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                        this.initImport()
                    })
            })
        }

        initImport() {
            this.importConfig = {
                templateUrl: window.location.origin + "/file/导入学员.xls",
                modelName: "jz_training_class_info",
                actionName: "import_class_member",
                bigActionImportParams: {
                    inputs_parameters: [],
                    selected_list: [
                        {
                            id: this.row.id,
                            v: 0,
                        },
                    ],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
        }

        private exportExcel() {
            this.detailView?.exportToExcel && this.detailView.exportToExcel()
        }

        private refresh() {
            this.detailView?.refreshList && this.detailView.refreshList()
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .contain-box {
        margin-bottom: 24px;
    }
    .title {
        width: 100%;
        height: 36px;
        font-size: 16px;
        background: #f8f8f8;
        padding-left: 20px;
        &.agent {
            background: transparent;
            padding-left: 0;
        }
    }
</style>
