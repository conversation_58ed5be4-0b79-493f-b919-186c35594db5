<template>
    <el-cascader
        ref="cascader"
        :key="componentKey"
        :value="selectedCodes"
        :options="options"
        :props="cascaderProps"
        @change="handleChange"
    ></el-cascader>
</template>

<script lang="ts">
    import { requestTreeData } from "@/views/pages/collect-task-manage/components/cache-tree"
    import {
        getLastRegionCode,
        getRegionName,
    } from "@/views/pages/collect-task-manage/components/region-tool"
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"

    @Component
    export default class RegionSelector extends Vue {
        @Prop({ default: "" })
        private selectedValue!: string

        private options: any[] = []
        private componentKey = 0

        private cascaderProps: any = {
            checkStrictly: true,
            lazy: true,
            lazyLoad: this.lazyLoadData,
        }

        get selectedCodes(): string[] {
            return this.selectedValue && this.selectedValue.length > 0
                ? this.selectedValue.split(",")
                : []
        }

        @Watch("selectedValue", { immediate: true })
        private onSelectedValueChanged() {
            this.loadInitialData()
        }

        private async loadNode(regionCode: string): Promise<any[]> {
            if (!regionCode) {
                return []
            }
            const res = await requestTreeData("region_code", regionCode, 5)
            if (res && res.children) {
                return res.children.map((item: any) => ({
                    value: item.data.region_code,
                    label: item.display,
                    leaf: item.leaf,
                }))
            }
            return []
        }

        private async loadInitialData() {
            const codes = this.selectedCodes
            const rootValue = getLastRegionCode()

            if (!rootValue) {
                this.options = []
                return
            }

            const rootNodeData = await requestTreeData("region_code", rootValue, 5)
            const rootLabel =
                rootNodeData && rootNodeData.current
                    ? rootNodeData.current.display
                    : getRegionName()
            const rootIsLeaf =
                rootNodeData && rootNodeData.current
                    ? rootNodeData.current.leaf
                    : false

            this.options = [
                {
                    label: rootLabel,
                    value: rootValue,
                    leaf: rootIsLeaf,
                },
            ]

            if (codes.length === 0 || codes[0] !== rootValue) {
                return
            }

            let currentNode: any = this.options[0]

            for (const codeToFind of codes.slice(1)) {
                const childNodes = await this.loadNode(currentNode.value)
                currentNode.children = childNodes

                const nextNode = currentNode.children.find(
                    (n: any) => n.value === codeToFind
                )

                if (nextNode && !nextNode.leaf) {
                    nextNode.children = []
                    currentNode = nextNode
                } else {
                    break
                }
            }
            this.componentKey += 1
        }

        private lazyLoadData(node: any, resolve: any) {
            const { value } = node
            this.loadNode(value || "")
                .then((nodes) => resolve(nodes))
                .catch(() => resolve([]))
        }

        private handleChange(value: string[]) {
            this.$emit("change", {
                regionCode: value[value.length - 1] || "",
                regionFullPath: value.join(","),
            })
        }
    }
</script>
