<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="查看简历详情"
        width="1200px"
    >
        <div v-if="!loading">
            <div class="title">个人基本信息</div>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items1"
                class="u-p-x-20"
            >
            </detail-row-col>
            <div class="title">就业信息</div>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items2"
                class="u-p-x-20"
            >
            </detail-row-col>
            <div class="title">培训信息</div>
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items3"
                class="u-p-x-20"
            >
            </detail-row-col>
            <table-container
                v-if="listConfig"
                v-model="listConfig"
                filedWidth="200"
                ref="table"
                :useTab="false"
            >
                <div slot="table" slot-scope="{ data }" class="bg-white">
                    <common-table :data="data" :columns="columns">
                        <div slot="h" class="u-flex u-row-between"></div>
                    </common-table>
                </div>
            </table-container>
        </div>
        <div class="gap" v-else v-loading="true"></div>
        <!-- <div class="u-flex u-row-center">
            <el-button type="primary" plain @click="close">取消</el-button>
            <el-button type="primary" class="u-m-l-30" @click="confirm"
                >确定</el-button
            >
        </div> -->
    </el-dialog>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { getAddress } from "@/utils"
    import { formatTime } from "@/utils/tools"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import { eduListConfig } from "@/views/pages/labour-manage/seeker-info/components/detail"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { seekerInfoPredict } from "./detail"

    @Component({ components: { DetailRowCol, TableContainer, CommonTable } })
    export default class ResumePop extends Mixins(DialogController) {
        @Prop()
        detail!: any

        private row: any = null
        loading = false

        private listConfig: TableConfig | null = null

        private get columns() {
            return this.listConfig?.column || []
        }

        private get labelStyle() {
            return {
                minWidth: "126px",
                textAlign: "right",
                marginRight: "10px",
                color: "#9098A6",
                lineHeight: "20px",
            }
        }

        onOpen() {
            this.init()
        }

        onClosing() {
            this.row = null
        }

        private init() {
            this.loading = true
            console.log(JSON.parse(JSON.stringify(this.detail)))
            pageLoading(() => {
                return sdk.core
                    .model("user_profile_basic")
                    .detail(
                        this.detail.profile_access_key,
                        // "second_version_detail"
                        "company_detail"
                    )
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, seekerInfoPredict)
                        this.listConfig = eduListConfig(
                            this.row.id_card_openid,
                            false
                        )
                        this.loading = false
                    })
            })
        }

        private get items1() {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "姓名",
                    value: this.row.name,
                },
                {
                    label: "身份证",
                    value: this.row.id_card_hide,
                },
                {
                    label: "性别",
                    value: this.row.sex_label,
                },
                {
                    label: "联系电话",
                    vNode: renDesensitizationView(h, {
                        value: this.row.mobile,
                    }),
                },
                {
                    label: "民族",
                    value: this.row.nation,
                },
                {
                    label: "注册状态",
                    vNode: [
                        h("span", this.row.uniplat_uid_calc ? "已注册" : "未注册"),
                    ],
                },
                {
                    label: "户口性质",
                    value: this.row.reg_residence_property_label,
                },
                {
                    label: "政治面貌",
                    value: this.row.political_outlook_label,
                },
                {
                    label: "户籍地址",
                    value: getAddress(this.row, [
                        "household_province",
                        "household_city",
                        "household_area",
                        "household_countryside",
                        "household_village",
                    ]),
                },
                {
                    label: "常住地址",
                    value: getAddress(this.row, [
                        "permanent_province",
                        "permanent_city",
                        "permanent_area",
                        "permanent_countryside",
                        "permanent_village",
                    ]),
                },
                {
                    label: "文化程度",
                    value: this.row.education_label,
                },
                {
                    label: "所学专业",
                    value: this.row.study_speciality,
                },
                {
                    label: "毕业时间",
                    value: formatTime.day(this.row.graduate_date),
                },
                {
                    label: "毕业院校",
                    value: this.row.graduate_school,
                },
            ].map((e) => {
                return { ...e, span: 8 }
            })
        }

        private get items2() {
            if (!this.row) return []
            const h = this.$createElement
            const row = this.row
            const arr1 =
                row.employment_status_label === "已就业创业"
                    ? [
                          {
                              label: "就业方式",
                              value: row.employment_type_label || "",
                          },
                          { label: "就业地点", value: row.work_city_label || "" },
                          { label: "就业单位", value: row.company_name || "" },
                          {
                              label: "从事行业",
                              value: row.job_industry_label || "",
                          },
                          {
                              label: "就业工种",
                              value: row.job_type_work_label || "",
                          },
                          { label: "月薪收入", value: row.salary || "" },
                      ]
                    : ([] as any)
            const arr2 =
                row.is_jobwilling_label === "是"
                    ? [
                          {
                              label: "择业地域意愿",
                              vNode: getAddress(row, [
                                  "jobwilling_province",
                                  "jobwilling_city",
                                  "jobwilling_region",
                              ]),
                          },
                          {
                              label: "月薪要求",
                              value: row.jobwilling_salary || "",
                          },
                          {
                              label: "就业意向行业",
                              value: row.job_willing_industry_label || "",
                          },
                          {
                              label: "就业意向工种",
                              value: row.job_willing_type_work_label || "",
                              span: 16,
                          },
                      ]
                    : ([] as any)

            const arr3 =
                row.is_start_job_label === "是"
                    ? [
                          {
                              label: "创业意向行业",
                              value: row.start_job_industry_label || "",
                          },
                          {
                              label: "创业意向工种",
                              value: row.start_job_type_work_label || "",
                          },
                      ]
                    : []
            return [
                {
                    label: "就业状态",
                    value: row.employment_status_label || "",
                    span: 24,
                },
                ...arr1,
                {
                    label: "是否有就业意愿",
                    value: row.is_jobwilling_label || "",
                },
                ...arr2,
                // {
                //     label: "是否有创业意愿",
                //     value: row.is_start_job_label || "",
                // },
                ...arr3,
                // {
                //     label: "查无此人原因",
                //     value: row.undefined_person_remark_label || "",
                // },
                {
                    label: "简历附件",
                    vNode: h("div", {
                        class: row.resume_label ? "primary" : "",
                        domProps: {
                            innerHTML: row.resume_label || "--",
                        },
                    }),
                },
            ].map((i) => {
                return { ...i, span: i.span || 8 }
            })
        }

        private get items3() {
            if (!this.row) return []
            const row = this.row
            return [
                {
                    label: "是否接受过职业培训",
                    value: this.row.is_train_job_label,
                },
                {
                    label: "参加培训时间",
                    value:
                        row.training_start_time_label +
                            `${row.training_end_time_label ? "至" : ""}` +
                            row.training_end_time_label || "--",
                },
                {
                    label: "参加培训专业",
                    value: this.row.train_speciality_label,
                },
                {
                    label: "技能特长情况",
                    value: this.row.speciality_detail_label,
                },
                {
                    label: "技能等级",
                    value: this.row.level_label,
                },
                // {
                //     label: "是否有培训意愿",
                //     value: this.row.is_training_willingess_label,
                // },
                // {
                //     label: "愿意参加的培训工种",
                //     value: this.row.training_willingness_work_type_label,
                // },
                // {
                //     label: "是否享受灵活就业社保补贴",
                //     value: this.row.flexible_employment_label,
                // },
                // {
                //     label: "享受灵活就业社保补贴期限",
                //     value: this.row.flexible_employment_month,
                // },
            ].map((i) => {
                return { ...i, span: 8 }
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .title {
        color: #4e5054 !important;
        font-size: 18px !important;
        line-height: 23px !important;
        background: transparent !important;
        min-height: 25px !important;
        font-weight: bold;
    }
    .gap {
        height: 400px;
    }
    ::v-deep .detail-row {
        padding: 15px 0;
        .item {
            line-height: 20px;
        }
    }
</style>
