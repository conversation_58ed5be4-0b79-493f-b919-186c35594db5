import { config, EnvProject } from "@/config"
import axios from "axios"

class Upgrade {
    private down = false
    public info = ""

    public setUp(force = false) {
        if (!config.isPro || process.env.VUE_APP_DISABLE_UPGRADE) {
            return Promise.resolve()
        }
        if (!force && this.down) {
            return Promise.resolve()
        }
        return axios
            .get(`https://qqxb-cms-api.hrs100.com/configuration/json?type=4`)
            .then((res) => {
                this.down = true
                const info = res.data
                if (
                    [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                        config.envProject
                    )
                ) {
                    this.info = info.serverstatus_jz
                } else if (config.envProject === EnvProject.孝感项目) {
                    this.info = info.serverstatus
                } else if (config.envProject === EnvProject.潜江项目) {
                    this.info = info.serverstatus_qj
                } else if (config.envProject === EnvProject.武汉数采项目) {
                    this.info = info.serverstatus_whsc
                } else if (config.envProject === EnvProject.十堰项目) {
                    this.info = info.serverstatus_shiyan
                }
                if (this.info) {
                    return Promise.reject()
                } else {
                    return Promise.resolve()
                }
            })
    }
}

export const upgrade = new Upgrade()
