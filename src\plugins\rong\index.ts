// 导入 IMLib 5.X
import * as Rong<PERSON>Lib from "@rongcloud/imlib-next"

// 导入 RTCLib、CallLib
import {
    installer as rtcInstaller,
    RCRTCClient,
    RCTrack,
    RCFrameRate,
    RCResolution,
} from "@rongcloud/plugin-rtc"

import {
    installer as callInstaller,
    RCCallClient,
    RCCallSession,
    RCCallErrorCode,
    ISessionListener,
    IEndSummary,
    ISenderInfo,
    IMuteUser,
    IInvitedUsers,
    RCCallLanguage,
    RCCallEndReason,
    RCCallMediaType,
    IOfflineRecord,
    RCCallSessionState,
    IRCCallParams,
} from "@rongcloud/plugin-call"
import { sdk } from "@/service"
RongIMLib.init({
    appkey: "y745wfm8ydlnv",
})
let rtcClient: RCRTCClient
let caller: RCCallClient
let video: HTMLVideoElement

const error = {
    [RCCallEndReason.CANCEL]: "己方取消已发出的通话请求",
    [RCCallEndReason.REJECT]: "己方拒绝收到的通话请求",
    [RCCallEndReason.HANGUP]: "己方挂断",
    [RCCallEndReason.BUSY_LINE]: "己方忙碌",
    [RCCallEndReason.NO_RESPONSE]: "己方未接听",
    [RCCallEndReason.ENGINE_UNSUPPORTED]: "己方不支持当前音视频引擎",
    [RCCallEndReason.NETWORK_ERROR]: "己方网络错误",
    [RCCallEndReason.GET_MEDIA_RESOURCES_ERROR]:
        "己方摄像头资源获取失败，可能是权限原因",
    [RCCallEndReason.PUBLISH_ERROR]: "己方资源发布失败",
    [RCCallEndReason.SUBSCRIBE_ERROR]: "己方订阅资源失败",
    [RCCallEndReason.REMOTE_CANCEL]: "对方取消发出的通话请求",
    [RCCallEndReason.REMOTE_REJECT]: "对方拒绝收到的通话请求",
    [RCCallEndReason.REMOTE_HANGUP]: "通话过程中对方挂断",
    [RCCallEndReason.REMOTE_BUSY_LINE]: "对方忙碌",
    [RCCallEndReason.REMOTE_NO_RESPONSE]: "对方未接听",
    [RCCallEndReason.REMOTE_ENGINE_UNSUPPORTED]: "对方引擎不支持",
    [RCCallEndReason.REMOTE_NETWORK_ERROR]: "对方网络错误",
    [RCCallEndReason.REMOTE_GET_MEDIA_RESOURCE_ERROR]:
        "对方摄像头资源获取失败，可能是权限原因",
    [RCCallEndReason.REMOTE_PUBLISH_ERROR]: "远端资源发布失败",
    [RCCallEndReason.REMOTE_SUBSCRIBE_ERROR]: "远端订阅资源失败",
    [RCCallEndReason.OTHER_CLIENT_JOINED_CALL]: "己方其他端已加入新通话",
    [RCCallEndReason.OTHER_CLIENT_IN_CALL]: "己方其他端已在通话中",
    [RCCallEndReason.KICKED_BY_SERVER]: "己方被禁止通话",
    [RCCallEndReason.ACCEPT_SYSTEM_CALL]: "己方接听系统通话（移动端特有）",
    [RCCallEndReason.REMOTE_OTHER_CLIENT_JOINED_CALL]: "远端其他端已加入新通话",
    [RCCallEndReason.REMOTE_OTHER_CLIENT_IN_CALL]: "远端其他端已在通话中",
    [RCCallEndReason.REMOTE_KICKED_BY_SERVER]: "远端被禁止通话",
    [RCCallEndReason.REMOTE_ACCEPT_SYSTEM_CALL]:
        "远端接听系统通话（移动端特有）",
    [RCCallEndReason.ACCEPT_BY_OTHER_CLIENT]: "其他端接听",
    [RCCallEndReason.HANGUP_BY_OTHER_CLIENT]: "其他端挂断",
    [RCCallEndReason.ADDED_TO_BLACKLIST]: "己方被对方加入黑名单",
    [RCCallEndReason.SERVICE_NOT_OPENED]: "音视频服务未开通",
}

export function getErrorMsg(reason: RCCallEndReason) {
    return error[reason] || "未知错误"
}

function initRtcClient() {
    // IM 客户端初始化（IMLib 5.X）
    // RTC 客户端初始化
    // RTCLib 全局变量定义为 RCRTC，使用 CDN 文件方式集成时，示例如下：
    // const rtcClient = RongIMLib.installPlugin(RCRTC.installer, { /* 配置项 */ })
    rtcClient = RongIMLib.installPlugin(rtcInstaller, {
        /* 配置项 */
    })!
}

function initCaller() {
    // CallLib 客户端初始化
    // CallLib 全局变量定义为 RCCall，使用 CDN 文件集成时，示例如下：
    // const caller = RongIMLib.installPlugin(RCCall.installer)
    caller = RongIMLib.installPlugin(callInstaller, {
        // rtcClient 实例 （必填）
        rtcClient,
        /**
         * 被动收到邀请 （收到一个远端发起的新会话）, 会产生一个新的 session 对象 （必填）
         */
        onSession(session: RCCallSession) {
            /**
             * **收到新的 session 后需要立即注册事件监听**
             */
            session.registerSessionListener({
                /**
                 * 当远端用户已开始响铃，表示对方已收到呼叫请求
                 * @param sender 已响铃的用户
                 * @param session 当前的 session 对象
                 */
                onRinging(_sender: ISenderInfo, _session: RCCallSession) {},
                /**
                 * 当远端用户同意接听
                 * @param sender 远端用户
                 * @param session 当前的 session 对象
                 */
                onAccept: function (
                    sender: ISenderInfo,
                    session: RCCallSession
                ): void {
                    throw new Error("Function not implemented.")
                },
                /**
                 * 当有远端用户挂断
                 * @param sender 远端用户
                 * @param reason 挂断的原因
                 * @param session 当前的 session 对象
                 */
                onHungup: function (
                    sender: ISenderInfo,
                    reason: RCCallEndReason,
                    session: RCCallSession
                ): void {
                    throw new Error("Function not implemented.")
                },
                onMemberModify: function (
                    sender: ISenderInfo,
                    invitedUsers: ISenderInfo[],
                    session: RCCallSession
                ): void {
                    throw new Error("Function not implemented.")
                },
                onMediaModify: function (
                    sender: ISenderInfo,
                    mediaType: RCCallMediaType,
                    session: RCCallSession
                ): void {
                    throw new Error("Function not implemented.")
                },
                /**
                 * 本端资源或远端资源已获取
                 * @param track 本端资源或远端资源, track 不可设置成 Vue 组件的响应式数据
                 * @param session 当前的 session 对象
                 */
                onTrackReady: function (
                    track: RCTrack,
                    session?: RCCallSession
                ): void {
                    throw new Error("Function not implemented.")
                },
                onAudioMuteChange: function (
                    muteUser: IMuteUser,
                    session: RCCallSession
                ): void {
                    throw new Error("Function not implemented.")
                },
                onVideoMuteChange: function (
                    muteUser: IMuteUser,
                    session: RCCallSession
                ): void {
                    throw new Error("Function not implemented.")
                },
            })
        },

        /**
         *  以下三条只要满足一条，就会触发onSessionClose
         *  1、本端用户自己主动挂断
         *  2、服务端把本端用户踢出 RTC 房间
         *  3、房间里小于2个人
         *
         *  @param {RCCallSession} session 被结束的 session 对象
         *  @param summaryInfo 结束一个 session 的后汇总信息
         */
        onSessionClose(session: RCCallSession, summaryInfo?: IEndSummary) {},

        /**
         * 接收 IM 离线期间收到的呼叫记录（按需监听）
         */
        onOfflineRecord(record: IOfflineRecord) {},
    })!
}
let isConnect = false
function conenct() {
    // 与 IM 服务建立连接（IMLib 5.X）
    return sdk.core
        .domainService("xg_project", "authApi", "testRongYunToken")
        .post<string>()
        .then((r) => {
            console.log(r)
            return RongIMLib.connect(r).then((res) => {
                if (res.code === 0) {
                    isConnect = true
                    console.log("链接成功, 链接用户 id 为: ", res)
                } else {
                    console.warn("链接失败, code:", res.code)
                }
            })
        })
}

export async function makeCall(
    userId: string | number,
    dom: HTMLVideoElement,
    listener?: Partial<ISessionListener>
) {
    const targetId = userId.toString()
    console.log("呼叫对象", targetId)
    /**
     * 发起单人通话，如果成功后会产生一个新的session
     * @param targetId 被呼叫一方的用户 id   必填
     * @param mediaType 1->音频呼叫 or 2->音视频呼叫  必填
     * @param listener session对象上注册的事件 （必填）
     * @param constraints 获取音频或音视频资源时的参数 可选
     * @param params.channelId 组织 Id 可选
     */
    const { code, session } = await caller.call({
        targetId,
        mediaType: 2,
        listener: {
            /**
             * 当远端用户已开始响铃，表示对方已收到呼叫请求 （必填）
             * @param sender 已响铃的用户
             * @param session 当前的 session 对象
             */
            onRinging(sender: ISenderInfo, session: RCCallSession) {
                console.log("onRinging", sender, session)
                const { userId } = sender
            },

            /**
             * 当远端用户同意接听 （必填）
             * @param sender 远端用户
             * @param session 当前的 session 对象
             */
            onAccept(sender: ISenderInfo, session: RCCallSession) {
                console.log("onAccept", sender, session)
                const { userId } = sender
            },

            /**
             * 当有远端用户挂断 （必填）
             * @param sender 远端用户
             * @param reason 挂断的原因
             * @param session 当前的 session 对象
             */
            onHungup(
                sender: ISenderInfo,
                reason: RCCallEndReason,
                session: RCCallSession
            ) {
                console.log("onAccept", sender, session)
                const { userId } = sender
            },

            /**
             * 本端资源或远端资源已获取 （必填）
             * @param track 本端资源或远端资源, track 不可设置成 Vue 组件的响应式数据
             * @param session 当前的 session 对象
             */
            onTrackReady(track: RCTrack, session?: RCCallSession) {
                // track.isLocalTrack() 是否为本地资源
                // track.isAudioTrack() 是否为音频
                // track.isVideoTrack() 是否为视频
                // track.getUserId()    产生该 track 的用户id
                // 播放音频。如果为远端音频，建议直接播放。如为本端音频，建议不播放，以减少回音。
            },
            onMemberModify: function (
                sender: ISenderInfo,
                invitedUsers: ISenderInfo[],
                session: RCCallSession
            ): void {
                throw new Error("Function not implemented.")
            },
            onMediaModify: function (
                sender: ISenderInfo,
                mediaType: RCCallMediaType,
                session: RCCallSession
            ): void {
                throw new Error("Function not implemented.")
            },
            onAudioMuteChange: function (
                muteUser: IMuteUser,
                session: RCCallSession
            ): void {
                throw new Error("Function not implemented.")
            },
            onVideoMuteChange: function (
                muteUser: IMuteUser,
                session: RCCallSession
            ): void {
                throw new Error("Function not implemented.")
            },
            ...listener,
        },
    })

    if (code === RCCallErrorCode.SUCCESS) {
        return session
    }
    return Promise.reject(new Error("makeCall error"))
}

export async function useRong() {
    if (!rtcClient) {
        initRtcClient()
    }
    if (!caller) {
        initCaller()
    }
    if (!isConnect) {
        await conenct()
    }

    return {
        makeCall,
    }
}
