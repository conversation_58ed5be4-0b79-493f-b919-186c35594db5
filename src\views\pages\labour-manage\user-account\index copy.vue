<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :showExpand="true"
            :defaultFilterIsExpand="false"
        >
            <div
                slot="title"
                v-if="!hideHeader"
                class="d-flex-item-center bold"
            >
                注册居民管理

                <div class="u-flex-1" />
                <el-button
                    v-role="'model.user_account.action.recommend_profile'"
                    type="primary"
                    @click="batchRecommend"
                    plain
                    v-if="!isYD"
                >
                    批量人才推荐
                </el-button>
            </div>
            <div slot="table" slot-scope="{ data }" class="bg-white">
                <div v-if="!hideHeader" class="u-flex u-row-right u-m-b-15">
                    <el-button
                        type="primary"
                        plain
                        size="mini"
                        @click="batch"
                        v-role="['model.user_account.action.set_tags']"
                    >
                        批量设置标签
                    </el-button>
                    <el-button
                        type="primary"
                        plain
                        size="mini"
                        @click="exportExcel"
                        :loading="exportLoading"
                    >
                        导出
                    </el-button>
                </div>
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="info" slot-scope="scope">
                        <div v-if="!scope.row.is_verified" class="color-8">
                            — 未实名 —
                        </div>
                        <div class="info" v-else>
                            <div class="u-flex u-row-between">
                                <div class="u-flex">
                                    <span
                                        :class="{
                                            'u-m-r-10': scope.row.real_name,
                                        }"
                                    >
                                        {{ scope.row.real_name }}
                                    </span>
                                    <span
                                        :class="{
                                            'u-m-r-10': scope.row.age,
                                        }"
                                    >
                                        {{ scope.row.age }}
                                    </span>
                                    <span
                                        :class="{
                                            'u-m-r-10': scope.row.sex_label,
                                        }"
                                    >
                                        {{ scope.row.sex_label }}
                                    </span>
                                </div>
                                <div
                                    class="info-right"
                                    :class="[
                                        scope.row.employment_status_label
                                            ? 'emp emp-' +
                                              scope.row.employment_status
                                            : 'color-9',
                                    ]"
                                >
                                    {{
                                        scope.row.employment_status_label || "-"
                                    }}
                                </div>
                            </div>
                            <div class="u-flex u-row-between">
                                <div class="u-flex">
                                    {{ scope.row.id_card_hide }}
                                </div>
                                <div class="info-right">
                                    <div
                                        class="primary pointer"
                                        v-if="
                                            scope.row.profile_access_key ||
                                            scope.row.profile_id
                                        "
                                        @click="
                                            toProfile(
                                                scope.row.profile_access_key ||
                                                    scope.row.profile_id
                                            )
                                        "
                                    >
                                        查看档案
                                    </div>
                                    <div v-else class="color-9">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <set-tag-dialog
            v-model="showSetTag"
            :ids="checkEdIds"
            @refresh="refreshList"
            model="user_account"
            action="batch_set_labels"
        ></set-tag-dialog>
        <batch-recommend
            v-model="showBatchRecommend"
            :selected="selected"
            @refresh="refreshList"
        />
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { Component, Prop } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "."
    import { updateTagItem } from "../../single-page/components/tags-view"
    import BatchRecommend from "./components/batch-recommend.vue"
    import SetTagDialog from "../seeker-info/components/set-tag.vue"
    import { config, EnvProject } from "@/config"

    @Component({
        name: routesMap.labourManage.userAccount,
        components: { TableContainer, CommonTable, BatchRecommend, SetTagDialog },
    })
    export default class UserAccount extends BaseTableController<{ id: number }> {
        @Prop({ default: false })
        private hideHeader!: boolean

        @Prop({ default: false })
        private is_verified!: boolean

        private isYD = config.envProject === EnvProject.宜都项目

        tableConfig: TableConfig | null = null
        private readonly columns: TableColumn[] = columns
        private checkEdIds: Array<number | string> = []
        private showSetTag = false

        public selected: {
            id: string
            v: number
            profile_id: string
        }[] = []

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.rows.map((e) => e.uniplat_uid)
            this.selected = d.rows.map((item) => {
                return {
                    profile_id: item.profile_id,
                    id: item.uniplat_uid,
                    v: item.v,
                }
            })
        }

        private batch() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量设置标签的居民！")
            }
            this.showSetTag = true
        }

        private showBatchRecommend = false

        private batchRecommend() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量人才推荐的居民信息！")
            }
            this.showBatchRecommend = true
        }

        mounted() {
            this.tableConfig = tableConfig(this.is_verified)
            updateTagItem({
                name: routesMap.labourManage.userAccount,
                breadcrumb: [
                    {
                        label: "注册居民管理",
                        to: routesMap.labourManage.userAccount,
                    },
                ],
            })
        }

        private toDetail(row: Row) {
            console.log("row", JSON.parse(JSON.stringify(row)))
            this.$router.push({
                name: routesMap.labourManage.userAccountDetail,
                query: {
                    id: row._access_key || row.uniplat_uid + "",
                    from: routesMap.labourManage.userAccount,
                },
            })
        }

        private toProfile(id: string) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: { id, from: routesMap.labourManage.userAccount },
            })
        }

        private exportExcel() {
            this.exportExcelUniplatV2({ template_name: "注册用户导出" })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .info {
        width: 200px;
        .info-right {
            width: 60px;
            text-align: left;
            &.emp {
                color: #3bbc6b;
                &.emp-2 {
                    color: #ff8b16;
                }
            }
        }
    }
</style>
