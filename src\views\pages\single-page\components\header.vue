<template>
    <div class="header">
        <div class="d-flex align-items-center left" @click="toHome">
            <img
                class="logo"
                :style="projectConfig.headerLogoStyle"
                :src="projectConfig.headerLogo"
                alt=""
            />
            <div class="title">
                {{ headerTitle || appTitle }}
            </div>
            <img
                class="u-m-l-10"
                v-if="isOperate"
                src="/img/icon/home.svg"
                alt=""
                srcset=""
            />
            <div class="u-m-l-10 u-font-16">
                {{ operateDepartmentName }}
            </div>
            <org-select v-if="isOperate"></org-select>
        </div>
        <div v-if="needUp">
            检测到系统更新，
            <el-button size="mini" type="warning" @click="reload">
                点击刷新
            </el-button>
        </div>
        <div class="bg-white color-3 u-p-10" v-if="isTest">
            权限控制
            <span class="u-font-12">(仅测试线)</span>
            ：
            <el-switch
                active-text="开启"
                inactive-text="关闭"
                v-model="isEnableRole"
                @change="changeRole"
            ></el-switch>
        </div>
        <div class="u-flex">
            <div
                class="d-flex align-items-center cursor-pointer u-m-r-40"
                @click="goPlatform"
                v-if="showHrEntrance"
            >
                <img src="./img/header.png" class="header-icon" alt="" />
                跳转至企业平台
            </div>
            <div
                class="d-flex align-items-center cursor-pointer u-m-r-40"
                @click="goHr"
                v-if="showOrgEntrance"
            >
                <img src="./img/header.png" class="header-icon" alt="" />
                跳转至服务机构平台
            </div>
            <div
                class="d-flex align-items-center u-m-r-20"
                @click="agentViewClick"
                v-if="multiAgent"
            >
                <!-- <agent-view @change="change"></agent-view> -->
                <div class="cursor-pointer">
                    {{ currentAgentLabel || "" }}
                </div>
                <span
                    class="cursor-pointer el-icon-arrow-down u-m-l-5"
                    v-if="getList.length > 1"
                ></span>
            </div>
            <select-company
                v-if="!isGrid"
                v-model="showSelectCompanyDialog"
                @change="change"
            ></select-company>
            <select-company
                v-else
                v-model="showSelectCompanyDialog"
                @change="change"
                title="所属的组织机构切换"
            ></select-company>
            <el-badge
                v-if="showMessageCount"
                :value="messageCount"
                :hidden="!messageCount"
                class="u-m-r-20"
            >
                <img
                    @click="fetchingNews(true)"
                    class="message"
                    src="/img/company/message.svg"
                    alt=""
                />
            </el-badge>

            <div class="d-flex">
                <el-dropdown
                    v-if="userInfo"
                    class="user"
                    @command="userCommand"
                >
                    <div class="d-flex align-items-center">
                        <el-avatar
                            :size="32"
                            :src="userInfo.avatar_url"
                        ></el-avatar>
                        <div class="user-name">{{ name }}</div>
                    </div>

                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="editPassword">
                            修改密码
                        </el-dropdown-item>
                        <el-dropdown-item command="forgetPassword">
                            忘记密码
                        </el-dropdown-item>
                        <el-dropdown-item command="logout">
                            退出登录
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <!-- IM -->
        <!-- <im-detail /> -->
        <forget-password-pop
            v-if="userInfo"
            :mobile="userInfo.mobile || ''"
            v-model="showForget"
        ></forget-password-pop>
        <edit-password v-model="showEdit"></edit-password>
    </div>
</template>

<script lang="ts">
    import { config, EnvApplication, EnvProject } from "@/config"
    import { PassportTokenController } from "@/core-ui/service/passport/token"
    import { UserInfo, userInfoService } from "@/core-ui/service/passport/user-info"
    import { WebUpdater } from "@/core-ui/service/web-updater"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { AgentInfo, multiAgent, userService } from "@/service/service-user"
    import { Component, Vue } from "vue-property-decorator"
    import AgentView from "./agent-view.vue"
    import EditPassword from "./password/edit-password.vue"
    import ForgetPasswordPop from "./password/forget-password-pop.vue"
    import SelectCompany from "./select-compony.vue"
    import OrgSelect from "./org-select.vue"
    import { PositionRecommendType, sysConfigService } from "@/service/sys-config"

    @Component({
        components: {
            EditPassword,
            AgentView,
            SelectCompany,
            ForgetPasswordPop,
            OrgSelect,
        },
    })
    export default class Header extends Vue {
        private userInfo: UserInfo | null = null
        private showEdit = false
        private showForget = false
        private name = ""
        private needUp = false
        private projectConfig = config.projectConfig
        private headerTitle = config.headerTitle
        private appTitle = config.appTitle
        private multiAgent = multiAgent
        private showOrgEntrance = false
        private showHrEntrance = false
        private agentInfo: null | AgentInfo = null
        private getList: AgentInfo[] = []
        private h_id = ""
        private operateDepartmentName = ""
        private showSelectCompanyDialog = false

        isHr = config.isHr
        isOperate = config.isOperate
        isGrid = config.isGrid
        private get currentAgentLabel() {
            return this.agentInfo?.label
        }

        private isTest = process.env.VUE_APP_ENV === "test"
        isEnableRole = !window.localStorage.getItem("isDisableRole")
        changeRole() {
            if (this.isEnableRole) {
                window.localStorage.removeItem("isDisableRole")
            } else {
                window.localStorage.setItem("isDisableRole", "1")
            }
            window.location.reload()
        }

        mounted() {
            this.getPersonEntrance()
            this.getOrgEntrance()
            userInfoService.get().then((r) => {
                this.userInfo = r
                if (!this.userInfo.avatar_url) {
                    this.userInfo.avatar_url = "/img/icon/avatar.png"
                }
                this.name = r.display_name
            })
            if (config.isBuild) {
                const up = new WebUpdater({
                    baseUrl: process.env.BASE_URL,
                    timer: 1e3 * 60 * 3,
                })
                up.on("update", () => {
                    this.needUp = true
                })
            }
            if (config.isOperate) {
                this.operateDepartmentName = userService.getCurAgent()?.label || ""
                const region_name = userService.getCurAgent()?.data.region_name
                if (region_name) {
                    this.operateDepartmentName += `（${region_name}）`
                }
            }
            if (
                config.envProject === EnvProject.孝感项目 &&
                config.envApplication === EnvApplication.企业端
            ) {
                this.showMessageCount = true
                this.fetchingNews()
                setTimeout(() => {
                    this.fetchingNews()
                }, 1e3 * 60 * 3)
                this.$root.$on("refreshHeaderNews", () => {
                    this.fetchingNews()
                })
            }
        }

        change() {
            this.showOrgEntrance = false
            this.getPersonEntrance()
            this.getOrgEntrance()
        }

        private getPersonEntrance() {
            if (config.isOperate) return
            userService.setup().then(async (r) => {
                this.agentInfo = r
                this.getList = userService.getList
                if (!r?.key) return
                if (userService.agentRow) {
                    this.h_id = userService.agentRow.human_agent_id
                    if (userService.agentRow.is_human_agent_manager) {
                        this.showOrgEntrance = await this.getPersonSourceStatus()
                    }
                }
            })
        }

        private async getPersonSourceStatus() {
            const positionRecommendMapping = await sysConfigService.data
                .position_recommend_type_mapping

            return !!positionRecommendMapping.find(
                (i) => i.key === PositionRecommendType.人力资源机构撮合服务
            )
        }

        private getOrgEntrance() {
            if (!config.isHr) return
            userService.setup().then((r) => {
                this.agentInfo = r
                if (!r?.key) return
                return sdk.core
                    .model("xg_human_agent")
                    .detail(r?.key || 0, "company_detail")
                    .queryKeyCustom()
                    .then((r) => {
                        const row = sdk.buildRow<{
                            agent_id: string
                            is_agent_manager: boolean
                        }>(r.row, {
                            agent_id: "xg_agent#id",
                            is_agent_manager: "",
                        })
                        this.h_id = row.agent_id
                        this.showHrEntrance = row.is_agent_manager
                    })
            })
        }

        reload() {
            window.location.reload()
        }

        private userCommand(command: "forgetPassword" | "logout" | "editPassword") {
            if (command === "forgetPassword") {
                this.showForget = true
            }
            if (command === "editPassword") {
                this.showEdit = true
            }
            if (command === "logout") {
                userService.logout()
            }
        }

        private toHome() {
            this.$router.push({ name: routesMap.home.root })
        }

        private goPlatform() {
            this.toPage(process.env.VUE_APP_ENTERPRISE_URL)
        }

        private goHr() {
            this.toPage(process.env.VUE_APP_HR_URL)
        }

        private toPage(v: string) {
            window.open(
                `${v}/login?token=${PassportTokenController.hasToken()}&scene_key=${
                    this.h_id
                }`
            )
        }

        private agentViewClick() {
            userService.getAgentList().then((r1) => {
                if (r1.length > 1) {
                    this.showSelectCompanyDialog = true
                }
            })
        }

        messageCount = 0
        showMessageCount = false
        fetchingNews(to?: boolean) {
            if (to) {
                this.$router.push({
                    name: routesMap.home.news,
                })
            }
            return sdk.core
                .model("agent_msg")
                .list("for_agent")
                .addPrefilter({
                    status: 0,
                })
                .query({
                    item_size: 0,
                    pageIndex: 1,
                })
                .then((r) => {
                    this.messageCount = r.pageData.record_count
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .header {
        width: 100%;
        height: @banner-height;
        background: var(--primary);
        box-shadow: 0px 2px 16px 0px rgba(0, 0, 0, 0.1);
        position: relative;
        padding: 0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 1;
        color: #fff;
        .left {
            cursor: pointer;
        }
        .logo {
            margin-right: 10px;
        }

        .title {
            font-size: 20px;
            font-weight: 500;
            .icon {
                margin-left: 10px;
            }
            ::v-deep .el-button {
                &.el-button--default {
                    min-width: auto;
                }
                &.el-button--mini {
                    padding: 7px;
                }
            }
        }
        .message {
            width: 25px;
            height: 25px;
            cursor: pointer;
        }
        .user {
            z-index: 4;
            font-size: 14px;
            .customer-icon {
                width: 22px;
                height: 22px;
            }
            .user-name {
                color: #fff;
                cursor: pointer;
                font-size: 14px;
                margin-left: 10px;
                max-width: 100px;
            }
            + .user {
                margin-left: 40px;
            }
        }

        .header-icon {
            width: 14px;
            height: 14px;
            margin-right: 10px;
        }

        .cursor-pointer {
            cursor: pointer;
        }
    }
</style>
