<template>
    <table-container
        v-if="tableConfig"
        showTableFilter
        filedWidth="200"
        ref="table"
        v-model="tableConfig"
        class="container"
        :useTab="true"
    >
        <div slot="table" slot-scope="{ data }" class="bg-white">
            <common-table :data="data" :columns="columns"> </common-table>
        </div>
    </table-container>
</template>

<script lang='ts'>
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { sdk } from "@/service"
    import { routesMap } from "@/router/direction"

    const enum Status {
        待提交 = -1,
        已提交审核 = 0,
        通过 = 1,
        不通过 = 2,
    }

    interface Row {
        create_time: string
        name: number
        mobile: string
        action_name: string
        invoker_id: number
        invoker_action_name: number
        id: number
        v: number
    }

    const tableConfig = (obj_id: string): TableConfig => ({
        model: sdk.core.model("work_flow_log@xg_project").list("detail_log"),
        preFilter: { model_name: "server_stage", obj_id },
        defaultPageSize: 10,
        predict: {
            create_time: "label",
            name: "system_user_references#info#name",
            mobile: "system_user_references#mobile",
            action_name: "",
            invoker_id: "",
            invoker_action_name: "",
        },
        oneTabFilter: true,
    })

    @Component({ components: { TableContainer, CommonTable } })
    export default class OperateList extends BaseTableController<Row> {
        @Prop()
        id!: string

        private tableConfig: TableConfig | null = null
        private columns: TableColumn[] = [
            {
                label: "用户姓名",
                prop: "name",
                showOverflowTip: true,
            },
            {
                label: "手机号",
                prop: "mobile",
                showOverflowTip: true,
            },
            {
                label: "动作",
                prop: "action_name",
                showOverflowTip: true,
            },
            {
                label: "操作时间",
                prop: "create_time_label",
                showOverflowTip: true,
            },
        ]

        mounted() {
            this.tableConfig = tableConfig(this.id)
        }

        private toPersonDetail(key: string) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: key,
                    from: this.$route.name,
                },
            })
        }

        private toAgentDetail(key: string) {
            this.$router.push({
                name: routesMap.employmentManage.companyManageDetail,
                query: { id: key, from: this.$route.name },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .list-view {
        background: #fff;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
        .table {
            padding: 0;
        }
    }
</style>
