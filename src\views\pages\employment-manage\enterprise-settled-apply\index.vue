<template>
    <div class="check-list-container">
        <table-container
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            class="container"
        >
            <div slot="title" class="d-flex-item-center bold">
                企业入驻申请管理
            </div>

            <div slot="header-right">
                <el-button type="primary" plain @click="exportToExcel">
                    导出
                </el-button>
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table
                    :data="data"
                    :tableConfig="tableConfig"
                    :columns="columns"
                >
                    <div slot="legal_person" slot-scope="scope">
                        <div class="text-center">
                            {{ scope.row.legal_person_hide }}
                        </div>
                        <div class="text-center">
                            {{ scope.row.legal_card_open_id_hide }}
                        </div>
                    </div>
                    <div slot="province_code" slot-scope="scope">
                        <div class="text-center">
                            {{ scope.row.region_name1
                            }}{{ scope.row.region_name2
                            }}{{ scope.row.region_name3 }}
                        </div>
                    </div>
                    <div slot="contact_person" slot-scope="scope">
                        <div class="text-center">
                            {{ scope.row.contact_person }}
                        </div>
                        <div class="text-center">
                            {{ scope.row.contact_mobile_hide }}
                        </div>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button
                            type="text"
                            @click="openAuditPop(scope.row)"
                            v-if="scope.row.auth_status === 0"
                        >
                            审核
                        </el-button>
                        <el-button
                            type="text"
                            @click="openDetailPop(scope.row)"
                            v-else
                        >
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <AuditPop
            v-model="displayAuditPop"
            :rowId="curId"
            @refresh="refreshList"
        ></AuditPop>
        <DetailPop
            v-model="displayDetailPop"
            :rowId="curId"
            @refresh="refreshList"
        ></DetailPop>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn as TableColumnImpl } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { Component } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "."
    import AuditPop from "./components/audit-pop.vue"
    import DetailPop from "./components/detail-pop.vue"

    @Component({
        name: routesMap.employmentManage.enterpriseSettledApply,
        components: { TableContainer, CommonTable, AuditPop, DetailPop },
    })
    export default class PolicyBasis extends BaseTableController<Row> {
        private tableConfig = tableConfig()
        private readonly columns: TableColumnImpl[] = columns
        private displayAuditPop = false
        private displayDetailPop = false
        private curId = 0

        private get title() {
            return this.$route.meta?.title || ""
        }

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.employmentManage.enterpriseSettledApply,
        }

        openAuditPop(row: Row) {
            this.curId = row.id
            this.displayAuditPop = true
        }

        openDetailPop(row: Row) {
            this.curId = row.id
            this.displayDetailPop = true
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .check-list-container {
        width: 100%;
    }

    .header {
        padding-top: 15px;
        padding-bottom: 15px;

        .title {
            font-size: 22px;
            color: #000000;
        }
    }

    .text-center {
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
</style>
