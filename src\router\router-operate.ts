import { config, EnvProject } from "@/config"
import layout from "@/views/pages/single-page/index.vue"
import { RouteConfig } from "."
import { routesMap } from "./direction"
import { base } from "./operate/base"
import { baseService } from "./operate/baseService"
import { collectTaskManage } from "./operate/collectTaskManage"
import { dataCheck } from "./operate/dataCheck"
import { dataStorage } from "./operate/dataStorage"
import { employmentTask } from "./operate/employmentTask"
import { enterpriseMange } from "./operate/enterpriseMange"
import { financial } from "./operate/financial"
import { groupService } from "./operate/groupService"
import { job } from "./operate/job"
import { jobStation } from "./operate/jobStation"
import { policy } from "./operate/policy"
import { report } from "./operate/report"
import { train } from "./operate/train"
import { users } from "./operate/users"
import { qjCompanyTaskManage } from "./operate/qjCompanyTaskManage"
import { equipmentManage } from "./operate/equipmentManage"
import { home } from "./operate/home"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const xg_project_operate: RouteConfig[] = [
    ...home,
    // 注册用户管理
    ...users,
    // 劳动力信息库
    ...collectTaskManage,
    // 市场主体信息管理
    ...enterpriseMange,
    // 企业用工信息库 （潜江）
    ...qjCompanyTaskManage,
    // 求职招聘服务提升
    ...job,
    // 重点人群服务跟踪管理
    ...groupService,
    // 政策与资讯
    ...policy,
    // 就业指标管理
    ...employmentTask,
    // 专项工作报表管理
    ...report,
    // 数据核查管理
    ...dataCheck,
    // 零工驿站管理
    ...jobStation,
    // 资金使用计划和内审
    ...financial,
    // 数据仓库及核查工具
    ...dataStorage,
    // 基础数据管理
    ...base,
    // 培训管理
    ...train,
    // 基层服务
    ...baseService,
    // 设备管理
    ...equipmentManage,
]
