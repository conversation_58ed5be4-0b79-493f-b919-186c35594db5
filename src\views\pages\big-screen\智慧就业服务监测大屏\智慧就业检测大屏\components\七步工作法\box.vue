<template>
    <div class="card u-flex u-row-center">
        <div :id="id" class="chart u-flex">
            <div
                class="item"
                v-for="(item, index) in list"
                :key="item.label + index"
            >
                <div class="u-flex u-col-center">
                    <div>
                        <div
                            class="item-box u-flex-col u-col-center u-row-around"
                        >
                            <Item
                                label="服务人次"
                                :value="item.value1"
                                :digitHeight="20"
                                :digitWidth="12"
                                :isRoute="!item.disabledGoRoute"
                                @toRoute="
                                    toRoute(index, '服务人次', item.value1)
                                "
                            ></Item>
                            <Item
                                :label="item.label"
                                :value="item.value2"
                                :digitHeight="16"
                                :digitWidth="10"
                                :isRoute="!item.disabledGoRoute2"
                                v-if="item.label"
                                class="small-item"
                                @toRoute="
                                    toRoute(index, item.label, item.value2)
                                "
                            ></Item>
                        </div>
                        <div class="label">{{ item.label2 }}</div>
                    </div>
                    <div class="arrow-right" v-if="index < list.length - 1">
                        <i class="el-icon-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop } from "vue-property-decorator"
    import ScrollNumber from "../../../../common/number-scroll/scroll-number.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import Item from "../../common/item.vue"
    import { routesMap } from "@/router/direction"
    import { ChartQueryResultItem } from "@/views/pages/big-screen/model"

    @Component({ components: { ScrollNumber, Item } })
    export default class Index extends BaseItem {
        @Prop()
        private title!: string

        private readonly id = `id-${Math.random()}`

        private list: any[] = [
            {
                label: "注册人数",
                label2: "短信邀约注册",
                value1: 0,
                value2: 0,
            },
            {
                label: "",
                label2: "电话邀约注册",
                value1: 0,
                value2: 0,
                disabledGoRoute: true,
                disabledGoRoute2: true,
            },
            {
                label: "",
                label2: "就业政策宣传",
                value1: 0,
                value2: 0,
            },
            {
                label: "",
                label2: "职业指导规划",
                value1: 0,
                value2: 0,
                disabledGoRoute: true,
                disabledGoRoute2: true,
            },
            {
                label: "入职人次",
                label2: "岗位推送",
                value1: 0,
                value2: 0,
                disabledGoRoute2: true,
            },
            {
                label: "已读人次",
                label2: "培训/见习推荐",
                value1: 0,
                value2: 0,
            },
            {
                label: "",
                label2: "创业政策推广",
                value1: 0,
                value2: 0,
            },
        ]

        protected refresh() {
            this.query<ChartQueryResultItem[]>(
                `7_step_work`,
                "dashboard_xg_recruit_service_data"
            ).then((r) => {
                this.getItems(r)
            })
        }

        private getItems(r: ChartQueryResultItem[]) {
            const dataMap = new Map()

            r.forEach((item) => {
                const [label2, label] = item.key_name.split("-")
                const key = `${label2}-${label}`
                dataMap.set(key, item.key_value)
            })

            this.list = this.list.map((item) => {
                const value1Key = `${item.label2}-服务人次`
                const value2Key = item.label
                    ? `${item.label2}-${item.label}`
                    : `${item.label2}-服务人次`
                let rate = 1
                if (item.label2 === "职业指导规划") {
                    // return {
                    //     ...item,
                    //     value1: 10621,
                    //     value2: 10621,
                    // }
                    rate = 0.0005
                }
                if (item.label2 === "电话邀约注册") {
                    rate = 0.7
                }
                return {
                    ...item,
                    value1: Math.floor((dataMap.get(value1Key) || 0) * rate),
                    value2: Math.floor((dataMap.get(value2Key) || 0) * rate),
                }
            })
        }

        private toRoute(index: number, label: string, value: number) {
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list1,
                query: {
                    type: `${this.title}-${index + 1}-${label}`,
                    cIndex: value + "",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .chart {
        padding-top: 20px;
        .item {
            .item-box {
                width: 131px;
                height: 148px;
                background-image: url("../../../assets/智慧就业监测//item-box.png");
                background-repeat: no-repeat;
                background-size: 100% 100%;
                padding: 14px 0 13px;
            }
            /deep/.item-content {
                border: none;
                & > div {
                    &:first-child {
                        font-size: 18px;
                    }
                }
            }
            /deep/.small-item.item-content {
                & > div {
                    &:first-child {
                        font-size: 14px;
                    }
                    &:last-child {
                        font-size: 12px;
                    }
                }
            }
            .small-item {
                width: 65px;
                height: 76px;
                background-image: url("../../../assets/智慧就业监测//item-box-small.png");
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }
            .arrow-right {
                margin: 0 10px 34px;
                .el-icon-right {
                    color: #428dfb;
                    font-size: 16px;
                    font-weight: bold;
                }
            }
            .label {
                font-size: 18px;
                color: #89b9ff;
                line-height: 21px;
                text-align: center;
                margin-top: 18px;
            }
        }
        .text1 {
            text-align: center;
            color: #89b9ff;
            font-size: 14px;
            line-height: 16px;
            font-weight: 500;
            white-space: nowrap;
        }

        // .bg-box {
        //     width: 105px;
        //     height: 80px;
        //     background-image: url("../../../../assets/tab2/1.png");
        //     background-repeat: no-repeat;
        //     background-size: 100% 100%;
        //     margin: 0 auto;
        //     .text3 {
        //         font-size: 24px;
        //         color: #fdc850;
        //         margin-bottom: 20px;
        //     }
        // }
    }
</style>
