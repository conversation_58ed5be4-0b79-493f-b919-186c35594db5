import { TableColumn } from "@/core-ui/component/table"

export interface DetailRow {
    status: Status
    id: number
    v: number
}

export const enum Status {
    禁用 = 0,
    启用 = 1,
}

export const columns: TableColumn[] = [
    {
        label: "数据源名称",
        prop: "func_name",
        showOverflowTip: true,
    },
    {
        label: "导出字段",
        prop: "column_comments",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        showOverflowTip: true,
    },
]

export const columns2: TableColumn[] = [
    {
        label: "数据集名称",
        prop: "table_display",
        showOverflowTip: true,
    },
    {
        label: "数据量",
        prop: "record_count",
        showOverflowTip: true,
    },
    {
        label: "使用说明",
        prop: "note",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        showOverflowTip: true,
    },
]

export interface DetailListRow {
    /** 关联单位 */
    name: string

    /** 核查需求简述 */
    require_note: string

    /** 结果描述 */
    result_note: number

    /** 是否有异常数据 */
    is_exception: number

    /** 创建人 */
    real_name: string

    /** 创建时间 */
    create_time: string

    /** 任务状态[文本] */
    status_label: string
    id: number
    v: number
}
