import { userService } from "@/service/service-user"

export const levelCodes = ["province", "city", "district", "town", "village"]

export function getRegionCode() {
    const code4CurAgent = userService.getCurAgent()?.data.region_code
    return code4CurAgent
    // return "420000000000,421000000000,421002000000"
}

export function buildRegionObjParams(params: string) {
    const obj = {}
    const p = params.split(",")
    levelCodes.forEach((i, index) => {
        p[index] &&
            Object.assign(obj, {
                [i]: p[index],
            })
    })
    return obj
}

export function getRegionKey() {
    const code4CurAgent = getRegionCode()
    const codes = (code4CurAgent || "").split(",")
    return levelCodes[codes.length - 1]
}

export function getRegionKeyByIndex(index: number) {
    return levelCodes[index]
}

export function getLastRegionCode() {
    const code4CurAgent = getRegionCode()
    const codes = (code4CurAgent || "").split(",")
    const last_region_code = codes[codes.length - 1] || ""
    return last_region_code
}

export function getSecondRegionCode() {
    const code4CurAgent = getRegionCode()
    const codes = (code4CurAgent || "").split(",")
    let secondRegionCode: string[] = []
    if (codes[1]) {
        secondRegionCode = [codes[0], codes[1]]
    } else {
        secondRegionCode = [codes[0]]
    }
    return secondRegionCode.join(",")
}

export function getRegionName() {
    const regionName = userService.getCurAgent()?.data.region_name
    return regionName || ""
}
