import { FormType } from "@/core-ui/component/form"
import { checkRole } from "@/installer/role"
import { desensitization } from "@/utils/tools"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"

export const spPolicy = ["孝感市城镇新增就业登记", "灵活就业社保补贴"]
export function getSpPolicyTemplate(policyName = "孝感市城镇新增就业登记") {
    return (
        (
            {
                孝感市城镇新增就业登记: "孝感市城镇新增就业登记导出",
                灵活就业社保补贴: "孝感灵活就业社保补贴导出",
            } as any
        )[policyName] || "普通政策导出"
    )
}
export function createC(policyName = "孝感市城镇新增就业登记"): any {
    const o: any = {
        孝感市城镇新增就业登记: [
            { label: "所属区域", prop: "handle_region_region_name" },
            { label: "姓名", prop: "profile_v2_name" },
            {
                label: "身份证号",
                prop: "profile_v2_id_card",
                width: "160px",
                formatter(row: any) {
                    return desensitization(row.profile_v2_id_card, 5)
                },
            },
            {
                label: "联系电话",
                prop: "extend_contact_mobile",
                width: "120px",
                render(h: any, row: any) {
                    return renDesensitizationView(h, {
                        value: row.extend_contact_mobile,
                    })
                },
            },
            { label: "民族", prop: "extend_nation_label" },
            { label: "户口性质", prop: "extend_reg_residence_property_label" },
            { label: "文化程度", prop: "extend_education_label" },
            { label: "毕业院校", prop: "extend_graduation_school" },
            { label: "毕业时间", prop: "extend_graduation_time_label" },
            { label: "就业形式", prop: "extend_employment_category_label" },
            { label: "就业单位", prop: "extend_employment_unit_label" },
            { label: "就业时间", prop: "extend_employment_time_label" },
            { label: "残疾人标识", prop: "extend_is_disabled_label" },
            { label: "户口所在地", prop: "extend_reg_region_code_label" },
            { label: "常住地区划", prop: "extend_permanent_region_code_label" },
            { label: "操作", prop: "h1" },
        ].map((e) => ({ ...e, showOverflowTip: true })),
        灵活就业社保补贴: [
            { label: "所属区域", prop: "handle_region_region_name" },
            {
                label: "身份证号",
                prop: "profile_v2_id_card",
                width: "160px",
                formatter(row: any) {
                    return desensitization(row.profile_v2_id_card, 5)
                },
            },
            { label: "姓名", prop: "profile_v2_name" },
            { label: "性别", prop: "sex_label" },
            {
                label: "联系电话",
                prop: "extend_contact_mobile",
                width: "120px",
                render(h: any, row: any) {
                    return renDesensitizationView(h, {
                        value: row.extend_contact_mobile,
                    })
                },
            },
            {
                label: "就业困难人员类别",
                prop: "employment_difficulties_category_label",
                width: "220px",
            },
            { label: "开户银行", prop: "bank_name" },
            { label: "银行账号", prop: "bank_account" },
            { label: "初次享受年月", prop: "first_enjoy_date_label" },
            { label: "本次补贴开始时间", prop: "subsidy_start_date_label" },
            {
                label: "本次补贴截止时间",
                prop: "subsidy_end_date_label",
            },
            { label: "补贴月数", prop: "subsidy_month_label" },
            { label: "补贴金额", prop: "amount_label" },
            { label: "备注", prop: "remark" },
            { label: "办理日期", prop: "dealer_time_label" },
            { label: "办理单位", prop: "handle_org" },
            { label: "操作", prop: "h1" },
        ].map((e) => ({ ...e, showOverflowTip: true })),
    }
    return o[policyName] || o.孝感市城镇新增就业登记 || []
}

export function policyImportConfig(p: string): any {
    return (
        {
            孝感市城镇新增就业登记: {
                templateUrl:
                    window.location.origin + "/file/城镇新增就业导入模板.xls",
                actionName: "import_offline_apply_person",
                role: "model.policy_form_apply.action.import_offline_apply_person",
                single: "add_offline_apply_person",
                showImportBtn: checkRole(
                    "model.policy_form_apply.action.import_offline_apply_person"
                ),
                hideOperateList: true,
            },
            灵活就业社保补贴: {
                templateUrl:
                    window.location.origin +
                    "/file/孝感灵活就业社保补贴导入模版.xlsx",
                actionName: "import_flexible_offline_apply_person",
                role: "model.policy_form_apply.action.import_flexible_offline_apply_person",
                single: "add_flexible_offline_apply_person",
                showImportBtn: checkRole(
                    "model.policy_form_apply.action.import_flexible_offline_apply_person"
                ),
            },
        }[p] || {
            templateUrl:
                window.location.origin + "/file/政策办理人员导入模板.xls",
            actionName: "import_apply_person",
            role: "model.policy_form_apply.action.import_apply_person",
            showImportBtn: checkRole(
                "model.policy_form_apply.action.import_apply_person"
            ),
        }
    )
}

export function getSpolicyFilter(p: string) {
    return {
        孝感市城镇新增就业登记: [
            {
                label: "用户信息搜索",
                prop: "name",
                type: FormType.Text,
                keyValueFilter: {
                    match: ListTypes.filterMatchType.fuzzy,
                },
            },
            {
                label: "所属区域",
                prop: "region_code",
                type: FormType.Cascader,
                option: {
                    elProps: {
                        checkStrictly: true,
                        // lazy: true,
                        // lazyLoad: codeLazyLoad,
                    } as any,
                    filterable: true,
                },
                // sourceInputsParameter: buildSelectSource([]),
            },
            {
                label: "就业时间",
                prop: "employment_time",
                type: FormType.DatePicker,
                option: {
                    type: "daterange",
                },
                // defaultValue: [
                //     `${moment().year()}-01-01`,
                //     `${moment().year()}-12-31`,
                // ],
            },
        ],
        灵活就业社保补贴: [
            {
                label: "用户信息搜索",
                prop: "name",
                type: FormType.Text,
                keyValueFilter: {
                    match: ListTypes.filterMatchType.fuzzy,
                },
            },
            {
                label: "所属区域",
                prop: "region_code",
                type: FormType.Cascader,
                option: {
                    elProps: {
                        checkStrictly: true,
                        // lazy: true,
                        // lazyLoad: codeLazyLoad,
                    } as any,
                    filterable: true,
                },
                // sourceInputsParameter: buildSelectSource([]),
            },
        ],
    }[p]
}
