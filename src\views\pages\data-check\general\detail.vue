<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button type="primary" @click="toCheck">
                    开始核查
                </el-button>
            </div>
        </div>
        <div class="detail-top-box">
            <detail-view :detailRow="row"></detail-view>
        </div>
        <div v-if="row">
            <DetailTable ref="detailTable" :detailId="detailId" />
        </div>
        <CheckLoadingPop :detailId="detailId" v-model="showCheckPop" />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailTable from "./components/detail-table.vue"
    import { pageLoading } from "@/views/controller"
    import { ColItem } from "@/views/components/detail-row-col"
    import { DetailRow } from "./index"
    import DetailView from "./components/detail-view.vue"
    import CheckLoadingPop from "./components/check-loading-pop.vue"

    @Component({
        name: routesMap.dataCheck.generalDetail,
        components: { DetailTable, DetailView, CheckLoadingPop },
    })
    export default class GeneralDetail extends Vue {
        private breadcrumbs: BreadcrumbItem[] = []

        private showList = false

        private items: ColItem[] = []

        private showAddPop = false
        private showCheckPop = false

        refreshConfig = {
            fun: this.init,
            name: routesMap.dataCheck.generalDetail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "通用数据核查任务详情",
                    to: {
                        name: routesMap.dataCheck.generalDetail,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.dataCheck.generalDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private detailId = ""

        private row: DetailRow | null = null

        created() {
            this.init()
        }

        private init() {
            this.row = null
            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("common_check_task")
                    .detail(this.$route.query.id as string, "back_detail")
                    .query()
                    .then((res) => {
                        this.row = sdk.buildRow(res.row, {
                            is_del: "",
                            create_time: "label",
                            real_name: "update_user_account#real_name",
                            name: "",
                            update_time: "",
                            final_change_time: "",
                            require_note: "",
                            result_note: "",
                            status: "label",
                            is_exception: "",
                            data_file_path: "",
                            org_name: "xg_login_user#xg_organization#name",
                            mobile_hide: "user_account#mobile_hide",
                            start: "label",
                            end: "label",
                            tool_name: "common_check_tool#name",
                        })
                        this.detailId = this.row?.id + "" || ""
                        this.items = res.meta.header.field_groups
                            .map((item) => {
                                return {
                                    label: item.label + "：",
                                    value: item.template,
                                    hide: !item.visible,
                                    span: 24,
                                }
                            })
                            .filter((i) => i) as ColItem[]
                    })
            })
        }

        private toAdd() {
            ;(this.$refs.detailTable as any).toAdd()
        }

        private refreshList() {
            ;(this.$refs.detailTable as any).refresh()
        }

        private toCheck() {
            pageLoading(() => {
                return sdk.core
                    .model("common_check_task")
                    .action("check")
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: +this.detailId }],
                    })
                    .execute()
                    .then(() => {
                        this.callRefresh(routesMap.dataCheck.generalIndex)
                        this.callRefresh(routesMap.dataCheck.generalDetail)
                    })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
    }
</style>
