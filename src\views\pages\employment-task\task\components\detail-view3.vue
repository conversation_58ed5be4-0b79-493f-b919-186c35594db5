<template>
    <div class="u-p-t-20">
        <div slot="table" class="detail-view">
            <div
                class="item"
                :class="{ 'item first u-flex u-row-around': !index }"
                v-for="(item, index) in list"
                :key="index"
                v-show="index || !hide0"
            >
                <template v-if="!index">
                    <div class="title">总体情况：</div>
                    <div
                        class="u-text-center"
                        v-for="d in item.list"
                        :key="d.prop"
                    >
                        <div class="label">{{ d.label }}</div>
                        <div class="num num1">
                            {{ d.value
                            }}{{ d.label === "当前得分" ? "分" : "" }}
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="title u-m-b-14 u-line-1">
                        {{ (item.name || "").replace(":", "") }}
                    </div>
                    <div class="u-flex">
                        <div
                            class="u-flex-1"
                            v-for="d in item.list"
                            :key="d.prop"
                        >
                            <div class="label">
                                {{ d.label }}
                            </div>
                            <div class="num">{{ d.value || 0 }}</div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { sdk } from "@/service"
    import { forEach, get, map, includes, find, replace } from "lodash"
    import { Component, Prop } from "vue-property-decorator"

    @Component({ components: {} })
    export default class Template extends BaseTableController<any> {
        @Prop()
        private config: any

        @Prop()
        private detail: any

        tableConfig: TableConfig | null = null

        private list: any[] = []

        private get currentPageNameR() {
            return this.$route.query.currentPageName as string
        }

        private get hide0() {
            if (
                this.detail.indicator_type_label === "城镇新增就业" &&
                this.currentPageNameR === "人社事业发展计划"
            ) {
                return true
            }
            if (
                this.detail.indicator_type_label ===
                    "”才聚荆楚”工程新增高校毕业生就业创业人数" &&
                this.currentPageNameR === "湖北省2024年十大民生项目"
            ) {
                return true
            }
            console.log("ddddd", JSON.parse(JSON.stringify(this.detail)))
            return false
        }

        mounted() {
            sdk.core
                .model(this.config.model)
                .list(this.config.listName)
                .addPrefilter(this.config.prefilters)
                .query({ pageIndex: 1, item_size: 99 })
                .then((r) => {
                    const d = get(r, "pageData.rows[0]", {})
                    const list = get(r, "pageData.meta.field_groups", []).map(
                        (e: any) => {
                            const prop = e.template
                                .replace("{", "")
                                .replace("}", "")
                                .replace(".", "#")
                            return {
                                name: e.name,
                                prop,
                                value: d[prop]?.display || "0",
                            }
                        }
                    )

                    this.$emit(
                        "setData",
                        list
                            .filter((e: any) => e.name.includes(":"))
                            .map((e: any) => {
                                return {
                                    ...e,
                                    name: e.name.replace(":", ""),
                                }
                            })
                    )
                    this.list = []
                    let lastL = ""
                    forEach(list, (e) => {
                        // name带 : 的 放在上面 下面不展示
                        if (e.name.includes(":")) return
                        const l = e.name.split("_")[0]
                        const l2 = e.name.split("_")[1]
                        if (l === lastL) {
                            const d = find(this.list, (e) => e.name === l)
                            d.list.push({ label: l2, prop: e.prop, value: e.value })
                        } else {
                            lastL = l
                            this.list.push({
                                name: l,
                                list: [{ label: l2, prop: e.prop, value: e.value }],
                            })
                        }
                    })
                })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    /deep/ .table {
        padding: 0 !important;
    }
    .detail-view {
        display: grid;
        gap: 20px;
        grid-template-columns: repeat(4, 1fr);
        .item {
            height: 90px;
            border-radius: 6px;
            background: #fafafa;
            padding: 10px;
            &.first {
                background: #f4f7ff;
                grid-column: span 2;
                .label {
                    color: #5780ab;
                }
                .num1 {
                    color: #4273d9;
                    font-weight: 600;
                }
            }
            .title {
                color: #222222;
                font-size: 14px;
            }
            .label {
                color: #888;
                font-size: 12px;
                margin-bottom: 6px;
            }
            .num {
                color: #666666;
                font-size: 20px;
                &.num2 {
                    color: #00a25c;
                }
            }
        }
    }
</style>
