import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { desensitization, formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"

const rowPredict = {
    mobile: "",
    name: "user_profile_basic#name",
    template_code: "",
    title: "sms_template#title",
    type: "sms_template#type_label",
    create_time: "",
    real_name: "creator_account#real_name",
    user_name: "user_account#user_profile_basic#name",
    user_age: "user_account#user_profile_basic#getAge",
    user_id_card: "user_account#user_profile_basic#id_card_hide",
    employment_status:
        "user_account#user_profile_basic#user_profile_current_job_info#employment_status_label",
    user_account_id: "user_account#user_profile_basic#id",
    params: "",
    content: "label",
}

const enum Type {
    定向发送短信 = 0,
    验证码短信 = 1,
    任务短信 = 2,
}

export interface Row {
    /** 接收手机号 */
    mobile: string
    /** 关联档案 */
    name: number
    /** 短信模板编码 */
    template_code: string
    /** 模板名称 */
    title: number
    /** 模板分类 */
    type: Type
    /** 模板分类[文本] */
    type_label: string
    /** 发送时间 */
    create_time: string
    /** 操作人 */
    real_name: number
    user_name: string
    user_age: string
    user_id_card: string
    employment_status: string
    employment_status_label: string
    user_account_id: string
    id: number
    v: number
}

export const tableFilter: TableFilter[] = [
    {
        prop: "real_name",
        label: "操作人",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "create_time",
        label: "发送时间",
        type: FormType.DatePicker,
    },
    {
        prop: "template_code",
        label: "模版编号",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "title",
        label: "模版名称",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
]

export const tableFilter1: TableFilter[] = [
    {
        prop: "name",
        label: "用户信息",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "create_time",
        label: "发送时间",
        type: FormType.DatePicker,
    },
    {
        prop: "title",
        label: "模版名称",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "employment_status",
        label: "就业状态",
        type: FormType.Select,
    },
    {
        prop: "birth_date",
        label: "出生年月",
        type: FormType.DatePicker,
    },
]

export const column: TableColumn[] = [
    {
        label: "模板内容",
        prop: "content",
        minWidth: "180px",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "发送时间",
        prop: "create_time",
        minWidth: "120px",
        align: "left",
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "模版名称",
        prop: "title",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "短信模版编号",
        prop: "template_code",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "操作人",
        prop: "real_name",
        minWidth: "80px",
        align: "left",
    },
    {
        label: "操作",
        prop: "h",
        align: "left",
    },
]

export const column1: TableColumn[] = [
    {
        label: "接收手机号",
        prop: "mobile",
        formatter(row) {
            return desensitization(row.mobile)
        },
        width: "130px",
    },
    {
        label: "模版名称",
        prop: "title",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "发送时间",
        prop: "create_time",
        minWidth: "120px",
        align: "left",
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "姓名",
        prop: "user_name",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "年龄",
        prop: "user_age",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "身份证号",
        prop: "user_id_card",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "就业状态",
        prop: "employment_status",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "关联档案id",
        prop: "user_account_id",
        minWidth: "150px",
        align: "left",
    },
    {
        label: "操作",
        prop: "h",
        align: "left",
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("sms_send_history").list("for_operate"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        column,
    }
}

export const cacheKey = "SMS_TABLE_LIST"
