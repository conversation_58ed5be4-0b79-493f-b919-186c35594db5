import { config, EnvProject } from "@/config"
import { roleConfig } from "@/installer/role"
import { CompanyRole, userService } from "@/service/service-user"
import layout from "@/views/pages/single-page/index.vue"
import { RouteConfig } from "."
import { routesMap } from "./direction"
const isDev = process.env.VUE_APP_ENV === "test"
const companyAdminRole: roleConfig = () => {
    return userService.getCurAgent()?.data.sys_role === CompanyRole.超级管理员
}

export const company = {
    recruit: {
        root: "",
        recruit: "",
        job: "",
        miniJob: "",
        jobDetail: "",
        jobDeliver: "",
        jobDeliverDetail: "",
        publicJobDeliver: "",
        publicJobDeliverDetail: "",
        createJob: "",
        person: "",
        personDetail: "",
        grid: "",
        gridDetail: "",
        group: "",
        groupDetail: "",
        cooperation: "",
        cooperationDetail: "",
        applyPerson: "",
        applyCooperation: "",
        applyGroup: "",
        applyGrid: "",
        jobFair: "",
        jobFairDetail: "",
        jobFairApply: "",
        jobFairAgent: "",
        jobFairAgentDetail: "",
        statistics: "",
    },
    consult: {
        root: "",
        index: "",
        policyCustomDetail: "",
        policyDetail: "",
        subsidyDetail: "",
    },
    research: {
        root: "",
        index: "",
    },
    companyManage: {
        root: "",
        manage: "",
        applyDetail: "",
        list: "",
        black: "",
    },
    shareEmployment: {
        root: "",
        applyList: "",
        createApply: "",
        applyDetail: "",
        infoList: "",
        infoDetail: "",
    },
    policy: {
        root: "",
        list: {
            index: "",
            detail: "",
        },
        report: {
            index: "",
            create: "",
        },
    },
    dimission: {
        root: "",
        index: "",
    },
    hrInfoManage: {
        root: "",
        staffList: "",
        staffListDetail: "",
        workInfoApply: "",
        workInfoApplyDetail: "",
        workDemandApply: "",
        workDemandApplyDetail: "",
    },
    companyInfo: {
        root: "",
        empList: "",
        empDetail: "",
        goOutList: "",
    },
}

export function xg_project_org(): RouteConfig[] {
    return [
        {
            path: `/home`,
            redirect: "home/page",
            name: routesMap.home.root,
            meta: { showOneChildren: true },
            component: layout,
            children: [
                {
                    path: "page",
                    name: routesMap.home.page,
                    meta: {
                        title: "首页",
                        svgIcon: require("@/assets/icon/menu/home.svg"),
                        // hideTag: true,
                        parentMenuName: routesMap.home.root,
                        // single: true,
                    },
                    component: () =>
                        import("@/views/page-company/home/<USER>"),
                },
                {
                    path: "news",
                    name: routesMap.home.news,
                    meta: {
                        title: "消息",
                        svgIcon: require("@/assets/icon/menu/home.svg"),
                        hideTag: true,
                        parentMenuName: routesMap.home.root,
                        // single: true,
                    },
                    component: () =>
                        import("@/views/page-company/home/<USER>"),
                },
            ],
        },
        {
            path: "/consult",
            redirect: "consult/index",
            name: routesMap.company.consult.root,
            meta: {
                title: "资讯与公告",
                hidden: [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                    config.envProject
                ),
                showOneChildren: true,
            },
            component: layout,
            children: [
                {
                    path: "index",
                    name: routesMap.company.consult.index,
                    meta: {
                        title: "资讯与公告",
                        svgIcon: require("@/assets/icon/menu/policy.svg"),
                    },
                    component: () =>
                        import("@/views/page-company/consult/index.vue"),
                },
                {
                    path: "policy-custom-detail",
                    name: routesMap.company.consult.policyCustomDetail,
                    meta: {
                        title: "政策咨询详情",
                        hidden: true,
                        parentMenuName: routesMap.company.consult.index,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/consult/policy-custom-detail.vue"
                        ),
                },
                {
                    path: "policy-detail",
                    name: routesMap.company.consult.policyDetail,
                    meta: {
                        title: "业务公告详情",
                        hidden: true,
                        parentMenuName: routesMap.company.consult.index,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/consult/policy-detail.vue"
                        ),
                },
                {
                    path: "consult-subsidy-detail",
                    name: routesMap.company.consult.subsidyDetail,
                    meta: {
                        title: "业务公告详情",
                        hidden: true,
                        parentMenuName: routesMap.company.consult.index,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/consult/subsidy-detail.vue"
                        ),
                },
            ],
        },
        {
            path: "/recruit",
            redirect: "recruit/job",
            name: routesMap.company.recruit.root,
            meta: {
                title: "招聘服务",
                svgIcon: require("@/assets/icon/menu/recruit.svg"),
            },
            component: layout,
            children: [
                {
                    path: "statistics",
                    name: routesMap.company.recruit.statistics,
                    meta: {
                        title: "招聘数据统计",
                        hidden: ![EnvProject.掇刀项目].includes(
                            config.envProject
                        ),
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/statistics/index.vue"
                        ),
                },
                {
                    path: "job",
                    name: routesMap.company.recruit.job,
                    meta: {
                        title: "招聘岗位列表",
                    },
                    component: () =>
                        import("@/views/page-company/recruit/job/index.vue"),
                },
                {
                    path: "mini-job",
                    name: routesMap.company.recruit.miniJob,
                    meta: {
                        title: "小程序自主投递列表",
                        parentMenuName: routesMap.company.recruit.job,
                        hidden: true,
                    },
                    component: () =>
                        import("@/views/page-company/recruit/job/mini-job.vue"),
                },
                {
                    path: "job-deliver",
                    name: routesMap.company.recruit.jobDeliver,
                    meta: {
                        title: "岗位投递列表",
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job-deliver/index.vue"
                        ),
                },
                {
                    path: "public-job-deliver",
                    name: routesMap.company.recruit.publicJobDeliver,
                    meta: {
                        title: "公益性岗位投递列表",
                        hidden:
                            [EnvProject.荆州项目, EnvProject.黄州项目].includes(
                                config.envProject
                            ) || true,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job-deliver/public-job.vue"
                        ),
                },
                {
                    path: "job-deliver-detail",
                    name: routesMap.company.recruit.jobDeliverDetail,
                    meta: {
                        title: "岗位投递详情",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.jobDeliver,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job-deliver/detail.vue"
                        ),
                },
                {
                    path: "job-detail",
                    name: routesMap.company.recruit.jobDetail,
                    meta: {
                        title: "招聘岗位详情",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.job,
                    },
                    component: () =>
                        import("@/views/page-company/recruit/job/detail.vue"),
                },
                {
                    path: "create-job",
                    name: routesMap.company.recruit.createJob,
                    meta: {
                        title: "创建/编辑岗位",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.job,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job/components/create-job.vue"
                        ),
                },
                {
                    path: "job-fair",
                    name: routesMap.company.recruit.jobFair,
                    meta: {
                        title: "招聘会列表",
                        hidden: [EnvProject.宜都项目].includes(
                            config.envProject
                        ),
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job-fair/index.vue"
                        ),
                },
                {
                    path: "job-fair-detail",
                    name: routesMap.company.recruit.jobFairDetail,
                    meta: {
                        title: "招聘会详情",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.jobFair,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job-fair/detail.vue"
                        ),
                },
                {
                    path: "job-fair-agent",
                    name: routesMap.company.recruit.jobFairAgent,
                    meta: {
                        title: "已报名招聘会",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.jobFair,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job-fair-agent/index.vue"
                        ),
                },
                {
                    path: "person",
                    name: routesMap.company.recruit.person,
                    meta: {
                        title: "人才推荐服务",
                        hidden: true,
                    },
                    component: () =>
                        import("@/views/page-company/recruit/person/index.vue"),
                },
                {
                    path: "person-detail",
                    name: routesMap.company.recruit.personDetail,
                    meta: {
                        title: "人才推荐申请详情",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.person,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/person/detail.vue"
                        ),
                },
                {
                    path: "grid",
                    name: routesMap.company.recruit.grid,
                    meta: {
                        title: "网格推广服务",
                        hidden: true,
                    },
                    component: () =>
                        import("@/views/page-company/recruit/grid/index.vue"),
                },
                {
                    path: "grid-detail",
                    name: routesMap.company.recruit.gridDetail,
                    meta: {
                        title: "网格推广申请详情",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.grid,
                    },
                    component: () =>
                        import("@/views/page-company/recruit/grid/detail.vue"),
                },
                {
                    path: "group",
                    name: routesMap.company.recruit.group,
                    meta: {
                        title: "社群推广服务",
                        hidden: true,
                    },
                    component: () =>
                        import("@/views/page-company/recruit/group/index.vue"),
                },
                {
                    path: "group-detail",
                    name: routesMap.company.recruit.groupDetail,
                    meta: {
                        title: "社群推广申请详情",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.group,
                    },
                    component: () =>
                        import("@/views/page-company/recruit/group/detail.vue"),
                },
                {
                    path: "cooperation",
                    name: routesMap.company.recruit.cooperation,
                    meta: {
                        title: "人力资源机构代招服务",
                        hidden: true,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/cooperation/index.vue"
                        ),
                },
                {
                    path: "cooperation-detail",
                    name: routesMap.company.recruit.cooperationDetail,
                    meta: {
                        title: "人力资源撮合申请详情",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.cooperation,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/cooperation/detail.vue"
                        ),
                },
                {
                    path: "apply-person",
                    name: routesMap.company.recruit.applyPerson,
                    meta: {
                        title: "申请人才推荐服务",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.jobDetail,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job/apply/person.vue"
                        ),
                },
                {
                    path: "apply-group",
                    name: routesMap.company.recruit.applyGroup,
                    meta: {
                        title: "申请社群推广服务",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.jobDetail,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job/apply/group.vue"
                        ),
                },
                {
                    path: "apply-cooperation",
                    name: routesMap.company.recruit.applyCooperation,
                    meta: {
                        title: "申请人力资源机构代招服务",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.jobDetail,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job/apply/cooperation.vue"
                        ),
                },
                {
                    path: "apply-grid",
                    name: routesMap.company.recruit.applyGrid,
                    meta: {
                        title: "申请网格推广服务",
                        hidden: true,
                        parentMenuName: routesMap.company.recruit.jobDetail,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/recruit/job/apply/grid.vue"
                        ),
                },
            ],
        },

        {
            path: "/company-info",
            name: routesMap.company.companyInfo.root,
            meta: {
                title: "企业员工信息管理",
                svgIcon: require("@/assets/icon/menu/research.svg"),
                hidden: [EnvProject.宜都项目, EnvProject.掇刀项目].includes(
                    config.envProject
                ),
            },
            component: layout,
            children: [
                {
                    path: "staff-list",
                    name: routesMap.company.companyInfo.empList,
                    meta: {
                        title: "企业员工列表",
                    },
                    component: () =>
                        import(
                            "@/views/zq-company/hr-info-manage/staff-list/index.vue"
                        ),
                },
                {
                    path: "staff-list-detail",
                    name: routesMap.company.companyInfo.empDetail,
                    meta: {
                        title: "企业员工详情",
                        hidden: true,
                        parentMenuName: routesMap.company.companyInfo.empList,
                    },
                    component: () =>
                        import(
                            "@/views/zq-company/hr-info-manage/staff-list/detail.vue"
                        ),
                },
                {
                    path: "index",
                    name: routesMap.company.companyInfo.goOutList,
                    meta: {
                        title: "离职人员登记表",
                    },
                    component: () =>
                        import("@/views/page-company/dimission/index.vue"),
                },
            ],
        },

        {
            path: "/hr-info-manage",
            redirect: "hr-info-manage/index",
            name: routesMap.company.hrInfoManage.root,
            meta: {
                title: "企业信息调查",
                svgIcon: require("@/assets/icon/menu/hrInfoManage.svg"),
                hidden: [EnvProject.宜都项目].includes(config.envProject),
            },
            component: layout,
            children: [
                {
                    path: "index",
                    name: routesMap.company.research.index,
                    meta: {
                        title: "企业信息调查",
                        hidden: [
                            EnvProject.荆州项目,
                            EnvProject.掇刀项目,
                        ].includes(config.envProject),
                    },
                    component: () =>
                        import("@/views/page-company/research/index.vue"),
                },
                {
                    path: "work-info-apply",
                    name: routesMap.company.hrInfoManage.workInfoApply,
                    meta: {
                        title: "企业用工信息填报",
                    },
                    component: () =>
                        ![EnvProject.掇刀项目].includes(config.envProject)
                            ? import(
                                  "@/views/zq-company/hr-info-manage/work-info-apply/index.vue"
                              )
                            : import(
                                  "@/views/page-company/work-info-survey/work-info-apply/index.vue"
                              ),
                },
                {
                    path: "work-info-apply-detail",
                    name: routesMap.company.hrInfoManage.workInfoApplyDetail,
                    meta: {
                        title: "填报详情",
                        hidden: true,
                        parentMenuName:
                            routesMap.company.hrInfoManage.workInfoApply,
                    },
                    component: () =>
                        ![EnvProject.掇刀项目].includes(config.envProject)
                            ? import(
                                  "@/views/zq-company/hr-info-manage/work-info-apply/detail.vue"
                              )
                            : import(
                                  "@/views/page-company/work-info-survey/work-info-apply/detail.vue"
                              ),
                },
                {
                    path: "work-demand-apply",
                    name: routesMap.company.hrInfoManage.workDemandApply,
                    meta: {
                        title: "企业用工需求填报",
                        hidden: [EnvProject.掇刀项目].includes(
                            config.envProject
                        ),
                    },
                    component: () =>
                        import(
                            "@/views/zq-company/hr-info-manage/work-demand-apply/index.vue"
                        ),
                },
                {
                    path: "work-demand-apply-detail",
                    name: routesMap.company.hrInfoManage.workDemandApplyDetail,
                    meta: {
                        title: "填报详情",
                        hidden: true,
                        parentMenuName:
                            routesMap.company.hrInfoManage.workDemandApply,
                    },
                    component: () =>
                        import(
                            "@/views/zq-company/hr-info-manage/work-demand-apply/detail.vue"
                        ),
                },
            ],
        },

        {
            path: "/share-employment",
            redirect: "share-employment/apply-list",
            name: routesMap.company.shareEmployment.root,
            meta: {
                title: "共享用工",
                svgIcon: require("@/assets/icon/menu/share-employment-default.svg"),
                hidden: [EnvProject.宜都项目].includes(config.envProject),
            },
            component: layout,
            children: [
                {
                    path: "apply-list",
                    name: routesMap.company.shareEmployment.applyList,
                    meta: {
                        title: "发布共享申请",
                        parentMenuName: routesMap.company.shareEmployment.root,
                    },
                    // 荆州使用孝感一样的共享用工逻辑
                    // import(
                    //     "@/views/page-company/share-employment/manage.vue"
                    // )
                    component: () =>
                        import(
                            "@/views/page-company/share-employment/apply/index.vue"
                        ),
                },
                {
                    path: "create-apply",
                    name: routesMap.company.shareEmployment.createApply,
                    meta: {
                        title: "创建/编辑申请",
                        hidden: true,
                        parentMenuName:
                            routesMap.company.shareEmployment.applyList,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/share-employment/apply/components/create-apply.vue"
                        ),
                },
                {
                    path: "apply-detail",
                    name: routesMap.company.shareEmployment.applyDetail,
                    meta: {
                        title: "发布共享申请详情",
                        hidden: true,
                        parentMenuName:
                            routesMap.company.shareEmployment.applyList,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/share-employment/apply/detail.vue"
                        ),
                },
                {
                    path: "info-list",
                    name: routesMap.company.shareEmployment.infoList,
                    meta: {
                        title: "共享用工信息列表",
                    },
                    component: () =>
                        import(
                            "@/views/page-company/share-employment/info/index.vue"
                        ),
                },
                {
                    path: "info-detail",
                    name: routesMap.company.shareEmployment.infoDetail,
                    meta: {
                        title: "共享用工信息详情",
                        hidden: true,
                        parentMenuName:
                            routesMap.company.shareEmployment.infoList,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/share-employment/info/detail.vue"
                        ),
                },
            ],
        },
        {
            path: "/work",
            name: routesMap.company.policy.root,
            meta: {
                title: "政策申报",
                svgIcon: require("@/assets/icon/menu/policy-company2.svg"),
            },
            component: layout,
            children: [
                {
                    path: "list",
                    name: routesMap.company.policy.list.index,
                    meta: {
                        title: "政策列表",
                    },
                    component: () =>
                        import("@/views/page-company/policy/list/index.vue"),
                },
                {
                    path: "list-detail",
                    name: routesMap.company.policy.list.detail,
                    meta: {
                        title: "政策详情",
                        hidden: true,
                        parentMenuName: routesMap.company.policy.list.index,
                    },
                    component: () =>
                        import("@/views/page-company/policy/list/detail.vue"),
                },
                {
                    path: "report",
                    name: routesMap.company.policy.report.index,
                    meta: {
                        title: "申报记录",
                    },
                    component: () =>
                        import("@/views/page-company/policy/report/index.vue"),
                },
                {
                    path: "report-create",
                    name: routesMap.company.policy.report.create,
                    meta: {
                        title: "申报",
                        hidden: true,
                        parentMenuName: routesMap.company.policy.report.index,
                    },
                    component: () =>
                        import("@/views/page-company/policy/report/create.vue"),
                },
            ],
        },
        {
            path: "/company-manage",
            redirect: "company-manage/manage",
            name: routesMap.company.companyManage.root,
            meta: {
                title: "企业基础数据管理",
                svgIcon: require("@/assets/icon/menu/base.svg"),
            },
            component: layout,
            children: [
                {
                    path: "company-manage/manage",
                    name: routesMap.company.companyManage.manage,
                    meta: {
                        title: "企业基本信息管理",
                    },
                    component: () =>
                        import(
                            "@/views/page-company/company-manage/manage/index.vue"
                        ),
                },
                {
                    path: "company-manage/black",
                    name: routesMap.company.companyManage.black,
                    meta: {
                        title: "求职者黑名单",
                        hidden: [
                            EnvProject.荆州项目,
                            EnvProject.黄州项目,
                            EnvProject.宜都项目,
                        ].includes(config.envProject),
                    },
                    component: () =>
                        import(
                            "@/views/page-company/company-manage/black/index.vue"
                        ),
                },
                {
                    path: "company-manage/apply-detail",
                    name: routesMap.company.companyManage.applyDetail,
                    meta: {
                        title: "人力资源机构申请",
                        hidden: true,
                        parentMenuName: routesMap.company.companyManage.manage,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/company-manage/manage/detail.vue"
                        ),
                },
                {
                    path: "company-manage/list",
                    name: routesMap.company.companyManage.list,
                    meta: {
                        title: "管理员列表",
                        role: companyAdminRole,
                    },
                    component: () =>
                        import(
                            "@/views/page-company/company-manage/list/index.vue"
                        ),
                },
            ],
        },
    ]
}
