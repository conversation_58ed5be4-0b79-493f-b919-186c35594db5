<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container-index container"
            :showExpand="false"
            :alwaysShowPageIndex="false"
        >
            <div slot="table" slot-scope="{ data }" class="bg-white">
                <common-table :data="data" :columns="columns"></common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: { TableContainer, CommonTable } })
    export default class Tab1 extends BaseTableController<any> {
        @Prop()
        detail!: any

        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []
        mounted() {
            this.init()
        }

        private init() {
            return buildConfig4RemoteMeta(
                "work_flow_log@xg_project",
                "detail_log",
                {
                    useLabelWidth: true,
                    disabledOpt: false,
                    useTabs: true,
                    optColumn: {
                        label: "操作",
                        prop: "h",
                        fixed: "right",
                        width: "200px",
                    },
                    prefilters: {
                        model_name: "live_broadcast",
                        obj_id: this.detail.id,
                    },
                }
            ).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            this.columns = r.columns
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
