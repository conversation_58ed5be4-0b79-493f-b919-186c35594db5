<template>
    <div class="seamless-table-container">
        <slot name="columns" />
        <seamless
            v-if="items.length"
            :data="items"
            :class-option="optionSingleHeightTime"
            class="scroll-table"
            :style="rowStyle"
            :scroll="scroll"
            ref="seamless"
        >
            <ul class="table-row-item">
                <li
                    v-for="(item, index) in items"
                    :key="index"
                    class="table-row d-flex"
                >
                    <slot :slot-scope="item" />
                </li>
            </ul>
        </seamless>

        <div class="empty" v-if="!items.length">暂无数据</div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Ref, Vue } from "vue-property-decorator"
    import { ResizeController } from "../../model/resize"
    import { baseSize } from "../rem/size"
    import Seamless from "./index.vue"

    @Component({ components: { Seamless } })
    export default class Index extends Vue {
        @Prop({ default: 74 })
        private readonly lineHeight!: number

        @Prop()
        private readonly items!: any[]

        @Ref()
        private readonly seamless!: Seamless

        @Prop({ default: 4 })
        private readonly rows!: number

        @Prop({ default: true })
        private readonly showMasks!: boolean

        @Prop({ default: 1 })
        private readonly top!: number

        @Prop()
        private readonly hasColumns!: boolean

        @Prop({ default: false })
        private readonly scroll!: boolean

        private readonly optionSingleHeightTime = {
            singleHeight: this.lineHeight || 74,
            waitTime: 5000,
            step: 0,
        }

        private get maxHight() {
            return this.rows * 74
        }

        private get rowStyle() {
            return { "max-height": `${this.maxHight / baseSize}rem` }
        }

        private get masks() {
            const length = this.items.length
            return _.take(
                _.map(_.range(_.floor(length / 2)), (i) => i),
                this.rows - 2
            )
        }

        mounted() {
            ResizeController.register(this, this.adjustLineHight)
            this.adjustLineHight()
            setTimeout(() => this.reset(), this.optionSingleHeightTime.waitTime)
        }

        private adjustLineHight() {
            const h = this.$el.querySelector(".table-row-item") as HTMLElement
            if (h) {
                const subs = h.querySelectorAll(".table-row")
                this.optionSingleHeightTime.singleHeight =
                    h.clientHeight / subs.length

                const top = this.optionSingleHeightTime.singleHeight + "px"

                this.$el.querySelectorAll(".masks .mask").forEach((e) => {
                    ;(e as HTMLElement).style.height = top
                })
            }
        }

        private reset() {
            this.optionSingleHeightTime.step = 1
            this.seamless && this.seamless.reset()
        }

        public stop() {
            this.seamless && this.seamless._stopMove()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .seamless-table-container {
        position: relative;
    }

    .scroll-table {
        min-height: 160px;
        overflow: hidden;
        padding: 0 0px;
        position: relative;
        z-index: 2;
        .table-row {
            overflow: hidden;
            // font-size: 14px;
            // color: #89b9ff;
            z-index: 2;
            position: relative;

            // .cell-1 {
            //     width: 210px;
            // }
            // .cell-2 {
            //     width: 52px;
            //     margin: 0 22px;
            // }
            // .cell-3 {
            //     width: 52px;
            //     text-align: right;
            // }
        }
    }

    ul,
    li {
        margin: 0;
        padding-left: 0;
    }

    @item-height: 74px;

    .masks {
        position: absolute;
        left: 20px;
        top: 0;
        right: 20px;
        bottom: 0;

        &.offset {
            margin-top: @item-height;
        }

        &.top-1 {
            top: @item-height;
        }

        .mask {
            height: @item-height;
            background-color: #013689;

            & + .mask {
                margin-top: @item-height;
            }
        }
    }

    .empty {
        font-size: 13px;
        color: #89b9ff;
        opacity: 0.7;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        margin-top: 80px;
    }
</style>
