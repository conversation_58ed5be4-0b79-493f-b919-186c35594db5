<template>
    <Container class="detail-box">
        <div class="home-container flex-fill d-flex" v-if="returnRow">
            <div class="left">
                <Card1 :row="returnRow"></Card1>
                <Card2 :row="row"></Card2>
                <Card3 :row="row"></Card3>
            </div>
            <div class="middle">
                <Card5 :row="row"></Card5>
            </div>
            <div class="right">
                <div class="export-btn u-flex u-row-center u-col-center">
                    <ExportPdf class="exportPDF" :count="6"></ExportPdf>
                </div>
                <Card6 :row="returnRow"></Card6>
                <Card7 :row="row"></Card7>
            </div>
        </div>
    </Container>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"
    import Container from "../common/container.vue"
    import Card1 from "./components-person/基本信息/index.vue"
    import Card2 from "../../智慧就业检测大屏/components-person/就业信息/index.vue"
    import Card3 from "../../智慧就业检测大屏/components-person/培训信息/index.vue"
    import Card5 from "../../智慧就业检测大屏/components-person/中间画像/index.vue"
    import Card6 from "./components-person/返乡信息/index.vue"
    import Card7 from "./components-person/服务记录/index.vue"
    import { sdk } from "@/service"
    import { routesMap } from "@/router/direction"
    import { Row, seekerInfoPredict } from "@/views/pages/labour-manage/seeker-info"
    import { pageLoading } from "@/views/controller"
    import BaseMap from "../../../common/base-map"
    import { detailPredict, DetailRow } from "."
    import { exportPdfService } from "@/views/common/exportPdf"
    import ExportPdf from "@/views/common/exportPdf/index.vue"

    @Component({
        name: routesMap.bigScreen.dataStatisticsAnalysis.personDetail,
        components: {
            Container,
            Card1,
            Card2,
            Card3,
            Card5,
            Card6,
            Card7,
            ExportPdf,
        },
    })
    export default class Template extends BaseMap {
        refreshConfig = {
            fun: () => {
                this.init(true)
            },
            name: "refresh-xiaogan-detail",
        }

        mounted() {
            this.init()
        }

        private id = ""
        private profileId = ""
        private cacheId = ""
        private row: Row | null = null
        private returnRow: DetailRow | null = null

        private init(force = false) {
            console.log("123")
            exportPdfService.clearData()
            exportPdfService.pushItem({
                title: "孝感市2025年春风行动数据统计分析大屏",
                order: 0,
                type: "title",
            })
            this.id =
                (this.$route.query.id as string) ||
                "a38e7e6ce67fd1bb7861193b98e73f4cabba410ba43e8c9d7945b6ea92e28def"
            this.profileId = (this.$route.query.profile_id as string) || ""
            if (this.cacheId !== this.id || force) {
                this.row = null
            }

            const promises = [this.getReturnDetail()]

            if (this.profileId) {
                promises.push(this.getProfileDetail())
            }

            return pageLoading(
                () => {
                    return Promise.all(promises).then((r) => {
                        if (r[1]) {
                            this.row = {
                                ...sdk.buildRow<Row>(r[1].row, {
                                    ...seekerInfoPredict,
                                    profile_labels: "",
                                }),
                                ...(r[1].row.object_data || {}),
                                ...{
                                    tags: r[1].row.tags || {},
                                },
                            } as unknown as Row
                        }
                        if (r[0]) {
                            this.cacheId = this.id
                            this.returnRow = {
                                ...sdk.buildRow<DetailRow>(r[0].row, detailPredict),
                                profile_labels:
                                    (this.row as any)?.profile_labels || "",
                            }
                        }
                    })
                },
                {
                    customClass: "service-loading-xiaogan-big-screen",
                }
            )
        }

        private getReturnDetail() {
            return sdk.core
                .model("person_return_exanchange")
                .detail(this.id, "big_screen_detail")
                .query()
        }

        private getProfileDetail() {
            return sdk.core
                .model("user_profile_basic")
                .detail(this.id, "second_version_detail")
                .query()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .detail-box {
        background-image: url("../../智慧就业检测大屏/assets/detail-bg.webp") !important;
    }

    .home-container {
        padding-left: 20px;
        padding-right: 20px;
    }

    .left,
    .middle,
    .right {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    .left,
    .right {
        .content {
            background: rgba(33, 50, 146, 0.6);
        }
    }
    .right {
        position: relative;
        .export-btn {
            font-weight: 500;
            font-size: 14px;
            color: #a1c8ff;
            width: 88px;
            height: 32px;
            background: rgba(113, 159, 227, 0.2);
            border-radius: 4px;
            border: 1px solid rgba(137, 185, 255, 0.5);
            cursor: pointer;
            position: fixed;
            top: 72px;
            right: 20px;
            &:hover {
                background: rgba(113, 159, 227, 0.4);
            }
        }
    }

    /deep/.job-content {
        height: 377px;
    }
    /deep/.train-content {
        height: 265px;
    }
    /deep/ .back_info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        .item-title {
            grid-column-start: 1;
            grid-column-end: 2;
        }
    }
</style>

<style lang="less">
    .el-loading-mask {
        &.service-loading-xiaogan-big-screen {
            background-color: rgba(1, 25, 155, 0.5);
        }
    }
</style>
