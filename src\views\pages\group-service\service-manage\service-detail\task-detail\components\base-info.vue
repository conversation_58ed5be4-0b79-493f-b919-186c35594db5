<template>
    <div>
        <div class="core-ui-custom-header">
            <div class="title">
                <slot />
            </div>
            <el-button
                v-if="!item.status"
                :disabled="!canPublish || isPublishing"
                type="primary"
                :plain="!canPublish || isPublishing"
                @click="onPublish"
            >
                {{ isPublishing ? "发布中" : "发布" }}
            </el-button>
        </div>
        <div class="contain-box">
            <div class="title-container u-font-16 bold u-flex">
                <div class="title u-flex-1"></div>
                <el-button
                    type="primary"
                    class="u-m-r-20"
                    :plain="true"
                    @click="showEditInfoPop = true"
                    v-if="!item.status"
                    :disabled="isPublishing"
                >
                    编辑
                </el-button>
                <el-button
                    type="primary"
                    class="u-m-r-20"
                    :plain="true"
                    @click="onDel"
                    v-if="!item.status"
                    :disabled="isPublishing"
                >
                    删除
                </el-button>
                <div class="color-2">状态：</div>
                <div :style="{ color: statusColor[item.status] }">
                    {{ item.status_label }}
                </div>
            </div>

            <detail-row-col class="u-m-t-10" :list="detailRow" />

            <div class="u-m-t-15" style="color: #bc904b" v-if="!item.status">
                请完成编辑任务内容、通知配置、设置服务对象，配置完成后可发布任务
            </div>
            <div class="u-m-t-12 d-flex" v-if="!item.status">
                <div
                    class="config-btn"
                    v-for="(item, key) in configBtn"
                    :key="key"
                    :class="{ done: item.done }"
                >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="handleFunc(item.handleBtn)"
                        :icon="item.done ? 'el-icon-check' : ''"
                        :plain="!item.done"
                        :disabled="isPublishing"
                    >
                        {{ item.label }}
                    </el-button>
                    <div v-if="item.done" class="u-p-x-10 u-p-t-10 pointer">
                        <span @click="handleFunc(item.handleTip)">
                            {{ item.tip }}
                            <i class="el-icon-arrow-right bold"></i>
                        </span>
                    </div>
                </div>
            </div>
            <service-card :task_id="item.id" :item="item" v-else />
        </div>
        <task-operator v-model="showTaskOperator" :taskId="item.id" />
        <follow-up-pop
            v-model="showfollowUpPop"
            :taskId="item.id"
            @refresh="$emit('refresh')"
        />
        <edit-info-pop
            v-model="showEditInfoPop"
            :taskId="item.id"
            @refresh="$emit('refresh')"
        />
        <notify-config
            v-model="showNotifyConfig"
            :taskId="item.id"
            @refresh="$emit('refresh')"
        />
    </div>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import { checkRole } from "@/installer/role"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { formatTime } from "@/utils/tools"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { MessageBox } from "element-ui"
    import { every } from "lodash"
    import moment from "moment"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import ServiceCard from "../../components/service-card.vue"
    import { ServiceTaskRow, statusColor, taskToolContentPage } from "../../task"
    import EditInfoPop from "./edit-info-pop.vue"
    import FollowUpPop from "./follow-up-pop.vue"
    import NotifyConfig from "./notify-config/index.vue"
    import TaskOperator from "./task-operator.vue"

    const isDev = process.env.VUE_APP_ENV === "test"

    @Component({
        components: {
            DetailRowCol,
            ServiceCard,
            TaskOperator,
            NotifyConfig,
            FollowUpPop,
            EditInfoPop,
        },
    })
    export default class BaseInfo extends Vue {
        @Prop()
        private item!: ServiceTaskRow

        statusColor = statusColor
        showTaskOperator = false
        showNotifyConfig = false
        showfollowUpPop = false
        showEditInfoPop = false

        private toContentEdit() {
            const page = taskToolContentPage.get(this.item.task_type)
            this.$router.push({
                name: page,
                query: {
                    id: this.item.id + "",
                },
            })
        }

        private get configBtn() {
            let flag = !!this.item.serve_target_count
            if (!isDev && checkRole("/tablelist/user_profile_basic/manage_v3")) {
                flag = true
            }
            return [
                {
                    label: "编辑任务内容",
                    done: !!this.item.related_obj_title,
                    tip: this.item.related_obj_title,
                    handleBtn: () => {
                        this.toContentEdit()
                    },
                    handleTip: () => {
                        this.toContentEdit()
                    },
                },
                {
                    label: "通知配置",
                    done: !!this.item.msg_config_count,
                    tip: "待办提醒消息",
                    handleBtn: () => {
                        this.showNotifyConfig = true
                    },
                    handleTip: () => {
                        this.showNotifyConfig = true
                    },
                },
                {
                    label: "设置服务对象",
                    done: flag,
                    tip: `已配置对象${this.item.serve_target_count || 0}个`,
                    handleBtn: () => {
                        this.$router.push({
                            name: routesMap.groupService.serviceManageDetail.result
                                .serve_target_count,
                            query: {
                                from: routesMap.groupService.serviceManageDetail
                                    .task,
                                task_id: this.item.id + "",
                                status: this.item.status + "",
                            },
                        })
                    },
                    handleTip: () => {
                        this.$router.push({
                            name: routesMap.groupService.serviceManageDetail.result
                                .serve_target_count,
                            query: {
                                from: routesMap.groupService.serviceManageDetail
                                    .task,
                                task_id: this.item.id + "",
                                status: this.item.status + "",
                            },
                        })
                    },
                },
            ]
        }

        private get canPublish() {
            return every(this.configBtn, (i) => i.done)
        }

        private get isPublishing() {
            return this.item.status === 0 && +this.item.data_status > 0
        }

        handleFunc(fun?: Function) {
            if (this.isPublishing) return
            fun && fun()
        }

        onPublish() {
            MessageBox.confirm("发布后将不可修改！请确认是否发布？", "提示", {
                beforeClose: (action, instance, done) => {
                    if (action !== "confirm") {
                        return done()
                    }
                    instance.confirmButtonLoading = true
                    sdk.core
                        .model("serve_task")
                        .action("update_status")
                        .addInputs_parameter({
                            status: "1",
                        })
                        .updateInitialParams({
                            selected_list: [{ id: this.item.id, v: 0 }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("发布成功")
                            this.$emit("refresh")
                            done()
                        })
                        .finally(() => {
                            instance.confirmButtonLoading = false
                        })
                },
            })
        }

        onDel() {
            MessageBox.confirm("请确认是否删除？", "提示", {
                beforeClose: (action, instance, done) => {
                    if (action !== "confirm") {
                        return done()
                    }
                    instance.confirmButtonLoading = true
                    sdk.core
                        .model("serve_task")
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ id: this.item.id, v: 0 }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("删除成功")
                            this.$emit("refresh")
                            this.$router.push({
                                name: routesMap.groupService.serviceManageDetail
                                    .detail,
                                query: { id: this.item.serve_project_id + "" },
                            })
                            this.callRefresh(routesMap.groupService.taskManage)
                            this.callRefresh(
                                routesMap.groupService.serviceManageDetail
                                    .detailItem.content
                            )
                            done()
                        })
                        .finally(() => {
                            instance.confirmButtonLoading = false
                        })
                },
            })
        }

        toEdit() {
            this.$router.push({
                name: routesMap.groupService.serviceManageEdit,
                query: {
                    id: "1",
                },
            })
        }

        private get detailRow(): ColItem[] {
            const h = this.$createElement
            const item = this.item
            return [
                {
                    label: "所属服务：",
                    value: item.serve_project_p_name,
                },
                {
                    label: "服务内容：",
                    value: item.project_content_title,
                },
                {
                    label: "最近更新：",
                    vNode: [
                        h("div", [
                            h(
                                "span",
                                `${item.update_username || ""} ${formatTime.seconds(
                                    item.update_time
                                )}`
                            ),
                            h(
                                "span",
                                {
                                    style: {
                                        paddingLeft: "8px",
                                        color: "#5782EC",
                                    },
                                    class: "pointer",
                                    on: {
                                        click: () => {
                                            this.showTaskOperator = true
                                        },
                                    },
                                },
                                "记录"
                            ),
                            h("i", { class: "el-icon-arrow-right" }),
                        ]),
                    ],
                },
                {
                    label: "任务工具：",
                    value: item.task_type_label,
                },
                {
                    label: "任务时间：",
                    value: `${moment(this.item.start_date).format(
                        "YYYY.MM.DD"
                    )} - ${moment(this.item.end_date).format("YYYY.MM.DD")}`,
                },
                {
                    label: "跟进人：",
                    // value: item.follow_up_name,
                    vNode: [
                        h("div", [
                            h("span", `${item.follow_up_name}`),
                            h(
                                "span",
                                {
                                    style: {
                                        paddingLeft: "8px",
                                        color: "#5782EC",
                                    },
                                    class: "pointer",
                                    on: {
                                        click: () => {
                                            this.showfollowUpPop = true
                                        },
                                    },
                                },
                                "修改"
                            ),
                            h("i", { class: "el-icon-arrow-right" }),
                        ]),
                    ],
                },
            ].map((i) => ({ ...i, span: 8 }))
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .contain-box {
        background-color: #fff;
        padding: 20px;
        margin-bottom: 24px;
        .title-container {
            width: 100%;
            height: 36px;
            background: #f8f8f8;
            padding: 20px;
            color: #222;
        }
        .config-btn {
            width: 135px;
            font-size: 12px;
            color: #6d82ac;
            line-height: 18px;
            &.done {
                height: 96px;
                background-color: #f5f9ff;
                border-radius: 0 0 4px 4px;
            }
            & + .config-btn {
                margin-left: 10px;
            }
            .el-button {
                width: 135px;
                ::v-deep i {
                    font-weight: bold;
                    font-size: 14px;
                }
            }
        }
    }
</style>
