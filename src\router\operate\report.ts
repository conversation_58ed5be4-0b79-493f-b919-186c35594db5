import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const report = [
    {
        path: "/report",
        name: routesMap.home.report,
        meta: {
            title: ![EnvProject.潜江项目].includes(config.envProject)
                ? "报表管理"
                : "专项工作报表管理",
            hidden: ![
                EnvProject.荆州项目,
                // EnvProject.黄州项目,
                EnvProject.潜江项目,
            ].includes(config.envProject),
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/financial.svg"),
        },
        component: layout,
        children: [
            {
                path: "report-manage-list",
                name: routesMap.reportManage.list.index,
                meta: {
                    title: [EnvProject.潜江项目].includes(config.envProject)
                        ? "创建报表"
                        : "报表模板列表",
                    role: ![EnvProject.潜江项目].includes(config.envProject)
                        ? "/tablelist/xg_indicator_group_ref/for_operate"
                        : "/tablelist/xg_indicator_group_ref/for_operate_two_dimensional",
                },
                component: () =>
                    ![EnvProject.潜江项目].includes(config.envProject)
                        ? import("@/views/pages/report-manage/list/index.vue")
                        : import(
                              "@/views/pages/report-manage-qj/list/index.vue"
                          ),
            },
            {
                path: "report-manage-list-detail",
                name: routesMap.reportManage.list.detail,
                meta: {
                    title: "模板详情",
                    hidden: true,
                    parentMenuName: routesMap.reportManage.list.index,
                },
                component: () =>
                    ![EnvProject.潜江项目].includes(config.envProject)
                        ? import("@/views/pages/report-manage/list/detail.vue")
                        : import(
                              "@/views/pages/report-manage-qj/list/detail.vue"
                          ),
            },
            {
                path: "report-manage-task",
                name: routesMap.reportManage.creator.index,
                meta: {
                    title: [EnvProject.潜江项目].includes(config.envProject)
                        ? "报表填报"
                        : "报表管理",
                    role: ![EnvProject.潜江项目].includes(config.envProject)
                        ? "/tablelist/xg_indicator_task/for_operate"
                        : "/tablelist/xg_indicator_task/for_operate_two_dimensional",
                },
                component: () =>
                    ![EnvProject.潜江项目].includes(config.envProject)
                        ? import(
                              "@/views/pages/report-manage/creator/index.vue"
                          )
                        : import(
                              "@/views/pages/report-manage-qj/creator/index.vue"
                          ),
            },
            {
                path: "report-manage-task-list",
                name: routesMap.reportManage.creator.list,
                meta: {
                    title: "报表列表",
                    role: "/tablelist/xg_indicator_task/for_operate_two_dimensional",
                    hidden: true,
                    parentMenuName: routesMap.reportManage.creator.index,
                },
                component: () =>
                    import("@/views/pages/report-manage-qj/creator/list.vue"),
            },
            {
                path: "report-manage-task-detail",
                name: routesMap.reportManage.creator.detail,
                meta: {
                    title: "报表详情",
                    hidden: true,
                    parentMenuName: routesMap.reportManage.creator.index,
                },
                component: () =>
                    ![EnvProject.潜江项目].includes(config.envProject)
                        ? import(
                              "@/views/pages/report-manage/creator/detail.vue"
                          )
                        : import(
                              "@/views/pages/report-manage-qj/creator/detail.vue"
                          ),
            },
            {
                path: "report-manage-target",
                name: routesMap.reportManage.executor.index,
                meta: {
                    title: [EnvProject.潜江项目].includes(config.envProject)
                        ? "报表结果"
                        : "报表填报",
                    role: ![EnvProject.潜江项目].includes(config.envProject)
                        ? "/tablelist/xg_indicator_task_target/for_operate"
                        : "/tablelist/xg_indicator_task_target/for_operate_two_dimensional",
                },
                component: () =>
                    ![EnvProject.潜江项目].includes(config.envProject)
                        ? import(
                              "@/views/pages/report-manage/executor/index.vue"
                          )
                        : import(
                              "@/views/pages/report-manage-qj/executor/index.vue"
                          ),
            },
            {
                path: "report-manage-target-detail",
                name: routesMap.reportManage.executor.detail,
                meta: {
                    title: "报表详情",
                    hidden: true,
                    parentMenuName: routesMap.reportManage.executor.index,
                },
                component: () =>
                    ![EnvProject.潜江项目].includes(config.envProject)
                        ? import(
                              "@/views/pages/report-manage/executor/detail.vue"
                          )
                        : import(
                              "@/views/pages/report-manage-qj/executor/detail.vue"
                          ),
            },
        ],
    },
]
