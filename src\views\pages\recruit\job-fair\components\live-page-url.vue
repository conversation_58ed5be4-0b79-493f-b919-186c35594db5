<template>
    <div class="u-flex">
        <div v-if="live_page_url" class="u-line-3">
            {{ live_page_url }}
        </div>
        <div v-else>--</div>
        <el-button class="u-m-l-20" type="text" @click="open()">设置</el-button>
        <el-dialog title="设置直播页面" :visible.sync="show">
            <el-input v-model="newUrl" />

            <div class="u-m-t-40 u-flex-center">
                <el-button @click="show = false">取消</el-button>
                <el-button
                    class="u-m-l-40"
                    type="primary"
                    @click="submit()"
                    :loading="loading"
                >
                    确定
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
    import { getBkKey } from "@/encrypt"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { decryptAesString, getUrlParams } from "@/utils/tools"
    import axios from "axios"
    import { get } from "lodash"
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: {} })
    export default class LivePageUrl extends Vue {
        @Prop({ default: "" })
        private live_page_url!: string

        @Prop({ default: 0 })
        private jobFairId!: number

        newUrl = ""
        show = false
        loading = false

        open() {
            this.newUrl = this.live_page_url
            this.show = true
        }

        async submit() {
            this.loading = true
            if (this.newUrl) {
                await this.checkUrl()
                    .catch(() => {
                        this.$message.error("直播地址不合法")
                        return Promise.reject()
                    })
                    .finally(() => {
                        this.loading = false
                    })
            }

            this.loading = true
            return sdk.core
                .model("job_fair")
                .action("update_live_page_url")
                .updateInitialParams({
                    selected_list: [{ id: this.jobFairId, v: 0 }],
                })
                .addInputs_parameter({
                    live_page_url: this.newUrl,
                })
                .execute()
                .then(() => {
                    this.show = false
                    this.callRefresh(routesMap.recruit.jobFairDetail)
                })
                .finally(() => {
                    this.loading = false
                })
        }

        checkUrl() {
            const liveData = getUrlParams(this.newUrl)
            const { LVE001, UCE385 } = liveData
            return axios
                .get(
                    `https://www.hbggzp.cn/PER/LV/COMMON/V1/H5GETINFO?LVE001=${LVE001}&UCE385=${UCE385}`
                )
                .then((r) => {
                    console.log(r)
                    let d = {}
                    try {
                        if (getBkKey()) {
                            d = JSON.parse(decryptAesString(r.data, getBkKey()))
                        } else {
                            d = r || {}
                        }
                    } catch (e) {
                        console.log(e)
                        d = r || {}
                    }
                    if (get(d, "Result", null)) {
                        return Promise.resolve()
                    } else {
                        return Promise.reject()
                    }
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .live {
        display: flex;
    }
</style>
