<template>
    <div class="content">
        <div class="core-ui-custom-header">
            <div class="title">{{ title }}</div>
            <div class="u-flex u-row-right"></div>
        </div>
        <div class="content1">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="area-select">所在区域：</div>
                    <RegionTree
                        :data="regionTreeData"
                        @change="regionTreeChange"
                        :maxLevel="2"
                    ></RegionTree>

                    <div class="year-selected" v-if="false">
                        <el-select
                            v-model="year"
                            @change="init"
                            placeholder="请选择年份"
                        >
                            <template slot="prefix">
                                <i
                                    class="el-icon-date prefix-selected-icon"
                                ></i>
                            </template>
                            <el-option
                                v-for="year in years"
                                :key="year"
                                :label="year.toString() + '年'"
                                :value="year"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div>
                    <el-button type="primary" @click="toSearch">
                        信息查询
                    </el-button>
                </div>
            </div>
            <div class="info-items" v-for="(item, index) in data" :key="index">
                <div class="info-label">{{ item.label }}</div>
                <div class="info-box d-flex">
                    <div
                        class="left-box"
                        v-for="(data, subIndex) in item.leftBox"
                        :key="'left' + subIndex"
                    >
                        <div class="item">
                            <div
                                class="item-label"
                                :class="{ bigger: item.rightBox.length > 1 }"
                            >
                                {{ data.value }}
                            </div>
                            <div>{{ data.label }}</div>
                        </div>
                    </div>
                    <div class="right-box">
                        <div
                            class="right-box-line"
                            v-for="(line, lineIndex) in item.rightBox"
                            :key="'line' + lineIndex"
                        >
                            <div
                                class="item"
                                v-for="(subItem, subItemIndex) in line"
                                :key="'right' + subItemIndex"
                            >
                                <div class="item-label">
                                    {{ subItem.value }}
                                </div>
                                <div>{{ subItem.label }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="content2">
            <table-container
                v-if="tableConfig"
                filedWidth="200"
                :showPageIndex="false"
                ref="table"
                v-model="tableConfig"
                class="container"
            >
                <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                    <common-table :data="data" :columns="columns">
                    </common-table>
                </div>
            </table-container>
        </div>
    </div>
</template>

<script lang='ts'>
    import { sdk } from "@/service"
    // import { cloneDeep } from "lodash"
    import { Component, Prop } from "vue-property-decorator"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { userService } from "@/service/service-user"
    import moment from "moment"
    import RegionTree from "../components/region-tree.vue"
    import { getLastRegionCode } from "../components/region-tool"

    interface Row {
        id: number
    }

    @Component({ components: { TableContainer, CommonTable, RegionTree } })
    export default class CommonList extends BaseTableController<Row> {
        @Prop()
        private title!: string

        @Prop()
        private list_name!: string

        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []

        private years = this.getLastFiveYears()
        private year = moment().year() + ""

        private firstData: any = []
        private dataInfo: any = []

        private data: any[] = []
        private region_code = getLastRegionCode()

        private regionTreeData: any = {
            level: 3,
            type: "region_code",
            treeInfo: {
                manage_region_full_path:
                    userService.getCurAgent()?.data.region_code,
            },
        }

        private itemWidth = 0

        created() {
            this.getListData({})
            this.init()
        }

        private getLastFiveYears() {
            const currentYear = new Date().getFullYear()
            const years = []
            for (let i = 0; i < 5; i++) {
                years.push(currentYear - i + "")
            }
            return years
        }

        private getListData(detail: any) {
            this.data = [
                {
                    label: "企业总体情况",
                    leftBox: [
                        {
                            label: "用工总人数",
                            value: detail.total || 0,
                        },
                    ],
                    rightBox: [
                        // [
                        //     {
                        //         label: "单位就业人员",
                        //         value: detail.company_count || 0,
                        //     },
                        //     {
                        //         label: "灵活就业人员",
                        //         value: detail.gov_count || 0,
                        //     },
                        //     {
                        //         label: "本地就业",
                        //         value: detail.current_job_count || 0,
                        //     },
                        //     {
                        //         label: "外地就业",
                        //         value: detail.un_current_job_count || 0,
                        //     },
                        // ],
                        [
                            // {
                            //     label: "第一需工产业",
                            //     value: detail["第一产业"] || 0,
                            // },
                            // {
                            //     label: "第二需工产业",
                            //     value: detail["第二产业"] || 0,
                            // },
                            // {
                            //     label: "第三需工产业",
                            //     value: detail["第三产业"] || 0,
                            // },
                            // {
                            //     label: "第三需工产业",
                            //     value: detail["第三产业"] || 0,
                            // },
                        ],
                        [
                            // {
                            //     label: "重点企业需工人数",
                            //     value: detail["重点企业用工数量"] || 0,
                            // },
                        ],
                    ],
                },
            ]
        }

        private init() {
            console.log("this.region_code", this.region_code)
            sdk.getDomainService(
                "get_company_task_detail",
                "back_api",
                "xg_project"
            )
                .post({
                    region_codes: this.region_code,
                    // year: this.year,
                })
                .then((res: any) => {
                    this.getListData(res[0])
                })
            sdk.getDomainService(
                "get_company_task_detail_list",
                "back_api",
                "xg_project"
            )
                .post({
                    region_codes: this.region_code,
                    // year: this.year,
                })
                .then((res: any) => {
                    const tableData: any = {
                        region_name: "下级区域",
                        total: "用工总人数",
                        // company_count: "单位就业人员",
                        // gov_count: "灵活就业人员",
                    }

                    const query: any = {
                        request: () => {
                            return new Promise((resolve) => {
                                const columns: any[] = []
                                Object.keys(tableData).forEach((i: string) => {
                                    columns.push({
                                        label: tableData[i],
                                        prop: i,
                                        showOverflowTip: true,
                                    })
                                })
                                this.columns = columns
                                resolve({
                                    data: res,
                                    total_count: res.length,
                                })
                            })
                        },
                    }

                    this.tableConfig = null

                    this.$nextTick(() => {
                        this.tableConfig = {
                            domainService: query,
                            defaultPageSize: 99,
                        }
                    })
                })
        }

        private toSearch() {
            this.$emit("toSearch")
        }

        private regionTreeChange(r: any) {
            this.$emit("regionTreeChange", r)
            if (r.values && r.values.length > 0) {
                const codes = r.values[0].split(",")
                this.region_code = codes[codes.length - 1]
                this.init()
            }
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        overflow: auto;
    }
    .content1 {
        background-color: #fff;
        padding: 20px;

        .area-select {
            width: 120px;
            color: #222222;
            font-size: 18px;
            word-break: keep-all;
        }

        .info-label {
            height: 50px;
            font-weight: 600;
            font-size: 18px;
            color: #000000;
            line-height: 50px;
        }

        .info-box {
            width: 100%;
            gap: 20px;

            .left-box {
                width: 22%;
                height: 90px;
                background: #f4f1ff;
                border-radius: 8px;

                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }

            .right-box {
                // display: flex;
                flex-direction: column;
                gap: 20px;
                flex: 1;

                .right-box-line {
                    display: flex;
                    flex: 1;
                    gap: 20px;

                    .item {
                        background: #f1f5ff;
                    }

                    &:nth-child(2) {
                        .item {
                            background-color: #ebf9f9;

                            .item-label {
                                color: #32b2b2;
                            }
                        }
                    }
                }
            }

            .item {
                flex: 1;
                border-radius: 8px;
                height: 90px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 10px;
                color: #333333;

                .item-label {
                    font-weight: 600;
                    font-size: 30px;
                    color: #5782ec;
                    line-height: 30px;
                    text-align: center;
                    font-style: normal;

                    &.bigger {
                        font-size: 40px;
                        line-height: 40px;
                    }
                }
            }
        }
    }

    .content2 {
        background-color: #fff;
        margin-top: 20px;
    }

    .year-selected {
        margin-left: 20px;
    }

    .prefix-selected-icon {
        line-height: 40px;
        margin-left: 5px;
    }
</style>
