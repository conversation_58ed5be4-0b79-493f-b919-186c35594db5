<template>
    <div
        class="core-ui-table-container container"
        v-if="row"
        :key="refreshQueryParams"
    >
        <div class="core-ui-custom-header w-100">
            <div class="title u-flex u-row-between w-100">
                <bread-crumb :items="breadcrumbs" />
                <div class="u-flex">
                    <!-- <el-button
                        type="primary"
                        @click="toEnd"
                        v-if="
                            row.live_audit_status ===
                                liveAuditStatus.审核通过 &&
                            row.live_shelf_status === liveShelfStatus.已发布 &&
                            row.live_status === liveStatus.正在直播
                        "
                    >
                        结束直播
                    </el-button> -->
                    <el-button
                        type="primary"
                        @click="showAddLink = true"
                        v-if="
                            row.live_audit_status ===
                                liveAuditStatus.审核通过 &&
                            row.live_shelf_status === liveShelfStatus.已发布
                        "
                    >
                        回放地址
                    </el-button>
                    <template
                        v-if="
                            row.live_audit_status === liveAuditStatus.审核通过
                        "
                    >
                        <el-button
                            v-if="
                                row.live_shelf_status === liveShelfStatus.未发布
                            "
                            type="primary"
                            @click="toggleShell(row)"
                        >
                            上架
                        </el-button>
                        <el-button
                            v-else
                            type="primary"
                            @click="toggleShell(row)"
                        >
                            下架
                        </el-button>
                    </template>
                    <el-button
                        v-if="
                            row.live_audit_status !==
                                liveAuditStatus.审核通过 ||
                            row.live_shelf_status === liveShelfStatus.未发布
                        "
                        type="primary"
                        @click="edit(row)"
                        plain
                    >
                        编辑
                    </el-button>
                    <el-button
                        v-if="row.live_audit_status === liveAuditStatus.待审核"
                        type="primary"
                        @click="showAudit = true"
                    >
                        审核
                    </el-button>
                    <el-button
                        v-if="row.live_audit_status === liveAuditStatus.草稿"
                        type="primary"
                        @click="toAudit(row)"
                    >
                        提交审核
                    </el-button>
                </div>
            </div>
        </div>
        <div class="content bg-white u-p-30">
            <div class="title u-flex">
                <div>直播基本信息</div>
                <div class="u-m-l-10 status">
                    {{ row.live_audit_status_label }}
                </div>
                <div class="u-m-l-10 u-font-12 color-6">
                    {{ row.live_shelf_status_label }}
                </div>
            </div>
            <detail-row-col
                :list="computeList"
                :labelStyle="labelStyle"
            ></detail-row-col>
        </div>
        <div class="u-p-y-30 u-m-t-20">
            <el-tabs v-model="current">
                <el-tab-pane label="直播内容" name="0" lazy>
                    <Tab1 :detail="row"></Tab1>
                </el-tab-pane>
                <el-tab-pane v-if="hide" label="直播推广" name="1" lazy>
                    <Tab2 :detail="row"></Tab2>
                </el-tab-pane>
                <el-tab-pane label="操作日志" name="2" lazy>
                    <Tab3 :detail="row"></Tab3>
                </el-tab-pane>
            </el-tabs>
        </div>
        <Pop v-model="showCount" :detail="row"></Pop>
        <CommonPop
            labelWidth="140px"
            v-model="showAudit"
            title="审核"
            sdkModel="live_broadcast"
            sdkAction="setAudit"
            @refresh="init"
            :id="row.id"
        />
        <CommonPop
            labelWidth="110px"
            v-model="showAddLink"
            title="回放地址"
            sdkModel="live_broadcast"
            sdkAction="add_link"
            @refresh="init"
            :id="row.id"
        />
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import moment from "moment"
    import { Component, Vue } from "vue-property-decorator"
    import {
        LiveAuditStatus,
        LiveShelfStatus,
        LiveStatus,
        predict,
        toAudit,
        toggleShell,
    } from "."
    import Tab1 from "./components/tab-1.vue"
    import Tab2 from "./components/tab-2.vue"
    import Tab3 from "./components/tab-3.vue"
    import Pop from "./components/pop.vue"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { config, EnvProject } from "@/config"
    import { MessageBox } from "element-ui"

    @Component({
        name: routesMap.recruit.live.detail,
        components: { DetailRowCol, Tab1, Tab2, Tab3, Pop, CommonPop },
    })
    export default class Template extends Vue {
        private id = ""
        private row: any = null
        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.live.detail,
        }

        private current = "0"
        private showAudit = false
        private showAddLink = false

        private hide = ![EnvProject.咸丰项目].includes(config.envProject)

        private liveAuditStatus = LiveAuditStatus
        private liveShelfStatus = LiveShelfStatus
        private liveStatus = LiveStatus

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: "直播管理详情",
                    to: {
                        name: routesMap.recruit.live.detail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.live.detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private labelStyle = {
            fontSize: "14px",
            marginRight: "10px",
            lineHeight: "34px",
            "word-break": "keep-all",
            width: "auto",
            color: "#555",
        }

        private get from() {
            return this.$route.query.from as string
        }

        mounted() {
            this.init()
        }
        // 2024-12-18 18:15:55/2024-12-19 00:00:00

        private get computeList() {
            const h = this.$createElement
            return [
                {
                    label: "直播标题",
                    value: this.row.live_title || "",
                },
                {
                    label: "直播类型",
                    value: this.row.live_type_label || "",
                },
                {
                    label: "直播时间",
                    value: [this.row.live_start_time, this.row.live_end_time]
                        .map((e) => moment(e).format("yyyy-MM-DD HH:mm"))
                        .join(" 至 "),
                },
                {
                    label: "直播间封面图",
                    vNode: this.row.live_cover
                        ? h("el-image", {
                              attrs: {
                                  src: sdk.buildImage(this.row.live_cover),
                                  previewSrcList: [
                                      sdk.buildImage(this.row.live_cover),
                                  ],
                              },
                              style: {
                                  width: "116px",
                                  height: "65px",
                              },
                          })
                        : h("span", "暂无"),
                },
                {
                    label: "预约人数",
                    vNode: h(
                        "div",
                        {
                            class: "u-flex u-col-top",
                        },
                        [
                            h(
                                "span",
                                { class: "u-m-r-10" },
                                this.row.live_profiles || "0"
                            ),
                            h(
                                "el-button",
                                {
                                    attrs: {
                                        type: "text",
                                    },
                                    class: "primary",
                                    on: {
                                        click: this.toggleCount,
                                    },
                                },
                                "查看预约人数"
                            ),
                        ]
                    ),
                },
                {
                    label: "直播推广渠道",
                    value: this.row.live_channels || "",
                },
                {
                    label: "直播回放地址",
                    value: this.row.live_url || "",
                },
            ].map((i) => ({ ...i, span: 12 }))
        }

        private showCount = false
        private toggleCount() {
            this.showCount = true
        }

        private init() {
            this.id = (this.$route.query.id as string) || this.id || ""
            this.setBreadcrumbs()
            if (!this.id) return
            pageLoading(() => {
                return sdk.core
                    .model("live_broadcast")
                    .detail(this.id, "detail_operator")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                        console.log("r1", JSON.parse(JSON.stringify(this.row)))
                    })
            })
        }

        private toggleShell(row: any) {
            toggleShell(row).then(() => {
                this.init()
            })
        }

        private toAudit(row: any) {
            toAudit(row).then(() => {
                this.init()
            })
        }

        private edit(row: any) {
            this.$router.push({
                name: routesMap.recruit.live.add,
                query: {
                    id: row?.id || undefined,
                    from: this.$route.name,
                },
            })
        }

        private toEnd() {
            MessageBox.confirm(`确认结束直播？`, "提示", {
                beforeClose: (actionMsg, instance, done) => {
                    if (actionMsg !== "confirm") {
                        return done()
                    }
                    instance.confirmButtonLoading = true
                    sdk.core
                        .model("live_broadcast")
                        .action("set_end")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.row.id }],
                        })
                        .execute()
                        .then(() => {
                            done()
                            this.init()
                        })
                        .finally(() => {
                            instance.confirmButtonLoading = false
                        })
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";

    ::v-deep .detail-row .item {
        line-height: 34px;
        font-size: 14px;
        color: #333;
        margin: 0;
    }
    .content {
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
            .status {
                line-height: 16px;
                font-size: 12px;
                font-weight: 400;
                background-color: #d7f8ea;
                color: #22bd7a;
                padding: 0 8px;
            }
        }
    }
</style>
