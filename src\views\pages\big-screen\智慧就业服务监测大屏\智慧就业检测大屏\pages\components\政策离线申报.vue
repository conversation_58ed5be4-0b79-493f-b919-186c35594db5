<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="list-container"
            :showExpand="false"
            :defaultFilterIsExpand="true"
            :outCustomTotal="outCustomTotal"
            @getData="loadingChange"
        >
            <div slot="table" slot-scope="{ data, emptyText }">
                <common-table
                    :emptyText="emptyText"
                    :data="data"
                    :columns="columns"
                >
                    <div slot="order" slot-scope="scope">
                        {{ Number(scope.index) + 1 }}
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center handler-btn"
                        slot-scope="scope"
                    >
                        <div
                            v-if="scope.row.profile_access_key"
                            @click="toDetail(scope.row)"
                        >
                            详情
                        </div>
                        <div v-else>--</div>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import { FormType } from "@/core-ui/component/form"
    import { TableConfig, TableFilter } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { sdk } from "@/service"
    import { ListTypes } from "uniplat-sdk"
    import { Component, Prop } from "vue-property-decorator"
    import {
        predict2,
        column2,
    } from "@/views/pages/publish-policy/components/apply-list.vue"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { maskMobile, maskName } from "../../common/tools"
    import { cloneDeep } from "lodash"
    import { routesMap } from "@/router/direction"
    const tableFilter: TableFilter[] = [
        {
            label: "申报人：",
            option: { placeholder: "请输入" },
            type: FormType.Text,
            prop: "name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.exact,
            },
        },
        {
            label: "政策名称：",
            option: { placeholder: "请输入" },
            type: FormType.Text,
            prop: "policy_name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.exact,
            },
        },
        {
            label: "状态：",
            prop: "status",
            type: FormType.Select,
        },
        {
            label: "处理人：",
            option: { placeholder: "请输入" },
            type: FormType.Text,
            prop: "real_name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.exact,
            },
        },
        {
            label: "办理机构：",
            option: { placeholder: "请输入" },
            type: FormType.Text,
            prop: "handle_org",
            keyValueFilter: {
                match: ListTypes.filterMatchType.exact,
            },
        },
        {
            label: "所在区域：",
            type: FormType.Select,
            prop: "region_code",
            option: {
                multiple: true,
            },
        },
    ]
    @Component({ components: { TableContainer, CommonTable } })
    export default class Template extends BaseTableController<any> {
        @Prop({ default: 10 })
        private cusPageSize!: number

        private get type(): string {
            return (this.$route.query?.type as string) || ""
        }

        private get form_id(): string {
            return (this.$route.query?.form_id as string) || ""
        }

        private get form_id__in(): string {
            return (this.$route.query?.form_id__in as string) || ""
        }

        protected pageSizes = [10]

        private outCustomTotal = 0

        tableConfig: TableConfig | null = null

        created() {
            this.pageSizes = [this.cusPageSize]
        }

        private loading = true

        private loadingChange() {
            this.loading = false
            this.setTotal()
        }

        public setTotal() {
            if (this.$route.query?.cIndex) {
                this.outCustomTotal = (this.$route.query?.cIndex as any) * 1
                ;(this.table as any).total = (this.$route.query?.cIndex as any) * 1
            }
        }

        private get emptyText() {
            return this.loading ? "加载中..." : "暂无数据"
        }

        mounted() {
            const preFilter = this.form_id__in
                ? { form_id__in: this.form_id__in }
                : { form_id: this.form_id }
            this.tableConfig = {
                model: sdk.core
                    .model("policy_form_apply")
                    .list("apply_profile_for_policy"),
                defaultPage: "离线申报",
                filter: cloneDeep(tableFilter),
                oneTab: true,
                defaultPageSize: this.pageSizes[0],
                predict: predict2,

                handleFilterData: (params) => {
                    const res: any = {
                        ...params,
                    }
                    if (!res.region_code) {
                        res.region_code = "420901"
                    }
                    return res
                },
                preFilter,
                // handleFilterData: (params: any) => {
                //     return params
                // },
            }
            if (this.type.includes("办结数")) {
                this.tableConfig.filter?.forEach((i) => {
                    if (i.prop === "status") {
                        i.defaultValue = "1"
                    }
                })
            }
        }

        private readonly columns: any[] = column2
            .map((i) => {
                if (i.prop === "real_name") {
                    return {
                        label: "申报人",
                        prop: "real_name",
                        formatter: (row: any) => {
                            return maskName(row.real_name)
                        },
                        showOverflowTip: true,
                    }
                }
                if (i.prop === "contact_mobile") {
                    return {
                        label: "联系方式",
                        prop: "contact_mobile",
                        formatter: (row: any) => {
                            return maskMobile(row.contact_mobile)
                        },
                        showOverflowTip: true,
                    }
                }
                return i
            })
            .concat([
                {
                    label: "操作",
                    prop: "h",
                },
            ])

        toDetail(row: any) {
            const params = {
                id: row.profile_access_key || "",
                type: (this.$route.query?.type as string) || "",
                cIndex: this.$route.query?.cIndex || "",
            } as any
            if (this.form_id) {
                params.form_id = this.form_id
            }
            if (this.form_id__in) {
                params.form_id__in = this.form_id__in
            }
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.personDetail,
                query: params,
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "./table.less";
    /deep/ .table {
        background: transparent !important;
        padding: 0;
    }

    /deep/.table-tabs {
        display: none;
    }
</style>
