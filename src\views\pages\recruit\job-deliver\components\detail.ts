import { config, EnvProject } from "@/config"
import { BuildFormConfig, FormType } from "@/core-ui/component/form"

export function createFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_candidate_order",
        sdkAction: "interview",
        id,
        forms: [
            {
                label: "面试时间",
                type: FormType.DatePicker,
                option: {
                    type: "datetime",
                },
                prop: "interview_time",
                required: true,
                rules: [
                    {
                        validator: (rule, value, callback) => {
                            if (+new Date(value) < +new Date()) {
                                callback(new Error("面试时间需大于当前时间"))
                            } else {
                                callback()
                            }
                        },
                    },
                ],
            },
            {
                label: "面试地点",
                type: FormType.Text,
                prop: "interview_address",
                required: true,
            },
        ],
    }
}

export function statusFormConfig(
    select_list: any,
    isBatch: boolean
): BuildFormConfig {
    return {
        sdkModel: "xg_candidate_order",
        sdkAction: isBatch ? "batch_audit_feedback" : "audit_feedback",
        select_list,
        forms: !isBatch
            ? [
                  {
                      label: "投递状态",
                      type: FormType.Select,
                      prop: "status",
                      required: true,
                      needListen: true,
                  },
                  {
                      label: "面试类型",
                      type: FormType.Select,
                      prop: "interview_type",
                      required: true,
                      needListen: true,
                  },
                  {
                      label: "面试时间",
                      type: FormType.DatePicker,
                      option: {
                          type: "datetime",
                      },
                      prop: "interview_time",
                      required: true,
                      rules: [
                          {
                              validator: (rule, value, callback) => {
                                  if (+new Date(value) < +new Date()) {
                                      callback(
                                          new Error("面试时间需大于当前时间")
                                      )
                                  } else {
                                      callback()
                                  }
                              },
                          },
                      ],
                      needListen: true,
                  },
                  {
                      label: "面试地点",
                      type: FormType.Text,
                      prop: "interview_address",
                      needListen: true,
                  },
              ]
            : [
                  {
                      label: "投递状态",
                      type: FormType.Select,
                      prop: "status",
                      required: true,
                  },
              ],
    }
}

export function qualificationFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_candidate_order",
        sdkAction: "qualification_audit",
        id,
        forms: [
            {
                label: "审核资格",
                type: FormType.Select,
                prop: "qualification_audit_status",
                required: true,
                needListen: true,
            },
            {
                label: "不符合原因",
                type: FormType.Text,
                prop: "qualification_audit_memo",
                required: true,
                option: {
                    maxlength: 20,
                    showWordLimit: true,
                    placeholder: "请输入最多20个字符",
                },
                needListen: true,
                handlerDisplay: (data) => {
                    // eslint-disable-next-line eqeqeq
                    return data.qualification_audit_status == 2
                },
            },
        ],
    }
}
