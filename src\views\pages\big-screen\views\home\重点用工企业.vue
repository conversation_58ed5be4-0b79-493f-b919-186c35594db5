<template>
    <card :text="title">
        <div class="d-flex flex-wrap container">
            <div
                v-for="(item, index) in items"
                :key="index"
                :style="hz ? 'width:25%' : ''"
            >
                <div class="circle d-flex flex-column">
                    <!-- <span>{{ item.value }}</span> -->
                    <span class="text-truncate-3">{{ item.label }}</span>
                </div>

                <div class="text d-flex-item-center">
                    <div>
                        共
                        <span>{{ item.value }}</span>
                        家
                    </div>
                    <!-- <div>
                        招工<span class="last">{{ item.require }}</span>
                    </div> -->
                </div>
            </div>
        </div>
    </card>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import { take } from "lodash"
    import { Component } from "vue-property-decorator"
    import Base from "../../common/base-card"
    import { ChartQueryResultItem } from "../../model"

    @Component({ components: {} })
    export default class Index extends Base {
        private items: {
            value: number
            label: string
        }[] = []

        private total = 0
        private hz = [EnvProject.黄州项目].includes(config.envProject)
        get title() {
            if (this.hz) {
                return `重点企业`
            } else {
                return `重点用工企业 ${this.total} 家`
            }
        }

        created() {
            return this.query<ChartQueryResultItem[]>("important_company").then(
                (r) => {
                    const items: {
                        value: number
                        label: string
                    }[] = r.map((i) => ({
                        label: i.key_name,
                        value: i.key_value,
                    }))
                    this.total = _.sum(items.map((i) => i.value))
                    // for (let i = 0; i < items.length; i++) {
                    //     items[i].itemStyle = {
                    //         color: colors[i] || colors[i % colors.length],
                    //     }
                    //     items[i].name += ` ${items[i].value}`
                    //     this.total += items[i].value
                    // }
                    this.items = items
                    return items
                }
            )
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .container {
        padding: 16px 0 2px 5px;
        // gap: 0px 19px;
        min-height: 160px;

        > div {
            margin: 0 0 12px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 50%;
            margin-bottom: 15px;
        }
    }

    .circle {
        background-image: url("./../../assets/circle-80.png");
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;

        span {
            // color: #fff;
            // font-size: 20px;
            // line-height: 24px;
            // max-width: 55px;
            // text-align: center;
            // & + span {
            //     font-size: 12px;
            //     color: #89b9ff;
            //     line-height: 14px;
            // }
            line-height: 24px;
            max-width: 55px;
            text-align: center;
            font-size: 12px;
            color: #89b9ff;
            line-height: 14px;
        }
    }

    .text {
        color: #89b9ff;
        font-size: 12px;
        transform: scale(0.9);
        max-width: 110px;
        // overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 10px;
        text-align: center;
        margin-left: -2px;
        display: flex;
        gap: 12px;

        span {
            color: #fdc850;
            transform: scale(0.8);
            // position: relative;
            // left: -1px;
            letter-spacing: -0.1px;

            // &.last {
            //     left: -2px;
            // }
        }
    }
</style>
