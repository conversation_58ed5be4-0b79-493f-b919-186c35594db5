<template>
    <div class="bg-white">
        <DetailTable
            :tableColumns="tableColumns"
            :tableRows="tableRows"
            :rows="rows"
            :detail="detail"
            :isEditing="false"
            ref="detailTable"
            @toDetail="toDetail"
            @toTaskDetail="toTaskDetail"
        />
        <div class="u-flex u-row-center u-m-t-10">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="index"
                :page-sizes="pageSizes"
                :page-size="size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
        > -->
        <!-- <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white"> -->
        <!-- <common-table :data="tableRows" :columns="columns">
            <div slot="h" class="u-flex u-row-center" slot-scope="scope">
                <el-button type="text" @click="toDetail(scope.row)">
                    详情
                </el-button>
            </div>
        </common-table> -->
        <!-- </div>
        </table-container> -->
    </div>
</template>

<script lang='ts'>
    import { Component, Prop } from "vue-property-decorator"
    import {
        BaseTable,
        BaseTableController,
    } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import {
        TableColumn,
        TableConfig,
        TableFilter,
    } from "@/core-ui/component/table"
    import { sdk } from "@/service"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import DetailTable from "../../work-info-audit/components/detail-table.vue"
    import { pageLoading } from "@/views/controller"
    import { routesMap } from "@/router/direction"
import { ReportItem } from "../../work-info-audit"

    @Component({
        components: { CommonTable, TableContainer, ExcelImport, DetailTable },
    })
    export default class RecordList extends BaseTable<any> {
        @Prop()
        detail!: any

        private showImportPop = false

        private tableColumns: TableColumn[] | any[] = []
        private tableRows: any[] = []
        private rows: any[] = []

        mounted() {
            this.onFilterChanged()
        }

        protected async onFilterChanged() {
            pageLoading(() => {
                return Promise.all([this.getColumns(), this.getData()]).then(
                    (r: any[]) => {
                        this.tableColumns = r[0].schema?.map((i: ReportItem) => {
                            return {
                                label: i.display_name,
                                prop: i.p_union_code,
                                value_type: i.value_type,
                                showOverflowTip: true,
                                column_type: i.type,
                                default_value: i.default_value,
                                computer_item: i.computer_item,
                                minWidth: "130px",
                                children:
                                    i.children?.map((j: ReportItem) => {
                                        return {
                                            label: j.display_name,
                                            prop: j.p_union_code,
                                            showOverflowTip: true,
                                            children: j.children || [],
                                            value_type: j.value_type,
                                            column_type: j.type,
                                            default_value: j.default_value,
                                            computer_item: j.computer_item,
                                            minWidth:
                                                j.display_name.length > 10
                                                    ? `${
                                                          j.display_name.length * 13
                                                      }px`
                                                    : "94px",
                                        }
                                    }) || [],
                            }
                        })
                        this.tableColumns.forEach((i) => {
                            if (i.prop === "company_task_name") {
                                i.fixed = "left"
                            }
                            if (
                                i.prop === "company_task_record_status" ||
                                i.prop === "company_task_report_create_time"
                            ) {
                                i.minWidth = "150px"
                                i.fixed = "right"
                            }
                        })
                        this.tableColumns.push({
                            label: "操作",
                            prop: "h",
                            fixed: "right",
                        })
                        this.total = +r[1].total
                        this.rows = r[1].reports
                        this.tableRows = this.rows.map((item: any) => {
                            return item.report.reduce((acc: any, cur: any) => {
                                acc[cur.p_union_code] = cur.indicator_value
                                acc.record_access_key = item.record_access_key
                                acc.task_access_key = item.task_access_key
                                return acc
                            }, {})
                        })
                    }
                )
            })
        }

        private getColumns() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_report_indicator_for_company"
                )
                .post<{ schema: any[] }>({
                    comany_code: this.detail?.company_code,
                })
        }

        private getData() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_company_code_record_report_all"
                )
                .post<{ schema: any[] }>({
                    company_code: this.detail?.company_code,
                    page_index: this.index,
                    page_size: this.size,
                })
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.employmentManage.hrInfoManage.workInfoAuditCheck,
                query: {
                    key: row.record_access_key + "",
                },
            })
        }

        private toTaskDetail(row: any) {
            this.$router.push({
                name: routesMap.employmentManage.hrInfoManage.workInfoApplyDetail,
                query: {
                    id: row.task_access_key + "",
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
