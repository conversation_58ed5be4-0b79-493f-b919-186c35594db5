import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import {
    rowPredict as rowCommonPredict,
    checkColumn as checkCommonColumn,
} from "@/views/zq-company/hr-info-manage/work-demand-apply/index"
import { Status } from "../work-info-audit"
import { split } from "lodash"

const tableFilter: TableFilter[] = [
    {
        label: "公司名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_province_region_code",
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
]

export const rowPredict = {
    agent_name: "tg_enterprise#name",
    title: "",
    upload_date: "",
    update_time: "",
    xuqiu_fill_count: "",
    status: "label",
    description: "",
    file: "company_task_file_record#file_id",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("company_task_record")
            .list("list_for_plat_xuqiu"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        column,
        oneTabFilter: true,
    }
}

export const enum AuthStatus {
    待认证 = 0,
    已认证 = 1,
    认证不通过 = 2,
}

export interface Row {
    /** 企业名称 */
    agent_name: string

    /** 统—社会信用代码 */
    company_code: string

    /** 法人 */
    legal_person: string

    /** 法人身份证号 */
    legal_card_open_id: string

    /** 区域名称 */
    region_name: string

    /** 企业所在地详细地址 */
    address_detail: string

    /** 联系人姓名 */
    contact_person: string

    /** 联系人姓名电话 */
    contact_mobile: string

    /** 所属行业 */
    industory_catalog: string

    /** 所属行业[文本] */
    industory_catalog_label: string

    /** 状态 */
    auth_status: AuthStatus

    /** 状态[文本] */
    auth_status_label: string
    status: Status
    id: number
    v: number
}

export const column: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "调查标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "上报时间",
        prop: "upload_date",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.upload_date)
        },
    },
    {
        label: "文件",
        prop: "file",
        showOverflowTip: true,
        render: (h, row: any) => {
            return h(
                "a",
                {
                    class: "primary",
                    attrs: {
                        href: sdk.buildFilePath(row.file || ""),
                        target: "__blank",
                    },
                },
                split(row.file, "__")[1]
            )
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const column1: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "调查标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "审核时间",
        prop: "update_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.update_time)
        },
    },
    {
        label: "文件",
        prop: "file",
        showOverflowTip: true,
        render: (h, row: any) => {
            return h(
                "a",
                {
                    class: "primary",
                    attrs: {
                        href: sdk.buildFilePath(row.file || ""),
                        target: "__blank",
                    },
                },
                split(row.file, "__")[1]
            )
        },
    },
    {
        label: "审核结果",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "220",
        showOverflowTip: true,
    },
]

export const column2: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "调查标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "描述",
        prop: "description",
        showOverflowTip: true,
    },
    {
        label: "文件",
        prop: "file",
        showOverflowTip: true,
        render: (h, row: any) => {
            return h(
                "a",
                {
                    class: "primary",
                    attrs: {
                        href: sdk.buildFilePath(row.file || ""),
                        target: "__blank",
                    },
                },
                split(row.file, "__")[1]
            )
        },
    },
    {
        label: "状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "220",
        showOverflowTip: true,
    },
]

export function buildItems(rows: Row | {}) {
    const row = (rows || {}) as Row
    return [
        { label: "公司名称：", value: row.agent_name || "" },
        { label: "填报次数：", value: row.company_code || "" },
        { label: "当前任务状态：", value: row.company_code || "34342" },
        // { label: "服务区域：", value: row.company_code || "" },
        // {
        //     label: "人力资源许可证：",
        //     value: row.company_code || "",
        //     // vNode: handlePreImg(row.company_code, vue),
        // },
        // { label: "业务介绍：", value: row.company_code || "" },
    ].map((i) => {
        return { ...i, span: 8 }
    })
}

export const commonColumn: TableColumn[] = [
    ...checkCommonColumn,
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const checkColumn: TableColumn[] = [
    { type: "selection", prop: "select" },
    ...commonColumn,
]

export const checkColumn1: TableColumn[] = [...commonColumn]

export const checkColumn2: TableColumn[] = [
    ...commonColumn.filter((i) => {
        return i.prop !== "h"
    }),
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const checkRowPredict = {
    ...rowCommonPredict,
}

export function checkTableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("company_task_wroker_require_record")
            .list("list_for_review"),
        filter: [],
        defaultPageSize: 10,
        predict: checkRowPredict,
        column: checkColumn1,
        oneTabFilter: true,
    }
}
