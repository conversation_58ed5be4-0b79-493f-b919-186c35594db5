import { reduce } from "lodash"
import { company } from "./route-company"
import { hr } from "./route-hr"
import { config, EnvProject } from "@/config"
import { grid } from "./route-grid"
import { shucai } from "./direction-sc"

const xiaogan = {
    home: {
        root: "",
        page: "",
        qjPage: {
            root: "",
            index: "",
        },
        news: "",
        // 注册用户管理
        users: "",
        // 劳动力信息库
        collectTaskManage: "",
        // 市场主体信息管理
        enterpriseMange: "",
        // 求职招聘服务提升
        job: "",
        // 重点人群服务跟踪管理
        groupService: "",
        // 政策与资讯
        policy: "",
        // 就业指标管理
        employmentTask: "",
        // 专项工作报表管理
        report: "",
        // 数据局核查管理
        dataCheck: "",
        // 零工驿站管理
        jobStation: "",
        // 资金使用计划和内审
        financial: "",
        // 数据仓库管理
        dataStorage: "",
        // 系统基础信息及数据管理
        base: "",
        // 培训管理
        train: "",
        // 基层服务
        baseService: "",
        // 企业用工信息库
        qjCompanyTaskManage: "",
    },
    qqxbJob: {
        qqxbJob: "",
        my: "",
    },
    labourManage: {
        root: "",
        seekerInfo: "",
        seekerDetail: "",
        recordList: "",
        recordDetail: "",
        seekerQuestion: "",
        seekerQuestionStatistics: "",
        seekerQuestionServiceList: "",
        seekerQuestionModel: "",
        serviceRecordManage: "",
        userAccount: "",
        userAccountDetail: "",
        dimission: "",
        dimissionDetail: "",
        registerStatistics: "",
        aspirationCollect: "",
        xcdManage: "",
        xcdDetail: "",
    },
    dataAcquisition: {
        root: "",
        labour: "",
        returnHome: "",
        analyze: "",
        empBigScreen: "",
    },
    employmentManage: {
        employmentManage: "",
        companyManage: "",
        companyManageDetail: "",
        companyManageAdd: "",
        humanResourceCheck: "",
        humanResourceCheckDetail: "",
        humanResource: "",
        humanResourceDetail: "",
        humanResourceAdd: "",
        subsidy: "",
        subsidyDetail: "",
        apply: "",
        questionnaire: "",
        questionnaireStatistics: "",
        questionnaireDetail: "",
        questionnaireAdd: "",
        companyQuestionnaire: "",
        companyJoinCms: "",
        questionnaireDetailReadonly: "",
        enterpriseSettledApply: "",
        marketMain: "",
        marketMainDetail: "",
        blacklist: "",
        blacklistDetail: "",
        hrInfoManage: {
            hrInfoManage: "",
            staffList: "",
            staffListDetail: "",
            workInfoApply: "",
            workInfoApplyAdd: "",
            workInfoApplyDetail: "",
            workInfoApplyRecord: "",
            workInfoAudit: "",
            workInfoAuditCheck: "",
            workDemandApply: "",
            workDemandApplyAdd: "",
            workDemandApplyDetail: "",
            workDemandApplyRecord: "",
            workDemandAudit: "",
            workDemandAuditCheck: "",
        },
    },
    groupService: {
        school: {
            index: "",
            add: "",
            serviceRecordManage: "",
            serviceRecordManageDetail: "",
        },
        groupService: "",
        serviceManage: "",
        serviceManageList: "",
        serviceManageCreate: "",
        serviceManageEdit: "",
        serviceCreate: "",
        serviceTaskCreate: "",
        serviceManageDetail: {
            root: "",
            detail: "",
            detailItem: {
                content: "",
                task: "",
                record: "",
            },
            taskRoot: "",
            task: "",
            taskContent: {
                collect: "",
                notify: "",
                policy: "",
                job: "",
                question: "",
            },
            result: {
                serve_target_count: "",
                serve_qy_target_count: "",
                served_target_count: "",
                served_qy_target_count: "",
                serve_record_count: "",
                apply_job_count: "",
                answered_questionnaire_count: "",
            },
            item: "",
        },
        taskManage: "",
        recordManage: "",
    },
    financial: {
        financial: "",
        toolkit: {
            toolkit: "",
            checkList: "",
            checkListAdd: "",
            checkListDetail: "",
            policyBasis: "",
            policyBasisDetail: "",
            policyBasisAdd: "",
        },
        fundsPlan: "",
        fundsPlanAdd: "",
        fundsPlanDetail: "",
        fundsRecord: "",
        fundsRecordAdd: "",
        fundsRecordDetail: "",
        manage: "",
        manageAdd: "",
        manageDetail: "",
        funds: "",
        fundsAdd: "",
        fundsDetail: "",
    },
    dataStorage: {
        dataStorage: "",
        resourceManage: "",
        resourceManageAdd: "",
        resourceManageDetail: "",
        resourceManageAddItem: "",
        comparisonManage: "",
        comparisonManageDetail: "",
        comparisonManageAdd: "",
        applyForm: "",
        applyFormAdd: "",
        applyFormDetail: "",
        queryUsageRecord: "",
        queryUsageRecordDetail: "",
        collectManage: "",
        collectDetailManage: "",
        personDataCompare: "",
        personDataCompareDetail: "",
        personDataCompareTask: "",
        personDataCompareTaskDetail: "",
    },
    jobBoards: {
        root: "",
        jobBoards: "",
    },
    live: {
        root: "",
        job: "",
        message: "",
    },
    recruit: {
        statistics: "",
        orderList: "",
        gridList: "",
        recruit: "",
        jobIndex: "",
        job: "",
        jobDetail: "",
        jobContactDetail: "",
        jobTrackDetail: "",
        jobDeliverIndex: "",
        jobDeliver: "",
        jobDeliverDetail: "",
        publicJobDeliver: "",
        publicJobDeliverDetail: "",
        publicJobIndex: "",
        publicJob: "",
        publicJobDetail: "",
        jobEdit: "",
        publicJobEdit: "",
        person: "",
        personJobAutoMatch: "",
        personJobAutoMatchDetail: "",
        personJobAutoMatchPositionDetail: "",
        personDetail: "",
        personIntelligentRecommendDetail: "",
        grid: "",
        gridDetail: "",
        group: "",
        hotJob: "",
        hotJobDetail: "",
        groupDetail: "",
        cooperation: "",
        cooperationDetail: "",
        jobStatisticsGroup: "",
        jobFairStatics: "",
        specialRecruitment: "",
        recruitmentActivity: "",
        jobFair: "",
        jobFairIndex: "",
        jobFairDetail: "",
        addJobFair: "",
        auditDetail: "",
        applyDetail: "",
        agents: "",
        agentsDetail: "",
        agentsJobDetail: "",
        agentAudit: "",
        workIndex: "",
        work: "",
        workDetail: "",
        workAdd: "",
        area: "",
        areaDetail: "",
        liveManage: "",
        complaintIndex: "",
        complaint: "",
        complaintDetail: "",
        labourServiceComplaint: "",
        labourServiceComplaintDetail: "",
        DataResourceStatistics: "",
        positionCollectedIndex: "",
        positionCollected: "",
        positionCollectedAdd: "",
        positionCollectedDetail: "",
        live: {
            root: "",
            index: "",
            add: "",
            detail: "",
            video: "",
            videoDetail: "",
            videoAdd: "",
            train: "",
            trainDetail: "",
            trainAdd: "",
        },
    },
    wx: {
        root: "",
        group: "",
        groupDetail: "",
        persons: "",
        friend: "",
        friendDetail: "",
        statistics: "",
        groupStatistics: "",
        statisticsList: "",
        wxFriendList: "",
        qr: "",
        assistant: "",
    },
    policy: {
        policy: "",
        manage: "",
        create: "",
        updatePcPolicy: "",
        manageDetail: "",
        declarationStatistics: "",
    },
    publishPolicy: {
        list: "",
        formList: "",
        formDetail: "",
        create: "",
        policyDetail: "",
        reportDetail: "",
        batchDetail: "",
    },
    publishPolicyDD: {
        list: "",
        formList: "",
        formDetail: "",
        create: "",
        policyMange: "",
        policyDetail: "",
        reportDetail: "",
    },
    policyApplyRecord: {
        list: "",
        detail: "",
    },
    policyApplyRecordDD: {
        list: "",
        list2: "",
        detail: "",
        audit: "",
    },
    preferentialPolicy: {
        preferentialPolicy: "",
        manage: "",
        create: "",
        manageDetail: "",
        apply: "",
    },
    jobStation: {
        root: "",
        manage: "",
        manageDetail: "",
        statistics: "",
        jobStationStatisticsDetail: "",
        recommendStatisticsDetail: "",
        shareStatisticsDetail: "",
        brandManage: "",
        deviceMange: "",
        deviceDetail: "",

        ban: "",
        inviteLog: "",
        publishSeekInfo: "",
        publishRecruitmentInfo: "",
        hzBigScreen: "",
        hzJobFairBigScreen: "",
    },
    base: {
        root: "",
        org: {
            list: "",
        },
        role: {
            list: "",
            detail: "",
        },
        user: {
            list: "",
        },
        grid: {
            list: "",
        },
        mp: {
            list: "",
            listDetail: "",
            business: "",
            tools: "",
            groupEntrance: "",
            advertisingPosition: "",
            mpRoleManage: "",
            kefu: "",
        },
        sms: {
            list: "",
            add: "",
            detail: "",
        },
        registerUser: {
            list: "",
            detail: "",
        },
        operateLog: "",
        gzwBanner: "",
        gzwGanggao: "",
        smsTemplate: {
            list: "",
            add: "",
            send: "",
        },
        gridUserManage: {
            list: "",
        },
        regionUserManage: {
            list: "",
            add: "",
            manage: "",
            manageAdd: "",
        },
        csQrCode: "",
        userAgreement: {
            list: "",
            add: "",
        },
        equipmentManagement: "",
        automatic: "",
        statisticalMnalysis: "",
        equipment: "",
        callList: "",
        smartEmploymentMonitor: "",
        channel: {
            index: "",
        },
    },
    ...shucai,
    companyTaskManage: {
        root: "",
        index: "",
        list: "",
        search: "",
    },
    policyDataBase: {
        root: "",
        index: "",
        detail: "",
        add: "",
        screen: "",
        other: "",
        otherDetail: "",
        otherSource: "",
        otherSourceDetail: "",
    },
    employmentTask: {
        root: "",
        taskList: "",
        addTask: "",
        taskDetail: "",
        warning: "",
        screen: "",
    },
    dataCheck: {
        root: "",
        generalToolIndex: "",
        generalToolDetail: "",
        generalIndex: "",
        generalDetail: "",
        specialIndex: "",
        specialAdd: "",
        specialDetail: "",
    },
    laborInfoBaseManage: {
        root: "",
        laborInfoBase: "",
        laborInfoBaseFilter: "",
        laborInfoBaseDetail: "",
        manageRegionIncomplete: "",
        manageBackHome: "",
        manageBackHomeFilter: "",
        manageBackHomeDetail: "",
        manageStudent: "",
        manageStudentFilter: "",
        manageStudentDetail: "",
        laborServicesBrand: "",
        laborServicesBrandCreateBrand: "",
        laborServicesBrandDetail: "",
        uploadManage: "",
        managePushProvince: "",
        managePushProvinceDetail: "",
    },
    dataAcquisitionLabour: "",
    bigScreen: {
        nav: "",
        home: "",
        jobServiceMonitor: "",
        selfCleaningMonitor: "",
        keyGroupMonitor: "",
        policy: {
            company: "",
            companyTown: "",
            companyDetail: "",
            companyDetailTown: "",
            person: "",
            personTown: "",
            personDetail: "",
            personDetailTown: "",
        },
        returnHome: "",
        report: "",
        report1: "",
        emp: "",
        emp2: {
            index: "",
            personList: "",
            companyList: "",
            tempList: "",
        },
        important: {
            graduate: "",
            importantType1: "",
            importantType2: "",
            importantType3: "",
            importantType4: "",
            importantType5: "",
            importantType6: "",
            importantType7: "",
            importantType8: "",
            hard: "",
            out: "",
            in: "",
            noJob: "",
            hasJob: "",
        },
        qjBigscreen: { index: "" },
        task: {
            warning: "",
        },
        xgEmployment: {
            index: "",
            screen: "",
        },
        dataStatisticsAnalysisSingle: "",
        dataStatisticsAnalysis: {
            index: "",
            list1: "",
            personDetail: "",
        },
        smartEmploymentMonitor: {
            index: "",
            personDetail: "",
            companyDetail: "",
            list1: "",
            list2: "",
            list3: "",
            list9: "",
        },
    },
    bigScreen4Province: {
        nav: "",
        job: {
            home: "",
            requirement: "",
            hirement: "",
        },
        recruit: {
            home: "",
            enterprise: "",
            personal: "",
            report: "",
            reportbroadcast: "",
            reportservice: "",
        },
    },
    toolsInner: {
        index: "",
    },
    messageNotify: {
        root: "",
        message: "",
        detail: "",
    },
    credit: {
        root: "",
        index: "",
        detail: "",
        accredit: "",
        accreditDetail: "",
        bigScreen: "",
    },
    reportManage: {
        root: "",
        creator: {
            root: "",
            index: "",
            list: "",
            detail: "",
        },
        executor: {
            root: "",
            index: "",
            detail: "",
        },
        list: {
            root: "",
            index: "",
            detail: "",
        },
    },
    device: {
        root: "",
        list: "",
        brand: "",
    },
    recruitCompany: {
        root: "",
        index: "",
        companyList: "",
        companyDetail: "",
        jobList: "",
        jobDetail: "",
        companyIndex: "",
        companySearch: "",
        manage: "",
        manageAdd: "",
        manageDetail: "",
    },
    baseService: {
        root: "",
        grid: "",
        gridDetail: "",
        policy: "",
        policyDetail: "",
        jobFair: "",
        jobFairModel: {
            index: "",
            detail: "",
        },
        jobFairDetail: "",
        ledger: "",
        ledgerDetail: "",
        ledgerChartDetail: "",
    },
    biSetting: {
        root: "",
        setting: "",
    },
    branch: {
        root: "",
        service: {
            index: "",
            detail: "",
            add: "",
        },
    },
    // 设备管理
    equipmentManage: {
        root: "",
        hardwareManage: "",
        hardwareManageDetail: "",
        infoPublishIndex: "",
        infoPublishDetail: "",
        infoPublish: "",
        materialStore: "",
        materialStoreDetail: "",
        materialStoreAdd: "",
    },
}

const routesObject = {
    root: "",
    login: "",
    register: "",
    apply: "",
    applyH5: "",
    questionnaireDesign: {
        index: "",
        detail: "",
    },
    role: {
        root: "",
        no: "",
    },
    bigScreen4JobFair: {
        nav: "",
        navJingZhou4out: "",
        navHonghu4out: "",
        navCommon: "",
        statisticJobFair4out: "",
        statisticJobFair: "",
    },
    bigScreen4PeopleJob: {
        nav: "",
    },
    blacklist: { root: "", index: "", company: "" },
    showPage: "",
    upgrade: "",
    ...xiaogan,
    company,
    hr,
    grid,
}

function handlerRoutesMap(routes: typeof routesObject) {
    const computeObject = (o: any, key: string) => {
        if (typeof o === "string") {
            return [...o.split("-"), key].filter(Boolean).join("-")
        } else {
            return reduce(
                o,
                (prev, cur, k) => {
                    prev[k] = computeObject(
                        cur,
                        [key, k].filter(Boolean).join("-")
                    )
                    return prev
                },
                o as any
            )
        }
    }
    return computeObject(routes, "")
}

export const routesMap: RouteMap = handlerRoutesMap(routesObject)
export type RouteMap = typeof routesObject
export type RouteRelationConfig = Record<
    string,
    {
        route: string[]
        rule?: "include" | "all" | "exclude"
    }
>

const handleRouteKey2Array = () => {
    const rp = JSON.parse(JSON.stringify(routesMap))
    const obj = {}
    Object.keys(rp).forEach((i) => {
        const r1 = rp[i]
        let value = []
        if (typeof r1 === "string") {
            value = [r1]
        } else {
            const v1 = collectValues(r1)
            value = v1
        }

        Object.assign(obj, {
            [i]: value.flatMap((i) => i),
        })
    })
    return obj
}

function collectValues(obj: Record<string, any>) {
    const values: string[] = []

    function recursiveCollect(element: any) {
        if (typeof element === "object" && element !== null) {
            Object.values(element).forEach(recursiveCollect)
        } else {
            values.push(element)
        }
    }

    recursiveCollect(obj)

    return values
}

export const routeKey2Array: Record<string, string[]> = handleRouteKey2Array()

const xgOperateRouteRelationConfig: RouteRelationConfig = {
    // 居民管理
    [routesMap.labourManage.root]: {
        rule: "include",
        route: [
            routesMap.labourManage.root,
            // 劳动力数据分析大屏
            routesMap.dataAcquisition.labour,
        ],
    },
    // 市场主体管理
    [routesMap.employmentManage.employmentManage]: {
        rule: "include",
        route: [
            routesMap.employmentManage.employmentManage,
            // 零工驿站管理
            // routesMap.jobStation.root,
            // 市场主体数据分析大屏
            routesMap.dataAcquisition.returnHome,
            routesMap.employmentManage.hrInfoManage.hrInfoManage,
            routesMap.employmentManage.marketMain,
            routesMap.collectTaskManage.marketDatabaseManage.root,
            routesMap.dataAcquisition.empBigScreen,
            routesMap.bigScreen.xgEmployment.screen,
        ],
    },
    // 企业用工情况调查
    [routesMap.employmentManage.hrInfoManage.hrInfoManage]: {
        rule: "include",
        route: [
            routesMap.employmentManage.employmentManage,
            routesMap.employmentManage.hrInfoManage.hrInfoManage,
            // routesMap.collectTaskManage.marketDatabaseManage.root,
            routesMap.dataAcquisition.returnHome,
            routesMap.dataAcquisition.empBigScreen,
            routesMap.bigScreen.xgEmployment.screen,
        ],
    },
    // 市场主体管理
    // [routesMap.collectTaskManage.marketDatabaseManage.root]: {
    //     rule: "include",
    //     route: [
    //         routesMap.employmentManage.employmentManage,
    //         routesMap.collectTaskManage.marketDatabaseManage.root,
    //         routesMap.employmentManage.hrInfoManage.hrInfoManage,
    //         routesMap.dataAcquisition.returnHome,
    //     ],
    // },
    // 零工驿站管理
    [routesMap.jobStation.root]: {
        rule: "include",
        route: [
            // routesMap.employmentManage.employmentManage,
            // 零工驿站管理
            routesMap.jobStation.root,
            // 市场主体数据分析大屏
            // routesMap.dataAcquisition.returnHome,
            routesMap.dataAcquisition.empBigScreen,
            routesMap.bigScreen.xgEmployment.screen,
        ],
    },
    // 招聘管理
    [routesMap.live.root]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
        ],
    },
    // 招聘管理
    [routesMap.recruit.recruit]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.publicJobIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.jobDeliverIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.jobIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.workIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.complaintIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.positionCollectedIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.live.root]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruitCompany.root]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruitCompany.root,
        ],
    },
    // 重点人群跟踪服务
    [routesMap.groupService.groupService]: {
        rule: "include",
        route: [routesMap.groupService.groupService],
    },
    // 资金使用计划和内审
    [routesMap.financial.financial]: {
        rule: "include",
        route: [routesMap.financial.financial, routesMap.jobBoards.root],
    },
    // 就业看版
    [routesMap.jobBoards.root]: {
        rule: "include",
        route: [routesMap.financial.financial, routesMap.jobBoards.root],
    },
    // 数据仓库及核查工具
    [routesMap.dataStorage.dataStorage]: {
        rule: "include",
        route: [routesMap.dataStorage.dataStorage],
    },
    // 政策资讯管理与宣传推广
    [routesMap.policy.manage]: {
        rule: "include",
        route: [routesMap.policy.manage],
    },
    // 基础数据管理
    [routesMap.base.root]: {
        rule: "include",
        route: [
            routesMap.base.root,
            // 政务微信管理
            routesMap.wx.root,
            routesMap.messageNotify.root,
            // 平台黑名单
            routesMap.blacklist.root,
            routesMap.reportManage.root,
            routesMap.biSetting.root,
            routesMap.base.equipment,
        ],
    },
    [routesMap.base.equipment]: {
        rule: "include",
        route: [
            routesMap.base.root,
            // 政务微信管理
            routesMap.wx.root,
            routesMap.messageNotify.root,
            // 平台黑名单
            routesMap.blacklist.root,
            routesMap.reportManage.root,
            routesMap.biSetting.root,
            routesMap.base.equipment,
            routesMap.device.root,
        ],
    },
    // 政务微信管理
    [routesMap.wx.root]: {
        rule: "include",
        route: [
            routesMap.base.root,
            // 政务微信管理
            routesMap.wx.root,
            routesMap.messageNotify.root,
            // 平台黑名单
            routesMap.blacklist.root,
            routesMap.reportManage.root,
            routesMap.biSetting.root,
            routesMap.base.equipment,
            routesMap.device.root,
        ],
    },
    [routesMap.messageNotify.root]: {
        rule: "include",
        route: [
            routesMap.base.root,
            // 政务微信管理
            routesMap.wx.root,
            routesMap.messageNotify.root,
            // 平台黑名单
            routesMap.blacklist.root,
            routesMap.reportManage.root,
            routesMap.biSetting.root,
            routesMap.base.equipment,
            routesMap.device.root,
        ],
    },
    [routesMap.blacklist.root]: {
        rule: "include",
        route: [
            routesMap.base.root,
            // 政务微信管理
            routesMap.wx.root,
            routesMap.messageNotify.root,
            // 平台黑名单
            routesMap.blacklist.root,
            routesMap.reportManage.root,
            routesMap.biSetting.root,
            routesMap.base.equipment,
            routesMap.device.root,
        ],
    },
    [routesMap.collectTaskManage.root]: {
        rule: "include",
        route: [
            routesMap.collectTaskManage.root,
            routesMap.laborInfoBaseManage.root,
            routesMap.dataAcquisitionLabour,
        ],
    },
    [routesMap.laborInfoBaseManage.root]: {
        rule: "include",
        route: [
            routesMap.collectTaskManage.root,
            routesMap.laborInfoBaseManage.root,
            routesMap.dataAcquisitionLabour,
        ],
    },
    [routesMap.reportManage.root]: {
        rule: "include",
        route: [
            routesMap.base.root,
            // 政务微信管理
            routesMap.wx.root,
            routesMap.messageNotify.root,
            // 平台黑名单
            routesMap.blacklist.root,
            routesMap.reportManage.root,
            routesMap.biSetting.root,
            routesMap.base.equipment,
        ],
    },
    [routesMap.device.root]: {
        rule: "include",
        route: [
            routesMap.base.root,
            // 政务微信管理
            routesMap.wx.root,
            routesMap.messageNotify.root,
            // 平台黑名单
            routesMap.blacklist.root,
            routesMap.reportManage.root,
            routesMap.biSetting.root,
            routesMap.base.equipment,
            routesMap.device.root,
        ],
    },
    [routesMap.baseService.root]: {
        rule: "include",
        route: [routesMap.baseService.root, routesMap.branch.root],
    },
    [routesMap.branch.root]: {
        rule: "include",
        route: [routesMap.branch.root, routesMap.baseService.root],
    },
    [routesMap.credit.root]: {
        rule: "include",
        route: [routesMap.credit.root],
    },
}

const jzOperateRouteRelationConfig: RouteRelationConfig = {
    [routesMap.employmentManage.employmentManage]: {
        rule: "include",
        route: [
            routesMap.employmentManage.employmentManage,
            routesMap.labourManage.root,
        ],
    },
    [routesMap.labourManage.root]: {
        rule: "include",
        route: [
            routesMap.employmentManage.employmentManage,
            routesMap.labourManage.root,
        ],
    },
    // 市场主体管理
    [routesMap.collectTaskManage.marketDatabaseManage.root]: {
        rule: "include",
        route: [
            routesMap.collectTaskManage.marketDatabaseManage.root,
            routesMap.employmentManage.hrInfoManage.hrInfoManage,
            routesMap.dataAcquisition.returnHome,
            // routesMap.jobStation.root,
            routesMap.dataAcquisition.empBigScreen,
            routesMap.bigScreen.xgEmployment.screen,
            routesMap.bigScreen.qjBigscreen.index,
        ],
    },
    [routesMap.employmentManage.hrInfoManage.hrInfoManage]: {
        rule: "include",
        route: [
            routesMap.employmentManage.hrInfoManage.hrInfoManage,
            routesMap.collectTaskManage.marketDatabaseManage.root,
            routesMap.dataAcquisition.returnHome,
            // routesMap.jobStation.root,
            routesMap.dataAcquisition.empBigScreen,
            routesMap.bigScreen.xgEmployment.screen,
            routesMap.bigScreen.qjBigscreen.index,
        ],
    },
    [routesMap.credit.root]: {
        rule: "include",
        route: [routesMap.credit.root],
    },
    // 招聘管理
    [routesMap.recruit.recruit]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.publicJobIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.jobDeliverIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.jobIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.workIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.complaintIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.positionCollectedIndex]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruit.live.root]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruit.positionCollectedIndex,
            routesMap.recruitCompany.root,
            routesMap.recruit.live.root,
        ],
    },
    [routesMap.recruitCompany.root]: {
        rule: "include",
        route: [
            routesMap.recruit.recruit,
            routesMap.recruit.publicJobIndex,
            routesMap.recruit.jobDeliverIndex,
            routesMap.recruit.jobIndex,
            routesMap.live.root,
            routesMap.recruit.jobFairIndex,
            routesMap.recruit.workIndex,
            routesMap.recruit.complaintIndex,
            routesMap.recruitCompany.root,
        ],
    },
    [routesMap.dataCheck.root]: {
        rule: "include",
        route: [routesMap.dataCheck.root],
    },
}
const saasOperateRouteRelationConfig: RouteRelationConfig = {
    [routesMap.employmentManage.employmentManage]: {
        rule: "include",
        route: [
            routesMap.employmentManage.employmentManage,
            routesMap.labourManage.root,
        ],
    },
    [routesMap.labourManage.root]: {
        rule: "include",
        route: [
            routesMap.employmentManage.employmentManage,
            routesMap.labourManage.root,
        ],
    },
    // 市场主体管理
    [routesMap.collectTaskManage.marketDatabaseManage.root]: {
        rule: "include",
        route: [
            routesMap.collectTaskManage.marketDatabaseManage.root,
            routesMap.employmentManage.hrInfoManage.hrInfoManage,
            routesMap.dataAcquisition.returnHome,
        ],
    },
    [routesMap.employmentManage.hrInfoManage.hrInfoManage]: {
        rule: "include",
        route: [
            routesMap.employmentManage.hrInfoManage.hrInfoManage,
            routesMap.collectTaskManage.marketDatabaseManage.root,
            routesMap.dataAcquisition.returnHome,
        ],
    },
    [routesMap.credit.root]: {
        rule: "include",
        route: [routesMap.credit.root],
    },
    // 招聘管理
    [routesMap.recruit.recruit]: {
        rule: "include",
        route: [routesMap.recruit.recruit, routesMap.recruitCompany.root],
    },
    [routesMap.recruitCompany.root]: {
        rule: "include",
        route: [routesMap.recruit.recruit, routesMap.recruitCompany.root],
    },
}
const qjOperateRouteRelationConfig: RouteRelationConfig = {
    [routesMap.labourManage.root]: {
        rule: "include",
        route: [
            routesMap.employmentManage.employmentManage,
            routesMap.labourManage.root,
        ],
    },
    [routesMap.companyTaskManage.root]: {
        rule: "include",
        route: [routesMap.companyTaskManage.root],
    },
    [routesMap.policyDataBase.root]: {
        rule: "include",
        route: [routesMap.policyDataBase.root],
    },
    [routesMap.employmentTask.root]: {
        rule: "include",
        route: [routesMap.employmentTask.root],
    },
    [routesMap.dataCheck.root]: {
        rule: "include",
        route: [routesMap.dataCheck.root],
    },
    // 基础数据管理
    [routesMap.base.root]: {
        rule: "include",
        route: [routesMap.base.root, routesMap.biSetting.root],
    },
    // 专项工作报表管理
    [routesMap.reportManage.root]: {
        rule: "include",
        route: [routesMap.reportManage.root],
    },
}
const hzOperateRouteRelationConfig: RouteRelationConfig = {
    [routesMap.recruit.recruit]: {
        rule: "include",
        route: [routesMap.recruit.recruit, routesMap.jobStation.root],
    },
    [routesMap.jobStation.root]: {
        rule: "include",
        route: [routesMap.recruit.recruit, routesMap.jobStation.root],
    },
}

function handle4OtherProject() {
    return [] as any
    if (
        config.envProject === EnvProject.潜江项目 ||
        config.envProject === EnvProject.武汉数采项目 ||
        config.envProject === EnvProject.红安项目 ||
        config.envProject === EnvProject.十堰项目
    ) {
        return {
            ...xgOperateRouteRelationConfig,
            ...qjOperateRouteRelationConfig,
        }
    }

    if (
        [
            EnvProject.荆州项目,
            EnvProject.宜都项目,
            EnvProject.鄂州项目,
        ].includes(config.envProject)
    ) {
        return {
            ...xgOperateRouteRelationConfig,
            ...jzOperateRouteRelationConfig,
        }
    }
    if (config.envProject === EnvProject.saas项目) {
        return {
            ...xgOperateRouteRelationConfig,
            ...saasOperateRouteRelationConfig,
        }
    }

    if ([EnvProject.黄州项目].includes(config.envProject)) {
        return {
            ...xgOperateRouteRelationConfig,
            ...jzOperateRouteRelationConfig,
            ...hzOperateRouteRelationConfig,
        }
    }

    return xgOperateRouteRelationConfig
}

export const operateRouteRelationConfig: RouteRelationConfig =
    handle4OtherProject()
