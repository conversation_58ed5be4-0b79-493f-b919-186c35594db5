<template>
    <div class="content">
        <Header :title="title"></Header>
        <Items :items="items" @toRoute="toRoute"></Items>
        <Canvas :data="data"></Canvas>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import Header from "../../common/header.vue"
    import Items from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/items.vue"
    import Canvas from "./canvas.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { ChartQueryResultItem } from "@/views/pages/big-screen/model"
    import { routesMap } from "@/router/direction"

    @Component({ components: { Header, Items, Canvas } })
    export default class Template extends BaseItem {
        private model = [
            {
                label: "劳动力总数",
                value: 0,
                isRoute: true,
            },
            {
                label: "微孝就业注册总数",
                value: 0,
                isRoute: true,
                remoteKey: "已注册劳动力总数",
            },
            {
                label: "已入群人数",
                value: 0,
                isRoute: true,
            },
            {
                label: "总服务人次",
                value: 0,
            },
        ]

        private items: any[] = this.model

        private title = "劳动力"

        protected refresh() {
            this.query<ChartQueryResultItem[]>(
                `labor_count`,
                "dashboard_xg_recruit_service_data"
            ).then((r) => {
                this.items = this.getItems(r)
                if (this.currentRegion) {
                    if (this.currentRegion.code + "" === "420900") {
                        const t = this.items.find((i) => i.label === "劳动力总数")
                        t && (t.value = 2621129)
                    }
                }
            })
        }

        private getItems(r: ChartQueryResultItem[]) {
            return this.formatData(this.model, r)
        }

        private toRoute(label: string, value: number) {
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list1,
                query: {
                    type: `${this.title}-${label}`,
                    cIndex: value + "",
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 613px;
        height: 323px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;
    }
</style>
