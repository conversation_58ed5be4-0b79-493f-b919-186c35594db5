import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import router from "@/router"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"
export const ModelConfig = {
    model: "xg_company_position_recommend",
    list: "manage_apply6_list",
    action1: "end_recommend",
    action2: "recommend_apply_6",
    detail: "manage_apply_6_detail",
}
export const modelTitle = "政策推广服务"

export const predict = {
    policy_name: "policy_form#policy_name",
    policy_access_key: "policy_form#_access_key",
    policy_serve_target_type: "policy_form#serve_target_type_label",
    policy_status: "policy_form#status_label",
    apply_region: "",
    recommend_region: "",
    status_memo: "",
    start_time: "label",
    end_time: "label",

    audit_status: "label",
    audit_time: "label",
    audit_memo: "label",
    audit_user: "auditor_name",
    status: "label",
}

export const enum ServeTargetType {
    居民 = 1,
    企业 = 2,
}

export const enum PolicyStatus {
    下架 = 0,
    上架 = 1,
}

export function canEnd(status: RecommendStatusMemo) {
    return [RecommendStatusMemo.推广中, RecommendStatusMemo.待推广].includes(
        status
    )
}

export function canEdit(status: RecommendStatusMemo) {
    return ![RecommendStatusMemo.已结束].includes(status)
}

export interface Row {
    policy_name: string
    policy_access_key: string
    policy_status: PolicyStatus
    policy_status_label: string
    policy_serve_target_type: ServeTargetType
    policy_serve_target_type_label: string
    status: RecommendStatus
    status_label: string
    apply_region: string
    recommend_region: string
    status_memo: RecommendStatusMemo
    start_time: string
    start_time_label: string
    end_time: string
    end_time_label: string
    id: number
    _access_key: string
    v: number
}

export enum RecommendStatus {
    申请中,
    待推广,
    推广中,
    已结束,
    取消申请,
}

export enum RecommendStatusMemo {
    申请中 = "申请中",
    待推广 = "待推广",
    推广中 = "推广中",
    已结束 = "已结束",
    取消申请 = "取消申请",
}

const tableFilter: TableFilter[] = [
    {
        label: "政策名称",
        type: FormType.Text,
        prop: "policy_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "政策类型",
        type: FormType.Select,
        prop: "serve_target_type",
        option: {
            multiple: true,
        },
    },
    {
        label: "推广开始时间",
        type: FormType.DatePicker,
        prop: "start_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "推广结束时间",
        type: FormType.DatePicker,
        prop: "end_time",
        option: {
            type: "daterange",
        },
    },
]
const column: TableColumn[] = [
    {
        label: "政策名称",
        prop: "policy_name",
        showOverflowTip: true,
        minWidth: "200",
        render: (h, row) =>
            h(
                "span",
                {
                    on: {
                        click: () => {
                            router.push({
                                name: routesMap.publishPolicy.policyDetail,
                                query: {
                                    id: row.policy_access_key,
                                },
                            })
                        },
                    },
                    class: "pointer primary",
                },
                row.policy_name
            ),
    },
    {
        label: "服务对象",
        prop: "policy_serve_target_type_label",
        showOverflowTip: true,
        minWidth: "100",
    },
    {
        label: "政策状态",
        prop: "status_label",
        showOverflowTip: true,
        minWidth: "100",
    },
    {
        label: "推广区域",
        prop: "recommend_region",
        showOverflowTip: true,
        minWidth: "220",
    },
    {
        label: "推广状态",
        prop: "status_memo",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "推广时间",
        prop: "apply_time_label",
        minWidth: "200",
        showOverflowTip: true,
        formatter: (row: Row) =>
            row.start_time_label + " ~ " + row.end_time_label,
    },
    {
        label: "操作",
        prop: "h",
        width: "110",
        fixed: "right",
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model(ModelConfig.model).list(ModelConfig.list),
        filter: tableFilter,
        oneTabFilter: true,
        defaultPageSize: 10,
        predict,
        column,
    }
}
