<template>
    <div class="bg-white u-p-20">
        <div class="info">
            企业已入驻 (2024-07-12） ｜ 企业管理员已注册登记 2 人 ｜
            管理员最近登录日期 2024-07-15 ｜ 平台岗位 5 个 ｜ 待审校 1 个 ｜
            已发布 1 个
        </div>
    </div>
</template>

<script lang='ts'>
    import { Component, Vue } from "vue-property-decorator"

    @Component({ components: {} })
    export default class Tab1 extends Vue {}
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .info {
    }
</style>
