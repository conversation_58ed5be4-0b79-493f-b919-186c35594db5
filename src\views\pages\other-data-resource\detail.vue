<template>
    <div class="core-ui-table-container container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title u-flex u-row-between w-100">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="u-p-x-20 content bg-white u-p-y-30" v-if="row">
            <div class="title u-flex">
                <div>{{ row.name }}</div>
                <!-- <div class="status u-m-l-20" :class="['status-' + row.status]">
                    {{ row.status_label }}
                </div> -->
            </div>
            <detail-row-col
                :list="baseList"
                :labelStyle="labelStyle"
            ></detail-row-col>
        </div>
        <div class="u-m-t-20" v-if="row">
            <el-tabs
                v-model="currentPageName"
                @tab-click="currentPageName = $event.name"
            >
                <el-tab-pane label="数据详情" name="1">
                    <DetailList
                        class="bg-white"
                        :modelName="row.modelName"
                    ></DetailList>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { detailPredict, getConditions } from "."
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../single-page/components/tags-view"
    import DetailList from "./components/detail-list.vue"

    @Component({
        name: routesMap.policyDataBase.otherSourceDetail,
        components: { DetailRowCol, DetailList },
    })
    export default class Template extends Vue {
        private id = ""
        private row: any = null
        private currentPageName = "1"
        refreshConfig = {
            fun: this.init,
            name: routesMap.policyDataBase.otherSourceDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "其他数据资源详情",
                    to: {
                        name: routesMap.policyDataBase.otherSourceDetail,
                        query: {
                            id: this.$route.query.id || this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.policyDataBase.otherSourceDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private init() {
            this.id = (this.$route.query.id as string) || ""
            this.row = null
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("common_check_table_source")
                    .detail(this.id, "back_details")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, detailPredict)
                    })
            })
        }

        private getList() {
            if (!this.row) return
            return getConditions(this.row.modelName).then(
                (r) => (this.infoList = r)
            )
        }

        private labelStyle = {
            fontSize: "14px",
            marginRight: "10px",
            lineHeight: "34px",
            color: "#555",
            width: "108px",
        }

        private get baseList() {
            if (!this.row) return []
            return [
                {
                    label: "数据时间跨度：",
                    value: this.row.collect_gap || "-",
                    span: 8,
                },
                {
                    label: "数据量：",
                    value: this.row.data_count || 0,
                    span: 8,
                },
                {
                    label: "使用说明：",
                    value: this.row.note || "-",
                    span: 24,
                },
            ]
        }

        private infoList: any = []

        mounted() {
            this.init()
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        background: #fff;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
        .status {
            line-height: 12px;
            font-size: 16px;
            margin-left: 10px;
            color: #f5820f;
            &.status-0 {
                color: #22bd7a;
            }
        }
    }
    /deep/ .detail-row {
        .value {
            line-height: 34px !important;
        }
    }
</style>
