<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            @getRows="getRows"
            :showExpand="false"
            @tabName="tabName"
            @getData="getData"
        >
            <div slot="title" class="d-flex-item-center bold" v-if="!detailId">
                <div class="d-flex-item-center bold">
                    <bread-crumb :backRoute="false" :items="breadcrumbs" />
                </div>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="tableConfig.column">
                    <div slot="order" slot-scope="scope">
                        {{ Number(scope.index) + 1 }}
                    </div>
                    <div slot="contact_mobile" slot-scope="scope">
                        {{
                            scope.row.showMobile
                                ? scope.row.contact_mobile
                                : desensitization(scope.row.contact_mobile)
                        }}
                        <el-button
                            v-if="
                                !scope.row.showMobile &&
                                scope.row.contact_mobile
                            "
                            class="u-m-l-10"
                            type="text"
                            @click="scope.row.showMobile = true"
                            >查看</el-button
                        >
                        <span class="color-9" v-if="!scope.row.contact_mobile"
                            >-</span
                        >
                    </div>
                    <div slot="policy_name" slot-scope="scope">
                        <span
                            class="primary pointer"
                            @click="toPolicyDetail(scope.row.form_access_key)"
                        >
                            {{ scope.row.policy_name }}
                        </span>
                    </div>
                    <div slot="real_name" slot-scope="scope">
                        <span
                            :class="isQianJiang ? '' : 'primary pointer'"
                            @click="
                                toPersonDetail(scope.row.profile_access_key)
                            "
                        >
                            {{ scope.row.real_name }}
                        </span>
                    </div>
                    <div slot="agent_name" slot-scope="scope">
                        <span
                            class="primary pointer"
                            @click="toAgentDetail(scope.row.agent_access_key)"
                        >
                            {{ scope.row.agent_name }}
                        </span>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            申报内容
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import UpdateSingleType from "@/views/pages/policy/manage/components/update-single-type.vue"
    import UpdateTypePop from "@/views/pages/policy/manage/components/update-type-pop.vue"
    import { cloneDeep } from "lodash"
    import { Component, Prop } from "vue-property-decorator"
    import { updateTagItem } from "../single-page/components/tags-view"
    import {
        Row,
        columns1,
        columns2,
        queryParamsKey,
        tableFilter,
        predict,
    } from "./index"
    import { desensitization } from "@/utils/tools"
    import { buildConfig4RemoteMeta } from "../collect-task-manage/components/build-table"
    import { SelectOption } from "@/core-ui/component/form"
    import { config, EnvProject } from "@/config"

    @Component({
        name: routesMap.policyApplyRecord.list,
        components: {
            TableContainer,
            CommonTable,
            UpdateTypePop,
            UpdateSingleType,
        },
    })
    export default class PolicyApplyRecordIndex extends BaseTableController<Row> {
        @Prop()
        private detailId?: number

        tableConfig: TableConfig | null = null

        refreshConfig = {
            name: routesMap.policyApplyRecord.list,
            fun: this.reloadList,
        }

        private rows: Row[] | any[] = []

        breadcrumbs: BreadcrumbItem[] = []

        private curTabName = ""

        private desensitization = desensitization

        private pageData: { name: string; item_index: number }[] = []
        private queryParams = {}

        isQianJiang = config.envProject === EnvProject.潜江项目

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "政策申报管理",
                    to: {
                        name: this.$route.name as string,
                    },
                },
            ]
            updateTagItem({
                name: this.$route.name as string,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        created() {
            if (!this.detailId) {
                this.setBreadcrumbs()
            }
            this.init()
        }

        private init() {
            return buildConfig4RemoteMeta("policy_form_apply", "apply_manage", {
                useLabelWidth: true,
                useTabs: true,
                disabledFilter: true,
                optColumn: {
                    label: "操作",
                    prop: "h",
                    fixed: "right",
                    minWidth: "160px",
                },
            }).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig = r.tableConfig as TableConfig
            tableConfig.filter = tableFilter
            tableConfig.column = columns1
            tableConfig.predict = predict
            if (this.detailId) {
                tableConfig.preFilter = {
                    object_id: this.detailId,
                    model_name: "xg_agent",
                }
                tableConfig.tabPages = ["企业"]
                tableConfig.oneTab = true
                tableConfig.filter = tableConfig.filter?.filter(
                    (i) => i.prop !== "agent_name"
                )
            }
            const serveTargetType = (r.meta.filters || []).find(
                (item: { property: string }) => {
                    return item.property === "serve_target_type"
                }
            )
            if (serveTargetType) {
                const o = _.get(
                    serveTargetType,
                    "ext_properties.mapping.mapping_values",
                    []
                ) as SelectOption[]
                const hasOrg =
                    o.findIndex((item) => {
                        return item.value === "企业"
                    }) !== -1

                if (!hasOrg) {
                    tableConfig.oneTab = true
                }
            }

            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private refresh(force?: boolean) {
            if (force) {
                return this.reloadList(true)
            }
            this.refreshList()
        }

        private toDetail(row: any) {
            let pageIndex = 1
            if (!this.curTabName) {
                pageIndex = this.pageData[0].item_index
            } else {
                pageIndex =
                    this.pageData?.find((i) => i.name === this.curTabName)
                        ?.item_index || 1
            }
            sessionStorage.setItem(
                queryParamsKey,
                JSON.stringify({ pageIndex, queryParams: this.queryParams })
            )
            this.$router.push({
                name: routesMap.policyApplyRecord.detail,
                query: {
                    id: row._access_key + "",
                    rowId: row.id,
                    serviceType: row.serve_target_type + "",
                    from: this.$route.name,
                },
            })
        }

        tabName(name: string) {
            this.curTabName = name
            if (this.curTabName === "企业") {
                const c = cloneDeep(columns2)
                c.forEach((i) => {
                    if (i.label === "申报人") {
                        i.prop = "contact_person"
                    }
                })
                this.tableConfig!.column = c.filter((i) => {
                    if (this.detailId && i.prop === "agent_name") {
                        return false
                    } else {
                        return true
                    }
                })
            } else {
                this.tableConfig!.column = cloneDeep(columns1)
            }
        }

        getData(
            data: { name: string; item_index: number }[],
            pageName: string,
            params: any
        ) {
            this.pageData = data
            this.queryParams = params
        }

        private toPolicyDetail(key: string) {
            this.$router.push({
                name: routesMap.publishPolicy.policyDetail,
                query: {
                    id: key,
                    from: this.$route.name,
                },
            })
        }

        private toPersonDetail(key: string) {
            if (this.isQianJiang) {
                return
            }
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: key,
                    from: this.$route.name,
                },
            })
        }

        private toAgentDetail(key: string) {
            this.$router.push({
                name: routesMap.employmentManage.companyManageDetail,
                query: { id: key, from: this.$route.name },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
