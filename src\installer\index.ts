import { config } from "@/config"
import Button from "@/core-ui/component/element-ui/button.vue"
import "@/service"
import ElementUI from "element-ui"
import Theme from "theme-vue"
import Vue, { RenderContext, VueConstructor } from "vue"
import { install as filter } from "./filter"
import "./lodash"
import RefreshMixin from "./mixin-refresh"
import { popoverInstall } from "./popver"
import { roleInstall } from "./role"

Vue.use(ElementUI)
Vue.mixin(RefreshMixin)
const Th = new Theme()
Th.changeTheme(config.projectConfig.theme)
// eslint-disable-next-line @typescript-eslint/no-var-requires
const scrollbar = require("element-ui/lib/scrollbar")
const eventInstaller = function install(Vue: VueConstructor) {
    Vue.prototype.attachDocumentEvent = function (
        this: Vue,
        target: HTMLElement | Element | Document,
        type: string,
        listener: EventListenerOrEventListenerObject,
        options?: boolean | AddEventListenerOptions
    ) {
        target.addEventListener(type, listener, options)
        this.$once("hook:beforeDestroy", () =>
            target.removeEventListener(type, listener, options)
        )
    }
}

const componentInstaller = function install(Vue: VueConstructor) {
    Vue.component("hrs-button", Button)
    Vue.component("v-node", {
        functional: true,
        render: (_, ctx: RenderContext) => ctx.props.vNode,
    })
}

Vue.prototype.registerPostMessageEvent = function (
    callBack: () => void | Promise<void>
) {
    window.addEventListener("message", callBack, false)
    this.$once("hook:beforeDestroy", () =>
        window.removeEventListener("message", callBack, false)
    )
}

Vue.component("el-scrollbar", scrollbar.default)

Vue.use(eventInstaller)
Vue.use(componentInstaller)
Vue.use(filter)
Vue.use(roleInstall)
Vue.use(popoverInstall)
