<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :useTab="true"
            class="container"
            :showExpand="false"
        >
            <div slot="title" class="d-flex-item-center bold">
                <div class="u-flex-1">
                    {{ modelTitle }}
                </div>
                <el-button type="primary" @click="showModal = true">
                    发起推广服务
                </el-button>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="tableConfig.column">
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button
                            v-if="canEnd(scope.row.status_memo)"
                            type="text"
                            @click="toEnd(scope.row)"
                            v-role="
                                'model.xg_company_position_recommend.action.end_recommend'
                            "
                        >
                            结束推广
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <CommonPop
            v-model="showModal"
            title="发起推广"
            :labelWidth="'120px'"
            :sdkModel="ModelConfig.model"
            :sdkAction="ModelConfig.action2"
        />
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { MessageBoxConfirm } from "@/views/components/common-pop"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { Component } from "vue-property-decorator"
    import { canEnd, ModelConfig, modelTitle, Row, tableConfig } from "."

    @Component({
        name: routesMap.baseService.policy,
        components: { TableContainer, CommonTable, CommonPop },
    })
    export default class Grid extends BaseTableController<Row> {
        tableConfig: TableConfig | null = tableConfig()
        modelTitle = modelTitle
        showModal = false
        ModelConfig = ModelConfig
        readonly canEnd = canEnd

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.baseService.policy,
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.baseService.policyDetail,
                query: { id: row._access_key + "" },
            })
        }

        toEnd(row: Row) {
            MessageBoxConfirm({
                content: "是否要结束推广？",
                fun: () => {
                    return sdk.core
                        .model(ModelConfig.model)
                        .action(ModelConfig.action1)
                        .updateInitialParams({
                            selected_list: [{ id: row._access_key, v: 0 }],
                        })
                        .execute()
                },
            }).then(() => {
                this.reloadList()
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
</style>
