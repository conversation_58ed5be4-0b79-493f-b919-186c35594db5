<template>
    <el-dialog
        append-to-body
        :visible="value"
        :title="title"
        @close="close"
        :close-on-click-modal="false"
        width="1200px"
        top="10px"
    >
        <baidu-map
            class="map"
            :center="config.center"
            :zoom="config.zoom"
            :ak="key"
            @ready="mapReady"
        >
            <bm-control style="width: 400px" class="u-m-l-20">
                <div class="u-m-y-20 u-flex">
                    <el-input
                        v-model="keyword"
                        @input="searchDebounce"
                        placeholder="请输入搜索关键字"
                    ></el-input>
                </div>
                <el-scrollbar
                    v-if="keyword && searchList.length"
                    class="scrollbar overflow-x-hidden"
                >
                    <div class="u-p-10 u-p-r-15">
                        <div
                            class="address-item"
                            @click="clickListItem(item)"
                            v-for="item in searchList"
                            :key="item.uid"
                            :class="{
                                active:
                                    item.point.lat === myValue.lat &&
                                    item.point.lng === myValue.lng,
                            }"
                        >
                            {{ item.title }}
                            <div class="detail">
                                {{ item.address }}
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
            </bm-control>
            <bm-marker
                v-if="myValue.lng && myValue.lat"
                :position="{ lng: myValue.lng, lat: myValue.lat }"
                :dragging="false"
                animation="BMAP_ANIMATION_BOUNCE"
            >
            </bm-marker>
            <!-- 地图控件位置 -->
            <bm-navigation anchor="BMAP_ANCHOR_TOP_RIGHT" />
            <!-- 地图容器 -->
            <bm-view
                :style="{
                    width: '100%',
                    height: config.clientHeight + 'px',
                    flex: 1,
                }"
            />
        </baidu-map>

        <div class="u-flex u-row-center">
            <el-form-item
                v-if="region_codeFormItem"
                class="u-m-t-20"
                label="所属地"
                label-width="66px"
            >
                <filed
                    style="width: 400px"
                    :isEdit="true"
                    :type="region_codeFormItem.type"
                    v-bind="region_codeFormItem"
                    :formItem="region_codeFormItem"
                    v-model="region_code"
                />
            </el-form-item>
            <el-form-item
                v-if="formItem"
                class="u-m-t-20"
                label="详细地址"
                label-width="80px"
            >
                <el-input
                    style="width: 400px"
                    v-model="myValue.address_detail"
                    placeholder="请输入详细地址"
                >
                </el-input>
            </el-form-item>
        </div>
        <div class="u-flex u-row-center u-m-t-30">
            <el-button plain @click="close">取消</el-button>
            <el-button type="primary" class="u-m-l-40" @click="confirm">
                确定
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
    import { FormItem } from "@/core-ui/component/form"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { assign, cloneDeep, debounce, find, get } from "lodash"
    import {
        BaiduMap,
        BmAutoComplete,
        BmControl,
        BmMarker,
        BmView,
        BmNavigation,
        BmLocalSearch,
        BmPolygon,
    } from "vue-baidu-map"
    import {
        Component,
        InjectReactive,
        Mixins,
        Prop,
        Watch,
    } from "vue-property-decorator"
    import { get_area_info, MapValue } from "."

    interface searchData {
        address: string
        city: string
        province: string
        title: string
        detailUrl: string
        isAccurate: boolean
        phoneNumber: string
        uid: searchData
        point: {
            lat: number
            lng: number
        }
    }

    @Component({
        components: {
            BaiduMap,
            BmView,
            BmControl,
            BmAutoComplete,
            BmMarker,
            BmNavigation,
            BmLocalSearch,
            BmPolygon,
            Filed: () => import("@/core-ui/component/form/filed/index.vue"),
        },
    })
    export default class MapSelector extends Mixins(DialogController) {
        readonly key = process.env.VUE_APP_BAIDU_KEY

        location: string = process.env.VUE_APP_BM_AUTO_COMPLETE_LOCATION || "孝感市"
        @Prop()
        protected readonly formItem!: FormItem

        @Prop()
        private readonly propsValue!: MapValue

        @Prop()
        private readonly title!: string

        get regionKey() {
            return (this.formItem.option as any)?.regionKey || "region_code"
        }

        @InjectReactive({ from: "formCompData", default: () => ({}) })
        formCompData!: Record<string, any>

        @InjectReactive({ from: "formBuilderItems", default: () => [] })
        formBuilderItems!: FormItem[]

        get region_codeFormItem() {
            return find(this.formBuilderItems, { prop: this.regionKey })
        }

        region_code = ""

        setRegionCode() {
            return get_area_info({
                lng: this.myValue.lng + "",
                lat: this.myValue.lat + "",
            }).then((res) => {
                const address = res.address
                this.region_code = [
                    address.province.id,
                    address.city.id,
                    address.area.id,
                ]
                    .filter(Boolean)
                    .join(",")
            })
        }

        loading = false
        myValue: MapValue = {
            address_detail: "",
            lng: "",
            lat: "",
        }

        private async confirm() {
            if (!this.myValue.address_detail) {
                return this.$message.error("请输入位置名称")
            }
            if (!this.myValue.lat || !this.myValue.lng) {
                return this.$message.error(
                    "请从地图中选择位置，或者根据关键字查找位置"
                )
            }
            await this.$emit("updateValue", this.myValue)
            await assign(this.formCompData, { region_code: this.region_code })
            this.close()
        }

        keyword = ""

        async onOpen() {
            setTimeout(async () => {
                this.myValue.address_detail = this.propsValue.address_detail
                // this.inputText
                if (this.propsValue.lng) {
                    this.myValue.lng = this.propsValue.lng
                    this.config.center.lng = this.propsValue.lng
                }
                if (this.propsValue.lat) {
                    this.myValue.lat = this.propsValue.lat
                    this.config.center.lat = this.propsValue.lat
                }

                this.region_code = get(this.formCompData, this.regionKey, "")
                if (
                    !this.myValue.lat &&
                    !this.myValue.lng &&
                    (this.myValue.address_detail || this.location)
                ) {
                    await this.setPointToMapWithAddress(
                        this.myValue.address_detail || this.location
                    )
                }

                if (!this.region_code && this.myValue.lat && this.myValue.lng) {
                    await this.setRegionCode()
                    assign(this.formCompData, { region_code: this.region_code })
                }
            }, 200)
        }

        config = {
            clientHeight: 600,
            // 天安门经纬度
            center: { lng: "116.403765", lat: "39.91485" } as {
                lng: string | number
                lat: string | number
            },
            zoom: 17,
        }

        mounted() {
            if (!this.key) {
                throw new Error("请先配置百度key")
            }
        }

        searchcompleteConfirm(e: any) {
            const v = e.item.value
            const inputText = {
                prefix: "",
                area: "",
            }
            inputText.prefix = ``
            inputText.area = `${v.street}${v.streetNumber}${v.business}`
            if (!inputText.area.includes(v.province)) {
                inputText.prefix += v.province
            }
            if (!inputText.area.includes(v.city)) {
                inputText.prefix += v.city
            }
            if (!inputText.area.includes(v.district)) {
                inputText.prefix += v.district
            }
            this.keyword = `${inputText.prefix}${inputText.area}`
            this.search()
        }

        setPointToMapWithAddress(address: string) {
            return sdk.core
                .domainService(
                    "xg_project",
                    "anonymous/client_api",
                    "get_lbs_by_word"
                )
                .get<{
                    result: {
                        location: {
                            lat: number
                            lng: number
                        }
                    }
                }>({ address })
                .then(async (res) => {
                    const location = res.result.location
                    this.myValue.lat = location.lat + ""
                    this.myValue.lng = location.lng + ""
                    this.config.center.lat = location.lat + ""
                    this.config.center.lng = location.lng + ""
                })
        }

        BMap: any
        map: any
        geocoder: any
        local: any
        searchList: searchData[] = []

        search() {
            this.local?.search(this.keyword)
        }

        searchDebounce = debounce(this.search, 800)

        onSearchComplete(results: { as: searchData[] }) {
            if (results?.as) {
                this.searchList = results.as
            } else {
                this.searchList = []
            }
            if (this.searchList.length) {
                this.clickListItem(this.searchList[0])
            }
            console.log(results)
        }

        mapReady({ BMap, map }: any) {
            this.BMap = BMap
            this.map = map
            this.local = new BMap.LocalSearch(map, {
                onSearchComplete: this.onSearchComplete,
            })
            this.geocoder = new BMap.Geocoder()

            map.addEventListener("click", ({ point }: any) => {
                this.geocoder.getLocation(point, (res: any) => {
                    this.keyword = ""
                    const v = res.addressComponents
                    const surroundingPois = get(res, "surroundingPois[0]")
                    if (surroundingPois) {
                        this.setCurrentPoint(
                            {
                                address_detail: surroundingPois.title,
                                lng: point.lng,
                                lat: point.lat,
                            },
                            false
                        )
                    } else {
                        this.setCurrentPoint(
                            {
                                address_detail: `${v.district}${v.street}${v.streetNumber}`,
                                lng: point.lng,
                                lat: point.lat,
                            },
                            false
                        )
                    }
                })
            })
        }

        clickListItem(e: searchData) {
            this.setCurrentPoint({
                address_detail: e.title,
                lng: e.point.lng,
                lat: e.point.lat,
            })
        }

        setCurrentPoint(
            e: {
                address_detail: string
                lng: string | number
                lat: string | number
            },
            changeCenter = true
        ) {
            this.myValue = e
            this.setRegionCode()
            if (changeCenter) {
                this.config.center.lat = e.lat + ""
                this.config.center.lng = e.lng + ""
            }
        }
    }
</script>
 <style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="less" scoped>
    @import "~@/css/variables.less";
    .scrollbar {
        background-color: #fff;
        height: 400px;
        border-radius: 8px;
        .address-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            line-height: 1.5;
            .detail {
                font-size: 12px;
                margin-top: 4px;
                border-top: 1px solid #eee;
                padding-top: 4px;
            }
            &.active {
                background-color: var(--primary);
                color: #fff;
            }
        }
    }
</style>
<style>
    .tangram-suggestion-main {
        z-index: 99999999999999999999999;
    }
</style>
