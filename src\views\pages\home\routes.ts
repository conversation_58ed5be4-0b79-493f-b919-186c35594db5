import { checkRole } from "@/installer/role"
import { RouteConfig } from "@/router"
import { xg_project_operate } from "@/router/router-operate"
import { forEach } from "lodash"

export function getRoutes() {
    const list: RouteConfig[] = []
    forEach(
        xg_project_operate.filter((e) => !e?.meta?.hidden),
        (e) => {
            if (e.path === "/home") return
            if (e.meta?.role && !checkRole(e.meta?.role)) return
            const i = handlerRoutes(e)
            i?.children?.length && list.push(i)
        }
    )
    console.log("list", JSON.parse(JSON.stringify(list)))
    return list
}

/** 递归route子集是否有权限 */
export function handlerRoutes(routes: RouteConfig) {
    if (
        (routes.meta?.role && !checkRole(routes.meta?.role)) ||
        routes.meta?.hidden ||
        routes.meta?.hideMenu
    ) {
        return undefined
    }
    if (routes.children?.length === 0) return routes
    const arr: any = []
    forEach(routes.children, (e) => {
        if ((e.meta?.role && !checkRole(e.meta?.role)) || e.meta?.hidden) return
        if (e.children?.length) {
            e.children = handlerRoutes(e)?.children
        }
        arr.push(e)
    })
    routes.children = arr
    return routes
}
