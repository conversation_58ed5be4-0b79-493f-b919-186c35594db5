<template>
    <div class="detail-container" v-if="row">
        <div class="content u-flex-none">
            <div class="title u-flex u-row-between">
                {{ row.title }}
            </div>
            <div class="u-p-t-10 u-p-x-20 u-p-b-20 info">
                <div class="u-flex u-col-top">
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">公司名称：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.agent_name }}
                            </div>
                        </div>
                    </div>
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">填报次数：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.version }}
                            </div>
                        </div>
                    </div>
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">当前任务状态：</div>
                            <div class="u-flex u-flex-none">
                                <span class="status">{{
                                    row.status_label
                                }}</span>
                                <el-button
                                    type="text"
                                    class="extra-btn u-m-l-10"
                                    v-if="showChangeStatus"
                                    @click="changeStatus"
                                >
                                    更改审核状态 >
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="u-flex u-col-top">
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">文件：</div>
                            <a
                                v-if="fileName"
                                :href="filePath"
                                class="primary"
                                target="_blank"
                                >{{ fileName }}</a
                            >
                            <span>-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <change-status-pop
            v-model="showChangePop"
            @refresh="refreshList"
            :row="row"
        />
    </div>
</template>

<script lang='ts'>
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { DetailRow, Status } from ".."
    import ChangeStatusPop from "./change-status-pop.vue"
    import { split } from "lodash"
    import { sdk } from "@/service"

    @Component({
        components: { ChangeStatusPop },
    })
    export default class WorkInfoApplyView extends Vue {
        @Prop()
        private row!: DetailRow

        private get showChangeStatus() {
            return (
                // this.row.status !== Status.审核不通过 &&
                // this.row.status !== Status.审核通过 &&
                // this.row.status !== Status.待填报
                this.row.status === Status.审核中
            )
        }

        private showChangePop = false

        private get filePath() {
            return this.row && sdk.buildFilePath(this.row.file || "")
        }

        private get fileName() {
            return this.row && split(this.row.file, "__")[1]
        }

        private refreshList() {
            this.$emit("refresh")
        }

        private changeStatus() {
            this.showChangePop = true
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        background: #fff;
        .content {
            padding: 20px;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
                .edit-btn {
                    width: 100px;
                    height: 30px;
                }
            }
            .label {
                width: 98px;
                margin-right: 10px;
                color: #555;
                flex: none;
                line-height: 28px;
            }
            .value {
                line-height: 28px;
            }
            .status {
                color: #e87005;
            }
            .info {
                line-height: 34px;
                color: #333;
                font-size: 14px;
            }
            .selector {
                position: relative;
            }
        }
    }
    .picker {
        .display {
            flex: none;
            line-height: 40px;
        }
        ::v-deep .el-date-editor {
            width: 120px;
            .el-input__prefix {
                display: none;
            }
        }
        ::v-deep .el-input__inner {
            padding: 0 10px;
        }
    }
    .group-view {
        width: 510px;
        height: 384px;
    }
    .code {
        width: 50px;
        height: 50px;
    }
    .city {
        max-width: calc(100vw * 400 / 1920);
    }
</style>
