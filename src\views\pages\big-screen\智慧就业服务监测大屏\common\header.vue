<template>
    <div class="d-flex header-content">
        <div></div>
        <div>{{ title }}</div>
        <slot></slot>
        <!-- <div>更多 ></div> -->
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: {} })
    export default class Template extends Vue {
        @Prop({ default: "" })
        private title!: string
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .header-content {
        // background-image: url("../../assets/title.png");
        background-image: url("../assets/title-v2.png");
        background-size: 540px 40px;
        width: 540px;
        height: 40px;

        display: flex;
        align-items: center;
        padding-left: 40px;
        font-weight: 600;
        font-size: 18px;
        color: #ffffff;
        line-height: 18px;

        margin-bottom: 16px;
    }
</style>
