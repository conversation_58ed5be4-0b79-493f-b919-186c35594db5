<template>
    <div class="core-ui-table-container container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :items="breadcrumbs" />
            </div>
        </div>
        <div class="bg-white u-p-y-30">
            <div class="u-p-x-20 u-flex u-row-center u-col-top">
                <div class="content u-p-x-40">
                    <form-builder
                        ref="formBuilder"
                        labelWidth="140px"
                        :onValueChange="onValueChange"
                        @updateQueryRemote="updateQueryRemote"
                        @updateControlsProperties="updateControlsProperties"
                    ></form-builder>
                </div>
                <div class="preview u-flex-none">
                    <div class="preview-content u-flex-none u-flex" v-if="data">
                        <div class="img">
                            <img :src="getSrc" />
                        </div>
                        <div>
                            <div class="title color-3 u-line-2">
                                {{ data.live_title || "待输入直播标题" }}
                            </div>
                            <div class="u-flex u-row-between">
                                <div class="color-9">
                                    开播时间：{{ getTime }}
                                </div>
                                <div class="btn">预约直播</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button type="primary" @click="close" plain>
                    取消
                </el-button>
                <el-button type="primary" @click="confirm('')">
                    保存至草稿状态
                </el-button>
                <el-button type="primary" @click="confirm('1')">
                    提交审核
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { forEach, reduce } from "lodash"
    import moment from "moment"
    import { Component, Vue } from "vue-property-decorator"
    import { LiveAuditStatus, toAudit } from "."

    @Component({ name: routesMap.recruit.live.add, components: { FormBuilder } })
    export default class Template extends FormController {
        private id = ""
        breadcrumbs: BreadcrumbItem[] = []
        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.live.add,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from),
                {
                    label: this.id ? "编辑直播" : "新增直播",
                },
            ]
            updateTagItem({
                name: routesMap.recruit.live.add,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private data: any = null

        private get getSrc() {
            if (!this.data?.live_cover) return ""
            return sdk.buildImage(this.data.live_cover)
        }

        private get getTime() {
            if (!this.data?.live_start_time) return "--"
            return moment(this.data.live_start_time).format("MM-DD hh:mm")
        }

        private get from() {
            return this.$route.query.from as string
        }

        mounted() {
            this.init()
        }

        private updateControlsProperties() {
            this.$forceUpdate()
        }

        private init() {
            this.id = (this.$route.query.id as string) || ""
            this.setBreadcrumbs()
            return buildFormSections({
                sdkAction: this.id ? "edit_live" : "create_live",
                sdkModel: "live_broadcast",
                forms: [],
                id: this.id,
                sortFormItem: (forms: any) => {
                    const f = forms.filter(
                        (e: any) => e.prop !== "live_audit_status"
                    )
                    let live_propel_channel: any = null
                    const t = reduce(
                        f,
                        (r: any, e: any) => {
                            if (e.prop === "propel_type1") {
                                r.push({
                                    prop: "tip1",
                                    label: "",
                                    type: FormType.Tip,
                                    labelWidth: "0px",
                                    itemStyle: {
                                        color: "#F56C6C",
                                        fontSize: "14px",
                                        whiteSpace: "nowrap",
                                        marginLeft: "14px",
                                    },
                                    col: { span: 1 },
                                    option: {
                                        placeholder: "*",
                                    },
                                })
                                r.push({
                                    prop: "tip",
                                    label: "",
                                    type: FormType.Tip,
                                    labelWidth: "0px",
                                    itemStyle: {
                                        color: "#606266",
                                        fontSize: "14px",
                                        whiteSpace: "nowrap",
                                        marginLeft: "3px",
                                    },
                                    col: { span: 20 },
                                    option: {
                                        placeholder: "直播推广渠道",
                                    },
                                })
                            }
                            if (
                                [
                                    "propel_type1",
                                    "propel_type2",
                                    "propel_type3",
                                ].includes(e.prop)
                            ) {
                                e.col.span = 8
                            }
                            if (e.prop === "live_propel_channel") {
                                live_propel_channel = e
                                return r
                            }
                            if (e.prop === "live_cover") {
                                e.option.placeholder =
                                    "建议上传160*90尺寸png、jpg格式图片"
                            }
                            r.push(e)
                            return r
                        },
                        [] as any
                    )
                    this.$forceUpdate()
                    return [...t, live_propel_channel]
                },
            }).then((r) => {
                this.data = r.data
                this.buildFormFull(r)
            })
        }

        private onValueChange(prop = "", value = "") {
            this.data[prop] = value
            this.data = { ...this.data }
        }

        private confirm(audit = "") {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data, audit)
                }
            })
        }

        private submit(data: any, audit = "") {
            pageLoading(() => {
                return sdk.core
                    .model("live_broadcast")
                    .action(this.id ? "edit_live" : "create_live")
                    .addInputs_parameter({
                        ...data,
                        live_audit_status: audit
                            ? LiveAuditStatus.待审核
                            : LiveAuditStatus.草稿,
                    })
                    .updateInitialParams({
                        selected_list: this.id
                            ? [
                                  {
                                      id: this.id,
                                      v: 0,
                                  },
                              ]
                            : [],
                    })
                    .execute()
                    .then((r) => {
                        this.finish()
                    })
            })
        }

        private finish() {
            this.$message.success(this.id ? "编辑成功" : "创建成功")
            this.callRefresh(routesMap.recruit.live.index)
            this.callRefresh(routesMap.recruit.live.detail)
            this.close()
        }

        private close() {
            closeCurrentTap({
                name: this.from,
                query: this.id
                    ? {
                          id: this.id,
                      }
                    : undefined,
            })
        }

        private updateQueryRemote(property: string, r: any) {
            forEach(r.masters, (e: any) => {
                if (e.type === "multi_intentSearch") {
                    e.type = "multi_intentSearchRemote"
                } else if (e.type === "intentSearch") {
                    e.type = "intentSearchRemote"
                }
            })
        }

        private confirmAudit() {}
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        width: 800px;
    }
    .preview {
        width: 400px;
        .preview-content {
            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #d8d8d8;
            padding: 12px;
            .title {
                line-height: 15px;
                font-size: 13px;
                margin-bottom: 9px;
            }
            .img {
                flex: none;
                border-radius: 8px;
                overflow: hidden;
            }
            img {
                width: 116px;
                height: 65px;
                margin-right: 8px;
            }
            .btn {
                width: 72px;
                line-height: 28px;
                background: rgba(87, 130, 236, 0.1);
                border-radius: 100px 100px 100px 100px;
                font-size: 12px;
                color: #2254d4;
                text-align: center;
                margin-left: 5px;
            }
        }
    }
</style>
