import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目

const defaultPolicy = [
    {
        path: "/policy",
        name: routesMap.home.policy,
        meta: {
            title: [EnvProject.鄂州项目, EnvProject.潜江项目].includes(
                config.envProject
            )
                ? "政策管理"
                : "政策与资讯",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/policy.svg"),
            hidden: config.envProject === EnvProject.saas项目,
        },
        component: layout,
        children: [
            {
                path: "manage",
                name: routesMap.policy.manage,
                meta: {
                    title: [EnvProject.鄂州项目].includes(config.envProject)
                        ? "政策管理"
                        : "就业资讯管理",
                    role: "/tablelist/g_policy_advice",
                },
                component: () =>
                    import("@/views/pages/policy/manage/index.vue"),
            },
            {
                path: "create",
                name: routesMap.policy.create,
                meta: {
                    parentMenuName: routesMap.policy.manage,
                    title: [EnvProject.鄂州项目].includes(config.envProject)
                        ? "政策文章"
                        : "政策文章",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/policy/manage/create/index.vue"),
            },
            {
                path: "update-pc-policy",
                name: routesMap.policy.updatePcPolicy,
                meta: {
                    parentMenuName: routesMap.policy.manage,
                    title: "就业创业政策汇编",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/policy/manage/create/update-pc-policy.vue"
                    ),
            },
            {
                path: "publish-policy-list",
                name: routesMap.publishPolicy.list,
                meta: {
                    title: "政策管理",
                    role: "/tablelist/policy_form/manage_list",
                },
                component: () =>
                    import("@/views/pages/publish-policy/list.vue"),
            },
            {
                path: "publish-policy-create",
                name: routesMap.publishPolicy.create,
                meta: {
                    title: "创建政策",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/create.vue"),
            },
            {
                path: "publish-policy-form-list",
                name: routesMap.publishPolicy.formList,
                meta: {
                    title: "政策表单维护",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/form-list.vue"),
            },
            {
                path: "publish-policy-form-detail",
                name: routesMap.publishPolicy.formDetail,
                meta: {
                    title: "表单详情",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/form-detail.vue"),
            },
            {
                path: "publish-policy-policy-detail",
                name: routesMap.publishPolicy.policyDetail,
                meta: {
                    title: "政策详情",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/detail.vue"),
            },
            {
                path: "publish-policy-batch-detail",
                name: routesMap.publishPolicy.batchDetail,
                meta: {
                    title: "批次详情",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicy.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy/batch-detail.vue"),
            },
            {
                path: "policy-apply-record",
                name: routesMap.policyApplyRecord.list,
                meta: {
                    title: "政策申报管理",
                    role: "/tablelist/policy_form_apply/apply_manage",
                },
                component: () =>
                    import("@/views/pages/policy-apply-record/index.vue"),
            },
            {
                path: "policy-apply-record-detail",
                name: routesMap.policyApplyRecord.detail,
                meta: {
                    title: "申报内容",
                    hidden: true,
                    parentMenuName: routesMap.policyApplyRecord.list,
                },
                component: () =>
                    import("@/views/pages/policy-apply-record/detail.vue"),
            },
            {
                path: "subsidy",
                name: routesMap.employmentManage.subsidy,
                meta: {
                    title: "企业业务管理",
                    role: "/tablelist/company_subsidy/for_operate",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/employment-manage/subsidy/index.vue"),
            },
            {
                path: "apply",
                name: routesMap.employmentManage.apply,
                meta: {
                    title: "企业业务申请",
                    role: "/tablelist/company_subsidy_apply",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/employment-manage/apply/index.vue"),
            },
            {
                path: "subsidy-detail",
                name: routesMap.employmentManage.subsidyDetail,
                meta: {
                    title: "补贴业务详情",
                    hidden: true,
                    parentMenuName: routesMap.employmentManage.subsidy,
                },
                component: () =>
                    import(
                        "@/views/pages/employment-manage/subsidy/detail.vue"
                    ),
            },
            {
                path: "manage-detail",
                name: routesMap.policy.manageDetail,
                meta: {
                    title: "就业资讯管理详情",
                    hidden: true,
                    parentMenuName: routesMap.policy.manage,
                },
                component: () =>
                    import("@/views/pages/policy/manage/detail.vue"),
            },
            {
                path: "preferential-policy",
                name: routesMap.preferentialPolicy.manage,
                meta: {
                    title: "惠民政策管理",
                    role: "/tablelist/policy/for_operate",
                    // hidden: [EnvProject.荆州项目].includes(config.envProject),
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/preferential-policy/index.vue"),
            },
            {
                path: "create-preferential-policy",
                name: routesMap.preferentialPolicy.create,
                meta: {
                    parentMenuName: routesMap.preferentialPolicy.manage,
                    title: "创建惠民政策",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/preferential-policy/create/index.vue"
                    ),
            },
            {
                path: "preferential-policy-detail",
                name: routesMap.preferentialPolicy.manageDetail,
                meta: {
                    title: "惠民政策详情",
                    hidden: true,
                    parentMenuName: routesMap.preferentialPolicy.manage,
                },
                component: () =>
                    import("@/views/pages/preferential-policy/detail.vue"),
            },
            {
                path: "apply-preferential-policy",
                name: routesMap.preferentialPolicy.apply,
                meta: {
                    title: "政策办理申请",
                    role: "/tablelist/policy_apply/for_operate",
                    hidden: isYD,
                },
                component: () =>
                    import("@/views/pages/preferential-policy/apply/index.vue"),
            },
            {
                path: "gzwBanner",
                name: routesMap.base.gzwBanner,
                meta: {
                    title: "官网banner管理",
                    role: "/base/gzwBanner",
                    hidden: config.envProject !== EnvProject.荆州项目,
                },
                component: () =>
                    import("@/views/pages/base/mp/gzw/banner/index.vue"),
            },
            {
                path: "gzwGanggao",
                name: routesMap.base.gzwGanggao,
                meta: {
                    title: "官网广告位管理",
                    role: "/base/gzwGuanggao",
                    hidden: config.envProject !== EnvProject.荆州项目,
                },
                component: () =>
                    import("@/views/pages/base/mp/gzw/ganggao/index.vue"),
            },
        ],
    },
]
const qjPolicy = [
    // 政策信息库（潜江）
    {
        path: "/policy-database-manage",
        redirect: "policy-database-manage/index",
        name: routesMap.policyDataBase.root,
        meta: {
            title: "政策信息库",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/policy.svg"),
            hidden: ![EnvProject.潜江项目].includes(config.envProject),
        },
        component: layout,
        children: [
            {
                path: "index",
                name: routesMap.policyDataBase.index,
                meta: {
                    title: "政策信息库",
                    role: "/tablelist/policy_info/list_operate",
                },
                component: () =>
                    import("@/views/pages/policy-database/index.vue"),
            },
            {
                path: "detail",
                name: routesMap.policyDataBase.detail,
                meta: {
                    title: "政策信息库详情",
                    parentMenuName: routesMap.policyDataBase.index,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/policy-database/detail.vue"),
            },
            {
                path: "add",
                name: routesMap.policyDataBase.add,
                meta: {
                    title: "添加政策",
                    parentMenuName: routesMap.policyDataBase.index,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/policy-database/add.vue"),
            },
            {
                path: "other-index",
                name: routesMap.policyDataBase.other,
                meta: {
                    title: "其他业务信息库",
                    role: "/tablelist/policy_info/listOthers_operate",
                },
                component: () =>
                    import("@/views/pages/other-database/index.vue"),
            },
            {
                path: "other-detail",
                name: routesMap.policyDataBase.otherDetail,
                meta: {
                    title: "其他业务信息库详情",
                    parentMenuName: routesMap.policyDataBase.other,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/other-database/detail.vue"),
            },
            {
                path: "other-data-resource",
                name: routesMap.policyDataBase.otherSource,
                meta: {
                    title: "其他数据资源",
                    role: "/tablelist/common_check_table_source/list_data",
                },
                component: () =>
                    import("@/views/pages/other-data-resource/index.vue"),
            },
            {
                path: "other-data-resource-detail",
                name: routesMap.policyDataBase.otherSourceDetail,
                meta: {
                    title: "其他数据资源详情详情",
                    parentMenuName: routesMap.policyDataBase.otherSource,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/other-data-resource/detail.vue"),
            },
            {
                path: "policy-bigscreen",
                name: routesMap.policyDataBase.screen,
                meta: {
                    title: "政策分析大屏",
                    newPage: true,
                    targetPath:
                        `${process.env.BASE_URL}/big-screen/policy-person`.replace(
                            "//",
                            "/"
                        ),
                    role: "/redirect/xg_project/policyDataBase",
                },
                component: () =>
                    import("@/views/pages/policy-database/index.vue"),
            },
        ],
    },
]

const ddPolicy = [
    {
        path: "/policy",
        name: routesMap.home.policy,
        meta: {
            title: [EnvProject.鄂州项目, EnvProject.潜江项目].includes(
                config.envProject
            )
                ? "政策管理"
                : "政策与资讯",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/policy.svg"),
            hidden: config.envProject === EnvProject.saas项目,
        },
        component: layout,
        children: [
            {
                path: "manage",
                name: routesMap.policy.manage,
                meta: {
                    title: [EnvProject.鄂州项目].includes(config.envProject)
                        ? "政策管理"
                        : "就业资讯管理",
                    role: "/tablelist/g_policy_advice",
                },
                component: () =>
                    import("@/views/pages/policy/manage/index.vue"),
            },
            {
                path: "create",
                name: routesMap.policy.create,
                meta: {
                    parentMenuName: routesMap.policy.manage,
                    title: [EnvProject.鄂州项目].includes(config.envProject)
                        ? "政策文章"
                        : "政策文章",
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/policy/manage/create/index.vue"),
            },
            {
                path: "update-pc-policy",
                name: routesMap.policy.updatePcPolicy,
                meta: {
                    parentMenuName: routesMap.policy.manage,
                    title: "就业创业政策汇编",
                    hidden: true,
                },
                component: () =>
                    import(
                        "@/views/pages/policy/manage/create/update-pc-policy.vue"
                    ),
            },
            {
                path: "publish-policy-list",
                name: routesMap.publishPolicyDD.list,
                meta: {
                    title: "政策管理",
                    role: "/tablelist/zc_apply_type/manage",
                },
                component: () =>
                    import("@/views/pages/publish-policy-dd/list.vue"),
            },
            {
                path: "publish-policy-manage-list",
                name: routesMap.publishPolicyDD.policyMange,
                meta: {
                    title: "政策类型管理",
                    parentMenuName: routesMap.publishPolicyDD.list,
                    hidden: true,
                },
                component: () =>
                    import("@/views/pages/publish-policy-dd/manage.vue"),
            },
            {
                path: "publish-policy-create",
                name: routesMap.publishPolicyDD.create,
                meta: {
                    title: "创建政策",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicyDD.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy-dd/create.vue"),
            },
            {
                path: "publish-policy-form-list",
                name: routesMap.publishPolicyDD.formList,
                meta: {
                    title: "政策表单维护",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicyDD.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy-dd/form-list.vue"),
            },
            {
                path: "publish-policy-form-detail",
                name: routesMap.publishPolicyDD.formDetail,
                meta: {
                    title: "表单详情",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicyDD.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy-dd/form-detail.vue"),
            },
            {
                path: "publish-policy-policy-detail",
                name: routesMap.publishPolicyDD.policyDetail,
                meta: {
                    title: "政策详情",
                    hidden: true,
                    parentMenuName: routesMap.publishPolicyDD.list,
                },
                component: () =>
                    import("@/views/pages/publish-policy-dd/detail.vue"),
            },
            {
                path: "policy-apply-record",
                name: routesMap.policyApplyRecordDD.list,
                meta: {
                    title: "企业政策申报管理",
                    role: "/tablelist/zc_apply_instance/manage_company_list",
                },
                component: () =>
                    import("@/views/pages/policy-apply-record-dd/index.vue"),
            },
            {
                path: "policy-apply-record2",
                name: routesMap.policyApplyRecordDD.list2,
                meta: {
                    title: "居民政策申报管理",
                    role: "/tablelist/zc_apply_instance/manage_personal_list",
                },
                component: () =>
                    import("@/views/pages/policy-apply-record-dd/index2.vue"),
            },
            {
                path: "policy-apply-record-detail",
                name: routesMap.policyApplyRecordDD.detail,
                meta: {
                    title: "申报内容",
                    hidden: true,
                    parentMenuName: routesMap.policyApplyRecordDD.list,
                },
                component: () =>
                    import("@/views/pages/policy-apply-record-dd/detail.vue"),
            },
            {
                path: "policy-apply-record-audit",
                name: routesMap.policyApplyRecordDD.audit,
                meta: {
                    title: "去审核",
                    hidden: true,
                    parentMenuName: routesMap.policyApplyRecordDD.list,
                },
                component: () =>
                    import("@/views/pages/policy-apply-record-dd/audit.vue"),
            },
            {
                path: "manage-detail",
                name: routesMap.policy.manageDetail,
                meta: {
                    title: "就业资讯管理详情",
                    hidden: true,
                    parentMenuName: routesMap.policy.manage,
                },
                component: () =>
                    import("@/views/pages/policy/manage/detail.vue"),
            },
        ],
    },
]

export const policy =
    (
        {
            [EnvProject.潜江项目]: qjPolicy,
            [EnvProject.掇刀项目]: ddPolicy,
        } as any
    )[config.envProject] || defaultPolicy
