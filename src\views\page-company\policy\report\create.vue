<template>
    <div class="core-ui-table-container" v-if="detail">
        <div class="core-ui-custom-header justify-content-between">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="detail-container">
            <div class="info">
                <div class="d-flex">
                    <div class="info-label">政策名称</div>
                    <div v-if="detail">{{ detail.policy_name }}</div>
                </div>
                <el-collapse
                    v-model="activeNames"
                    class="company-custom-class mt--10"
                >
                    <el-collapse-item name="1" class="u-m-b-20">
                        <template slot="title">
                            <div
                                class="d-flex justify-content-between collapse-title"
                            >
                                <div class="info-label">政策内容</div>
                                <div class="icon-tips">
                                    {{
                                        activeNames.includes("1")
                                            ? "收起"
                                            : "展开"
                                    }}
                                </div>
                            </div>
                        </template>

                        <div class="html-content" v-if="detail">
                            <div v-html="detail.content"></div>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>
            <!-- 附件模板 -->
            <div>
                <div class="d-flex align-items-start">
                    <div class="info-label">附件模板</div>
                    <FileList :detail="detail" v-if="detail"></FileList>
                </div>
            </div>

            <div class="u-p-x-20 create-position u-flex u-row-center u-p-y-30">
                <div class="content">
                    <form-builder
                        ref="formBuilder"
                        label-width="200px"
                        modelName="collect_task"
                        label-position="top"
                    >
                        <div slot="file-table">
                            <FileList :detail="detail" v-if="detail"></FileList>
                        </div>
                    </form-builder>
                    <ExtraForm
                        ref="extraForm"
                        :detail="detail"
                        :profile="profile"
                        :companyData="companyData"
                        :applyId="applyId"
                        @changeValidate="(e) => (isValidate = e)"
                        @getValue="(e) => (extraData = e)"
                    ></ExtraForm>
                    <div class="u-flex u-m-t-20 u-row-center">
                        <el-button
                            type="primary"
                            plain
                            @click="close"
                            class="custom-btn btn u-m-r-30"
                        >
                            取消
                        </el-button>
                        <el-button
                            @click="confirm('0')"
                            class="custom-btn btn u-m-r-30"
                        >
                            保存
                        </el-button>
                        <el-button
                            @click="confirm('1')"
                            type="primary"
                            class="custom-btn btn"
                        >
                            保存并提交
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Ref } from "vue-property-decorator"
    import DetailSection from "@/views/pages/collect-task-manage/components/detail-section.vue"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { routesMap } from "@/router/direction"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { sdk } from "@/service"
    import FileList from "../components/file-list.vue"
    import RecordList from "../components/record-list.vue"
    import {
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import { Action } from "uniplat-sdk"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import ExtraForm from "../components/extra-form.vue"
    import { sleep } from "@/utils"
    import { CustomProfile, profileService } from "@/service/profile"
    import { userService } from "@/service/service-user"
    import { forEach, map } from "lodash"
    import { pageLoading } from "@/views/controller"

    @Component({
        name: routesMap.company.policy.report.create,
        components: { DetailSection, FormBuilder, FileList, RecordList, ExtraForm },
    })
    export default class TemplateView extends FormController {
        @Ref()
        extraForm!: any

        breadcrumbs: BreadcrumbItem[] = []

        private activeNames = []

        private showBtn = false
        private isValidate = false

        private setBreadcrumbs() {
            const from = this.$route.query.from as string
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(from),
                {
                    label: "申报",
                    to: {
                        name: routesMap.company.policy.report.create,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.company.policy.report.create,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        refreshConfig = {
            fun: this.refresh,
            name: routesMap.company.policy.report.create,
        }

        private detailId = ""

        private refresh() {
            this.init()
        }

        private goCreate() {
            this.$router.push({
                name: routesMap.company.policy.report.create,
            })
        }

        created() {
            this.setBreadcrumbs()
            this.init()
        }

        private id = ""
        private action?: Action

        private get actionName() {
            return this.isEdit ? "company_edit" : "company_client_apply"
        }

        private getAction() {
            if (!this.action) {
                this.action = sdk.core
                    .model(this.isEdit ? "policy_form_apply" : "policy_form")
                    .action(this.actionName)
                    .updateInitialParams({
                        selected_list: this.isEdit
                            ? [{ id: this.id, v: 0 }]
                            : [{ id: this.detail.id, v: 0 }],
                    })
            }
            return this.action
        }

        private buildCurForm() {
            return buildFormSections({
                action: this.getAction(),
                forms: [
                    {
                        label: "申报人",
                        prop: "contact_person",
                        type: FormType.Text,
                        col: {
                            span: 11,
                        },
                    },
                    {
                        label: "联系方式",
                        prop: "contact_mobile",
                        type: FormType.Text,
                        col: {
                            span: 11,
                        },
                        rules: [
                            {
                                validator: (__, value, callback) => {
                                    const v = _.isPhone(value)
                                    if (v) {
                                        callback()
                                    } else {
                                        callback(new Error())
                                    }
                                },
                                message: "手机号格式不正确",
                            },
                        ],
                    },
                    {
                        label: "申报说明",
                        prop: "apply_memo",
                        type: FormType.Text,
                        option: {
                            type: "textarea",
                            rows: 4,
                            resize: "vertical",
                            autosize: {
                                minRows: 4,
                                maxRows: 10,
                            },
                        },
                        col: {
                            span: 24,
                        },
                    },
                    // {
                    //     label: this.detail.upload_desc,
                    //     prop: "file-table",
                    //     type: FormType.Text,
                    //     col: {
                    //         span: 11,
                    //     },
                    // },
                    // {
                    //     label: "上传附件",
                    //     prop: "attachments",
                    //     type: FormType.MyUpload,
                    //     option: {
                    //         drag: true,
                    //         placeholder: "",
                    //         // limit: 1,
                    //     },
                    //     col: {
                    //         span: 11,
                    //     },
                    // },
                    // {
                    //     label: "申报人",
                    //     prop: "contact_person",
                    // },
                ],
            }).then((r) => {
                this.buildFormFull(r)
            })
        }

        private detail: any = null

        private isEdit = false
        private profile: CustomProfile | null = null
        private companyData: any = null
        private applyId = ""

        private async init() {
            this.id = this.$route.query.id as string
            this.detailId = this.$route.query.detailId as string
            this.isEdit = !!this.$route.query.edit
            this.profile = await profileService.setup()
            this.companyData = await userService.setup().then((r) => r.data)
            this.id &&
                (await sdk.core
                    .model("policy_form_apply")
                    .detail(this.id, "client_detail")
                    .query()
                    .then((r) => {
                        const row = sdk.buildRow<{
                            id: string
                        }>(r.row, {})
                        this.applyId = row.id || ""
                    }))
            this.detailId &&
                sdk.core
                    .model("policy_form")
                    .detail(this.detailId, "client_detail")
                    .query()
                    .then((r) => {
                        const obj = {}
                        Object.keys(r.row).forEach((i) => {
                            Object.assign(obj, {
                                [i]: i + "_label",
                            })
                        })
                        const detail = sdk.buildRow<any>(r.row, obj)
                        this.detail = detail

                        this.buildCurForm()
                    })
        }

        private async getExtraFormValue() {
            const extraForm = this.$refs.extraForm as any
            if (extraForm.getValue) {
                return await extraForm.getValue()
            }
        }

        private extraData = {}
        private confirmExtraForm(data: Record<string, string>) {
            this.extraForm.validate()
            return sleep(100).then(() => {
                if (!this.isValidate) {
                    return Promise.reject()
                }
                this.submitExtra(data)
            })
        }

        private submitExtra(data: Record<string, string>) {
            const obj: any = {}
            forEach(this.extraData, (e, p) => {
                if (!p.includes("-")) {
                    const i = p.replace("prop", "")
                    obj[i] = {
                        ...obj[i],
                        question_id: i,
                        answer: e,
                    }
                } else {
                    const [key] = p
                        .replace("-", "prop")
                        .split("prop")
                        .filter(Boolean)
                    obj[key] = {
                        ...obj[key],
                        extra_answer: e,
                    }
                }
            })
            const d = {
                questionnaire_id: this.detail.questionnaire_id,
                task_id: this.detail.id,
                user_answer: map(obj),
                apply_id: this.applyId || undefined,
                ...data, // 额外三个
            }
            console.log("d", JSON.parse(JSON.stringify(d))) // 提交d
            pageLoading(() => {
                return sdk
                    .getDomainService(
                        "user_answer",
                        "general_questionnaire",
                        "xg_project"
                    )
                    .post(d)
                    .then((r) => {
                        console.log("rr", r)
                        this.finish()
                    })
            })
        }

        private confirm(submit_type: "0" | "1") {
            const values = this.getFormValues()
            delete values["file-table"]
            Object.assign(values, {
                submit_type,
            })

            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(values)
                }
            })
        }

        private async submit(data: Record<string, string>) {
            if (!this.detail.questionnaire_id) {
                pageLoading(() => {
                    return this.getAction()
                        .addInputs_parameter(data)
                        .execute()
                        .then(() => {
                            this.finish()
                        })
                })
                return
            }
            try {
                await this.confirmExtraForm(data)
            } catch (e) {}
        }

        private finish() {
            this.$message.success("操作成功")
            this.callRefresh(this.$route.query.from as string)
            closeCurrentTap()
        }

        private close() {
            closeCurrentTap()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .detail-container {
        background: #fefeff;
        box-shadow: 0px 5px 10px 0px rgba(211, 223, 240, 0.5);
        border-radius: 5px 5px 5px 5px;
        padding-left: 30px;
        padding-right: 30px;
        padding-top: 30px;
        padding-bottom: 30px;
        display: flex;
        flex-direction: column;
    }

    .info-label {
        font-size: 14px;
        color: #7a808c;
        margin-right: 18px;
    }

    .content-title {
        font-size: 18px;
        color: #36383b;
        text-align: left;
        margin-bottom: 23px;

        .desc {
            color: #7a808c;
            margin-left: 10px;
            font-size: 14px;
        }
    }

    .info-content {
        display: flex;
        justify-content: space-between;

        background: rgba(242, 247, 255, 0.5);
        border-radius: 5px 5px 5px 5px;
        padding: 23px 29px;

        .label {
            font-size: 14px;
            color: #7a808c;
            margin-right: 20px;
        }

        .value {
            font-size: 14px;
            color: #36383b;
        }
    }

    .html-content {
        background: rgba(242, 247, 255, 0.5);
        border-radius: 5px 5px 5px 5px;
        padding: 30px;
    }

    .content {
        width: 100%;

        /deep/ .el-col {
            &:nth-child(2) {
                margin-left: auto;
            }
            &:nth-child(5) {
                margin-left: auto;
            }
        }

        // /deep/ .el-row {
        //     gap: 20px;
        // }
    }

    .company-custom-class {
        /deep/ &.el-collapse {
            border: none;

            .content-title {
                margin-bottom: 0px;
            }

            .el-collapse-item__wrap,
            .el-collapse-item__header {
                border: none;
            }

            .el-collapse-item__arrow {
                margin-left: 5px;
            }

            .el-collapse-item__content {
                padding-bottom: 0px;
            }
        }

        .collapse-title {
            user-select: none;
        }

        .icon-tips {
            color: #598bff;
            margin-right: 2px;
            user-select: none;
        }
    }

    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
