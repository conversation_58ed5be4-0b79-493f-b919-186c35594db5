<template>
    <div>
        <div class="core-ui-custom-header">
            <div class="title">
                <div class="u-flex u-row-between">
                    <bread-crumb :backRoute="true" :items="breadcrumbs" />
                    <!-- <el-button
                        type="primary"
                        @click="createJob"
                        class="custom-btn batch-btn"
                        >创建岗位</el-button
                    > -->
                </div>
            </div>
        </div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            :useTab="true"
            ref="table"
            v-model="tableConfig"
            class="container-index container shadow u-p-20"
        >
            <div slot="table" slot-scope="{ data }" class="bg-white">
                <common-table :data="data" :columns="columns">
                    <div slot="name" slot-scope="scope">
                        <div class="u-flex">
                            <div class="u-m-r-10">{{ scope.row.name }}</div>
                            <el-button
                                v-if="scope.row.profile_access_key"
                                type="text"
                                @click="viewResume(scope.row)"
                                >查看简历</el-button
                            >
                        </div>
                        <div class="u-flex color-9">
                            {{ handlerName(scope.row) }}
                        </div>
                    </div>
                    <div slot="position_name" slot-scope="scope">
                        <div class="u-line-1">
                            {{ scope.row.position_name }}
                        </div>
                        <div class="u-flex">
                            <div class="u-m-r-10 color-9">
                                {{ scope.row.create_time_label }}
                            </div>
                            <el-button
                                type="text"
                                @click="toPositionDetail(scope.row)"
                                >查看</el-button
                            >
                        </div>
                    </div>
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="toDetail(scope.row)"
                            >详情</el-button
                        >
                    </div>
                </common-table>
            </div>
        </table-container>
        <resume-pop v-model="showResume" :detail="resumeDetail"></resume-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { desensitization } from "@/utils/tools"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { Component } from "vue-property-decorator"
    import { predict, tableConfig } from "."
    import ResumePop from "./components/resume-pop.vue"

    @Component({
        name: routesMap.company.recruit.jobDeliver,
        components: { TableContainer, CommonTable, ResumePop },
    })
    export default class CompanyBlackList extends BaseTableController<any> {
        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.company.recruit.jobDeliver,
        }

        private id = ""

        tableConfig: TableConfig | null = null

        private get columns() {
            return this.tableConfig?.column || []
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "岗位投递列表",
                    to: {
                        name: routesMap.company.recruit.jobDeliver,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.company.recruit.jobDeliver,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private init() {
            this.tableConfig = tableConfig()
            this.setBreadcrumbs()
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.company.recruit.jobDeliverDetail,
                query: {
                    id: row._access_key || row.id,
                    from: routesMap.company.recruit.jobDeliver,
                },
            })
        }

        private handlerName(row: any) {
            return [
                desensitization(row.mobile) || "",
                +row.age ? row.age + "岁" : "",
                row.education_label || "",
            ]
                .filter((e) => e)
                .join(" | ")
        }

        private showResume = false
        private resumeDetail = null
        private viewResume(row: any) {
            pageLoading(() => {
                return sdk.core
                    .model("xg_candidate_order")
                    .detail(row._access_key, "company")
                    .query()
                    .then((r) => {
                        this.resumeDetail = sdk.buildRow(r.row, predict)
                        this.showResume = true
                    })
            })
        }

        private toPositionDetail(row: any) {
            this.$router.push({
                name: routesMap.company.recruit.jobDetail,
                query: {
                    id: row.position_access_key || row.position_id,
                    from: routesMap.company.recruit.jobDeliver,
                },
            })
        }

        mounted() {
            this.init()
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    ::v-deep .table-tabs {
        background-color: #fff;
    }
    ::v-deep .filter-container-out {
        position: relative;
        &::after {
            content: "";
            display: block;
            height: 20px;
            background: #fff;
        }
    }
    ::v-deep .core-ui-table-container .el-tabs__item.is-active {
        background: rgba(#5782ea, 0.9) !important;
        color: #fff;
    }
</style>
