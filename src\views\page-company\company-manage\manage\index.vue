<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title u-flex">
                企业信息 <img src="/img/icon/company.png" class="icon" />
            </div>
        </div>
        <div class="bg-gray shadow">
            <div class="title u-flex u-row-between">
                企业基本信息
                <div class="u-flex">
                    <el-button
                        class="btn custom-btn"
                        type="primary"
                        v-if="!isEdit"
                        @click="isEdit = true"
                    >
                        编辑
                    </el-button>
                    <template v-else>
                        <el-button
                            class="btn custom-btn"
                            type="primary"
                            plain
                            @click="cancel"
                        >
                            取消
                        </el-button>
                        <el-button
                            class="btn custom-btn"
                            type="primary"
                            @click="confrim"
                        >
                            保存
                        </el-button>
                    </template>
                </div>
            </div>
            <div class="content">
                <form-builder
                    ref="formBuilder"
                    labelWidth="120px"
                    :isEdit="isEdit"
                >
                </form-builder>
            </div>
            <Remark :id="id" :row="row" v-if="id" />
            <div
                class="u-p-x-40 u-p-b-40"
                v-if="row && !human_agent_id && showPersonSource"
            >
                <div class="footer u-flex u-row-between u-p-x-40">
                    <div class="tip-img u-flex-none">
                        <el-image
                            :src="src"
                            :preview-src-list="[src]"
                            class="qr-code"
                        />
                        <div>扫一扫咨询客服</div>
                    </div>
                    <div class="des u-flex-1">
                        如果您是人力资源机构，可点击右侧按钮申请成为平台认证的人力资源机构，<br />
                        人力资源行业服务商聚合对接平台，共享商机、整合资源、业务协同、创造价值，助力行业发展。
                    </div>
                    <el-button
                        type="primary"
                        class="custom-btn btn"
                        @click="toDetail"
                    >
                        申请成为人力资源机构
                    </el-button>
                </div>
            </div>
        </div>
        <div class="bg-gray shadow u-m-t-20" v-if="row && human_agent_id && showPersonSource">
            <div class="title u-flex u-row-between">
                <div class="u-flex">
                    <div>人力资源机构申请信息</div>
                    <div
                        class="status u-flex"
                        :class="['status-' + row.audit_status]"
                    >
                        {{ row.audit_status_label }}
                    </div>
                </div>
                <div v-if="row.audit_status === 2">
                    <el-button @click="toEdit" type="primary"
                        >编辑申请</el-button
                    >
                </div>
            </div>
            <div class="row u-m-t-26 u-p-x-40 u-p-b-10">
                <div class="u-flex u-col-top">
                    <div class="u-flex-1 u-flex u-col-top">
                        <div class="label">服务区域</div>
                        <div class="value">
                            {{ row.service_code_name_label || "-" }}
                        </div>
                    </div>
                    <div class="u-flex-1 u-flex" v-if="isXg">
                        <div class="label">是否经营零工驿站</div>
                        <div class="value">
                            {{ !!row.odd_job_id ? "是" : "否" }}
                        </div>
                    </div>
                </div>
                <div class="u-flex u-col-top">
                    <div class="label">业务介绍</div>
                    <div class="value">
                        {{ row.remark || "-" }}
                    </div>
                </div>
                <div class="u-flex u-col-top">
                    <div class="label">人力资源服务许可证</div>
                    <div class="value u-flex u-col-top">
                        <template v-if="personFile">
                            <el-image
                                :src="personFile"
                                :preview-src-list="[personFile]"
                                class="img u-m-r-10"
                            ></el-image>
                            <el-button type="text" @click="download"
                                >下载</el-button
                            >
                        </template>
                        <view v-else>-</view>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { Component } from "vue-property-decorator"
    import { companyFormConfig, getCompanyInfo, Row } from "."
    import { userService } from "@/service/service-user"
    import { pageLoading } from "@/views/controller"
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { sdk } from "@/service"
    import { config, EnvProject } from "@/config"
    import Filed from "@/core-ui/component/form/filed/index.vue"
    import Remark from "./components/remark.vue"
    import { find } from "lodash"
    import { clientConfigService } from "@/service/client-config"
    import { PositionRecommendType, sysConfigService } from "@/service/sys-config"

    @Component({
        name: routesMap.company.companyManage.manage,
        components: { FormBuilder, Filed, Remark },
    })
    export default class CompanyManageIndex extends FormController {
        private row: Row | null = null
        private isEdit = false
        private id = ""
        private isXg = config.envProject === EnvProject.孝感项目

        private src = "/img/company/code.png"

        private showPersonSource = false

        refreshConfig = {
            fun: this.init,
            name: "refreshCompanyManage",
        }

        private labelStyle = {
            width: "126px",
            textAlign: "right",
            marginRight: "10px",
        }

        private get human_agent_id() {
            return this.row?.human_agent_id || ""
        }

        private get personFile() {
            return this.row?.person_file
                ? sdk.buildFilePath(this.row?.person_file || "")
                : ""
        }

        private download() {
            window.open(this.personFile, "_blank")
        }

        private async setPersonSourceStatus() {
            const positionRecommendMapping = await sysConfigService.data
                .position_recommend_type_mapping

            this.showPersonSource = !!positionRecommendMapping.find(
                (i) => i.key === PositionRecommendType.人力资源机构撮合服务
            )
        }

        private init() {
            this.setPersonSourceStatus()
            clientConfigService.setup().then((r) => {
                if (r.advInfo.qr_code) {
                    this.src = sdk.buildImage(r.advInfo.qr_code)
                }
            })
            pageLoading(() => {
                return userService.setup().then((r) => {
                    this.id = r?.key || ""
                    buildFormSections(companyFormConfig(+this.id)).then((r) => {
                        const addressEditFiled = find(r.forms, {
                            prop: "addressEditFiled",
                        })
                        if (addressEditFiled) {
                            const data: any = {}
                            const subForms: any = {}
                            ;["address_detail", "lat", "lng"].forEach((i) => {
                                const d = find(r.forms, { prop: i })
                                data[i] = r.data[i]
                                subForms[i] = d
                            })
                            addressEditFiled.extendData = {
                                subForms,
                                displayValue: data.address_detail || "暂无",
                            }
                            addressEditFiled.defaultValue = data
                        }
                        this.buildForm(r.forms)
                    })
                    return getCompanyInfo(r?.key as string).then((r) => {
                        this.row = r
                        console.log(JSON.parse(JSON.stringify(r)))
                    })
                })
            })
        }

        private cancel() {
            this.isEdit = false
            this.resetData()
        }

        mounted() {
            this.init()
        }

        private toDetail() {
            this.$router.push({
                name: routesMap.company.companyManage.applyDetail,
            })
        }

        private toEdit() {
            this.$router.push({
                name: routesMap.company.companyManage.applyDetail,
                query: {
                    id: this.human_agent_id,
                },
            })
        }

        private confrim() {
            const data = this.getFormValues()
            if (!data.pay_amount) {
                data.pay_amount = 0
            }
            console.log("data", JSON.parse(JSON.stringify(data)))
            this.validateForm((v: boolean) => {
                if (v) {
                    this.isEdit = false
                    this.submit({
                        ...data,
                        ...data.addressEditFiled,
                        apply_type: 0,
                    })
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model("xg_agent")
                    .action("edit_agent")
                    .updateInitialParams({
                        selected_list: [
                            {
                                v: 0,
                                id: this.id,
                            },
                        ],
                    } as any)
                    .addInputs_parameter(data)
                    .execute()
                    .then(() => {
                        this.init()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .icon {
        width: 19px;
        height: 19px;
        margin-left: 5px;
    }
    .bg-gray {
        background: #fefeff;
        .title {
            font-size: 18px;
            line-height: 18px;
            color: #4e5054;
            padding: 34px 40px 0 39px;
            .status {
                border-radius: 12px;
                line-height: 18px;
                height: 18px;
                color: #fff;
                font-size: 10px;
                font-weight: 500;
                margin-left: 7px;
                padding: 0 9px;
                &.status-0 {
                    background: #f6c056;
                }
                &.status-1 {
                    background: #47cc78;
                }
                &.status-2 {
                    background: #ff8d59;
                }
            }
        }
        .row {
            font-size: 500;
            line-height: 18px;
            font-size: 14px;
            .label {
                margin-bottom: 30px;
                color: #9098a6;
                width: 130px;
                text-align: right;
                margin-right: 18px;
                flex: none;
            }
            .value {
                margin-bottom: 30px;
                color: #4e5054;
            }
            .img {
                width: 150px;
                height: 105px;
            }
        }
    }
    .content {
        padding: 30px 40px;
        padding-top: 10px;
        background: #fefeff;
        border-radius: 5px;
    }
    .footer {
        height: 130px;
        background: #f5f9ff;
        border-radius: 5px;
        .des {
            font-weight: 500;
            font-size: 14px;
            line-height: 21px;
            color: #9098a6;
            width: 610px;
        }
        .btn {
            width: 220px;
            height: 40px;
        }

        .tip-img {
            color: #9098a6;
            font-weight: 500;
            font-size: 12px;
            line-height: 14px;
            margin-right: 30px;
            .qr-code {
                width: 81px;
                height: 81px;
                margin-bottom: 6px;
            }
        }
    }
    .btn {
        width: 88px;
        height: 40px;
    }
    ::v-deep .upload {
        .el-upload-list__item-status-label {
            display: none;
        }
        .el-upload-list {
            height: 72px;
        }
        .el-upload-list--picture-card .el-upload-list__item,
        .el-upload--picture-card {
            width: 72px;
            height: 72px;
            line-height: 80px;
            flex: 1;
            .upload-tip {
                margin-top: 28px;
            }
        }
        .loader {
            width: 72px;
            height: 72px;
            .icon {
                margin-bottom: 0;
            }
            div {
                display: none;
            }
        }
        .upload-tip {
            flex: none;
            margin-left: 10px;
            color: #9098a6;
            display: inline-block;
        }
    }
    ::v-deep .limit-4 {
        margin-bottom: 12px;
    }
    ::v-deep .limit-5 {
        margin-bottom: 44px;
    }
    ::v-deep .limit-0 {
        ul.el-upload-list {
            display: none;
        }
    }
</style>
