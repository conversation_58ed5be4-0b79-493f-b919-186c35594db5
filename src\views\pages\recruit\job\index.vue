<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
        >
            <div slot="title" class="d-flex-item-center bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div slot="header-right">
                <el-button
                    type="primary"
                    @click="clickExport"
                    plain
                    class="custom-btn batch-btn"
                >
                    导出
                </el-button>
                <el-button
                    type="primary"
                    @click="showImportPop = true"
                    plain
                    class="custom-btn batch-btn"
                    v-role="[
                        'model.xg_company_position.action.import_common_position',
                    ]"
                >
                    导入岗位
                </el-button>
                <el-button
                    type="primary"
                    @click="toggleBatchPopularizePop"
                    plain
                    v-role="[
                        'model.xg_company_position.action.batch_recommend_apply',
                        'model.xg_company_position.action.recommend_apply',
                    ]"
                    class="custom-btn batch-btn"
                    v-if="!isEZ"
                >
                    招聘服务申请
                </el-button>
                <el-button
                    type="primary"
                    @click="toggleBatchCheck"
                    plain
                    class="custom-btn batch-btn"
                    v-role="['model.xg_company_position.action.batch_audit']"
                >
                    批量审核
                </el-button>
                <el-button
                    type="primary"
                    @click="
                        $router.push({
                            name: routesMap.recruit.jobEdit,
                        })
                    "
                    v-role="[
                        'model.xg_company_position.action.create_position',
                    ]"
                    class="custom-btn batch-btn"
                >
                    创建岗位
                </el-button>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="source_from_type_label" slot-scope="scope">
                        <img
                            :src="scope.row.source_from_logo"
                            class="logo"
                            v-if="scope.row.source_from_logo"
                        />
                        <span v-else>
                            {{ scope.row.source_from_type_label }}
                        </span>
                    </div>
                    <div
                        slot="city"
                        class="u-text-center u-line-1"
                        slot-scope="scope"
                    >
                        {{ computeCity(scope.row) || "-" }}
                    </div>
                    <div slot="info" slot-scope="scope">
                        <div>{{ scope.row.contact_person || "-" }}</div>
                        <div>
                            {{ desensitization(scope.row.contact_mobile) }}
                        </div>
                    </div>
                    <div slot="h" slot-scope="scope">
                        <div class="u-flex u-row-center">
                            <el-button
                                v-if="
                                    scope.row.online_status ===
                                        onlineStatus.已上架 &&
                                    scope.row.status === auditStatus.审核通过 &&
                                    !isEZ
                                "
                                type="text"
                                v-role="[
                                    'model.xg_company_position.action.recommend_apply',
                                ]"
                                @click="togglePopularizePop(scope.row)"
                            >
                                推广
                            </el-button>
                            <el-button
                                type="text"
                                @click="toDetail(scope.row)"
                                v-if="scope.row.status !== auditStatus.待审核"
                            >
                                详情
                            </el-button>
                            <el-button
                                type="text"
                                @click="drop(scope.row)"
                                v-role="[
                                    'model.xg_company_position.action.online',
                                    'model.xg_company_position.action.offline',
                                ]"
                                v-if="scope.row.status === auditStatus.审核通过"
                            >
                                {{
                                    scope.row.online_status ===
                                    onlineStatus.已上架
                                        ? "下"
                                        : "上"
                                }}架
                            </el-button>
                            <el-button
                                type="text"
                                v-if="scope.row.status === auditStatus.待审核"
                                @click="toDetail(scope.row)"
                                v-role="[
                                    'model.xg_company_position.action.audit',
                                ]"
                            >
                                审核
                            </el-button>
                            <el-button
                                type="text"
                                @click="toAudit(scope.row)"
                                v-if="scope.row.status === auditStatus.草稿"
                                v-role="[
                                    'model.xg_company_position.action.submit_audit',
                                ]"
                            >
                                提交审核
                            </el-button>
                        </div>
                        <div class="u-flex u-row-center">
                            <el-button
                                type="text"
                                @click="showTag(scope.row)"
                                v-role="[
                                    'model.xg_company_position.action.set_tags',
                                ]"
                            >
                                设置标签
                            </el-button>
                            <el-button
                                v-if="
                                    scope.row.online_status !==
                                    onlineStatus.已上架
                                "
                                type="text"
                                @click="del(scope.row)"
                                v-role="[
                                    'model.xg_company_position.action.remove',
                                ]"
                            >
                                删除
                            </el-button>
                        </div>
                    </div>
                </common-table>
            </div>
        </table-container>
        <popularize-pop
            v-model="showPopularizePop"
            :rowId="row && row._access_key"
            :name="row && row.name"
            @refresh="refresh"
        ></popularize-pop>
        <popularize-pop
            v-model="showBatchPopularizePop"
            :rowId="checkEdIds"
            :selectList="selectList"
            @refresh="refresh"
        ></popularize-pop>
        <batch-check-pop
            v-model="showBatchCheckPop"
            :rowId="checkEdIds"
            :selectList="selectList"
            @refresh="refresh"
        ></batch-check-pop>
        <audit-pop
            v-model="showAuditPop"
            :showTime="false"
            title="岗位审核"
            :row="row"
            @refresh="refresh"
        ></audit-pop>
        <create-position-pop
            v-model="showPositionPop"
            @refresh="refresh"
        ></create-position-pop>
        <online-pop
            v-model="showOnlinePop"
            :row="row"
            @refresh="refresh"
        ></online-pop>
        <set-tag-dialog
            v-model="showTagPop"
            :row="row"
            @refresh="refresh"
        ></set-tag-dialog>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入岗位"
            placeholder="请点击「确定」将岗位上传"
            :importConfig="importConfig"
            @refresh="reloadList"
        >
        </excel-import>
    </div>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { SelectOption } from "@/core-ui/component/form"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { desensitization } from "@/utils/tools"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { every } from "lodash"
    import { Component } from "vue-property-decorator"
    import { AuditStatus, OnlineStatus, Row, tableConfig } from "."
    import AuditPop from "./components/audit-position-pop.vue"
    import BatchCheckPop from "./components/batch-check-pop.vue"
    import CreatePositionPop from "./components/create-position-pop.vue"
    import OnlinePop from "./components/online-pop.vue"
    import PopularizePop from "./components/popularize-pop.vue"
    import SetTagDialog from "./components/set-tag.vue"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "../../single-page/components/tags-view"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"

    @Component({
        name: routesMap.recruit.job,
        components: {
            TableContainer,
            CommonTable,
            PopularizePop,
            CreatePositionPop,
            AuditPop,
            OnlinePop,
            SetTagDialog,
            BatchCheckPop,
            ExcelImport,
        },
    })
    export default class Job extends BaseTableController<Row> {
        tableConfig: TableConfig | null = tableConfig()
        selectOption: SelectOption[] = []
        private auditStatus = AuditStatus
        private onlineStatus = OnlineStatus
        private routesMap = routesMap
        private showTagPop = false
        private isEZ = config.envProject === EnvProject.鄂州项目
        showImportPop = false
        private importConfig: any = null

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.recruit.job,
        }

        private checkEdIds: Array<number | string> = []

        private get columns() {
            return this.tableConfig?.column || []
        }

        private showPopularizePop = false
        private showBatchPopularizePop = false
        private showBatchCheckPop = false
        private selectList: Row[] = []

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "招聘岗位列表",
                    to: {
                        name: routesMap.recruit.job,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.job,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        mounted() {
            this.setBreadcrumbs()
            this.importConfig = {
                templateUrl: window.location.origin + "/file/导入岗位模板.xlsx",
                modelName: "xg_company_position",
                actionName: "import_common_position",
                bigActionImportParams: {
                    inputs_parameters: [],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
        }

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.rows.map((i) => i._access_key)
            this.selectList = d.rows
        }

        private refresh() {
            this.refreshList()
            this.callRefresh(routesMap.recruit.jobDetail)
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.recruit.jobDetail,
                query: { id: row._access_key },
            })
        }

        private togglePopularizePop(row: Row) {
            this.row = row
            this.showPopularizePop = true
        }

        private toggleBatchPopularizePop() {
            if (!this.checkEdIds.length) return this.$message.warning("请选择数据")
            if (
                this.selectList.find((item) => item.status === AuditStatus.待审核)
            ) {
                return this.$message.warning("所选数据不能包含未通过审核的岗位")
            }
            this.showBatchPopularizePop = true
        }

        toggleBatchCheck() {
            if (!this.checkEdIds.length) return this.$message.warning("请选择数据")
            if (!every(this.selectList, { status: AuditStatus.待审核 })) {
                return this.$message.warning("所选数据需要全部为未通过审核的岗位")
            }
            this.showBatchCheckPop = true
        }

        private toAudit(row: Row) {
            MessageBox.confirm("是否将该岗位提交审核？", "提交审核").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_company_position")
                        .action("submit_audit")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row._access_key }],
                        })
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private showOnlinePop = false
        private drop(row: Row) {
            const isDrop = row.online_status === this.onlineStatus.已上架
            if (!isDrop) {
                const isDateExpired = (date: string) => {
                    if (!date) return false
                    const inputDate = new Date(date)
                    const today = new Date()
                    today.setHours(0, 0, 0, 0)
                    inputDate.setHours(0, 0, 0, 0)
                    return inputDate.getTime() < today.getTime()
                }
                if (isDateExpired(row.expired_date_label)) {
                    return MessageBox.confirm(
                        "招聘已过期请重新创建岗位。",
                        "提示",
                        {
                            confirmButtonText: "复制并创建新岗位",
                        }
                    ).then(() => {
                        this.$router.push({
                            name: routesMap.recruit.jobEdit,
                            query: {
                                copyId: row._access_key,
                                from: routesMap.recruit.job,
                            },
                        })
                    })
                }
                this.row = row
                this.$nextTick(() => {
                    this.showOnlinePop = true
                })
                return
            }
            const label = isDrop ? "下架" : "上架"
            const msg = [
                `岗位名称：${row.name}`,
                isDrop
                    ? "下架后用户将看不到此岗位，确认从个人端下架此岗位吗？"
                    : "上架后用户可以看到此岗位，确认上架到个人端吗？",
            ]
            MessageBox.confirm(msg.join("\n"), label).then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_company_position")
                        .action(isDrop ? "offline" : "online")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row._access_key }],
                        })
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private del(row: Row) {
            sdk.core
                .model("xg_company_position")
                .action("remove")
                .updateInitialParams({
                    selected_list: [{ v: 0, id: row.id }],
                })
                .query()
                .then((r) => {
                    // console.log(r)
                    let tips = "是否确定删除岗位"
                    let btnText = "确定"
                    const xgCompanyPosition = r.parameters.inputs_parameters.find(
                        (i) => i.model_name === "xg_company_position"
                    )
                    if (xgCompanyPosition?.default_value) {
                        tips =
                            `该岗位参加了${xgCompanyPosition?.default_value}，删除会在招聘会同步移除该岗位，是否继续删除？` ||
                            ""
                        btnText = "仍要继续删除"
                    }

                    MessageBox.confirm(tips, "删除岗位", {
                        confirmButtonText: btnText,
                    }).then(() => {
                        pageLoading(() => {
                            return sdk.core
                                .model("xg_company_position")
                                .action("remove")
                                .updateInitialParams({
                                    selected_list: [{ v: 0, id: row._access_key }],
                                })
                                .execute()
                                .then(() => {
                                    this.refreshList()
                                })
                        })
                    })
                })
                .catch(() => {
                    console.error("删除失败")
                })
        }

        private showAuditPop = false

        private toggleshowAuditPop(row: Row) {
            this.row = row
            this.showAuditPop = true
        }

        private showPositionPop = false
        private createPosition() {
            this.showPositionPop = true
        }

        private computeCity(row: Row) {
            return [row.province, row.city, row.area].filter(Boolean).join("")
        }

        private desensitization(v: string) {
            return v ? desensitization(v) : "-"
        }

        private showTag(row: Row) {
            this.row = row
            this.$nextTick(() => {
                this.showTagPop = true
            })
        }

        clickExport() {
            this.exportExcelUniplatV2({ template_name: "招聘岗位管理导出" })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .logo {
        height: 25px;
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
    ::v-deep .v-select-2 .item {
        border: 1px solid #d8d8d8 !important;
        background: #fbfbfb !important;
        &.active {
            border: none !important;
            background: #5782ec !important;
            color: #ffffff !important;
        }
    }

    ::v-deep .one-self-containter {
        background-color: #fff;
        padding: 20px;
        padding-bottom: 0;
        margin-bottom: 0.5px;
    }
</style>
