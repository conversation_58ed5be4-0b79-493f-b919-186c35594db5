<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div v-if="isNoFinish">
                <!-- <el-button type="primary" plain @click="showImportPop = true">
                    批量导入
                </el-button> -->
                <!-- <el-button type="primary" @click="toAdd"> 数据填报 </el-button> -->
                <el-button v-if="isNoFillNegation" @click="cancelEdit">
                    取消
                </el-button>
                <el-button
                    type="primary"
                    v-if="isNoFill"
                    @click="confirm"
                    :disabled="!isHaveData"
                >
                    提交
                </el-button>
                <el-button
                    type="primary"
                    v-else
                    @click="toEdit"
                    :disabled="!isHaveData"
                >
                    编辑
                </el-button>
            </div>
        </div>

        <div class="detail-top-box" v-if="items && details">
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
                class="u-p-x-20"
            >
            </detail-row-col>
        </div>

        <div
            class="u-p-20 bg-white"
            v-if="details && tableColumns && tableColumns.length"
        >
            <DetailTableMultiple
                :columns="tableColumns"
                :id="details.task_id"
                :taskStatus="status"
                :fillInStatus="fillInStatus"
                :isEditing="isNoFill"
                ref="detailTable"
                @description="description"
                @getRows="getRowsY"
            />
            <!-- <DetailTable
                v-else
                :columns="tableColumns"
                :rows="rows"
                :taskStatus="status"
                ref="detailTable"
            /> -->
        </div>
        <AddIndicatorPop
            v-if="details && tableColumns && tableColumns.length"
            v-model="addIndicatorPop"
            :columns="tableColumns"
            :regionName="details.region_name"
            @submit="submit"
        />
        <excel-import
            v-if="importConfig && details"
            v-model="showImportPop"
            title="导入数据"
            placeholder="请点击「确定」将数据上传"
            :tableColumns="tableColumns"
            :importConfig="importConfig"
            :reportId="reportId"
            :regionName="details.region_name"
            :data="indicatorData"
            :columnsLevel="columnsLevel"
            @refresh="refreshList"
            @exportTemplate="exportTemplate"
        />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "../../single-page/components/tags-view"
    import DetailTable from "../../report-manage/components/detail-table.vue"
    import DetailTableMultiple, {
        Row,
    } from "../components/detail-table-multiple.vue"
    import AddIndicatorPop from "../../report-manage/executor/components/add-indicator-pop.vue"
    import { Loading, MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import {
        DetailItemType,
        DetailRow,
        FillInStatus,
        IndicatorColumn,
        IndicatorDataRows,
        primaryRows,
        Status,
    } from "../../report-manage/index"
    import { getShowBtn4Page } from "../../collect-task-manage/components/build-table"
    import ExcelImport from "../../report-manage/executor/components/excel-import.vue"
    import { ExcelColumnsGenerator } from "../../report-manage/excel-columns-generator"
    import { TableColumn } from "@/core-ui/component/table"
    import { cloneDeep } from "lodash"

    @Component({
        name: routesMap.reportManage.executor.detail,
        components: {
            DetailRowCol,
            DetailTable,
            DetailTableMultiple,
            AddIndicatorPop,
            ExcelImport,
        },
    })
    export default class ReportManageExecutorDetail extends Vue {
        private items: ColItem[] = []

        private breadcrumbs: BreadcrumbItem[] = []

        private showList = false
        private status: Status | null = null
        private fillInStatus: FillInStatus | null = null

        private columns: IndicatorColumn[] = []
        private rows: IndicatorDataRows[] = []
        private details: DetailRow | null = null
        private importConfig: any = null
        private exportDescription: any = {}
        private showImportPop = false
        private tableColumns: TableColumn[] | any[] = []
        private simpleColumns: any[] = []
        private indicatorData: { data: string }[] = []
        private childrenColumnsLabel: string[] = []
        private inputsParameters: {}[] = []
        private isEditng = false

        private get labelStyle() {
            return {
                minWidth: "88px",
                textAlign: "left",
                color: "#555",
            }
        }

        private get columnsLevel() {
            return this.childrenColumnsLabel?.length ? 2 : 1
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.reportManage.executor.detail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: `报表结果`,
                    to: {
                        name: routesMap.reportManage.executor.index,
                    },
                },
                {
                    label: "报表详情",
                    to: {
                        name: routesMap.reportManage.executor.detail,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.reportManage.executor.detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private reportId = ""

        private addIndicatorPop = false

        private isHaveData = false

        private get isNoFinish() {
            return this.status !== Status.已完成
        }

        private get isNoFill() {
            return this.fillInStatus !== FillInStatus.已完成 || this.isEditng
        }

        //  已填报但正在编辑
        private get isNoFillNegation() {
            return this.fillInStatus === FillInStatus.已完成 && this.isEditng
        }

        created() {
            this.init()
        }

        private init() {
            this.items = []
            this.columns = []
            this.tableColumns = []
            this.rows = []

            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_task_target")
                    .detail(
                        this.$route.query.id as string,
                        "for_operate_two_dimensional"
                    )
                    .query()
                    .then((res) => {
                        this.details = sdk.buildRow(res.row, {
                            type: "xg_indicator_task#xg_indicator_group_ref#item_type",
                            task_id: "",
                            region_name: "",
                            name: "xg_indicator_task#name",
                        })
                        this.reportId = this.details?.id + ""

                        this.status = res.row["xg_indicator_task#status"]
                            ?.value as Status
                        this.fillInStatus = res.row.status?.value as FillInStatus
                        this.items = res.meta.header.field_groups
                            .map((item) => {
                                return {
                                    label: item.label + "：",
                                    value: item.template,
                                    hide: !item.visible,
                                    span: 8,
                                }
                            })
                            .filter((i) => i) as ColItem[]
                        this.columns = res.row.indicator_meta
                            ?.value as IndicatorColumn[]
                        this.tableColumns = this.columns?.map((i) => {
                            return {
                                label: i.display_name,
                                prop: i.union_code,
                                description: i.description,
                                value_type: i.value_type,
                                showOverflowTip: true,
                                children:
                                    i.children?.map((j) => {
                                        return {
                                            label: j.display_name,
                                            prop: j.union_code,
                                            showOverflowTip: true,
                                            description: j.description,
                                            children: j.children || [],
                                            value_type: j.value_type,
                                        }
                                    }) || [],
                            }
                        })
                        this.rows = res.row.indicator_data
                            ?.value as IndicatorDataRows[]
                        let data = {}
                        this.rows = this.rows.map((i) => {
                            data = Object.keys(i.indicator_data).reduce(
                                (acc: any, key) => {
                                    acc[i.indicator_data[key].union_code] =
                                        i.indicator_data[key].input_value
                                    return acc
                                },
                                {}
                            )
                            return {
                                ...i,
                                ...data,
                            }
                        })
                        this.setChildrenColumnsLabel()
                        this.initImport()
                    })
            })
        }

        private setChildrenColumnsLabel() {
            this.childrenColumnsLabel = []
            this.columns.forEach((item: any) => {
                if (item.children?.length === 0) {
                    this.childrenColumnsLabel.push("")
                } else {
                    item.children?.forEach((child: any) => {
                        this.childrenColumnsLabel.push(child.display_name)
                    })
                }
            })
            const isEmptyString = (str: string) => {
                return str === ""
            }
            if (this.childrenColumnsLabel.every(isEmptyString)) {
                this.childrenColumnsLabel.length = 0 // 将数组置为空
            }
        }

        initImport() {
            this.importConfig = {
                modelName: "xg_indicator_task_target",
                actionName: "pub_indicator_for_multi_v2",
                bigActionImportParams: {
                    inputs_parameters: [],
                    selected_list: [
                        {
                            id: this.reportId,
                            v: 0,
                        },
                    ],
                    batchSchema: "导入数据",
                },
            }
        }

        private deleteDetail() {
            MessageBox.confirm(`确认删除？`, "删除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_agent_manager")
                        .action("disable_agent")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.reportId }],
                        })
                        .execute()
                        .then(() => {
                            this.init()
                        })
                })
            })
        }

        private toAdd() {
            this.addIndicatorPop = true
        }

        private refreshList() {
            ;(this.$refs.detailTable as any).refreshList()
            this.init()
        }

        private toImport() {}

        private description(description: any) {
            this.exportDescription = Object.entries(description).reduce(
                (acc: any, [key, value]) => {
                    acc[key] = value === "" ? "-" : value
                    return acc
                },
                {}
            )
        }

        private exportTemplate() {
            const columnsLabel = [] as string[]
            this.columns.forEach((item: any) => {
                if (item.union_code !== "h") {
                    columnsLabel.push(item.display_name)
                }

                if (item.children?.length > 0) {
                    for (let i = 0; i < item.children.length - 1; i++) {
                        columnsLabel.push("")
                    }
                }
            })
            ExcelColumnsGenerator.execute({
                primaryRows: primaryRows,
                columns: columnsLabel,
                childColumns: this.childrenColumnsLabel,
                rows: Object.values(this.exportDescription),
                fileName: this.details?.name + "-模板" || "报表填报模板",
            })
        }

        private submit(data: { data: string }[]) {
            this.indicatorData = data
            this.addIndicatorPop = false
            this.showImportPop = true
        }

        private async confirm() {
            if (
                ((this.$refs.detailTable as any).$refs["error-message"] as any)
                    ?.length
            ) {
                return false
            }
            const data = (this.$refs.detailTable as any).tableData
            const actualData = data.filter((i: Row) => i.id)
            const loading = Loading.service({})
            for (const item of actualData) {
                const dataRow = cloneDeep(item)
                Object.keys(dataRow).forEach((key) => {
                    const propExists = this.simpleColumns.some(
                        (
                            item:
                                | {
                                      prop: string
                                  }
                                | any
                        ) => item.prop === key
                    )
                    if (!propExists && key !== "id") {
                        delete dataRow[key]
                    }
                })
                this.simpleColumns.forEach(
                    (
                        item:
                            | {
                                  prop: string
                              }
                            | any
                    ) => {
                        if (!Object.keys(dataRow).includes(item.prop)) {
                            dataRow[item.prop] = ""
                        }
                    }
                )
                // newDataRow：删掉id字段，得到inputs_parameters需要的对象
                const newDataRow = Object.entries(dataRow)
                    .filter(([key]) => key !== "id")
                    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})
                const params = {
                    id: dataRow.id,
                    inputs_parameters: {
                        ...newDataRow,
                    },
                }
                await sdk.core
                    .domainService(
                        "xg_project",
                        "back_api",
                        "update_xg_indicator_target"
                    )
                    .post(params)
            }
            loading.close()
            this.$message.success("提交成功")
            this.isEditng = false
            this.refreshList()
        }

        private toEdit() {
            this.isEditng = true
        }

        private cancelEdit() {
            this.isEditng = false
            ;(this.$refs.detailTable as any).refreshList()
        }

        private getRowsY(rows: Row[], simpleColumns: any[]) {
            this.simpleColumns = simpleColumns.filter(
                (i) => !i.children?.length && i.prop !== "name"
            )
            if (rows?.length > 1) {
                // this.getInputsData(rows[1])
                return (this.isHaveData = true)
            }
            this.isHaveData = false
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .detail-top-header {
        padding-left: 20px;
        padding-right: 20px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .detail-top-title {
        font-weight: 600;
        font-size: 18px;
        color: #222222;
        line-height: 18px;
    }

    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
    }
</style>
