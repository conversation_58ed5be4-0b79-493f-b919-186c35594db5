<template>
    <div class="core-ui-table-container container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="u-p-30 bg-white u-flex u-row-center" :id="fid">
            <div class="content">
                <form-builder
                    ref="formBuilder"
                    label-width="120px"
                    label-position="right"
                    :onValueChange="onValueChange"
                >
                    <div slot="serve_target_type" slot-scope="scope">
                        <el-radio-group
                            v-model="targetType"
                            :disabled="!!id"
                            @input="targetTypeChanged"
                        >
                            <el-radio
                                v-for="(item, index) in scope.item
                                    .sourceInputsParameter.ext_properties
                                    .mapping.mapping_values"
                                :key="index"
                                :label="item.key"
                            >
                                {{ item.value }}
                            </el-radio>
                        </el-radio-group>
                        <span class="tips">（创建后不能修改）</span>
                    </div>
                    <template slot="limit_type">
                        <div>
                            <div class="u-flex u-col-center">
                                <el-radio-group
                                    v-model="limitType"
                                    :disabled="!!id"
                                    @input="limitTypeChanged"
                                >
                                    <el-radio
                                        v-for="(
                                            item, index
                                        ) in limitTypeOptions"
                                        :key="`type${index}`"
                                        :label="item.key"
                                        >{{ item.label }}</el-radio
                                    >
                                </el-radio-group>
                                <span class="limit-type-tips"
                                    >（1个居民或企业的申报次数限制，创建后不能修改）</span
                                >
                            </div>

                            <!-- <el-input
                                v-model="limitCount"
                                placeholder="请输入申报限制次数"
                                v-if="limitType === '1'"
                                :disabled="!!id"
                                @input="validateInput"
                            ></el-input> -->
                        </div>
                    </template>
                    <div slot="policy_form_attachment">
                        <CreateAttachmentTemplate
                            ref="attachmentTemplate"
                            :list="templateList"
                        />
                    </div>
                </form-builder>

                <div class="u-flex u-m-t-20 u-row-center">
                    <el-button
                        type="primary"
                        @click="close"
                        plain
                        class="custom-btn btn u-m-r-30"
                    >
                        取消
                    </el-button>
                    <el-button
                        type="primary"
                        @click="confirm('0')"
                        plain
                        class="custom-btn btn u-m-r-30"
                    >
                        保存
                    </el-button>
                    <el-button
                        type="primary"
                        @click="confirm('1')"
                        class="custom-btn btn u-m-0"
                    >
                        保存并上架
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        buildSelectSource,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { UICore } from "@/core-ui/service/setup"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Action } from "uniplat-sdk"
    import { Component } from "vue-property-decorator"
    import CreateAttachmentTemplate from "./components/create-attachment-template.vue"
    import { find, get } from "lodash"
    import { TemplateItem } from "."
    import { buildWangEditorFormItem } from "@/plugins/wangeditor"
    import { uuid } from "uniplat-sdk/build/main/helpers/uuid"
    import { config, EnvProject } from "@/config"
    import { sleep } from "@/utils"

    const enum LimitTypeItem {
        One = "999",
        Custom = "1",
        NoLimit = "0",
    }

    @Component({
        name: routesMap.publishPolicy.create,
        components: { FormBuilder, CreateAttachmentTemplate },
    })
    export default class PublishPolicyCreate extends FormController {
        refreshConfig = {
            fun: this.init,
            name: routesMap.publishPolicy.create,
        }

        breadcrumbs: BreadcrumbItem[] = []

        setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from as string),
                {
                    label: this.id ? "编辑政策" : "创建政策",
                },
            ]

            updateTagItem({
                name: routesMap.publishPolicy.create,
                breadcrumb: d,
                title: this.id ? "编辑政策" : "创建政策",
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string | undefined
        }

        mounted() {
            this.init()
        }

        private id = ""
        private fid = `e${uuid().replaceAll("-", "")}`

        private action?: Action

        private targetType = ""
        private limitType = ""
        private limitCount = ""
        private limitTypeOptions = [
            {
                key: LimitTypeItem.One,
                label: "1次",
            },
            {
                key: LimitTypeItem.Custom,
                label: "自定义次数",
            },
            {
                key: LimitTypeItem.NoLimit,
                label: "不限制",
            },
        ]

        private templateList: TemplateItem[] = []

        private getAction() {
            return (this.action = sdk.core
                .model("policy_form")
                .action(this.id ? "update_form" : "create_form")
                .updateInitialParams({
                    selected_list: this.id ? [{ id: this.id, v: 0 }] : [],
                }))
        }

        private async init() {
            this.id = (this.$route.query.id as string) || ""
            this.setBreadcrumbs()
            const id = this.id
            return buildFormSections({
                action: sdk.core
                    .model("policy_form")
                    .action(this.id ? "update_form" : "create_form"),
                id: this.id,
                forms: [
                    {
                        label: "服务对象",
                        prop: "serve_target_type",
                        type: FormType.Text,
                    },
                    {
                        label: "政策名称",
                        prop: "policy_name",
                        type: FormType.Text,
                    },
                    {
                        label: "",
                        prop: "create_unit_id",
                        type: FormType.Cascader,
                        hide: true,
                    },
                    {
                        label: "适用范围",
                        prop: "create_unit_id_demo",
                        type: FormType.MultipleCascader,
                        required: true,
                        option: {
                            elProps: {
                                checkStrictly: true,
                                multiple: true,
                            },
                        },
                        hide: ![EnvProject.孝感项目].includes(config.envProject),
                    },
                    {
                        label: "对外发布单位",
                        prop: "org_name_display",
                        type: FormType.Text,
                    },
                    {
                        label: "申报限制",
                        prop: "limit_type",
                        needListen: true,
                        type: FormType.Select,
                    },
                    {
                        label: "限制次数",
                        prop: "limit_times",
                        type: FormType.Text,
                        option: {
                            placeholder: "请输入申报限制次数",
                            disabled: !!this.id,
                        },
                        rules: [
                            {
                                validator(rules, value, callback) {
                                    if (+value % 1 !== 0) {
                                        callback(new Error("请输入整数"))
                                        return
                                    }
                                    if (+value <= 1) {
                                        callback(new Error("不能小于2"))
                                        return
                                    }
                                    callback()
                                },
                            },
                        ],
                        handlerDisplay(data) {
                            return (
                                +data.limit_type === 1 &&
                                (id ? +data.limit_times > 1 : true)
                            )
                        },
                    },
                    {
                        label: "关联表单",
                        prop: "question_id",
                        option: {
                            disabled: !!(await this.getDetail()),
                        },
                        type: FormType.IntentSearchRemote,
                    },
                    {
                        label: "上传附件说明",
                        prop: "upload_desc",
                        type: FormType.Text,
                    },
                    {
                        label: "附件模版",
                        prop: "policy_form_attachment",
                        type: FormType.Text,
                    },
                    {
                        label: "",
                        type: FormType.Checkbox,
                        prop: "standard",
                        sourceInputsParameter: buildSelectSource([
                            {
                                key: "1",
                                value: "是否使用标准化字体、字号、行高。使用后富文本内的字体、字号、行高将不生效，字体固定为苹方体，字号为16px，行高为24px",
                            },
                        ]),
                        option: {
                            class: "standard-checkbox",
                        } as any,
                        hide: true,
                    },
                    buildWangEditorFormItem({
                        label: "政策内容",
                        prop: "content",
                    }),
                ],
                needSourceData: true,
            }).then((r) => {
                this.buildFormFull(r)
                this.targetType = r.data.serve_target_type + ""
                this.limitType = r.data.limit_type + ""
                this.limitCount = r.data.limit_times + ""
                console.log("r", JSON.parse(JSON.stringify(r.data)))
                r.data.create_unit_id?.length &&
                    this.setCreateUnitId(r.data.create_unit_id)
                if (
                    this.limitType === LimitTypeItem.Custom &&
                    +this.limitCount === 1
                ) {
                    this.limitType = LimitTypeItem.One
                }
                this.templateList = get(
                    r,
                    "inputsParameters.parameters.details_parameters[0].datas",
                    []
                ) as TemplateItem[]
            })
        }

        setCreateUnitId(id = "") {
            sleep(200).then(() => {
                this.formBuilder?.onInput(
                    {
                        prop: "create_unit_id_demo",
                    },
                    id.split("|")
                )
            })
        }

        private getDetail() {
            if (!this.id) return Promise.resolve("")
            return this.getAction()
                .query()
                .then((r) => {
                    return (
                        find(
                            (get(r, "parameters.inputs_parameters") as any) || [],
                            (e: any) => e.property === "question_id"
                        ).default_value || ""
                    )
                })
        }

        private confirm(type: "0" | "1") {
            const data = this.getFormValues()
            const meta = `<meta standard="1">`
            if (data.standard === "1") {
                data.content = meta + data.content
            } else {
                data.content = data.content.replace(meta, "")
            }
            if (data.limit_type === LimitTypeItem.Custom) {
                // data.limit_times = this.limitCount
            } else if (
                data.limit_type === LimitTypeItem.One ||
                this.limitType === LimitTypeItem.One
            ) {
                data.limit_type = LimitTypeItem.Custom
                data.limit_times = "1"
            } else {
                data.limit_times = ""
            }
            Object.assign(data, {
                submit_status: type,
                create_unit_id: data.create_unit_id_demo.join("|"),
            })
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                    })
                }
            })
        }

        private close() {
            closeCurrentTap()
        }

        private submit(data: any) {
            pageLoading(() => {
                const templates = (this.$refs.attachmentTemplate as any).data
                const deleteList =
                    (this.$refs.attachmentTemplate as any).deleteList || []
                return this.getAction()
                    .execute({
                        inputs_parameters: UICore.buildActionParameter(data),
                        dataDetails: [
                            {
                                deleted: deleteList,
                                name: "policy_form_attachment",
                                values: templates.map(
                                    (i: {
                                        name: string
                                        template: string
                                        id?: string
                                        isNew?: boolean
                                    }) => {
                                        const row = [
                                            {
                                                property: "title",
                                                value: i.name,
                                            },
                                            {
                                                property: "template_file",
                                                value: i.template,
                                            },
                                        ]
                                        let keyValue = ""
                                        if (i.id && this.id) {
                                            let id = i.id
                                            if (i.isNew) {
                                                id = "0"
                                            }
                                            keyValue = id
                                            row.push({
                                                property: "id",
                                                value: id,
                                            })
                                        }
                                        return {
                                            keyValue,
                                            rowData: row,
                                        }
                                    }
                                ),
                            },
                        ],
                    })
                    .then(() => {
                        this.$message.success(this.id ? "编辑成功" : "创建成功")
                        // this.callRefresh(this.from as string)
                        this.close()
                    })
            })
        }

        private targetTypeChanged() {
            this.formBuilder?.onInput(
                { prop: "serve_target_type" },
                this.targetType
            )
        }

        private limitTypeChanged() {
            this.formBuilder?.onInput(
                { prop: "limit_type", needListen: true },
                this.limitType
            )
        }

        private validateInput() {
            this.limitCount = +this.limitCount.replace(/[^0-9]/g, "") + ""
        }

        onValueChange(prop: string, value: string, a: any) {
            console.log("prop", prop, value)
            if (prop === "standard") {
                const standardFlag = value === "1"
                let d: any = document.querySelector(
                    `#${this.fid} .w-e-text-container`
                )
                standardFlag
                    ? d?.classList.add("rich-standard")
                    : d?.classList.remove("rich-standard")
                d = null
            }
        }
    }
</script>

<style lang='scss' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .select {
        width: 320px;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
    .content {
        width: 900px;
        margin: 0 auto;
    }
    .tips {
        color: #999;
        margin-left: 50px;
    }
    .limit-type-tips {
        color: #999;
        margin-left: 10px;
    }
    /deep/ .el-checkbox-button {
        margin-right: 10px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    /deep/ .el-checkbox-button__inner {
        border: unset;
        border-radius: 4px;
    }

    /deep/ .el-checkbox-button:first-child .el-checkbox-button__inner {
        border: unset;
        border-radius: 4px;
    }

    /deep/ .el-checkbox-button:last-child .el-checkbox-button__inner {
        border-radius: 4px;
    }
</style>
