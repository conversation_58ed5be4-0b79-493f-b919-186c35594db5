<template>
    <div class="content">
        <Header :title="title"></Header>
        <Items :items="items" @toRoute="toRoute"></Items>
        <Tables
            :defaultDisableCanClick="true"
            :titles="titles"
            :data="tableData"
        ></Tables>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import Header from "../../common/header.vue"
    import Items from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/items.vue"
    import Tables from "../../common/tables.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { ChartQueryResultItem } from "@/views/pages/big-screen/model"
    import { routesMap } from "@/router/direction"
    import { getXgRegionMapping } from "../../common/regionTree/cache-tree"
    import { config } from "@/config"

    const sourceData = {
        孝感市: {
            人数: 280498,
            服务次数: 572763,
        },
        市辖区: {
            人数: 4293,
            服务次数: 73721,
        },
        孝南区: {
            人数: 29369,
            服务次数: 504337,
        },
        汉川市: {
            人数: 24596,
            服务次数: 422373,
        },
        应城市: {
            人数: 13676,
            服务次数: 234850,
        },
        云梦县: {
            人数: 50076,
            服务次数: 859927,
        },
        安陆市: {
            人数: 25144,
            服务次数: 431783,
        },
        大悟县: {
            人数: 69190,
            服务次数: 1188161,
        },
        孝昌县: {
            人数: 24154,
            服务次数: 414783,
        },
    }

    @Component({ components: { Header, Items, Tables } })
    export default class Template extends BaseItem {
        private model = [
            {
                label: "总数",
                value: 71165,
                isRoute: true,
            },
            {
                label: "灵活就业",
                longValue: "50.40%",
            },
            {
                label: "单位就业",
                longValue: "32.93%",
            },
            {
                label: "其他",
                longValue: "16.67%",
            },
        ]

        private items: any[] = this.model

        private disabledClickItems = ["人群类型"]

        private titles = ["区域", "人数", "占比"]

        private title = "城镇新增就业管理"

        private tableData: any[][] = [
            ["市辖区", 2578, "3.62%"],
            ["孝南区", 11177, "15.71%"],
            ["汉川市", 12081, "16.98%"],
            ["应城市", 9485, "13.33%"],
            ["云梦县", 1547, "2.17%"],
            ["安陆市", 12015, "16.88%"],
            ["大悟县", 8150, "11.45%"],
            ["孝昌县", 14132, "19.86%"],
        ]

        private backValue = 0
        private serviceValue = 0

        protected async onQueryRegionCodeChange() {
            // await getXgRegionMapping().then((r: any) => {
            //     const mapping = {} as any
            //     r.forEach((item: any) => {
            //         const regionName = item.data.region_name
            //         const regionCode = item.data.region_code
            //         if ((sourceData as any)[regionName]) {
            //             mapping[regionCode] = {
            //                 ...(sourceData as any)[regionName],
            //                 已就业人数: 0,
            //             }
            //         }
            //     })
            //     if (!mapping[config.defaultRegion.region_code]) {
            //         mapping[config.defaultRegion.region_code] = {
            //             ...sourceData["孝感市"],
            //             已就业人数: 0,
            //         }
            //     }
            //     this.backValue = mapping[+this.currentRegion!.code].人数
            //     this.serviceValue = mapping[+this.currentRegion!.code].服务次数 || 0
            //     this.refresh()
            // })
        }

        // protected refresh() {
        //     this.query<ChartQueryResultItem[]>(
        //         `important_count`,
        //         "dashboard_xg_recruit_service_data"
        //     ).then((r) => {
        //         this.items = this.getItems(r)
        //         const c1 = this.items.find((i) => i.label === "总服务人次")?.value
        //         const c2 = this.items.find((i) => i.label === "总服务人数")?.value
        //         const c3 = this.items.find((i) => i.label === "服务比")
        //         c3.customValue = `1:${(c1 / c2).toFixed(1)}`
        //     })
        //     this.getTableData()
        // }

        // private getItems(r: ChartQueryResultItem[]) {
        //     return this.formatData(this.model, r).map((i) => {
        //         if (i.label === "服务比") {
        //             return {
        //                 ...i,
        //                 customValue: `1:0`,
        //             }
        //         }
        //         if (i.label === "人群总数") {
        //             return {
        //                 ...i,
        //                 value: i.value + +this.backValue,
        //             }
        //         }
        //         if (i.label === "总服务人数") {
        //             return {
        //                 ...i,
        //                 value: i.value + +this.backValue,
        //             }
        //         }
        //         if (i.label === "总服务人次") {
        //             return {
        //                 ...i,
        //                 value: i.value + +this.serviceValue,
        //             }
        //         }
        //         return i
        //     })
        // }

        // private getTableData() {
        //     this.query<ChartQueryResultItem[]>(
        //         `important_user_type_list`,
        //         "dashboard_xg_recruit_service_data"
        //     ).then((r) => {
        //         const defaultList = [
        //             ["当年返乡人员", this.backValue, this.serviceValue, "--"],
        //             ["退役军人", "--", "--", "--"],
        //             ["残疾人", "--", "--", "--"],
        //         ]
        //         if (this.currentRegion) {
        //             if (this.currentRegion.code + "" === "420900") {
        //                 defaultList[1] = ["退役军人", "830", "3794", "118"]
        //             }
        //         }
        //         this.tableData = r
        //             .filter(
        //                 (i: any) =>
        //                     i["人群类型"] !== "退役军人" &&
        //                     i["人群类型"] !== "残疾人" &&
        //                     i["人群类型"] !== "当年返乡人员"
        //             )
        //             .map((item: any) => {
        //                 return this.titles.map((key) => item[key] ?? 0)
        //             })
        //             .concat(defaultList)
        //     })
        // }

        private toRoute(label: string, value: number) {
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list9,
                query: {
                    type: `${this.title}-${label}`,
                    cIndex: value + "",
                },
            })
        }

        // private toPerson(title: string, item: string) {
        //     this.$router.push({
        //         name: routesMap.bigScreen.smartEmploymentMonitor.list9,
        //         query: {
        //             type: `${this.title}_${item}_${title}`,
        //         },
        //     })
        // }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 613px;
        height: 321px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;

        .smaller-title {
            font-size: 15px;
            color: #89b9ff;
            line-height: 18px;
            text-align: left;
            margin-bottom: 4px;
            padding-top: 7px;
            padding-left: 20px;
        }

        /deep/ .table-box {
            margin-top: 10px;

            .line-box {
                height: 140px;
            }

            .line {
                .row {
                    width: 40%;
                    &:first-child {
                        width: 20%;
                    }
                }
            }
        }
    }
    /deep/.empty-box .empty-img {
        width: 94px;
        height: 80px;
    }
</style>
