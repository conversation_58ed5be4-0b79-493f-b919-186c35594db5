<template>
    <div class="card">
        <div :id="id" class="chart u-flex flex-fill">
            <div class="item" v-for="item in items" :key="item.label">
                <div class="d-flex justify-content-center">
                    <div class="text1">{{ item.label }}</div>
                </div>
                <div
                    class="bg-box d-flex justify-content-center align-items-center"
                    :class="{ route: item.isRoute }"
                    @click="toRoute(item.isRoute)"
                >
                    <scroll-number
                        :value="item.value"
                        class="text3"
                        :dot="false"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Watch } from "vue-property-decorator"
    import ScrollNumber from "../../../../common/number-scroll/scroll-number.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { ChartQueryResultItem } from "../../../../model"
    import { routesMap } from "@/router/direction"

    @Component({ components: { ScrollNumber } })
    export default class Index extends BaseItem {
        @Prop({ default: () => [] })
        private list: ChartQueryResultItem[] | any

        @Watch("list", { immediate: true })
        private onListChange() {
            const model = [
                {
                    label: "驿站总数",
                    value: 0,
                    isRoute: true,
                },
                {
                    label: "空闲求职者数",
                    value: 0,
                },
                {
                    label: "预计有效招工人数",
                    value: 0,
                    remoteKey: "预计有效用工人数",
                },
                {
                    label: "累计达成意向总数",
                    value: 0,
                    remoteKey: "达成意向总数",
                },
                {
                    label: "确认完工总数",
                    value: 0,
                },
            ]
            this.items = this.formatData(model, this.list)
        }

        private readonly id = `id-${Math.random()}`

        private items: any[] = []

        private toRoute(isRoute: boolean) {
            if (!isRoute) {
                return
            }
            this.$router.push({
                name: routesMap.bigScreen.smartEmploymentMonitor.list3,
                query: {},
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .chart {
        margin: auto;
        padding: 0 16px;
        padding-top: 14px;
        .item {
            width: 105px;
            &:not(:last-child) {
                margin-right: 12px;
            }
        }
        .text1 {
            text-align: center;
            color: #89b9ff;
            font-size: 14px;
            line-height: 16px;
            font-weight: 500;
            white-space: nowrap;
        }

        .route {
            /deep/ .scroll-number {
                cursor: pointer;
                border-bottom: 1px solid #fdc850;
            }
        }

        .bg-box {
            width: 105px;
            height: 80px;
            background-image: url("../../../../assets/tab2/1.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
            .text3 {
                font-size: 24px;
                color: #fdc850;
                margin-bottom: 20px;
            }
        }
    }
</style>
