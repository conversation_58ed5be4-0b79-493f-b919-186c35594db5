import { sdk } from "@/service"
import axios from "axios"
import { ExtraCheck } from "./extra-check"
import { app } from "@/main"
import { config, EnvProject } from "@/config"
import { regionData } from "./region"
import { hongHuRegion } from "./honghu-region-jinyu-huanyuan"
import { hongHuRegion2 } from "./honghu-region-tuopin"

export function getDashboardChartData<T>(e: {
    dashboard?: string
    chart: string
    [key: string]: any
}) {
    const extraUrl = ExtraCheck.getExtraUrl()
    if (extraUrl) {
        return new Promise<T>((resolve) => {
            axios
                .post(
                    `${extraUrl}/general/project/xg_project/service/anonymous/dashboard_api/get_dashboard_chart`,
                    {
                        ...e,
                        dashboard:
                            transDashboardValue(e.dashboard) ||
                            "dashboard_company",
                    }
                )
                .then((r) => {
                    return resolve(r.data.data)
                })
        })
    }

    const needLogin = app.$route.meta?.needLogin
    let p = "anonymous/dashboard_api"
    if (needLogin) {
        p = "dashboard_api"
    }

    const whiteRegionConfig = getWhiteRegionConfig(
        app.$route.query.regionUuid as string
    )
    if (whiteRegionConfig) {
        p = "anonymous/dashboard_api"
    }

    const isHongHu = config.envProject === EnvProject.洪湖项目

    if (isHongHu && !sessionStorage.getItem("HongHuUseShiJi")) {
        console.log("HongHuIsOk")
        return new Promise<T>((resolve) => {
            queryCacheData().then((r: any) => {
                for (let i = 0; i < r.length; i++) {
                    if (r[i].params.chart === e.chart) {
                        if (!e.dashboard) {
                            const res = r[i].result as any
                            return resolve(res)
                        } else if (r[i].params.dashboard === e.dashboard) {
                            const res = r[i].result as any
                            return resolve(res)
                        }
                    }
                }
            }) as Promise<T>
        })
    }

    return sdk.core
        .domainService("xg_project", p, "get_dashboard_chart")
        .post<T>({
            ...e,
            dashboard: transDashboardValue(e.dashboard) || "dashboard_company",
        })
}

function transDashboardValue(dashboard: string | undefined) {
    if (!dashboard || ![EnvProject.孝感项目].includes(config.envProject)) {
        return dashboard
    }
    const obj: Record<string, string> = {
        dashboard_age_labor_summary: "xg_dashboard_age_labor_summary",
        dashboard_special_college: "xg_dashboard_special_college",
        dashboard_important_labor: "xg_dashboard_important_labor",
        dashboard_special_migrant: "xg_dashboard_special_migrant",
        dashboard_labor_summary: "xg_dashboard_labor_summary",
    }
    return obj[dashboard] || dashboard
}

export function getWhiteRegionConfig(uid: string) {
    if (!uid) {
        return null
    }
    const obj: Record<
        string,
        {
            code: string
            name: string
            showBiCityVillage: string
            anonymous?: boolean // 是否匿名，更加彻底，直接在router里面设置meta属性
        }
    > = {
        ac257f1026121: {
            code: "************",
            name: "丹江口市",
            showBiCityVillage: "1",
        },
        ac257f1026122: {
            code: "************",
            name: "咸丰县",
            showBiCityVillage: "1",
            anonymous: true,
        },
    }
    return obj[uid] || {}
}

function queryCacheData() {
    return Promise.resolve([hongHuRegion, hongHuRegion2, regionData].flat())
}
