<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            @loaded="loaded"
            @tabName="onTabNameChange"
        >
            <div
                slot="table"
                slot-scope="{ data, index }"
                class="u-p-20 bg-white"
            >
                <div slot="title" class="u-flex u-row-right u-m-b-20">
                    <el-button
                        v-if="showImport1Btn"
                        type="primary"
                        @click="showImport1 = true"
                    >
                        导入员工列表
                    </el-button>
                    <el-button
                        type="primary"
                        @click="
                            exportExcelUniplatV2({
                                template_name: '荆门市就业生活补助申请表',
                            })
                        "
                        plain
                    >
                        导出
                    </el-button>
                </div>
                <common-table :data="data" :columns="columns">
                    <div slot="序号" slot-scope="scope">
                        {{ (index - 1) * 10 + scope.index + 1 }}
                    </div>
                    <div slot="real_name" slot-scope="scope">
                        <span
                            :class="isPerson ? 'primary pointer' : ''"
                            @click="
                                toPersonDetail(scope.row.profile_access_key)
                            "
                            >{{ scope.row.real_name }}</span
                        >
                    </div>
                    <div slot="agent_name" slot-scope="scope">
                        <span
                            class="primary pointer"
                            @click="toAgentDetail(scope.row.agent_access_key)"
                            >{{ scope.row.agent_name }}</span
                        >
                    </div>
                    <div slot="contact_mobile" slot-scope="scope">
                        {{
                            scope.row.showMobile
                                ? scope.row.contact_mobile
                                : desensitization(scope.row.contact_mobile)
                        }}
                        <el-button
                            v-if="
                                !scope.row.showMobile &&
                                scope.row.contact_mobile
                            "
                            class="u-m-l-10"
                            type="text"
                            @click="scope.row.showMobile = true"
                            >查看</el-button
                        >
                        <span class="color-9" v-if="!scope.row.contact_mobile"
                            >-</span
                        >
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <div class="handler-btn" @click="toDetail(scope.row)">
                            申报内容
                        </div>
                    </div>
                </common-table>
            </div>
        </table-container>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入政策办理人员"
            placeholder="请点击「确定」上传"
            :importConfig="importConfig"
            @refresh="reloadList"
        >
        </excel-import>
        <excel-import
            v-if="importConfig2"
            v-model="showImport1"
            title="批量上传"
            placeholder="请点击「确定」上传"
            :importConfig="importConfig2"
            @refresh="reloadList"
        >
        </excel-import>
    </div>
</template>

<script lang='ts'>
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { sdk } from "@/service"
    import { ServeTargetType } from ".."
    import { desensitization } from "@/utils/tools"
    import { routesMap } from "@/router/direction"
    import { queryParamsKey } from "../../policy-apply-record"
    import { cloneDeep } from "lodash"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"

    const enum Status {
        待提交 = -1,
        已提交审核 = 0,
        通过 = 1,
        不通过 = 2,
    }

    interface Row {
        /** 处理人 */
        real_name: number

        /** 申报时间 */
        create_time: string
        create_time_label: string

        /** 联系方式 */
        contact_mobile: string

        /** 处理时间 */
        dealer_time: number
        dealer_time_label: number

        /** 状态 */
        status: Status

        /** 状态[文本] */
        status_label: string
        _access_key: string
        profile_access_key: string
        agent_access_key: string
        id: number
        v: number
    }

    const column1: TableColumn[] = [
        {
            label: "序号",
            prop: "order",
            showOverflowTip: true,
        },
        {
            label: "申报人",
            prop: "real_name",
            showOverflowTip: true,
        },
        {
            label: "企业名称",
            prop: "agent_name",
            showOverflowTip: true,
        },
        {
            label: "申报时间",
            prop: "create_time_label",
            showOverflowTip: true,
        },
        {
            label: "联系方式",
            prop: "contact_mobile",
            showOverflowTip: true,
            width: "140px",
        },
        {
            label: "处理人",
            prop: "dealer_name",
            showOverflowTip: true,
        },
        {
            label: "处理时间",
            prop: "dealer_time_label",
            showOverflowTip: true,
        },
        {
            label: "状态",
            prop: "status_label",
            showOverflowTip: true,
        },
        { label: "操作", prop: "h", showOverflowTip: true },
    ]

    export const column2: TableColumn[] = [
        {
            label: "序号",
            prop: "order",
            showOverflowTip: true,
        },
        {
            label: "申报人",
            prop: "real_name",
            showOverflowTip: true,
        },
        {
            label: "申报时间",
            prop: "create_time_label",
            showOverflowTip: true,
        },
        {
            label: "联系方式",
            prop: "contact_mobile",
            showOverflowTip: true,
            width: "140px",
        },
        {
            label: "办理单位",
            prop: "handle_org",
            showOverflowTip: true,
        },
        {
            label: "所属区域",
            prop: "handle_region",
            showOverflowTip: true,
        },
        {
            label: "处理时间",
            prop: "dealer_time_label",
            showOverflowTip: true,
        },
        {
            label: "状态",
            prop: "status_label",
            showOverflowTip: true,
        },
    ]

    const predict1 = {
        real_name: "user#real_name",
        create_time: "label",
        contact_mobile: "",
        dealer_name: "dealer#real_name",
        dealer_time: "label",
        status: "label",
        profile_access_key: "profile#_access_key",
        showMobile: false,
    }

    export const predict2 = {
        real_name: "profile_v2#name",
        create_time: "label",
        contact_mobile: "",
        dealer_time: "label",
        status: "label",
        profile_access_key: "profile_v2#_access_key",
        handle_org: "",
        handle_region: "handle_region#region_name",
        showMobile: false,
    }

    @Component({ components: { TableContainer, CommonTable, ExcelImport } })
    export default class ApplyList extends BaseTableController<{ id: number }> {
        @Prop()
        id!: string

        @Prop()
        type!: ServeTargetType

        @Prop()
        policyName!: string

        @Prop()
        showImport1Btn!: boolean

        private tableConfig: TableConfig | null = null

        private desensitization = desensitization
        showImport1 = false

        private pageData: { item_index: number; item_size: number } | any = {}
        private queryParams = {}

        private activeName = "线上申报"
        private importConfig: any = null
        private importConfig2: any = null

        private columns: TableColumn[] = column1

        public showImportPop = false

        private get isPerson() {
            return this.type && this.type === ServeTargetType.居民
        }

        mounted() {
            this.init()
        }

        // private init() {
        //     this.tableConfig = tableConfig(this.id, this.type)
        //     if (this.isPerson) {
        //         this.columns = this.columns.filter((i) => i.prop !== "agent_name")
        //     }

        // }

        private init() {
            this.importConfig = {
                templateUrl:
                    window.location.origin + "/file/政策/首次来鄂离线申请导入.xls",
                modelName: "zc_apply_instance",
                actionName: "batch_import_offline_1",
                prefilters: [
                    {
                        property: "apply_type_id",
                        value: this.id,
                        prefilters: null,
                        relationalOperator: null,
                    },
                ],
                bigActionImportParams: {
                    // inputs_parameters: [
                    //     {
                    //         property: "apply_type",
                    //         value: "1",
                    //     },
                    // ],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                    prefilters: [
                        {
                            property: "apply_type_id",
                            value: this.id,
                            prefilters: null,
                            relationalOperator: null,
                        },
                    ],
                },
            }
            this.importConfig2 = {
                templateUrl:
                    window.location.origin + "/file/政策/就业生活补助导入表.xlsx",
                modelName: "zc_apply_personal",
                actionName: "batch_import_bk",
                prefilters: [
                    {
                        property: "company.instance.apply_type_id",
                        value: this.id,
                        prefilters: null,
                        relationalOperator: null,
                    },
                ],
                bigActionImportParams: {
                    // inputs_parameters: [
                    //     {
                    //         property: "apply_type",
                    //         value: "1",
                    //     },
                    // ],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                    prefilters: [
                        {
                            property: "company.instance.apply_type_id",
                            value: this.id,
                            prefilters: null,
                            relationalOperator: null,
                        },
                    ],
                },
            }
            return buildConfig4RemoteMeta(
                "zc_apply_personal",
                "manage_company_personal_list_jysh_for_detail",
                {
                    useLabelWidth: true,
                    useTabs: true,
                    disabledOpt: true,
                    prefilters: {
                        "company.instance.apply_type_id": this.id,
                    },
                }
            ).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig = r.tableConfig as TableConfig
            this.tableConfig = null
            this.$nextTick(() => {
                console.log("r", JSON.parse(JSON.stringify(r)))
                this.tableConfig = tableConfig
                this.tableConfig!.column = [...r.columns]
                // if (this.type === ServeTargetType.企业) {
                //     }
                this.columns = this.tableConfig!.column
            })
        }

        private toDetail(row: Row) {
            console.log("row", JSON.parse(JSON.stringify(row)))

            let pageIndex = 1
            pageIndex = this.pageData.item_index / this.pageData.item_size + 1

            sessionStorage.setItem(
                queryParamsKey,
                JSON.stringify({
                    pageIndex,
                    queryParams: {},
                    policyName: this.policyName,
                })
            )
            this.$router.push({
                name: routesMap.policyApplyRecordDD.detail,
                query: {
                    id: row._access_key + "",
                    rowId: row.id + "",
                    serviceType: this.type + "",
                    from: this.$route.name,
                },
            })
        }

        loaded(data: { name: string; item_index: number }[]) {
            this.pageData = data
        }

        private toPersonDetail(key: string) {
            if (!this.isPerson) {
                return
            }
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: key,
                    from: this.$route.name,
                },
            })
        }

        private toAgentDetail(key: string) {
            this.$router.push({
                name: routesMap.employmentManage.companyManageDetail,
                query: { id: key, from: this.$route.name },
            })
        }

        private onTabNameChange(tabName: string) {
            if (this.type === ServeTargetType.企业) return
            console.log("object", tabName)
            this.activeName = tabName
            this.$emit("onTabChange", this.activeName)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .list-view {
        background: #fff;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
    }
    /deep/.table {
        padding: 0;
        padding-bottom: 20px;
        .table-tabs {
            background: #f6f7f9;
        }
    }
</style>
