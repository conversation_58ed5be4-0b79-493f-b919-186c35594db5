import { RouteConfig } from "@/router"
import { routesMap } from "@/router/direction"
import { bigScreenSmartEmploymenteMonitor } from "./智慧就业检测大屏/routers"
import { bigScreeDataStatisticsAnalysisr } from "./数据统计分析大屏/routers"

export const bigScreenServiceMonitor: RouteConfig[] = [
    {
        path: `/big-screen/job-service-monitor`,
        name: routesMap.bigScreen.jobServiceMonitor,
        meta: {},
        component: () => import("./首页/index.vue"),
    },
    {
        path: `/big-screen/self-cleaning-monitor`,
        name: routesMap.bigScreen.selfCleaningMonitor,
        meta: {},
        component: () => import("./自洁/index.vue"),
    },
    {
        path: `/big-screen/key-group-monitor`,
        name: routesMap.bigScreen.keyGroupMonitor,
        meta: {},
        component: () => import("./重点人群/index.vue"),
    },
    {
        path: `/big-screen/data-statistics-analysis`,
        name: routesMap.bigScreen.dataStatisticsAnalysisSingle,
        meta: {},
        component: () => import("./数据统计分析大屏/index.vue"),
    },
    ...bigScreenSmartEmploymenteMonitor,
    ...bigScreeDataStatisticsAnalysisr,
]
