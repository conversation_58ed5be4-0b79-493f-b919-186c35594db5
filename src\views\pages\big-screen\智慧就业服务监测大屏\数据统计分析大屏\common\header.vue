<template>
    <div class="d-flex header-content">
        <div></div>
        <div>{{ title }}</div>
        <!-- <div>更多 ></div> -->
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: {} })
    export default class Template extends Vue {
        @Prop({ default: "" })
        private title!: string
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .header-content {
        // background-image: url("../../assets/title.png");
        background-image: url("../assets/title.png");
        background-size: 100% 43px;
        width: 100%;
        height: 43px;

        display: flex;
        align-items: center;
        padding-left: 40px;
        font-weight: 800;
        font-size: 22px;
        color: #bdd8ff;
        line-height: 18px;

        margin-bottom: 8px;
    }
</style>
