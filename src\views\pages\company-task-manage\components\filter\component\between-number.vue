<template>
    <div class="range">
        <el-form
            ref="form"
            :model="formValue"
            :rules="rules"
            class="d-flex align-items-center"
        >
            <el-form-item prop="leftInput" style="width: 40%">
                <el-input
                    v-model="formValue.preValue"
                    type="number"
                    :placeholder="
                        data.placeholder ? data.placeholder : '单位：元'
                    "
                ></el-input>
            </el-form-item>
            <div class="separated">-</div>
            <el-form-item prop="rightInput" style="width: 40%">
                <el-input
                    v-model="formValue.sufValue"
                    type="number"
                    :placeholder="
                        data.placeholder ? data.placeholder : '单位：元'
                    "
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script lang="ts">
    import { ElForm } from "element-ui/types/form"
    import { Component, Ref } from "vue-property-decorator"
    import FilterItem from "./filter-item"

    @Component({ components: {} })
    export default class BetweenNumber extends FilterItem {
        @Ref("form")
        private form!: ElForm

        private formValue = {
            preValue: "",
            sufValue: "",
        }

        created() {
            if (this.defaultParams) {
                if (this.defaultParams.filter_key === this.data.field) {
                    console.log("this.defaultParams  between-number inner")
                    console.log(this.defaultParams)
                    console.log(this.data)

                    const defaultValues =
                        this.defaultParams.filter_default_value || []
                    this.formValue.preValue = defaultValues[0].split("|")[0]
                    this.formValue.sufValue = defaultValues[0].split("|")[1]
                }
            }
        }

        private rules = {
            rightInput: [
                {
                    validator: (rule: any, value: any, callback: any) => {
                        if (
                            parseInt(this.formValue.sufValue) <=
                            parseInt(this.formValue.preValue)
                        ) {
                            callback(new Error("右边的值应大于左边的值"))
                        } else {
                            this.form?.clearValidate()
                            callback()
                        }
                    },
                    trigger: "blur",
                },
            ],

            leftInput: [
                {
                    validator: (rule: any, value: any, callback: any) => {
                        if (
                            parseInt(this.formValue.sufValue) <=
                            parseInt(this.formValue.preValue)
                        ) {
                            callback(new Error("右边的值应大于左边的值"))
                        } else {
                            this.form?.clearValidate()
                            callback()
                        }
                    },
                    trigger: "blur",
                },
            ],
        }

        private getDisplay() {
            return this.formValue.preValue + "-" + this.formValue.sufValue
        }

        private getValue() {
            return [this.formValue.preValue + "|" + this.formValue.sufValue]
        }

        public getInfo() {
            if (!this.formValue.preValue || !this.formValue.sufValue) {
                return null
            }
            const title = this.data.title
            const display = this.getDisplay()
            const key = this.data.field
            const value = this.getValue()
            const from = this.data.from
            return {
                title,
                display,
                key,
                value,
                from,
            }
        }
    }
</script>

<style lang='less' scoped>
    .range {
        display: flex;
        flex: 10px;
        gap: 10px;
        align-items: center;

        /deep/ .el-form-item {
            margin-bottom: 0;
        }

        .separated {
            margin-left: 5px;
            margin-right: 5px;
        }
    }
</style>
