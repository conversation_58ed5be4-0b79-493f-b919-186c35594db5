import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"
export interface Row {
    name: string
    mobile: string
    training_willingness_work_type: string
    training_willingness_work_type_label: string
    train_willing_province_code: string
    train_willing_city_code: string
    train_willing_area_code: string
    train_willing_countryside_code: string
    train_willing_village_code: string
    train_willing_province_code_two: string
    train_willing_city_code_two: string
    train_willing_area_code_two: string
    train_willing_countryside_code_two: string
    train_willing_village_code_two: number
    train_willing_province_code_three: string
    train_willing_city_code_three: string
    train_willing_area_code_three: string
    train_willing_countryside_code_three: string
    train_willing_village_code_three: number
    train_willing_datetime: string
    id: number
    v: number
    [key: string]: any
}

export const predict = {
    name: "",
    mobile: "",
    training_willingness_work_type: "label",
    train_willing_province_code: "",
    train_willing_city_code: "",
    train_willing_area_code: "",
    train_willing_countryside_code: "",
    train_willing_village_code: "",
    train_willing_province_code_two: "",
    train_willing_city_code_two: "",
    train_willing_area_code_two: "",
    train_willing_countryside_code_two: "",
    train_willing_village_code_two: "",
    train_willing_province_code_three: "",
    train_willing_city_code_three: "",
    train_willing_area_code_three: "",
    train_willing_countryside_code_three: "",
    train_willing_village_code_three: "",
    train_willing_datetime: "label",
    sys_province_name_region_name: "sys_province_name#region_name_label",
    sys_city_name_region_name: "sys_city_name#region_name",
    sys_area_name_region_name: "sys_area_name#region_name_label",
    sys_countryside_name_region_name: "sys_countryside_name#region_name_label",
    sys_village_name_region_name: "sys_village_name#region_name_label",
    sys_province_name_region_name_two:
        "sys_province_name_two#region_name_label",
    sys_city_name_region_name_two: "sys_city_name_two#region_name",
    sys_area_name_region_name_two: "sys_area_name_two#region_name_label",
    sys_countryside_name_region_name_two:
        "sys_countryside_name_two#region_name_label",
    sys_village_name_region_name_two: "sys_village_name_two#region_name_label",
    sys_province_name_region_name_three:
        "sys_province_name_three#region_name_label",
    sys_city_name_region_name_three: "sys_city_name_three#region_name",
    sys_area_name_region_name_three: "sys_area_name_three#region_name_label",
    sys_countryside_name_region_name_three:
        "sys_countryside_name_three#region_name_label",
    sys_village_name_region_name_three:
        "sys_village_name_three#region_name_label",
}

export const predict2 = {
    name: "id_card_openid#user_profile_basic.name",
    mobile: "id_card_openid#user_profile_basic.mobile",
    start_job_industry: "label",
    start_job_type_work_display: "",
    profile_access_key: ""

}

export const predict3 = {
	name_hide: "profile#name_hide",
	mobile: "profile#mobile",
	training_willingness_work_type: "label",
	train_willing_province_code: "",
	train_willing_city_code: "",
	train_willing_area_code: "",
	train_willing_countryside_code: "",
	train_willing_village_code: "",
	train_willing_datetime: "label",
    profile_access_key: "profile#_access_key",
    province_region_name: "province#region_name",
    city_region_name: "city#region_name",
    area_region_name: "area#region_name",
    countryside_region_name: "countryside#region_name",
    village_region_name: "village#region_name",
}

export const columns: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile",
        render(h, row) {
            return renDesensitizationView(h, row.mobile)
        },
        showOverflowTip: true,
    },
    {
        label: "培训意向工种",
        prop: "training_willingness_work_type_label",
        showOverflowTip: true,
    },
    {
        label: "期望培训地点",
        prop: "cfxd2024_count",
        render(h, row) {
            return h(
                "span",
                {},
                getAddress(row, [
                    "sys_province_name_region_name",
                    "sys_city_name_region_name",
                    "sys_area_name_region_name",
                    "sys_countryside_name_region_name",
                    "sys_village_name_region_name",
                ]) || "-"
            )
        },
        showOverflowTip: true,
    },
    {
        label: "期望培训地点2",
        prop: "xxzt_count",
        render(h, row) {
            return h(
                "span",
                {},
                getAddress(row, [
                    "sys_province_name_region_name_two",
                    "sys_city_name_region_name_two",
                    "sys_area_name_region_name_two",
                    "sys_countryside_name_region_name_two",
                    "sys_village_name_region_name_two",
                ]) || "-"
            )
        },
        showOverflowTip: true,
    },
    {
        label: "期望培训地点3",
        prop: "xxzt_count",
        render(h, row) {
            return h(
                "span",
                {},
                getAddress(row, [
                    "sys_province_name_region_name_three",
                    "sys_city_name_region_name_three",
                    "sys_area_name_region_name_three",
                    "sys_countryside_name_region_name_three",
                    "sys_village_name_region_name_three",
                ]) || "-"
            )
        },
        showOverflowTip: true,
    },
    {
        label: "期望培训时间",
        prop: "train_willing_datetime_label",
        showOverflowTip: true,
    },
]

export const columns2: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile",
        render(h, row) {
            return renDesensitizationView(h, row.mobile)
        },
        showOverflowTip: true,
    },
    {
        label: "创业意向行业",
        prop: "start_job_industry_label",
        showOverflowTip: true,
    },
    {
        label: "创业意向工种",
        prop: "start_job_type_work_display",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
        fixed: "right"
    },
]

export const columns3: TableColumn[] = [
    {
        label: "姓名",
        prop: "name_hide",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile",
        render(h, row) {
            return renDesensitizationView(h, row.mobile)
        },
        showOverflowTip: true,
    },
    {
        label: "培训意向工种",
        prop: "training_willingness_work_type_label",
        showOverflowTip: true,
        render(h, row) {
            return row.training_willingness_work_type_label || "-"
        },
    },
    {
        label: "期望培训地点",
        prop: "cfxd2024_count",
        render(h, row) {
            return h(
                "span",
                {},
                getAddress(row, [
                    "province_region_name",
                    "city_region_name",
                    "area_region_name",
                    "countryside_region_name",
                    "village_region_name",
                ]) || "-"
            )
        },
        showOverflowTip: true,
    },
    {
        label: "期望培训时间",
        prop: "train_willing_datetime_label",
        showOverflowTip: true,
        render(h, row) {
            return row.train_willing_datetime_label || "-"
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
        fixed: "right"
    },
]

const tableFilter: TableFilter[] = [
    {
        label: "人员信息",
        option: { placeholder: "请输入姓名、手机号" },
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "意向工种",
        type: FormType.Select,
        prop: "training_willingness_work_type",
    },
    {
        label: "意向时间",
        type: FormType.DatePicker,
        prop: "train_willing_datetime",
        option: {
            type: "daterange",
        },
    },
    {
        label: "户籍城市",
        type: FormType.Cascader,
        prop: "household_province_code",
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
]

const tableFilter2: TableFilter[] = [
    {
        label: "人员信息",
        option: { placeholder: "请输入姓名、手机号" },
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "意向工种",
        type: FormType.Select,
        prop: "start_job_type_work",
    },
    {
        label: "创业行业",
        type: FormType.Select,
        prop: "start_job_industry",
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("user_profile_train_willingness")
            .list("back_list"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
    }
}

export function tableConfig2(): TableConfig {
    return {
        model: sdk.core
            .model("user_profile_start_job")
            .list("list_start_intent"),
        filter: tableFilter2,
        defaultPageSize: 10,
        predict: predict2,
    }
}

export function tableConfig3(): TableConfig {
    return {
        model: sdk.core
            .model("user_profile_current_train_info")
            .list("back_list"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict3,
    }
}
