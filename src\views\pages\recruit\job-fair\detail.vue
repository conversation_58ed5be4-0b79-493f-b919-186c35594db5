<template>
    <div
        class="core-ui-table-container container"
        v-if="row"
        :key="refreshQueryParams"
    >
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    type="primary"
                    @click="toAdd(row.id)"
                    plain
                    v-role="['model.job_fair.action.update']"
                >
                    编辑
                </el-button>
                <el-button
                    type="primary"
                    plain
                    @click="showImportPop = true"
                    v-role="['model.job_fair.action.import_company_position']"
                    :disabled="disableImport"
                >
                    导入企业和岗位
                </el-button>
                <!-- <el-button type="primary" plain @click="copy" v-if="isDd">
                    复制小程序地址
                </el-button> -->
                <el-button
                    type="primary"
                    @click="drop"
                    v-if="row.audit_status === auditStatus.审核通过"
                    v-role="[
                        'model.job_fair.action.off_shelf',
                        'model.job_fair.action.on_shelf',
                    ]"
                >
                    {{ row.status === onlineStatus.已发布 ? "取消" : "" }}发布
                </el-button>
                <el-button
                    type="primary"
                    @click="submit(row.id)"
                    v-if="
                        [auditStatus.草稿, auditStatus.审核未通过].includes(
                            row.audit_status
                        )
                    "
                    v-role="['model.job_fair.action.audit_submit']"
                >
                    提交审核
                </el-button>
                <el-button
                    type="primary"
                    @click="toAudit(row.id)"
                    v-if="row.audit_status === auditStatus.待审核"
                    v-role="[
                        'model.job_fair.action.audit_deny',
                        'model.job_fair.action.audit_pass',
                    ]"
                >
                    审核
                </el-button>
            </div>
        </div>
        <el-tabs v-model="curTab">
            <el-tab-pane label="招聘会信息" name="1" lazy>
                <tab-1 ref="tab1" :row="row"></tab-1>
            </el-tab-pane>
            <!-- <el-tab-pane label="参会信息审核" name="5" lazy>
                <tab-5 ref="tab5" :row="row"></tab-5>
            </el-tab-pane> -->
            <el-tab-pane label="报名企业" name="2" lazy>
                <tab-2
                    ref="tab2"
                    :row2="row"
                    @changeTab3Filter="changeTab3Filter"
                    :disableImport="disableImport"
                ></tab-2>
            </el-tab-pane>
            <el-tab-pane label="报名岗位" name="3">
                <tab-3 ref="tab3" :row2="row" v-if="row.id"></tab-3>
            </el-tab-pane>
            <el-tab-pane label="参会人员" name="4" lazy v-if="!isEz">
                <tab-4 ref="tab4" :row2="row"></tab-4>
            </el-tab-pane>
            <el-tab-pane v-if="showStatics" label="数据填报" name="9" lazy>
                <TabStatics :row="row" />
            </el-tab-pane>
            <el-tab-pane label="投递信息" name="7" lazy>
                <tab-7 ref="tab7" :row2="row"></tab-7>
            </el-tab-pane>
            <el-tab-pane label="展位信息" name="6" lazy v-if="row.type === 2">
                <tab-6 ref="tab6" :row="row" @refresh="init"></tab-6>
            </el-tab-pane>
            <el-tab-pane label="操作记录" name="8" lazy>
                <tab-8 ref="tab8" :row2="row"></tab-8>
            </el-tab-pane>
        </el-tabs>
        <excel-import
            v-if="importConfig"
            v-model="showImportPop"
            title="导入参会企业"
            placeholder="请点击「确定」将参会企业上传"
            :importConfig="importConfig"
            :needVerifyContent="needVerifyContent"
            :canExport="true"
            exportFileName="列表文件"
            underListWidth="1200"
            @refresh="refreshImport"
        >
            <template v-slot:tips>
                <div class="tips">
                    <div>1.模板已于2024年10月28日更新</div>
                    <div>2.请下载最新模版填写上传</div>
                </div>
            </template>
        </excel-import>
    </div>
</template>

<script lang='ts'>
    import { config, EnvProject } from "@/config"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { copyTextToClipboard } from "uniplat-sdk/build/main/helpers/clipboard"
    import { Component, Vue } from "vue-property-decorator"
    import XLSX from "xlsx"
    import { AuditStatus, drop, predict, Status, submit2Apply } from "."
    import Tab1 from "./components/tab-1.vue"
    import Tab2 from "./components/tab-2.vue"
    import Tab3 from "./components/tab-3.vue"
    import Tab4 from "./components/tab-4.vue"
    import Tab5 from "./components/tab-5.vue"
    import Tab6 from "./components/tab-6/index.vue"
    import Tab7 from "./components/tab-7.vue"
    import Tab8 from "./components/tab-8.vue"
    import TabStatics from "./components/tab-statics.vue"

    const fileUniKey = "1a39f03c58e24dd8b432d9fdb940f2ae"

    @Component({
        name: routesMap.recruit.jobFairDetail,
        components: {
            Tab1,
            Tab2,
            Tab3,
            Tab4,
            Tab5,
            Tab6,
            Tab7,
            Tab8,
            ExcelImport,
            TabStatics,
        },
    })
    export default class jobFairDetail extends Vue {
        private row: any = null
        private id = ""
        private curTab = "1"
        private onlineStatus = Status
        private showImportPop = false
        private importConfig: any = null
        private auditStatus = AuditStatus
        private isEz = [EnvProject.鄂州项目].includes(config.envProject)

        showStatics = [EnvProject.荆州项目].includes(config.envProject)

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.jobFairDetail,
        }

        async refreshImport(v: any) {
            await sdk.core
                .model("uniplat_big_action_xg")
                .detail(v.taskId, "manage_detail")
                .query()
                .then((r: any) => {
                    const row: any = sdk.buildRow(r.row, {
                        total: "",
                        fail: "",
                        success: "",
                        file_show_name: "",
                    })
                    this.$message.success(
                        `${row.file_show_name} 导入完毕，成功${
                            row.success || "0"
                        }条，失败${row.fail || "0"}条，可进入历史记录查看详情`
                    )

                    return 1
                })
            if (v.showError) return
            // this.showImportPop = false
            this.init()
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const arr = this.from
                ? getCacheBreadcrumbsByRoutePath(this.from)
                : [
                      {
                          label: "招聘会管理",
                          to: routesMap.recruit.jobFair,
                      },
                  ]
            const d: BreadcrumbItem[] = [
                ...arr,
                {
                    label: this.row?.title || "招聘会详情",
                    to: {
                        name: routesMap.recruit.jobFairDetail,
                        query: {
                            ...this.$route.query,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.jobFairDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string | undefined
        }

        private get isDd() {
            return [EnvProject.掇刀项目].includes(config.envProject)
        }

        private get disableImport() {
            return +new Date() > +new Date(this.row.apply_time_label)
        }

        mounted() {
            this.init()
        }

        private needVerifyContent(originFile: any) {
            if (!originFile) {
                this.$message.warning("未检验到内容")
                return Promise.resolve(false)
            }
            return new Promise((resolve) => {
                const reader = new FileReader()
                const errorTips = "当前模版不是最新模版，请下载最新模版上传！"
                reader.onload = (e: any) => {
                    const data = new Uint8Array(e.target.result)
                    const workbook = XLSX.read(data, { type: "array" })
                    const sheetName = workbook.SheetNames[1]
                    const sheet = workbook.Sheets[sheetName]
                    if (!sheet) {
                        this.$message.warning(errorTips)
                        return resolve(false)
                    }
                    if (!sheet["!ref"]) {
                        this.$message.warning(errorTips)
                        return resolve(false)
                    }
                    const jsonData: string[][] = XLSX.utils.sheet_to_json(sheet, {
                        header: 1,
                    })
                    if (jsonData[0] && jsonData[0][0]) {
                        const res = jsonData[0][0]
                        if (res.trim && res.trim() === fileUniKey) {
                            return resolve(true)
                        }
                    }
                    this.$message.warning(errorTips)
                    resolve(false)
                }
                reader.readAsArrayBuffer(originFile.raw)
            })
        }

        changeCurTab(name: string) {
            this.curTab = name
        }

        private changeTab3Filter(name: string) {
            this.changeCurTab("3")
            this.$nextTick(() => {
                ;(this.$refs.tab3 as any).setFilter(name)
            })
        }

        private submit(id: string) {
            submit2Apply(id, this.init)
        }

        private init() {
            this.row = null
            const id = (this.$route.query.id as string) || this.id
            if (!id) return
            this.id = id
            this.setBreadcrumbs()
            this.importConfig = {
                templateUrl:
                    window.location.origin + "/file/参会企业导入模板1028.xlsx",
                modelName: "job_fair",
                actionName: "import_company_position",
                bigActionImportParams: {
                    inputs_parameters: [],
                    selected_list: [
                        {
                            id: this.id,
                            v: 0,
                        },
                    ],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
            pageLoading(() => {
                return sdk.core
                    .model("job_fair")
                    .detail(this.id, "for_operate")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                        this.setBreadcrumbs()
                        if (this.showStatics && this.$route.query?.tab === "9") {
                            this.curTab = "9"
                        }
                    })
            })
        }

        private drop() {
            return drop(this.row, this.init)
        }

        private toAudit(id: string) {
            this.$router.push({
                name: routesMap.recruit.auditDetail,
                query: {
                    id,
                },
            })
        }

        private toAdd(id?: string) {
            this.$router.push({
                name: routesMap.recruit.addJobFair,
                query: {
                    id,
                    from: routesMap.recruit.jobFairDetail,
                },
            })
        }

        private copy() {
            copyTextToClipboard(`/pages/sub/job-fair/detail?id=${this.id}`)
            this.$message.success("复制成功")
        }
    }
</script>

<style lang='scss' scoped>
    ::v-deep .detail-row .item {
        line-height: 34px;
    }

    .tips {
        width: 165px;
        font-size: 12px;
        color: red;
        margin-top: 7px;
        line-height: 1.5;
    }
</style>
