<template>
    <div class="core-ui-table-container container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="u-p-x-20 create-apply u-flex u-row-center u-p-y-30">
            <div class="content">
                <form-builder
                    ref="formBuilder"
                    labelWidth="90px"
                ></form-builder>
                <div class="u-flex u-m-t-20 u-row-center">
                    <el-button
                        type="primary"
                        @click="close"
                        plain
                        class="custom-btn btn u-m-r-30"
                    >
                        取消
                    </el-button>
                    <el-button
                        type="primary"
                        @click="confirm"
                        class="custom-btn btn u-m-0"
                    >
                        确定
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { Component } from "vue-property-decorator"
    import { createFormConfig } from "."
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
    } from "../../single-page/components/tags-view"

    @Component({ components: { FormBuilder } })
    export default class ApplyAdd extends FormController {
        breadcrumbs: BreadcrumbItem[] = []

        setBreadcrumbs() {
            let d: BreadcrumbItem[] = []
            this.from &&
                (d = [
                    ...getCacheBreadcrumbsByRoutePath(this.from),
                    {
                        label: (this.id ? "编辑" : "创建") + "申请",
                    },
                ])
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string | undefined
        }

        mounted() {
            this.init()
        }

        private id = ""

        private init() {
            this.id = (this.$route.query.id as string) || ""
            this.setBreadcrumbs()
            return buildFormSections(createFormConfig(+this.id)).then((r) => {
                this.buildFormFull(r)
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model("share_employee_apply")
                    .action(this.id ? "workbenchEdit" : "addNewApply")
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: this.id
                            ? [
                                  {
                                      id: this.id,
                                      v: 0,
                                  },
                              ]
                            : [],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success(this.id ? "编辑" : "创建成功")
                        this.callRefresh(routesMap.recruit.work)
                        this.callRefresh(routesMap.recruit.workDetail)
                        closeCurrentTap()
                    })
            })
        }

        private close() {
            closeCurrentTap({
                name: this.from,
                query: this.id
                    ? {
                          id: this.id,
                      }
                    : undefined,
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .select {
        width: 320px;
    }
    .btn {
        width: 100px;
        height: 36px;
    }
    .content {
        width: 600px;
    }
    ::v-deep .el-form-item__label {
        text-align: right !important;
    }
    ::v-deep .limit-3 {
        div.el-upload.el-upload--picture-card {
            display: none;
        }
    }
    .create-apply {
        background: #fff;
        ::v-deep .el-form-item__label {
            text-align: left;
            color: #555;
            font-size: 14px;
        }
    }
</style>
