<template>
    <div class="u-p-y-20 u-p-x-30 u-font-14 u-m-b-40">
        <div class="u-m-b-20 u-flex u-row-between title">
            <div>基础信息</div>
            <!-- <div class="pointer u-font-14 font-weight-4 primary">
                查看详情 >
            </div> -->
        </div>
        <div class="u-flex u-col-top">
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
                class="u-p-x-20"
            >
            </detail-row-col>
            <div class="pic">
                <div class="photo u-rela">
                    <img :src="pic" class="photo" />
                    <img :src="statusPic" class="status-pic" />
                </div>
            </div>
        </div>
        <div class="u-m-t-25" v-if="false">
            <div class="u-flex u-m-b-20">
                <div class="t u-flex u-row-right">
                    <div class="tag">困难人群</div>
                </div>
                <div class="hint">
                    该求职者属于困难人群，招聘后可领取一次性吸纳就业补贴
                </div>
                <el-button class="btn">了解详情</el-button>
            </div>
            <div class="u-flex">
                <div class="t u-flex u-row-right">
                    <div class="tag">高校毕业生</div>
                </div>
                <div class="hint">
                    该求职者属于高校毕业生，招聘后可领取高校毕业生就业补贴
                </div>
                <el-button class="btn">了解详情</el-button>
            </div>
        </div>
        <slot></slot>
        <div class="u-m-y-20 u-flex u-row-between">
            <div class="title">投递信息</div>
        </div>
        <detail-row-col
            :labelStyle="labelStyle"
            :list="items2"
            class="u-p-x-20"
        >
        </detail-row-col>
        <div class="u-m-y-20 u-flex u-row-between" v-if="!isPublic && isHz">
            <div class="title">企业反馈记录</div>
        </div>
        <FeedbackRecord :id="row.id" v-if="row && !isPublic && isHz" />
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { getAddress } from "@/utils"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import FeedbackRecord from "./feedback-record.vue"
    import { config, EnvProject } from "@/config"

    @Component({ components: { DetailRowCol, FeedbackRecord } })
    export default class DetailView extends Vue {
        @Prop()
        private row!: any

        @Prop()
        private isPublic!: boolean

        private showMobile = false
        private showIdCard = false

        private isHz = [EnvProject.黄州项目, EnvProject.孝感项目].includes(
            config.envProject
        )

        private get labelStyle() {
            return {
                minWidth: "126px",
                textAlign: "right",
                marginRight: "10px",
                color: "#9098A6",
                lineHeight: "20px",
            }
        }

        private get pic() {
            return this.row.avatar || "/img/company/recruit/deliver/pic.png"
        }

        private get statusPic() {
            return `/img/company/recruit/deliver/status-${this.row.status}.png`
        }

        private get items2(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "投递岗位",
                    vNode: h("span", { class: "salary" }, [
                        h("span", {}, this.row.position_name),
                        h(
                            "span",
                            {
                                class: "pointer primary u-m-l-20",
                                on: {
                                    click: this.toJob,
                                },
                            },
                            "查看详情"
                        ),
                    ]),
                    span: 8,
                },
                {
                    label: "投递时间",
                    value: this.row.create_time_label,
                },
                {
                    label: "投递平台",
                    value: this.row.client_label,
                },
                {
                    label: "投递来源",
                    value: this.row.created_from_label,
                },
                {
                    label: "面试时间",
                    value: this.row.interview_time_label,
                },
                {
                    label: "面试地点",
                    value: this.row.interview_address_label,
                },
            ].map((e) => {
                return { ...e, span: 8 }
            })
        }

        private toggleMobileShow() {
            this.showMobile = true
        }

        private get items(): Array<ColItem & { hidden?: boolean }> {
            if (!this.row) return []
            const h = this.$createElement
            const isP = this.row.profile_id
            return [
                {
                    label: "姓名",
                    value: this.row.name,
                },
                {
                    label: "联系方式",
                    vNode: renDesensitizationView(h, {
                        value: isP ? this.row.p_mobile : this.row.mobile,
                    }),
                },
                {
                    label: "身份证号",
                    vNode: renDesensitizationView(h, {
                        value: isP ? this.row.p_id_card : this.row.id_card,
                        dValue: isP
                            ? this.row.p_id_card_encode
                            : this.row.id_card_encode,
                    }),
                    hide: !this.isPublic,
                },
                {
                    label: "身份证号",
                    value: isP
                        ? this.row.p_id_card_encode
                        : this.row.id_card_encode,
                    hide: this.isPublic,
                },
                {
                    label: "性别",
                    value: isP ? this.row.p_sex_label : this.row.sex_label,
                },
                {
                    label: "年龄",
                    value: isP ? this.row.p_age : this.row.age,
                },
                {
                    label: "户籍地",
                    value: getAddress(
                        this.row,
                        this.row.profile_id
                            ? [
                                  "p_household_province",
                                  "p_household_city",
                                  "p_household_area",
                                  "p_household_detail",
                              ]
                            : [
                                  "household_province",
                                  "household_city",
                                  "household_area",
                                  "household_detail",
                              ]
                    ),
                },
                {
                    label: "常住地",
                    value: getAddress(
                        this.row,
                        this.row.profile_id
                            ? [
                                  "p_permanent_province",
                                  "p_permanent_city",
                                  "p_permanent_area",
                                  "p_permanent_detail",
                              ]
                            : [
                                  "permanent_province",
                                  "permanent_city",
                                  "permanent_area",
                                  "permanent_detail",
                              ]
                    ),
                },
                {
                    label: "学历",
                    value: isP
                        ? this.row.p_education_label
                        : this.row.education_label,
                },
                {
                    label: "政治面貌 ",
                    value: this.row.political_outlook_label,
                },
                {
                    label: "工作时间",
                    value: this.row.job_years,
                },
                {
                    label: "求职状态",
                    value: this.row.employment_status_label,
                },
                {
                    label: "自我介绍",
                    value: this.row.self_evaluation,
                },
                {
                    label: "简历来源",
                    value: this.row.profile_created_from_label,
                },
                {
                    label: "资格审核",
                    value: this.row.qualification_audit_status_label || "待审核",
                    hide: this.row.position_type !== 2,
                },
            ]
                .map((e) => {
                    return { ...e, span: 12 }
                })
                .filter((e) => !e.hide)
        }

        private toJob() {
            this.$router.push({
                name:
                    this.row!.position_type === 2
                        ? routesMap.recruit.publicJobDetail
                        : routesMap.recruit.jobDetail,
                query: {
                    id:
                        (this.row.position_access_key || this.$route?.query?.id) +
                        "",
                    from: routesMap.recruit.jobDeliverDetail,
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        font-weight: 500;
        color: #9098a6;
    }
    .value {
        margin-left: 20px;
    }
    .light {
        color: #7a808c;
    }
    .t {
        min-width: 100px;
        text-align: right;
        font-size: 14px;
        margin-right: 20px;
        .tag {
            background: rgba(#598bff, 0.1);
            border-radius: 5px 5px 5px 5px;
            color: #598bff;
            line-height: 28px;
            padding: 0 10px;
        }
    }
    .hint {
        color: #7a808c;
        font-size: 12px;
        width: 375px;
        &::before {
            content: "*";
            color: #ff8d59;
        }
    }
    .btn {
        height: 32px;
    }
    .pic {
        width: 150px;
        height: 150px;
        flex: none;
        text-align: right;
        .photo {
            width: 96px;
            height: 115px;
        }
        .status-pic {
            position: absolute;
            width: 74px;
            height: 65px;
            left: -43px;
            bottom: -30px;
            z-index: 1;
        }
    }

    .title {
        width: 100%;
        height: 40px;
        background: #f8f8f8;
        color: #222;
        font-size: 18px;
        font-weight: 600;
        line-height: 40px;
        padding: 0 20px;
    }
    ::v-deep .detail-row {
        border-radius: 5px;
        padding: 15px 0;
        padding-right: 100px;
        line-height: 20px;
        .item:nth-of-type(3n-1) .label {
            width: 120px;
        }
        .item {
            line-height: 20px;
        }
    }
</style>
