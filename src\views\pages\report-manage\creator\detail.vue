<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button
                    @click="actionTask('start')"
                    type="primary"
                    v-if="noStart && showStartBtn"
                    >开始填报</el-button
                >
                <el-button
                    @click="actionTask('finish')"
                    type="primary"
                    v-else-if="isGoing && showFinishBtn"
                    >结束填报</el-button
                >
                <el-button @click="edit" type="primary" v-if="showEditBtn" plain
                    >编辑</el-button
                >
                <el-button
                    @click="deleteDetail"
                    type="primary"
                    v-if="showDeleteBtn"
                    plain
                    >删除</el-button
                >
                <el-button
                    @click="exportTemplate"
                    type="primary"
                    plain
                    v-if="isMultiple"
                    >模板下载</el-button
                >
            </div>
        </div>

        <div class="detail-top-box" v-if="details && items">
            <detail-row-col
                :labelStyle="labelStyle"
                :list="items"
                class="u-p-x-20"
            >
            </detail-row-col>
        </div>

        <div class="u-p-20 bg-white" v-if="details && tableColumns && tableColumns.length">
            <DetailTableMultiple
                :columns="tableColumns"
                :id="reportId"
                v-if="isMultiple"
                ref="detailTable"
                @description="description"
            />
            <DetailTable :columns="tableColumns" :rows="rows" v-else />
        </div>
        <PublishPop v-model="showPublishPop" :id="reportId" />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        closeCurrentTap,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailTable from "../components/detail-table.vue"
    import DetailTableMultiple from "../components/detail-table-multiple.vue"
    import PublishPop from "./components/publish-pop.vue"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import {
        DetailItemType,
        DetailRow,
        IndicatorColumn,
        IndicatorDataRows,
        primaryRows,
        Status,
    } from "../index"
    import { getShowBtn4Page } from "../../collect-task-manage/components/build-table"
    import { ExcelColumnsGenerator } from "../excel-columns-generator"
    import { TableColumn } from "@/core-ui/component/table"

    @Component({
        name: routesMap.reportManage.creator.detail,
        components: { DetailRowCol, DetailTable, DetailTableMultiple, PublishPop },
    })
    export default class ReportManageDetail extends Vue {
        private items: ColItem[] = []

        private breadcrumbs: BreadcrumbItem[] = []

        private showStartBtn = false
        private showFinishBtn = false
        private showEditBtn = false
        private showDeleteBtn = false

        private showList = false

        private showPublishPop = false

        private status: Status | null = null

        private columns: IndicatorColumn[] = []
        private rows: IndicatorDataRows[] = []
        private details: DetailRow | null = null
        private tableColumns: TableColumn[] | any[] = []

        private exportDescription: any = {}

        private childrenColumnsLabel: string[] = []

        private get labelStyle() {
            return {
                minWidth: "88px",
                textAlign: "left",
                color: "#555",
            }
        }

        private get noStart() {
            return this.status === Status.未开始
        }

        private get isGoing() {
            return this.status === Status.进行中
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.reportManage.creator.detail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: `报表管理`,
                    to: {
                        name: routesMap.reportManage.creator.index,
                    },
                },
                {
                    label: "报表详情",
                    to: {
                        name: routesMap.reportManage.creator.detail,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.reportManage.creator.detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private actionTask(action: string) {
            return pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_task")
                    .action(action)
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: this.reportId }],
                    })
                    .execute()
                    .then(() => {
                        this.init()
                    })
            })
        }

        private reportId = ""

        private get isMultiple() {
            return this.details && this.details.type === DetailItemType.Multiple
        }

        created() {
            this.init()
        }

        private init() {
            this.details = null
            this.items = []
            this.columns = []
            this.tableColumns = []
            this.rows = []

            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_task")
                    .detail(this.$route.query.id as string, "for_operate")
                    .query()
                    .then((res) => {
                        this.details = sdk.buildRow(res.row, {
                            type: "xg_indicator_group_ref#item_type",
                            name: "",
                        })
                        this.reportId = this.details?.id + ""
                        this.showStartBtn = getShowBtn4Page(res, "start")
                        this.showFinishBtn = getShowBtn4Page(res, "finish")
                        this.showEditBtn = getShowBtn4Page(res, "update")
                        this.showDeleteBtn = getShowBtn4Page(res, "delete")
                        this.status = res.row.status?.value as Status
                        this.items = res.meta.header.field_groups
                            .map((item) => {
                                return {
                                    label: item.label + "：",
                                    value: item.template,
                                    hide: !item.visible,
                                    span: 8,
                                }
                            })
                            .filter((i) => i) as ColItem[]
                        this.columns = res.row.indicator_meta
                            ?.value as IndicatorColumn[]
                        this.tableColumns = this.columns?.map((i) => {
                            return {
                                label: i.display_name,
                                prop: i.union_code,
                                description: i.description,
                                value_type: i.value_type,
                                showOverflowTip: false,
                                children:
                                    i.children?.map((j) => {
                                        return {
                                            label: j.display_name,
                                            prop: j.union_code,
                                            showOverflowTip: false,
                                            description: j.description,
                                            children: j.children || [],
                                            value_type: j.value_type,
                                        }
                                    }) || [],
                            }
                        })

                        this.rows = res.row.indicator_data
                            ?.value as IndicatorDataRows[]
                        let data = {}
                        this.rows = this.rows.map((i) => {
                            data = Object.keys(i.indicator_data).reduce(
                                (acc: any, key) => {
                                    acc[i.indicator_data[key].union_code] =
                                        i.indicator_data[key].input_value
                                    return acc
                                },
                                {}
                            )
                            return {
                                ...i,
                                ...data,
                            }
                        })
                        this.setChildrenColumnsLabel()
                    })
            })
        }

        private setChildrenColumnsLabel() {
            this.childrenColumnsLabel = []
            this.columns.forEach((item: any) => {
                if (item.children?.length === 0) {
                    this.childrenColumnsLabel.push("")
                } else {
                    item.children?.forEach((child: any) => {
                        this.childrenColumnsLabel.push(child.display_name)
                    })
                }
            })
            const isEmptyString = (str: string) => {
                return str === ""
            }
            if (this.childrenColumnsLabel.every(isEmptyString)) {
                this.childrenColumnsLabel.length = 0 // 将数组置为空
            }
        }

        private edit() {
            this.showPublishPop = true
        }

        private deleteDetail() {
            MessageBox.confirm(`确认删除？`, "删除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_indicator_task")
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.reportId }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("删除成功")
                            closeCurrentTap()
                            this.callRefresh(routesMap.reportManage.creator.index)
                        })
                })
            })
        }

        private description(description: any) {
            this.exportDescription = Object.entries(description).reduce(
                (acc: any, [key, value]) => {
                    acc[key] = value === "" ? "-" : value
                    return acc
                },
                {}
            )
        }

        private exportTemplate() {
            const columnsLabel = [] as any[]
            this.columns.forEach((item: any) => {
                if (item.union_code !== "h") {
                    columnsLabel.push(item.display_name)
                }

                if (item.children?.length > 0) {
                    for (let i = 0; i < item.children.length - 1; i++) {
                        columnsLabel.push("")
                    }
                }
            })
            ExcelColumnsGenerator.execute({
                primaryRows: primaryRows,
                columns: columnsLabel,
                childColumns: this.childrenColumnsLabel,
                rows: Object.values(this.exportDescription),
                fileName: this.details?.name + "-模板" || "报表填报模板",
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .detail-top-header {
        padding-left: 20px;
        padding-right: 20px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .detail-top-title {
        font-weight: 600;
        font-size: 18px;
        color: #222222;
        line-height: 18px;
    }

    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
    }
</style>
