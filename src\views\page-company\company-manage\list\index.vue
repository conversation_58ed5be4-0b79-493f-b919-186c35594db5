<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
        >
            <div slot="title">
                <div>管理员列表</div>
                <div class="title-tips">
                    通过添加管理员功能，支持企业多个账号管理。
                </div>
            </div>
            <div slot="header-right">
                <el-button
                    type="primary"
                    class="custom-btn batch-btn"
                    @click="showCreatePop = true"
                    >添加管理员</el-button
                >
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div slot="order" slot-scope="scope">
                        {{ handlerIndex(scope.index) }}
                    </div>
                    <div slot="status_label" slot-scope="scope">
                        {{
                            scope.row.status === status.禁用
                                ? "已禁用"
                                : "已启用"
                        }}
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="edit(scope.row)">
                            编辑
                        </el-button>
                        <el-button
                            type="text"
                            @click="toggle(scope.row)"
                            :disabled="scope.row.uniplat_uid === uid"
                        >
                            <!-- {{
                                scope.row.status === status.禁用
                                    ? "启用"
                                    : "禁用"
                            }} -->
                            删除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <create-pop v-model="showCreatePop" @refresh="reloadList"></create-pop>
        <edit-pop
            v-model="showEditPop"
            @refresh="reloadList"
            :id="row && row.id"
        ></edit-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { userInfoService } from "@/core-ui/service/passport/user-info"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { Component } from "vue-property-decorator"
    import { Row, tableConfig, columns, Status } from "."
    import CreatePop from "./components/create-pop.vue"
    import EditPop from "./components/edit-pop.vue"

    @Component({
        name: routesMap.company.companyManage.list,
        components: { CommonTable, TableContainer, CreatePop, EditPop },
    })
    export default class CompanyManageList extends BaseTableController<Row> {
        private tableConfig = tableConfig
        private columns = columns
        private showCreatePop = false
        private showEditPop = false
        row: Row | null = null
        private status = Status
        private uid = ""

        private edit(row: Row) {
            this.row = row
            this.showEditPop = true
            console.log(JSON.parse(JSON.stringify(row)))
        }

        mounted() {
            userInfoService.get().then((r) => {
                this.uid = r.uid
            })
        }

        private toggle(row: Row) {
            MessageBox.confirm(`确认删除？`, "删除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_agent_manager")
                        .action("disable_agent")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private handlerIndex(index: string) {
            const count = Number(index) + 1
            return count < 10 ? "0" + count : count
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .batch-btn {
        width: 136px;
        height: 40px;
    }
    .title-tips {
        font-size: 12px;
        color: #9098a6;
        line-height: 18px;
    }
</style>
