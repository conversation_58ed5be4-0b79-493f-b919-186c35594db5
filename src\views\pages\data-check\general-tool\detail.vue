<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button type="primary" @click="toCreate">
                    创建核查任务
                </el-button>
            </div>
        </div>
        <div class="detail-top-box">
            <detail-row-col :list="items" class="u-p-x-20" labelStyle="120px">
            </detail-row-col>
        </div>
        <div v-if="row">
            <DetailTable ref="detailTable" :detailId="detailId" />
        </div>
        <AddPop :id="detailId" :policyId="policyId" v-model="showAddPop" />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailTable from "./components/detail-table.vue"
    import { pageLoading } from "@/views/controller"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { MessageBox } from "element-ui"
    import { DetailRow } from "./index"
    import AddPop from "./components/add-pop.vue"

    @Component({
        name: routesMap.dataCheck.generalToolDetail,
        components: { DetailTable, DetailRowCol, AddPop },
    })
    export default class GeneralDetail extends Vue {
        private breadcrumbs: BreadcrumbItem[] = []

        private showList = false

        private items: ColItem[] = []

        private showAddPop = false
        private policyId = ""
        private id = ""

        refreshConfig = {
            fun: this.init,
            name: routesMap.dataCheck.generalToolDetail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "查看详情",
                    to: {
                        name: routesMap.dataCheck.generalToolDetail,
                        query: {
                            id: this.$route.query.id || this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.dataCheck.generalToolDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private detailId = ""

        private row: DetailRow | null = null

        created() {
            this.init()
        }

        private init() {
            if (this.$route.query.id) {
                this.id = this.$route.query.id as string
            }
            if (!this.id) return
            this.row = null
            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("common_check_tool")
                    .detail(this.id, "back_detail")
                    .query()
                    .then((res) => {
                        this.row = sdk.buildRow(res.row, {
                            id: "",
                            policy_id: "",
                            is_create_task: false,
                        })
                        this.detailId = this.row?.id + "" || ""
                        this.policyId = this.row?.policy_id || ""
                        this.items = res.meta.header.field_groups
                            .map((item) => {
                                return {
                                    label: item.label + "：",
                                    value: item.template,
                                    hide: !item.visible,
                                    span: 24,
                                }
                            })
                            .filter(
                                (i) =>
                                    i &&
                                    i.label !== "policy id：" &&
                                    i.label !== "是否可以创建任务："
                            ) as ColItem[]
                    })
            })
        }

        private toAdd() {
            ;(this.$refs.detailTable as any).toAdd()
        }

        private refreshList() {
            ;(this.$refs.detailTable as any).refresh()
        }

        private publish(status: "1" | "0") {
            const label = status === "1" ? "发布" : "取消发布"
            MessageBox.confirm(`确认${label}？`, `${label}`).then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_indicator_group_ref")
                        .action(`change_status_${status}`)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.detailId }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("操作成功")
                            this.callRefresh(routesMap.dataCheck.generalToolDetail)
                        })
                })
            })
        }

        private toCreate() {
            if (!this.row?.is_create_task) {
                return MessageBox.alert(
                    `核查关联数据源和规则在调整中，暂停创建功能。`,
                    "提示",
                    {
                        closeOnClickModal: false,
                        showClose: false,
                    }
                )
            }
            this.showAddPop = true
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
    }
</style>
