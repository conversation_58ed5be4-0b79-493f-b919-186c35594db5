<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="新增管理员"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 create-position">
            <form-builder ref="formBuilder" labelWidth="90px"></form-builder>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="confirm"
                    class="custom-btn btn u-m-0"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        BuildFormConfig,
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"

    const createPositionFormSections: BuildFormConfig = {
        forms: [
            {
                label: "手机账号：",
                type: FormType.Text,
                prop: "mobile",

                required: true,
            },
            {
                label: "用户姓名：",
                type: FormType.Text,
                prop: "name",
            },
            {
                label: "用户角色：",
                type: FormType.Select,
                prop: "type",
            },
        ],
        sdkModel: "xg_agent_manager",
        sdkAction: "add_agent_manager",
        select_list: [],

        // sdkModel: "xg_agent_manager",
        // sdkAction: "add_agent_manager",
    }

    @Component({ components: { FormBuilder } })
    export default class CreatePositionPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop()
        private enterpriseId!: string

        @Prop({ default: "" })
        private readonly rowId!: number

        private createPositionFormSections = createPositionFormSections

        onOpen() {
            this.init()
        }

        private init() {
            return buildFormSections(this.createPositionFormSections).then((r) => {
                this.buildForm(r.forms)
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    if (!_.isPhone(data.mobile)) {
                        return this.$message.error("请填写正确的手机号！")
                    }
                    this.submit(data)
                }
            })
        }

        // .model("xg_agent_manager")
        // .action("add_agent_manager")
        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model("xg_agent_manager")
                    .action("add_agent_manager")
                    .updateInitialParams({
                        prefilters: [
                            {
                                property: "tg_enterprise_id",
                                value: this.enterpriseId,
                            },
                        ],
                    })
                    .addInputs_parameter(data)
                    .execute()
                    .then(() => {
                        this.$message.success("创建成功！")
                        this.$emit("refresh")
                    })
                    .finally(() => {
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .select {
        width: 320px;
    }
    .btn {
        width: 100px;
        height: 36px;
    }
    .create-position {
        ::v-deep .el-form-item__label {
            text-align: left;
            color: #555;
            font-size: 14px;
        }
    }
</style>
