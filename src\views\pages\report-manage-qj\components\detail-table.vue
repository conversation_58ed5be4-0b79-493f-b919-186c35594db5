<template>
    <div v-if="tableColumns && tableColumns.length">
        <common-table
            :data="tableRows"
            :columns="tableColumns"
            :tableConfig="{ 'row-class-name': tableRowClassName, stripe: true }"
        >
            <div
                :slot="item.prop"
                slot-scope="scope"
                v-for="item in editColumns"
                :key="item.prop"
                class="table-column"
            >
                <span
                    v-if="
                        !isEditRow ||
                        item.prop === 'name' ||
                        !scope.index
                    "
                >
                    {{ scope.row[item.prop] || "-" }}</span
                >
                <el-input
                    ref="orderInput"
                    v-model="scope.row[item.prop]"
                    placeholder="请输入"
                    v-else
                    @input="validateInput(scope.row, item)"
                ></el-input>
            </div>
            <div
                slot="h"
                class="u-flex u-row-center"
                slot-scope="scope"
                v-if="scope.row.id && !isFinish"
            >
                <el-button type="text" @click="submit" v-if="isEditRow">
                    更新
                </el-button>
                <el-button type="text" @click="toEdit(scope.row)">
                    {{
                        !isEditRow
                            ? isNoFillIn(scope.row.status)
                                ? "填报"
                                : "编辑"
                            : "取消"
                    }}
                </el-button>
            </div>
        </common-table>
    </div>
    <div v-else class="empty color-9 u-flex u-row-center u-p-30">暂无数据</div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop, Watch } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { cloneDeep } from "lodash"
    import { FillInStatus, Status } from "../../report-manage/"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { routesMap } from "@/router/direction"
    import { ValueType } from "../../report-manage/list/index"

    interface Row {
        [key: string]: any
        id: number
        v: number
    }

    @Component({
        components: { CommonTable },
    })
    export default class DetailTable extends BaseTableController<Row> {
        @Prop({ default: () => [] })
        private readonly columns!: TableColumn[]

        @Prop({ default: () => [] })
        private readonly rows!: []

        @Prop({ default: Status.未开始 })
        private readonly taskStatus!: Status

        tableConfig: TableConfig | null = null

        refreshConfig = {
            fun: this.init,
            name: "reportManageDetailTable",
        }

        private checkEdIds: number[] = []

        private tableColumns: TableColumn[] | any[] = []

        private tableRows: any[] = []

        private fillInStatus: FillInStatus | null = null

        private isEditRow = false

        private pubAction = "pub_indicator"
        private updateAction = "update_indicator"

        private inputsParameters: {}[] = []

        // private getRows(rows: { intents: { name: string }[] }[]) {
        //     this.rows.push(...(rows || []))
        // }

        private editRow = ""

        private get isFinish() {
            return this.taskStatus === Status.已完成
        }

        private get editColumns() {
            const flattenObjectArray = (arr: TableColumn[]) => {
                let result = [] as TableColumn[]
                arr.forEach((item) => {
                    result.push(item)
                    if (item.children) {
                        result = result.concat(flattenObjectArray(item.children))
                    }
                })
                return result
            }
            return flattenObjectArray(this.tableColumns)
        }

        @Watch("rows", { deep: true, immediate: true })
        private onRowsChanged() {
            this.init()
        }

        created() {
            this.init()
        }

        private init() {
            this.tableColumns = cloneDeep(this.columns)
            if (!this.tableColumns?.length) {
                return
            }
            this.tableColumns.unshift({
                label: "标题",
                prop: "name",
                showOverflowTip: true,
            })

            const flattenArray = (arr: TableColumn[]) => {
                let flattenedArray = [] as any[]
                arr.forEach((item) => {
                    flattenedArray.push(item)
                    if (item.children) {
                        flattenedArray = flattenedArray.concat(
                            flattenArray(item.children)
                        )
                    }
                })
                return flattenedArray
            }
            this.tableRows = cloneDeep(this.rows)
            const description = {} as any
            const simpleColumns = flattenArray(this.tableColumns)
            simpleColumns.forEach((i) => {
                description[i.prop] = i.description || ""
            })
            description.name = "填写说明"
            this.tableRows.unshift(description)
        }

        private toEdit(row: Row) {
            this.isEditRow = !this.isEditRow
            this.fillInStatus = row.status as FillInStatus
            this.rowId = row.id
            sdk.core
                .model("xg_indicator_task_target")
                .action(
                    this.isNoFillIn(this.fillInStatus)
                        ? this.pubAction
                        : this.updateAction
                )
                .updateInitialParams({
                    selected_list: this.rowId ? [{ v: 0, id: this.rowId }] : [],
                })
                .query()
                .then((r) => {
                    this.inputsParameters = r.parameters.inputs_parameters
                    const row = this.tableRows[1] as any
                    this.inputsParameters.forEach(
                        (
                            item: { property: string; default_value: string } | any
                        ) => {
                            if (Object.keys(row).includes(item.property)) {
                                row[item.property] = item.default_value
                            }
                        }
                    )
                })
        }

        private tableRowClassName({ rowIndex }: { rowIndex: number }) {
            if (rowIndex === 0) {
                return "description-row"
            }
        }

        // 未填报
        private isNoFillIn(status: FillInStatus) {
            return status < FillInStatus.已完成
        }

        private submit() {
            const row = this.tableRows[1] as any

            Object.keys(row).forEach((key) => {
                const propExists = this.inputsParameters.some(
                    (item: { property: string; default_value: string } | any) =>
                        item.property === key
                )
                if (!propExists) {
                    delete row[key]
                }
            })
            this.inputsParameters.forEach(
                (item: { property: string; default_value: string } | any) => {
                    if (!Object.keys(row).includes(item.property)) {
                        row[item.property] = ""
                    }
                }
            )
            pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_task_target")
                    .action(
                        this.isNoFillIn(this.fillInStatus as FillInStatus)
                            ? this.pubAction
                            : this.updateAction
                    )
                    .addInputs_parameter(row)
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: this.rowId }],
                    })
                    .execute()
                    .then(() => {
                        this.isEditRow = false
                        this.$message.success("提交成功")
                        this.callRefresh(routesMap.reportManage.executor.detail)
                    })
            })
        }

        private validateInput(row: Row, item: any) {
            if (+item.value_type === ValueType.整型) {
                row[item.prop] = row[item.prop].replace(/[^1-9]/g, "")
            } else if (+item.value_type === ValueType.浮点型) {
                row[item.prop] = row[item.prop].replace(/[^0-9.]/g, "")
            }
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
    /deep/.el-table {
        .description-row .table-column {
            color: red !important;
        }
    }
    .empty {
        font-size: 14px;
    }
</style>
