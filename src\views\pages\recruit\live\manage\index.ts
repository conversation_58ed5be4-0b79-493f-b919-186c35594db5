import { sdk } from "@/service"
import { MessageBox } from "element-ui"

export enum LiveType {
    直播带岗 = 1,
    招聘会直播 = 2,
    综合直播 = 3,
}

export enum LiveStatus {
    即将开播 = 1,
    正在直播 = 2,
    直播结束 = 3,
}

export enum LiveAuditStatus {
    草稿 = 1,
    待审核 = 2,
    审核通过 = 3,
    审核不通过 = 4,
}

export enum LiveShelfStatus {
    未发布 = 1,
    已发布 = 2,
}

export const predict = {
    live_audit_status: "label",
    live_shelf_status: "label",
    live_title: "",
    live_type: "label",
    live_start_time: "",
    live_end_time: "",
    live_cover: "",
    live_profiles: "",
    live_blurb: "",
    job_fair_id: "job_fair#id",
    job_fair_title: "job_fair#title",
    live_propel_channel: "",
    live_channels: "",
    live_url: "",
    live_status: ""
}

/** 上下架 */
export function toggleShell(row: any) {
    return new Promise((resolve) => {
        const action =
            row.live_shelf_status === LiveShelfStatus.未发布
                ? "onShelf"
                : "unShelf"
        MessageBox(
            `请确认是否${
                row.live_shelf_status === LiveShelfStatus.未发布
                    ? "上架"
                    : "下架"
            }`,
            "提示"
        ).then(() => {
            return sdk.core
                .model("live_broadcast")
                .action(action)
                .updateInitialParams({
                    selected_list: [{ v: 0, id: row.id }],
                })
                .execute()
                .then(() => {
                    resolve(true)
                })
        })
    })
}

/** 提审 */
export function toAudit(row: any, pop = true) {
    if (!pop) {
        return sdk.core
            .model("live_broadcast")
            .action("submit")
            .updateInitialParams({ selected_list: [{ v: 0, id: row.id }] })
            .execute()
    }
    return new Promise((resolve) => {
        MessageBox(`请确认是否提交审核`, "提示").then(() => {
            return sdk.core
                .model("live_broadcast")
                .action("submit")
                .updateInitialParams({ selected_list: [{ v: 0, id: row.id }] })
                .execute()
                .then(() => {
                    resolve(true)
                })
        })
    })
}
