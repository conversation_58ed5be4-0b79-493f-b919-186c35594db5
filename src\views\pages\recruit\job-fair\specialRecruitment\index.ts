import {
    buildSelectSource,
    defaultTimePickerOptions,
    FormType,
} from "@/core-ui/component/form"
import { TableColumn, TableConfig } from "@/core-ui/component/table"
import { formatDate } from "@/core-ui/helpers/tools"
import { sdk } from "@/service"
import { sysConfigService } from "@/service/sys-config"
import { cloneDeep, find, get } from "lodash"
import moment from "moment"

export interface Row {
    region_code: string // 区县行政区划代码
    code_remark: string // 区县行政区划代码释义
    account: number // 合计
    type2: number // 现场
    type1: number // 网络
    type3: number // 直播
    agents: number // 参与企业
    positions: number // 提供岗位
    job_will_count: number // 达成就业意向
    pub_file_count: number // 发放宣传资料
    live_person_count: number // 观看直播
    make_work_count: number // 开展职业指导
    go_online_count: number // 进场求职人数
    distinct_agents: number // 参与企业（去重
}
export async function getFilters() {
    const meta = await sdk.core
        .model("job_fair")
        .list("for_operate")
        .query({
            pageIndex: 1,
            item_size: 0,
        })
        .then((r) => {
            return r.pageData.meta.filters
        })
    const f1 = get(
        find(meta, { property: "theme_type" }),
        "ext_properties.mapping.mapping_values"
    )
    const f2 = get(
        find(meta, { property: "activity_type" }),
        "ext_properties.mapping.mapping_values"
    )
    return [
        {
            label: "统计时间",
            type: FormType.DatePicker,
            defaultValue: [
                formatDate(+moment().startOf("month")),
                formatDate(+moment().endOf("month")),
            ],
            option: {
                type: "daterange",
                pickerOptions: defaultTimePickerOptions,
            },
            prop: "time",
        },
        {
            label: "主题类型",
            type: FormType.Cascader,
            prop: "theme_type",
            option: { elProps: { checkStrictly: true } },
            sourceInputsParameter: buildSelectSource(f1),
        },
        {
            label: "专场类型",
            type: FormType.Select,
            prop: "activity_type",
            option: {
                multiple: true,
            },
            sourceInputsParameter: buildSelectSource(f2),
        },
    ]
}
export async function tableConfig(): Promise<TableConfig> {
    return {
        filter: await getFilters(),
        predict: {},
        domainService: sdk.core.domainService(
            "xg_project",
            "back_api",
            "get_job_fair_area"
        ),
        column: columns,
        handleFilterData(d) {
            const params = cloneDeep(d)
            if (params?.time) {
                params.start_date = params.time[0]
                params.end_date = params.time[1]
                delete params.time
            }
            return {
                start_date: "",
                end_date: "",
                ...params,
            }
        },
    }
}

export const columns: TableColumn[] = [
    {
        label: "地区",
        prop: "code_remark",
        minWidth: "140",
        showOverflowTip: true,
        fixed: "left",
    },
    {
        label: "招聘形式（场）",
        prop: "招聘形式（场）",
        children: [
            {
                label: "合计",
                prop: "account",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "O2O类型",
                prop: "type2",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "直播",
                prop: "type3",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "录播",
                prop: "type1",
                minWidth: "100",
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "招聘数据信息",
        prop: "招聘数据信息",
        children: [
            {
                label: "参与企业（去重）",
                prop: "distinct_agents",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "参与企业（家次）",
                prop: "agents",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "招聘岗位（个）",
                prop: "distinct_positions",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "招聘岗位（个次）",
                prop: "positions",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "招聘人数（人）",
                prop: "distinct_recruit_count",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "拟招聘人数（人次）",
                prop: "recruit_count",
                minWidth: "100",
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "招聘成效信息",
        prop: "招聘成效信息",
        children: [
            {
                label: "进场求职（人）",
                prop: "go_online_count",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "达成就业意向（人）",
                prop: "job_will_count",
                minWidth: "120",
                showOverflowTip: true,
            },
            {
                label: "职业指导（人次）",
                prop: "make_work_count",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "发放宣传资料（份）",
                prop: "pub_file_count",
                minWidth: "120",
                showOverflowTip: true,
            },
            {
                label: "线上浏览（人次）",
                prop: "client_browse",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "线上互动（人次）",
                prop: "live_person_count",
                minWidth: "100",
                showOverflowTip: true,
            },
        ],
    },
]
