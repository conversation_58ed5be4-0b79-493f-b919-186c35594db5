<template>
    <div>
        <div class="core-ui-custom-header">
            <div class="title">零工驿站统计</div>
        </div>
        <div class="filter u-m-b-24">
            <table-filter
                :tableFilter="filter"
                @changeData="search"
                @search="search"
            ></table-filter>
        </div>
        <div v-if="data" v-loading="loading">
            <div class="content" v-for="p in showList" :key="p.prop">
                <div class="title u-flex u-row-between">
                    <div>{{ p.title }}</div>
                    <div
                        class="primary u-font-14 font-weight-4 pointer"
                        @click="toPage(p)"
                    >
                        查看驿站数据＞
                    </div>
                </div>
                <div class="list mini">
                    <div
                        class="item u-flex u-flex-col u-row-between"
                        v-for="item in list[p.prop]"
                        :key="item.title"
                    >
                        <div class="u-font-32">
                            {{ getNumber(p.prop, item.prop) }}
                        </div>
                        <div class="u-font-16 color-3 u-flex">
                            <div>
                                {{ item.title }}
                            </div>
                            <div v-if="item.info" class="u-m-l-5 pointer">
                                <el-tooltip
                                    effect="dark"
                                    :content="item.info"
                                    placement="bottom"
                                >
                                    <i
                                        class="el-icon-warning-outline primary"
                                    ></i>
                                </el-tooltip>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else v-loading="true"></div>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { Component, Vue } from "vue-property-decorator"
    import TableFilter from "@/core-ui/component/table/filter.vue"
    import { FormType } from "@/core-ui/component/form"
    import { bkApi, getBkToken, list } from "."
    import { ListTypes } from "uniplat-sdk"

    const filter = [
        // {
        //     label: "驿站名称",
        //     prop: "UCE385REMARK",
        //     type: FormType.Text,
        //     keyValueFilter: {
        //         match: ListTypes.filterMatchType.fuzzy,
        //     },
        //     col: { span: 8 },
        // },
        {
            label: "统计开始时间",
            prop: "UCK012_S",
            type: FormType.DatePicker,
            option: {
                type: "date",
            },
            col: { span: 8 },
        },
        {
            label: "统计结束时间",
            prop: "UCK012_E",
            type: FormType.DatePicker,
            option: {
                type: "date",
            },
            col: { span: 8 },
        },
    ]

    @Component({
        name: routesMap.jobStation.statistics,
        components: { TableFilter },
    })
    export default class JobStationStatistics extends Vue {
        private filter = filter
        private data: any = null
        private filterData: any = null
        private loading = false

        private list = list
        private showList = [
            {
                prop: "jobStation",
                title: "零工驿站统计概括",
                route: routesMap.jobStation.jobStationStatisticsDetail,
            },
            {
                prop: "recommend",
                title: "用工推荐统计概括",
                route: routesMap.jobStation.recommendStatisticsDetail,
            },
            {
                prop: "share",
                title: "驿站共享统计概括",
                route: routesMap.jobStation.shareStatisticsDetail,
            },
        ]

        private search(v: { time: string[] }) {
            if (typeof v.time === "string") return this.getList()
            this.filterData = {
                ...v,
            }
            this.$nextTick(() => {
                this.getList({
                    ...this.filterData,
                })
            })
        }

        mounted() {
            this.getList()
        }

        private getNumber(type: string, prop: string) {
            return (this.data[type] || {})[prop] || 0
        }

        private toPage(item: { title: string; prop: string; route: string }) {
            this.$router.push({
                name: item.route,
                query: {
                    prop: item.prop,
                },
            })
        }

        private async getList(filter: Record<string, string> = {}) {
            this.loading = true
            await getBkToken()
            const data: any = { jobStation: {} }
            Promise.all([
                bkApi("/ORG/U/DEF/ORG/V1/YZ/NUM", filter).then((r) => {
                    data.jobStation = {
                        ...data.jobStation,
                        position_create_num: r,
                    }
                }),
                bkApi("/ORG/LGSC/ORG/STATISTICS/V1/YZ", filter).then((r) => {
                    data.jobStation = { ...data.jobStation, ...r }
                }),
                bkApi("/ORG/LGSC/ORG/STATISTICS/V1/TJ", filter).then((r) => {
                    data.recommend = r
                }),
                bkApi("/ORG/LGSC/ORG/STATISTICS/V1/GX", filter).then((r) => {
                    data.share = r
                }),
            ])
                .then(() => {
                    this.data = { ...data }
                })
                .finally(() => {
                    this.loading = false
                })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .filter {
        ::v-deep .form-filed {
            width: 300px !important;
        }
    }
    .content {
        margin-bottom: 24px;
        background: #ffffff;
        padding: 20px;
        .title {
            font-weight: 600;
            color: #222;
            font-size: 18px;
            line-height: 18px;
            margin-bottom: 20px;
        }

        .list {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            overflow: scroll;
            gap: 18px;
            .item {
                border-radius: 8px;
                min-height: 110px;
                padding: 20px;
                padding-top: 16px;
                background: #f4f4ff;
                color: #6068e0;
                flex: 1;
            }
        }
    }
</style>
