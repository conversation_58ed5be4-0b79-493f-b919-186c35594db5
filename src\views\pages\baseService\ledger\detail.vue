<template>
    <div style="padding-block: 16px">
        <StaffInfo
            :region_code="region_code"
            :uniplat_uid="uniplat_uid"
        ></StaffInfo>

        <section style="margin-top: 40px">
            <el-tabs v-model="activeTab" type="card">
                <el-tab-pane
                    v-for="tab in tabs"
                    :key="tab.name"
                    :label="tab.label"
                    :name="tab.name"
                    lazy
                >
                    <div class="bg-white" style="padding: 20px">
                        <TabItem
                            :region_code="region_code"
                            :uniplat_uid="uniplat_uid"
                            :name="tab.name"
                            :columns="tab.columns"
                            :model_name="tab.model_name"
                            :id="id"
                        />
                    </div>
                </el-tab-pane>
            </el-tabs>
        </section>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"
    import StaffInfo from "./components/StaffInfo.vue"
    import TabItem from "./components/TabItem.vue"

    @Component({ components: { StaffInfo, TabItem } })
    export default class LedgerDetail extends Vue {
        private region_code = this.$route.query.region_code
        private uniplat_uid = this.$route.query.uniplat_uid
        private id = this.$route.query.id

        private activeTab = "get_grid_staff_labor_list"

        private tabs = [
            {
                label: "劳动力更新情况",
                name: "get_grid_staff_labor_list",
                columns: [
                    {
                        label: "劳动力信息",
                        prop: "name",
                    },
                    {
                        label: "户籍地",
                        prop: "household_village_code_remark",
                    },
                    {
                        label: "更新人",
                        prop: "collector_name",
                    },
                    {
                        label: "更新人",
                        prop: "update_time",
                    },
                ],
            },
            {
                label: "社群情况",
                name: "get_grid_group_chat_members",
                columns: [
                    {
                        label: "社群名称",
                        prop: "name",
                    },
                    {
                        label: "社群居民数量",
                        prop: "member_count",
                    },
                ],
            },
            {
                label: "岗位推广情况",
                name: "xg_company_position",
                model_name: true,
                columns: [
                    {
                        label: "推荐岗位",
                        prop: "params_json",
                        formatter: "json",
                        jsonKey: "name",
                    },
                    {
                        label: "推荐时间",
                        prop: "final_change_time",
                    },
                    {
                        label: "查看次数",
                        prop: "browse_count",
                    },
                ],
            },
            {
                label: "政策推广情况",
                name: "policy_form",
                model_name: true,
                columns: [
                    {
                        label: "推荐政策",
                        prop: "policy_name",
                        formatter(row: any) {
                            try {
                                const json =
                                    typeof row.params_json === "string"
                                        ? JSON.parse(row.params_json)
                                        : row.params_json
                                return json.policy_name || "--"
                            } catch (error) {
                                return "--"
                            }
                        },
                    },
                    {
                        label: "推荐时间",
                        prop: "final_change_time",
                    },
                    {
                        label: "政策类型",
                        prop: "policy_type",
                        formatter(row: any) {
                            try {
                                const json =
                                    typeof row.params_json === "string"
                                        ? JSON.parse(row.params_json)
                                        : row.params_json
                                return json.policy_type || "--"
                            } catch (error) {
                                return "--"
                            }
                        },
                    },
                    {
                        label: "查看次数",
                        prop: "browse_count",
                    },
                ],
            },
            {
                label: "招聘会推广情况",
                name: "job_fair",
                model_name: true,
                columns: [
                    {
                        label: "招聘会名称",
                        prop: "job_fair_name",
                        formatter(row: any) {
                            try {
                                const json =
                                    typeof row.params_json === "string"
                                        ? JSON.parse(row.params_json)
                                        : row.params_json
                                return json.job_fair_name || "--"
                            } catch (error) {
                                return "--"
                            }
                        },
                    },
                    {
                        label: "召开时间",
                        prop: "job_fair_time",
                        formatter(row: any) {
                            try {
                                const json =
                                    typeof row.params_json === "string"
                                        ? JSON.parse(row.params_json)
                                        : row.params_json
                                return json.job_fair_time || "--"
                            } catch (error) {
                                return "--"
                            }
                        },
                    },
                    {
                        label: "推荐时间",
                        prop: "final_change_time",
                    },
                    {
                        label: "查看次数",
                        prop: "browse_count",
                    },
                ],
            },
            {
                label: "培训推广情况",
                name: "hb_training",
                model_name: true,
                columns: [
                    {
                        label: "培训班",
                        prop: "train_class_name",
                        formatter(row: any) {
                            try {
                                const json =
                                    typeof row.params_json === "string"
                                        ? JSON.parse(row.params_json)
                                        : row.params_json
                                return json.train_class_name || "--"
                            } catch (error) {
                                return "--"
                            }
                        },
                    },
                    {
                        label: "培训专业",
                        prop: "training_plan_major_name",
                        formatter(row: any) {
                            try {
                                const json =
                                    typeof row.params_json === "string"
                                        ? JSON.parse(row.params_json)
                                        : row.params_json
                                return json.training_plan_major_name || "--"
                            } catch (error) {
                                return "--"
                            }
                        },
                    },
                    {
                        label: "推荐时间",
                        prop: "final_change_time",
                    },
                    {
                        label: "查看次数",
                        prop: "browse_count",
                    },
                ],
            },
        ]
    }
</script>

<style lang="less" scoped>
    @import "./style/tail.less";
    @import "./style/custom-tabs.less";
</style>
