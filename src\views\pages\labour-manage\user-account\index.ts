import { FormType, buildSelectSource } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress } from "@/utils"
import { forEach } from "lodash"
import { ListTypes } from "uniplat-sdk"
import { computeAge } from "../seeker-info"

const tableFilter: TableFilter[] = [
    {
        label: "注册手机号",
        type: FormType.Text,
        prop: "mobile",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "注册时间",
        type: FormType.DatePicker,
        option: { type: "daterange" },
        prop: "reg_time",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "最近登录时间",
        type: FormType.DatePicker,
        option: { type: "datetimerange" },
        prop: "last_login_time",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "姓名",
        type: FormType.Text,
        prop: "real_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    {
        label: "身份证号",
        type: FormType.Text,
        prop: "id_card",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "常住地",
        type: FormType.Cascader,
        option: { elProps: { checkStrictly: true }, filterable: true },
        prop: "permanent_province_code",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "户籍地",
        type: FormType.Cascader,
        option: { elProps: { checkStrictly: true }, filterable: true },
        prop: "household_province_code",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "注册来源",
        option: {
            multiple: true,
        },
        type: FormType.Select,
        prop: "pc",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "注册渠道",
        type: FormType.MultipleCascader,
        option: { elProps: { checkStrictly: true } },
        prop: "pc_from_grandpa",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "是否实名",
        type: FormType.Select,
        prop: "is_verified",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "已实名",
            },
            {
                key: "0",
                value: "未实名",
            },
        ]),
    },
    {
        label: "标签",
        type: FormType.Cascader,
        prop: "label_nos",
        option: {
            filterable: true,
            collapseTags: true,
            elProps: {
                // multiple: true,
                checkStrictly: true,
            },
        },
    },
    {
        label: "是否有就业意愿",
        type: FormType.Select,
        prop: "is_job_willing",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "求职年龄",
        type: FormType.TextRange,
        option: {
            type: "number",
        },
        prop: "birth_date",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "意向工种",
        type: FormType.Select,
        prop: "job_willing_type_work",
        option: {
            filterable: true,
            multiple: true,
            collapseTags: true,
        },
    },
    {
        label: "就业意向行业",
        type: FormType.Select,
        prop: "job_willing_industry",
        option: {
            filterable: true,
            multiple: true,
            collapseTags: true,
        },
    },
    {
        label: "期望工作地",
        type: FormType.Cascader,
        option: { elProps: { checkStrictly: true }, filterable: true },
        prop: "job_willing_province_id",
    },
    {
        label: "是否有创业意愿",
        type: FormType.Select,
        prop: "is_start_job",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "是否有培训意愿",
        type: FormType.Select,
        prop: "is_training_willingness",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "创业意向工作行业",
        type: FormType.Select,
        prop: "start_job_industry",
        option: {
            filterable: true,
            multiple: true,
            collapseTags: true,
        },
    },
    {
        label: "创业意向工种",
        type: FormType.Select,
        prop: "start_job_type_work",
        option: {
            filterable: true,
            multiple: true,
            collapseTags: true,
        },
    },
    {
        label: "就业状态",
        type: FormType.Select,
        prop: "employment_status",
        option: {
            multiple: true,
        },
    },
    {
        label: "是否有档案",
        type: FormType.Select,
        prop: "id",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "是否设置意向工种",
        type: FormType.Select,
        prop: "job_willing_remark",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
]

export type Row = {
    unionIds: string
    id: number
    v: number
    real_name: string
    real_name_hide: string
    mobile_hide: string
    last_login_time: string
    profile_id: string
    id_card_hide: string
    is_verified: string
    reg_time: string
    sex: string
    age: string
    tags: any
    pc: string
    household_province_name: string
    household_city_name: string
    household_area_name: string
    permanent_province_name: string
    permanent_city_name: string
    permanent_area_name: string
    employment_status: string
    uniplat_uid: string
    labels: string
} & {
    [key: string]: string
}

export const predict = {
    id_card_openid: "",
    object_data: "",
    real_name: "label",
    real_name_hide: "label",
    mobile_hide: "label",
    mobile: "",
    last_login_time: "label",
    profile_id: "profile#id",
    id_card_hide: "label",
    id_card: "",
    is_verified: "label",
    reg_time: "label",
    sex: "label",
    age: "label",
    tags: "tags",
    pc: "label",
    household_province_name:
        "profile#basic_info#household_province#region_name",
    household_city_name: "profile#basic_info#household_city#region_name",
    household_area_name: "profile#basic_info#household_area#region_name",
    household_countryside_name:
        "profile#basic_info#household_countryside#region_name",
    household_village_name: "profile#basic_info#household_village#region_name",
    permanent_province_name:
        "profile#basic_info#permanent_province#region_name",
    permanent_city_name: "profile#basic_info#permanent_city#region_name",
    permanent_area_name: "profile#basic_info#permanent_area#region_name",
    permanent_countryside_name:
        "profile#basic_info#permanent_countryside#region_name",
    permanent_village_name: "profile#basic_info#permanent_village#region_name",
    employment_status:
        "profile#user_profile_current_job_info#employment_status_label",
    pc_ref_id: "label",
    profile_mobile: "profile#mobile",
    login_times: "",
    verify_time: "user_citizen_check#CreatedDate_label",
    uniplat_uid: "",
    pc_from: "label",
    pc_from_group: "label",
    job_willing_type_work:
        "profile#job_willingness#job_willing_type_work_display_label",
    job_willing_industry: "profile#job_willingness#job_willing_industry_label",
    profile_access_key: "profile#_access_key",
    start_job_type_work:
        "profile#user_profile_current_job_info#start_job_type_work_label",
    start_job_industry:
        "profile#user_profile_current_job_info#start_job_industry_label",
    is_job_willing: "profile#job_willingness#is_job_willing_label",
    labels: "",
}
export function tableConfig(is_verified?: boolean): TableConfig {
    return {
        model: sdk.core.model("user_account").list("manage"),
        filter: tableFilter.filter(
            (i) => !is_verified || i.prop !== "is_verified"
        ),
        defaultPageSize: 10,
        predict: predict,

        handleFilterData: (params: any) => {
            if (params.birth_date) {
                params.birth_date = (params.birth_date || [])
                    .map((e: string) => computeAge(+e))
                    .reverse()
            }
            if (is_verified) {
                params.is_verified = "1"
            }
            return params
        },
    }
}

export const columns: TableColumn[] = [
    {
        type: "selection",
        prop: "select",
        selectable: (row) => {
            return !!(row as Row).profile_id
        },
    },
    {
        label: "注册手机号",
        prop: "mobile",
        showOverflowTip: true,
        minWidth: "120",
    },
    {
        label: "注册时间",
        prop: "time",
        showOverflowTip: true,
        minWidth: "120",
        render(h, row) {
            const t = (row.reg_time || "").split(" ").filter(Boolean)
            return h("div", {}, [
                t.length
                    ? h(
                          "div",
                          {},
                          t.map((e: string) => h("div", {}, e))
                      )
                    : h("span", { class: "color-9" }, "-"),
            ])
        },
    },
    {
        label: "最新登录时间",
        prop: "最新登录时间",
        showOverflowTip: true,
        minWidth: "150",
        render(h, row) {
            const t = (row.last_login_time || "").split("T").filter(Boolean)
            return h("div", {}, [
                t.length
                    ? h(
                          "div",
                          {},
                          t.map((e: string) => h("div", {}, e))
                      )
                    : h("span", { class: "color-9" }, "-"),
            ])
        },
    },
    { label: "实名信息", prop: "info", width: "220px", showOverflowTip: true },
    {
        label: "户籍地/常住地",
        prop: "address",
        width: "250px",
        showOverflowTip: true,
        render(h, row) {
            const household = getAddress(row, [
                "household_province_name",
                "household_city_name",
                "household_area_name",
                "household_countryside_name",
                "household_village_name",
            ])
            const permanent = getAddress(row, [
                "permanent_province_name",
                "permanent_city_name",
                "permanent_area_name",
                "permanent_countryside_name",
                "permanent_village_name",
            ])
            const show = household || permanent
            return h(
                "div",
                { class: "u-text-left" },
                show
                    ? [
                          h(
                              "div",
                              { class: "u-line-1" },
                              "户籍地：" + (household || "-")
                          ),
                          h(
                              "div",
                              { class: "u-line-1" },
                              "常住地：" + (permanent || "-")
                          ),
                      ]
                    : [h("div", { class: "u-text-center" }, "—")]
            )
        },
    },
    // { label: "注册来源", prop: "pc_label", showOverflowTip: true },
    {
        label: "注册渠道",
        prop: "pc_from_display",
        showOverflowTip: true,
        minWidth: "120",
    },
    {
        label: "意向工种",
        prop: "job_willing_type_work",
        showOverflowTip: true,
        minWidth: "120",
    },
    {
        label: "就业意向行业",
        prop: "job_willing_industry",
        width: "150px",
        showOverflowTip: true,
    },
    {
        label: "标签",
        prop: "tags",
        showOverflowTip: true,

        minWidth: "120",
        render: (h, row: Row) => {
            const tags = row.tags || {}
            let tagArr: string[] = []
            forEach(tags, (v) => {
                tagArr.push(...v)
            })
            tagArr = tagArr.filter(Boolean)
            const tagStr = tagArr.join("，")
            return h(
                "div",
                {
                    class: "pre-line u-line-3",
                    directives: [
                        {
                            name: "customerPopover",
                            value: { content: tagStr },
                        },
                    ],
                    key: row.access_key,
                },
                tagStr || "-"
            )
        },
    },
    { label: "操作", prop: "h", showOverflowTip: true, fixed: "right" },
]
