import { config, EnvProject } from "@/config"
import { QRCodeGenerator } from "@/core-ui/controller/qrcode"
import { sdk } from "@/service"
import { sleep } from "@/utils"
import { MessageBox } from "element-ui"
import { find, split } from "lodash"

export enum MapType {
    培训机构 = 1,
    零工驿站,
    孵化基地,
    社保服务,
}

export function getName(type: MapType) {
    return {
        [MapType.培训机构]: "train",
        [MapType.零工驿站]: "job-station",
        [MapType.孵化基地]: "base",
        [MapType.社保服务]: "service",
    }[type]
}
export function getPoints(e: any = { type: "", name: "", province_code: "" }) {
    const { type, name, province_code } = e
    const model = sdk.core.model("server_stage").list("")
    model.clearFilter()
    return model.query({ item_size: 99, pageIndex: 1 }).then((r) => {
        model.addFilter({ property: "status", value: 1 })
        if (name) {
            model.addFilter({
                property: "name",
                value: name,
                match: "fuzzy",
            } as any)
        }
        if (type) {
            model.addFilter({ property: "type", value: type })
        }
        if (province_code) {
            model.addFilter({
                property: "province_code",
                value: (province_code || "").split(","),
            })
        }
        return model.query({ item_size: 99, pageIndex: 1 }).then((r) => {
            return sdk
                .buildRows(r.pageData.rows, {
                    name: "",
                    type: "label",
                    region_name: "area#region_name",
                    address: "address_detail",
                    contact_person: "",
                    mobile_hide: "",
                    create_time: "",
                    update_time: "",
                    status: "label",
                    lat: "",
                    lng: "",
                    mobile: "",
                    remark: "",
                })
                .map((e: any) => {
                    return {
                        ...e,
                        icon: `${
                            process.env.BASE_URL || ""
                        }/img/xiaogan/map/${getName(e.type)}.png`.replace(
                            "//",
                            "/"
                        ),
                        icon2: `${
                            process.env.BASE_URL || ""
                        }/img/xiaogan/map/${getName(
                            e.type
                        )}-active.png`.replace("//", "/"),
                    }
                })
        })
    })
}

export function getFilters() {
    const model = sdk.core.model("server_stage").list("")
    model.clearFilter()
    return model.query({ item_size: 1, pageIndex: 1 }).then((r) => {
        const f: any = r.pageData.meta.filters
        const type = find(f, (e) => e.property === "type")
        if (type) {
            type.ext_properties.mapping.mapping_values.unshift({
                key: "",
                value: "全部",
            })
        }
        return {
            province_code: find(f, (e) => e.property === "province_code"),
            type,
        }
    })
}
const baseUrl: { [key: string]: string } = {
    [EnvProject.孝感项目]: "https://xgh5.teammix.com/#/pages/sub/map/",
    [EnvProject.黄州项目]: "https://yz.hzjycy.cn/HZJYh5/#/pages/sub/map/",
}
export async function showCode(item: any = {}, vue: any) {
    if ([MapType.孵化基地, MapType.社保服务].includes(item.type)) return
    const h = vue.$createElement
    const parmas = `?name=${item.name}&type=${item.type}&accessKey=${item._access_key}`
    const path = (baseUrl[config.envProject] || "") + parmas
    const div = document.createElement("div")
    QRCodeGenerator.build(div, path, 150, 150)
    await sleep(200)
    const imgSrc = div.children[0].getAttribute("src")
    MessageBox({
        title: item.name,
        message: h("div", { class: "u-flex u-row-center" }, [
            h("img", {
                attrs: { src: imgSrc },
                style: { width: "200px", height: "200px" },
            }),
        ]),
        showConfirmButton: false,
    })
}
