<template>
    <el-dialog
        :visible="value"
        @close="close"
        title="数据导出"
        :append-to-body="false"
        :modal-append-to-body="false"
        width="800px"
        top="8vh"
    >
        <div class="u-p-x-20">
            <div class="d-flex box-item">
                <div>为您找到数据：</div>
                <div>
                    <template>
                        <div class="result-text">
                            <span style="color: #5782ec; margin-right: 6px">{{
                                total
                            }}</span>
                            条
                        </div>
                    </template>

                    <!-- <template v-else>
                        <div class="process-content">
                            <div class="process-tips dots">正在努力查询中</div>
                        </div>
                    </template> -->
                </div>
            </div>
            <div class="d-flex box-item" v-if="rules">
                <div class="box-item-label">请选择要导出的数据：</div>
                <div>
                    <el-form
                        ref="form"
                        :model="formValue"
                        :rules="rules"
                        class="d-flex align-items-center"
                        height="32px"
                    >
                        <el-form-item prop="leftInput" style="width: 40%">
                            <div class="d-flex lh-32">
                                第
                                <el-input
                                    class="input-range"
                                    v-model="formValue.preValue"
                                    type="number"
                                ></el-input>
                                条
                            </div>
                        </el-form-item>
                        <div class="lh-32 separated">~</div>
                        <el-form-item prop="rightInput" style="width: 40%">
                            <div class="d-flex lh-32">
                                第
                                <el-input
                                    class="input-range"
                                    v-model="formValue.sufValue"
                                    type="number"
                                ></el-input>
                                条
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <div class="box-item tips">*单次最多能导出1万条数据</div>

            <div class="d-flex justify-content-center">
                <el-button @click="onExport" type="primary" :disabled="!total"
                    >申请导出</el-button
                >
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { ElForm } from "element-ui/types/form"
    import { Component, Prop, Ref, Watch } from "vue-property-decorator"

    @Component({ components: { FormBuilder } })
    export default class DialogAllocation extends DialogController {
        @Ref("form")
        private form!: ElForm

        @Prop()
        private filterData!: Record<string, any>

        @Prop()
        private total!: string

        private rules: any = null
        private formValue = {
            preValue: "1",
            sufValue: "",
        }

        @Watch("value")
        private onVisibilityChange(visible: boolean) {
            if (visible) {
                this.formValue.preValue = "1"
                this.formValue.sufValue = this.total || "1"
                this.$nextTick(() => {
                    this.form?.clearValidate()
                })
            }
        }

        private setRules() {
            this.rules = {
                leftInput: [
                    {
                        validator: (rule: any, v: any, callback: any) => {
                            const value = this.formValue.preValue
                            console.log(value)
                            if (!value) {
                                console.log("value 1")
                                callback(new Error("输入不能为空"))
                                return
                            }
                            if (parseInt(value) <= 0) {
                                callback(new Error("输入必须大于0"))
                                return
                            }
                            if (!/^\d+$/.test(value)) {
                                callback(new Error("输入必须是整数"))
                                return
                            }
                            // 检查右边的值是否大于左边的值
                            if (
                                parseInt(this.formValue.sufValue) <=
                                parseInt(this.formValue.preValue)
                            ) {
                                console.log("value 3")
                                callback(new Error("右边的值应大于左边的值"))
                                return
                            }
                            // 清除表单校验
                            this.form?.clearValidate()
                            callback()
                        },
                        trigger: "blur",
                    },
                ],
                rightInput: [
                    {
                        validator: (rule: any, v: any, callback: any) => {
                            const value = this.formValue.sufValue
                            if (!value) {
                                console.log("value 1")
                                callback(new Error("输入不能为空"))
                                return
                            }
                            if (parseInt(value) <= 0) {
                                callback(new Error("输入必须大于0"))
                                return
                            }
                            if (!/^\d+$/.test(value)) {
                                callback(new Error("输入必须是整数"))
                                return
                            }
                            // 检查右边的值是否大于左边的值
                            if (
                                parseInt(this.formValue.sufValue) <=
                                parseInt(this.formValue.preValue)
                            ) {
                                callback(new Error("右边的值应大于左边的值"))
                                return
                            }
                            // 检查右边的值是否比左边的值多不超过10000
                            if (
                                parseInt(this.formValue.sufValue) -
                                    parseInt(this.formValue.preValue) >
                                10000
                            ) {
                                callback(new Error("单次最多能导出1万条"))
                                return
                            }
                            // 检查右边的值是否不超过
                            if (
                                parseInt(this.formValue.sufValue) >
                                parseInt(this.total)
                            ) {
                                callback(new Error("最大值不能超过" + this.total))
                                return
                            }
                            // 清除表单校验
                            this.form?.clearValidate()
                            callback()
                        },
                        trigger: "blur",
                    },
                ],
            }
        }

        private onExport() {
            this.form?.validate().then(() => {
                this.$emit("export", this.formValue)
                this.close()
            })
        }

        mounted() {
            this.setRules()
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .dialog-title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 16px;
    }

    .box-item {
        margin-bottom: 30px;

        .box-item-label {
            flex: none;
            line-height: 32px;
        }

        /deep/ .el-form-item__content {
            line-height: 32px;
        }

        /deep/ .el-form-item {
            margin-bottom: 0px;
        }

        &.tips {
            color: red;
        }
    }

    .items {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: flex-start;
        max-height: 18vh;
        overflow-x: hidden;
        overflow-y: auto;

        .item {
            min-height: 30px;
            padding-left: 8px;
            padding-right: 8px;
            font-size: 14px;
            color: #5782ec;
            background: #f8f8f8;
            border-radius: 4px;
            border: 1px solid #e5e6ea;
            line-height: 30px;
            display: flex;
            max-width: 100%;
            height: auto;
            flex: none;

            .item-label {
                color: #333333;
                flex: none;
            }
        }
    }

    .separated {
        margin-left: 5px;
        margin-right: 5px;
    }

    .result-text {
        color: #000000;
        line-height: 14px;
        text-align: center;
    }

    .process-content {
        display: flex;
        width: 100%;
        justify-content: center;
        // flex-direction: column;
        align-items: center;

        .process-tips {
            font-size: 14px;
            color: #666666;
            line-height: 14px;
            margin-bottom: 20px;
        }

        .dots {
            &::after {
                content: ".";
                animation: dots 1.5s infinite;
            }
        }

        .process-box {
            width: 300px;
        }
    }

    @keyframes dots {
        0% {
            content: ".";
        }
        33% {
            content: "..";
        }
        66% {
            content: "...";
        }
        100% {
            content: ".";
        }
    }

    .input-range {
        width: 118px;
        height: 32px;
        line-height: 32px;
        border-radius: 0px;
        margin-left: 5px;
        margin-right: 5px;

        /deep/ .el-input__inner {
            height: 32px;
            line-height: 32px;
            border-radius: 0px;
        }
    }

    .lh-32 {
        line-height: 32px;
    }
</style>
