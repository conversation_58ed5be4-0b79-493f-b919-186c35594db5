import { RouteConfig } from "@/router"
import { routesMap } from "@/router/direction"

export const bigScreeDataStatisticsAnalysisr: RouteConfig[] = [
    {
        path: `/big-screen/data-statistics-analysis/index`,
        name: routesMap.bigScreen.dataStatisticsAnalysis.index,
        meta: {},
        component: () => import("./index.vue"),
    },
    {
        path: `/big-screen/data-statistics-analysis/person`,
        name: routesMap.bigScreen.dataStatisticsAnalysis.list1,
        meta: {},
        component: () => import("./pages/居民.vue"),
    },
    {
        path: `/big-screen/data-statistics-analysis/detail-person`,
        name: routesMap.bigScreen.dataStatisticsAnalysis.personDetail,
        meta: {},
        component: () => import("./pages/detail-person.vue"),
    },
]
