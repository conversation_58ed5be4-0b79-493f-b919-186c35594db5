<template>
    <div class="detail-container" v-if="row">
        <div class="content">
            <div class="title">共享用工信息</div>
            <div class="u-p-20">
                <detail-row-col
                    :list="list"
                    :labelStyle="labelStyle"
                ></detail-row-col>
                <div class="u-flex u-col-top">
                    <div :style="labelStyle">图片</div>
                    <div class="u-flex">
                        <el-image
                            :src="img"
                            v-for="img in images"
                            :key="img"
                            class="img"
                            :preview-src-list="images"
                        />
                        <span v-if="!images.length" style="lineheight: 34px"
                            >-</span
                        >
                    </div>
                </div>
            </div>
        </div>
        <div class="content">
            <div class="title">操作信息</div>
            <div class="u-p-20 u-p-b-40">
                <detail-row-col
                    :list="list2"
                    :labelStyle="labelStyle"
                ></detail-row-col>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { Row } from ".."
    import { Component, Prop, Vue } from "vue-property-decorator"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { getImages } from "@/utils"
    import { formatTime } from "@/utils/tools"

    @Component({ components: { DetailRowCol } })
    export default class DetailView extends Vue {
        @Prop()
        private row!: Row

        private labelStyle = {
            fontSize: "14px",
            minWidth: "70px",
            marginRight: "10px",
            lineHeight: "34px",
            color: "#555",
            flex: "none",
        }

        private get images() {
            if (!this.row) return []
            return getImages(this.row.images)
        }

        private get des() {
            const des = (this.row.apply_description || "")
                .split("\n")
                .filter(Boolean)
            return des.length ? des : ["-"]
        }

        private showMobile = false
        private get list(): ColItem[] {
            if (!this.row) return []
            const h = this.$createElement
            const des = this.des
            return (
                [
                    {
                        label: "企业名称",
                        value: this.row.agent_name,
                        span: 12,
                    },
                    { label: "涉及人数", value: this.row.person_num, span: 12 },
                    { label: "类型", value: this.row.type_label, span: 12 },
                    { label: "联系人", value: this.row.contact_person, span: 12 },
                    {
                        label: "所在地",
                        value: this.row.address_detail_label,
                        span: 12,
                    },
                    {
                        label: "联系电话",
                        vNode: h(
                            "div",
                            { class: "u-flex" },
                            this.showMobile
                                ? [h("div", this.row.contact_number)]
                                : [
                                      h("div", this.row.mobile_hide),
                                      h(
                                          "el-button",
                                          {
                                              attrs: {
                                                  type: "text",
                                              },
                                              class: "u-m-l-10",
                                              on: {
                                                  click: () => {
                                                      this.showMobile = true
                                                  },
                                              },
                                          },
                                          "查看"
                                      ),
                                  ]
                        ),
                        span: 12,
                    },
                    {
                        label: "申请时间",
                        value: this.row.final_change_time_label,
                        span: 12,
                    },

                    {
                        label: "有效期",
                        value: formatTime.day(this.row.expired_date),
                        span: 12,
                    },

                    {
                        label: "描述",
                        vNode: h(
                            "div",
                            {},
                            des.map((e) => {
                                return h("div", {}, e)
                            })
                        ),
                        span: 24,
                    },
                ] as ColItem[]
            ).filter((i) => !i.hide)
        }

        private get list2(): ColItem[] {
            if (!this.row) return []
            return (
                [
                    { label: "审核状态", value: this.row.status_label, span: 12 },
                    {
                        label: "上架状态",
                        value: this.row.shelf_status_label,
                        span: 12,
                    },
                    { label: "审核人", value: this.row.audit_user, span: 12 },
                    { label: "上架人", value: this.row.shelf_user, span: 12 },
                    {
                        label: "审核时间",
                        value: this.row.review_time_label,
                        span: 12,
                    },
                    {
                        label: "上架时间",
                        value: this.row.shelf_time_label,
                        span: 12,
                    },
                    { label: "审核意见", value: this.row.review_comment, span: 12 },
                ] as ColItem[]
            ).filter((i) => !i.hide)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        background: #fff;
        .content {
            padding: 20px;
            padding-bottom: 0;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
            .label {
                width: 98px;
                margin-right: 10px;
                color: #555;
                flex: none;
                line-height: 28px;
            }
            .value {
                line-height: 28px;
            }
            .info {
                line-height: 34px;
                color: #333;
                font-size: 14px;
            }
        }
    }
    .img {
        width: 80px;
        height: 80px;
        margin-right: 10px;
    }
    ::v-deep .detail-row .item {
        line-height: 34px;
    }
</style>
