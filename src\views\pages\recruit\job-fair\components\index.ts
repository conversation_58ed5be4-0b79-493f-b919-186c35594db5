import {
    BuildFormConfig,
    buildSelectSource,
    FileType,
    FormItem,
    FormType,
} from "@/core-ui/component/form"
import {
    getStatusLabel,
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"
import { getAddress } from "@/utils"
import { config, EnvProject } from "@/config"
import { flatMap, map } from "lodash"
const isJz = [EnvProject.荆州项目].includes(config.envProject)
const tableFilter1: TableFilter[] = []

export const predict1 = {
    action_name: "label",
    name: "system_user_references#uniplat_username",
    create_time: "label",
}
export function tableConfig1(id: string): TableConfig {
    return {
        model: sdk.core.model("work_flow_log@xg_project").list("detail_log"),
        filter: tableFilter1,
        defaultPageSize: 10,
        predict: predict1,
        preFilter: {
            obj_id: id,
            model_name: "job_fair",
        },
    }
}

export const columns1: TableColumn[] = [
    { label: "事项名称", prop: "action_name", showOverflowTip: true },
    { label: "操作人", prop: "name", showOverflowTip: true },
    { label: "操作时间", prop: "create_time_label", showOverflowTip: true },
    { label: "备注", prop: "remark", showOverflowTip: true },
]

const tableFilter2: TableFilter[] = [
    {
        label: "企业名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "统一社会信用代码",
        type: FormType.Text,
        prop: "company_code",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "企业所在地",
        type: FormType.Cascader,
        prop: "province_code",
        option: { elProps: { checkStrictly: true }, filterable: true },
    },
    // {
    //     label: "联系人",
    //     type: FormType.Text,
    //     prop: "contact_person",
    //     keyValueFilter: {
    //         match: ListTypes.filterMatchType.fuzzy,
    //     },
    // },
    {
        label: "所属行业",
        type: FormType.Text,
        prop: "industory_catalog",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "报名来源",
        type: FormType.Select,
        prop: "create_from",
    },
    {
        label: "报名时间",
        type: FormType.DatePicker,
        prop: "apply_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "签到状态",
        type: FormType.Select,
        prop: "audit_status",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
        hide: [EnvProject.鄂州项目].includes(config.envProject),
    },
    {
        label: "是否有违约记录",
        type: FormType.Select,
        prop: "break_count",
        option: {
            type: "boolean",
        },
        sourceInputsParameter: buildSelectSource([
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
]

export const predict2 = {
    agent_name: "agent#agent_name",
    company_code: "agent#company_code",
    agent_region_name: "agent#area#region_name",
    agent_industory_catalog: "agent#industory_catalog_display_label",
    apply_time: "label",
    break_count: "",
    create_from: "label",
    position_count: "label",
    position_num: "label",
    wx_qr_share_image: "",
    sign_status: "label",
    audit_status: "label",
    agent_address_detail: "agent#address_detail",
    agent_province: "agent#province#region_name",
    agent_city: "agent#city#region_name",
    agent_area: "agent#area#region_name",
    position_all_count: "",
    position_wait_count: "",
}
export function tableConfig2(id: string): TableConfig {
    return {
        model: sdk.core
            .model("job_fair_agent_apply")
            .list("in_job_fair_detail_for_operate"),
        filter: tableFilter2,
        defaultPageSize: 10,
        predict: predict2,
        preFilter: {
            job_fair_id: id,
        },
    }
}

export const columns2: TableColumn[] = [
    {
        type: "selection",
        prop: "select",
        selectable: (row: any) => {
            return row.audit_status_label === "审核通过"
        },
    },
    {
        label: "企业信息",
        prop: "agent_name",
        width: "200px",
        render(h, row) {
            return h("div", [
                h("div", row.agent_name),
                h("div", row.company_code),
            ])
        },
        showOverflowTip: true,
    },
    {
        label: "企业所在地",
        prop: "address",
        align: "left",
        formatter(row) {
            return getAddress(row, [
                "agent_province",
                "agent_city",
                "agent_area",
                "agent_address_detail",
            ])
        },
        showOverflowTip: true,
    },
    {
        label: "所属行业",
        prop: "agent_industory_catalog_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "报名时间",
        prop: "apply_time_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "违约信息",
        prop: "break_count",
        showOverflowTip: true,
        formatter(row) {
            return +row.break_count > 0 ? "有" : "无"
        },
        width: "80px",
    },
    {
        label: "报名来源",
        prop: "create_from_label",
        width: "80px",
        showOverflowTip: true,
    },
    {
        label: "报名岗位",
        prop: "position_count",
        width: "80px",
        formatter(row) {
            return `${row.position_all_count || 0}`
        },
        showOverflowTip: true,
    },
    {
        label: "企业二维码",
        prop: "wx_qr_share_image",
        width: "140px",
        showOverflowTip: true,
        hide: [EnvProject.鄂州项目].includes(config.envProject),
    },
    {
        label: "签到状态",
        prop: "sign_status_label",
        hide: [EnvProject.鄂州项目].includes(config.envProject),
        render(h, row) {
            return getStatusLabel(
                h,
                {
                    label: row.sign_status_label,
                    value: row.sign_status,
                },
                {
                    0: "#65D2A3",
                    1: "#ccc",
                }
            )
        },
        showOverflowTip: true,
    },
    {
        label: "审核状态",
        prop: "audit_status_label",
        render(h, row) {
            return getStatusLabel(
                h,
                {
                    label: row.audit_status_label,
                    value: row.audit_status,
                },
                ["#FF8B16", "#65D2A3", ""]
            )
        },
        showOverflowTip: true,
    },
    { label: "操作", prop: "h", width: "160px", showOverflowTip: true },
]

export const columns2qr: TableColumn[] = [
    { label: "企业名称", prop: "agent_name", showOverflowTip: true },
    { label: "推广二维码", prop: "wx_qr", showOverflowTip: true },
]

const tableFilter3: TableFilter[] = [
    {
        label: "岗位名称",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "岗位职能",
        type: FormType.Cascader,
        prop: "function_category_1",
        option: { elProps: { checkStrictly: true }, filterable: true },
    },
    {
        label: "企业名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "工作地点",
        type: FormType.Cascader,
        prop: "province_code",
        option: { elProps: { checkStrictly: true }, filterable: true },
    },
    {
        label: "薪资范围",
        type: FormType.Select,
        prop: "salary",
        option: { filterable: true },
    },
    {
        label: "工作性质",
        type: FormType.Select,
        prop: "work_type",
    },
    // {
    //     label: "工作年限",
    //     type: FormType.Text,
    //     prop: "agent_name2",
    //     keyValueFilter: {
    //         match: ListTypes.filterMatchType.fuzzy,
    //     },
    // },
    {
        label: "学历要求",
        type: FormType.Select,
        prop: "education",
    },
    // {
    //     label: "年龄要求",
    //     type: FormType.Text,
    //     prop: "agent_name3",
    //     keyValueFilter: {
    //         match: ListTypes.filterMatchType.fuzzy,
    //     },
    // },
    // {
    //     label: "报名时间",
    //     type: FormType.Text,
    //     prop: "agent_nam4e",
    //     keyValueFilter: {
    //         match: ListTypes.filterMatchType.fuzzy,
    //     },
    // },
]

export const predict3 = {
    agent_name: "position#agent#agent_name",
    company_code: "position#agent#company_code",
    name: "position#name",
    industry: "position#industry_label",
    salary: "position#salary_label",
    address_detail: "position#agent#address_detail",
    recruit_count: "position#recruit_count",
    industry_1: "position#industry_1",
    industry_2: "position#industry_2",
    industry_3: "position#industry_3",
    province: "position#province#region_name",
    city: "position#city#region_name",
    area: "position#area#region_name",
    function_categories: "position#function_categories",
    position_id: "position#id",
    audit_status: "label",
    education: "position#education_label",
    experience: "position#experience",
    work_type: "position#work_type_label",
    age_require: "position#age_require_label",
    create_time: "label",
    order_num: "label",
}
export function tableConfig3(id: string): TableConfig {
    return {
        model: sdk.core
            .model("job_fair_agent_position")
            .list("in_job_fair_detail_for_operate"),
        filter: tableFilter3,
        defaultPageSize: 10,
        predict: predict3,
        preFilter: {
            job_fair_id: id,
            // audit_status: 1,
        },
    }
}

export const columns3: TableColumn[] = [
    {
        prop: "select",
        type: "selection",
    },
    { label: "岗位名称", prop: "name", showOverflowTip: true },
    {
        label: "岗位职能",
        prop: "function_categories",
        showOverflowTip: true,
    },
    {
        label: "企业信息",
        prop: "agent_name",
        width: "200px",
        render(h, row) {
            return h("div", [
                h("div", row.agent_name),
                h("div", row.company_code),
            ])
        },
        showOverflowTip: true,
    },
    {
        label: "工作地址",
        prop: "address_detail",
        formatter(row) {
            return getAddress(row)
        },
        showOverflowTip: true,
    },
    {
        label: "薪资待遇",
        prop: "salary_label",
        formatter(row) {
            if (!row.salary_label) return "-"
            return row.salary_label + "/月"
        },
        showOverflowTip: true,
    },
    { label: "招聘人数", prop: "recruit_count", showOverflowTip: true },
    {
        label: "工作性质",
        prop: "work_type_label",
        width: "100px",
        showOverflowTip: true,
    },
    { label: "工作年限", prop: "experience", showOverflowTip: true },
    { label: "学历要求", prop: "education_label", showOverflowTip: true },
    { label: "年龄要求", prop: "age_require_label", showOverflowTip: true },
    { label: "报名时间", prop: "create_time_label", showOverflowTip: true },
    {
        label: "审核状态",
        prop: "audit_status_label",
        render(h, row) {
            return getStatusLabel(
                h,
                {
                    label: row.audit_status_label,
                    value: row.audit_status,
                },
                ["#FF8B16", "#65D2A3", "#E04B2D"]
            )
        },
        showOverflowTip: true,
    },
    { label: "投递数量", prop: "order_num", showOverflowTip: true },
    { label: "操作", prop: "h", showOverflowTip: true },
]

const tableFilter4: TableFilter[] = [
    {
        label: "姓名",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    {
        label: "出生日期",
        type: FormType.DatePicker,
        prop: "birth_date",
        option: {
            type: "daterange",
        },
    },
    {
        label: "学历",
        type: FormType.Select,
        prop: "education",
    },
    {
        label: "用户标签",
        type: FormType.Select,
        prop: "tags",
        useTag: "*",
        option: {
            filterable: true,
            multiple: true,
            collapseTags: true,
        },
    },
    {
        label: "意向岗位",
        type: FormType.Select,
        prop: "job_willing_type_work",
    },
    {
        label: "报名状态",
        type: FormType.Select,
        prop: "apply_status",
    },
    {
        label: "报名时间",
        type: FormType.DatePicker,
        prop: "apply_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "签到状态",
        type: FormType.Select,
        prop: "sign_status",
    },
    {
        label: "签到时间",
        type: FormType.DatePicker,
        prop: "sign_time",
        option: {
            type: "daterange",
        },
    },
]

export const predict4 = {
    name_hide: "user_account#profile#name_hide",
    phone_hide: "user_account#mobile_hide",
    mobile: "user_account#mobile",
    sex: "user_account#profile#sex_label",
    edu: "user_account#profile#basic_info#education_label",
    status: "label",
    sign_time: "label",
    apply_time: "label",
    recruit_position_count: "user_account#profile#recruit_position_count",
    profile_id: "user_account#profile#id",
    user_apply_position_count: "",
    tags: "tags",
    showMobile: false,
    profile_access_key: "user_account#profile#_access_key",
    job_willing_type_work:
        "user_account#profile#job_willingness#job_willing_type_work_display_label",
}
export function tableConfig4(id: string): TableConfig {
    return {
        model: sdk.core
            .model("job_fair_user_apply")
            .list("in_job_fair_detail_for_operate"),
        filter: tableFilter4,
        defaultPageSize: 10,
        predict: predict4,
        preFilter: {
            job_fair_id: id,
            is_del: "0",
        },
    }
}

export const columns4: TableColumn[] = [
    { label: "姓名", prop: "name_hide", showOverflowTip: true },
    {
        label: "电话",
        prop: "phone_hide",
        width: "140px",
        showOverflowTip: true,
    },
    {
        label: "性别",
        prop: "sex_label",
        showOverflowTip: true,
    },
    { label: "学历", prop: "edu_label", showOverflowTip: true },
    {
        label: "用户标签",
        prop: "tags",
        formatter(row) {
            return (row.tags["默认标签组"] || []).filter(Boolean).join(",")
        },
        showOverflowTip: true,
    },
    {
        label: "意向岗位",
        prop: "job_willing_type_work_label",
        showOverflowTip: true,
    },
    {
        label: "报名状态",
        prop: "apply_status",
        width: "80px",
        render(h, row) {
            return getStatusLabel(
                h,
                {
                    label: row.apply_time ? "已报名" : "未报名",
                    value: row.apply_time ? "0" : "1",
                },
                ["#5782EC", "#FF8B16", "#65D2A3"]
            )
        },
        showOverflowTip: true,
    },
    {
        label: "报名时间",
        prop: "apply_time_label",
        width: "180px",
        showOverflowTip: true,
    },
    {
        label: "签到状态",
        prop: "status_label",
        width: "80px",
        render(h, row) {
            return getStatusLabel(
                h,
                {
                    label: row.sign_time_label ? "已签到" : "未签到",
                    value: row.sign_time_label ? "0" : "1",
                },
                ["#5782EC", "#FF8B16", "#65D2A3"]
            )
        },
        showOverflowTip: true,
    },
    {
        label: "签到时间",
        prop: "sign_time_label",
        width: "180px",
        showOverflowTip: true,
    },
    // { label: "操作", prop: "h", showOverflowTip: true },
]

const tableFilter5: TableFilter[] = [
    {
        label: "企业名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "招聘会名称",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "企业资格审核状态",
        type: FormType.Select,
        prop: "audit_status",
    },
    {
        label: "是否有岗位未审核",
        type: FormType.Select,
        prop: "has_wait",
        sourceInputsParameter: buildSelectSource([
            {
                value: "全部",
                key: "",
            },
            {
                key: "1",
                value: "是",
            },
            {
                key: "0",
                value: "否",
            },
        ]),
    },
    {
        label: "岗位来源",
        type: FormType.Select,
        prop: "create_from",
    },
]

export const predict5 = {
    id: "",
    agent_name: "agent#agent_name",
    company_code: "agent#company_code",
    title: "job_fair#title",
    type: "job_fair#type_label",
    position_count: "",
    position_wait_count: "",
    apply_time: "label",
    audit_status: "label",
    create_from: "label",
    position_all_count: "",
    wx_qr: "",
}

export function tableConfig5(id: string): TableConfig {
    return {
        model: sdk.core.model("job_fair_agent_apply").list("for_operate"),
        filter: tableFilter5,
        defaultPageSize: 10,
        predict: predict5,
        preFilter: {
            job_fair_id: id,
        },
    }
}

export const columns5: TableColumn[] = [
    {
        label: "企业名称",
        prop: "agent_name",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "统一社会信用代码",
        prop: "company_code",
        width: "140px",
        showOverflowTip: true,
    },
    {
        label: "招聘会名称",
        prop: "title",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "类型",
        prop: "type_label",
        showOverflowTip: true,
    },
    {
        label: "岗位数量",
        prop: "position_all_count",
        width: "80px",
        showOverflowTip: true,
    },
    {
        label: "岗位待审核数",
        prop: "position_wait_count",
        width: "110px",
        showOverflowTip: true,
    },
    {
        label: "提交时间",
        prop: "apply_time_label",
        showOverflowTip: true,
    },
    {
        label: "企业资格 审核状态",
        prop: "audit_status_label",
        width: "90px",
        showOverflowTip: true,
    },
    {
        label: "岗位来源",
        prop: "create_from_label",
        showOverflowTip: true,
    },
    { label: "企业宣传码", prop: "qr", width: "120px", showOverflowTip: true },
    { label: "操作", prop: "h", width: "160px", showOverflowTip: true },
]

const tableFilter7: TableFilter[] = [
    {
        label: "姓名",
        type: FormType.Text,
        prop: "name",
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    {
        label: "出生日期",
        type: FormType.DatePicker,
        prop: "birth_date",
        option: {
            type: "daterange",
        },
    },
    {
        label: "学历",
        type: FormType.Select,
        prop: "education",
    },
    {
        label: "用户标签",
        type: FormType.Select,
        prop: "tags",
        useTag: "*",
        option: {
            filterable: true,
            multiple: true,
            collapseTags: true,
        },
    },
    {
        label: "意向岗位",
        type: FormType.Select,
        prop: "job_willing_type_work",
    },
    {
        label: "企业",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "投递时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
    // {
    //     label: "来源",
    //     type: FormType.Select,
    //     prop: "created_from",
    // },
]

export const predict7 = {
    name: "name",
    mobile: "mobile",
    mobile_encode: "",
    sex: "sex_label",
    getAge: "getAge",
    education: "education_label",
    job_willing_type_work: "job_willing_type_work_label",
    agent_name: "agent_name",
    company_code: "company_code",
    region_name: "region_name",
    salary: "salary",
    create_time: "label",
    status: "",
    status_memo: "",
    tags: "tags",
    position_name: "position#name",
    profile_job_willing_type_work:
        "profile#job_willingness#job_willing_type_work_label",
    position_agent_name: "position#agent#agent_name",
    position_company_code: "position#agent#company_code",
    position_create_type: "position#create_type_label",
    created_from: "label",
    profile_id: "profile#id",
    profile_access_key: "profile#_access_key",
}
export function tableConfig7(
    created_from_id: string,
    position_id = ""
): TableConfig {
    const preFilter: any = { created_from_id } as any
    if (position_id) {
        preFilter.position_id = position_id
    }
    return {
        model: sdk.core
            .model("xg_candidate_order")
            .list("in_job_fair_detail_for_operate"),
        filter: tableFilter7,
        defaultPageSize: 10,
        predict: predict7,
        preFilter,
    }
}

export const columns7: TableColumn[] = [
    { label: "姓名", prop: "name", showOverflowTip: true },
    { label: "电话", prop: "mobile_encode", showOverflowTip: true },
    { label: "性别", prop: "sex_label", showOverflowTip: true },
    { label: "学历", prop: "education_label", showOverflowTip: true },
    {
        label: "用户标签",
        prop: "tags",
        formatter(row) {
            return (map(flatMap(row.tags), "tagName") || [])
                .filter(Boolean)
                .join(",")
        },
        showOverflowTip: true,
    },
    // {
    //     label: "意向岗位",
    //     prop: "profile_job_willing_type_work",
    //     showOverflowTip: true,
    // },
    { label: "投递岗位", prop: "position_name", showOverflowTip: true },
    { label: "投递企业", prop: "position_agent_name", showOverflowTip: true },
    { label: "投递时间", prop: "create_time_label", showOverflowTip: true },
    {
        label: "来源",
        prop: "created_from_label",
        showOverflowTip: true,
    },
    { label: "操作", prop: "h", showOverflowTip: true },
]

export function createSeqFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "job_fair",
        sdkAction: "set_seq_number",
        id,
        forms: [
            {
                label: "排序值",
                type: FormType.Text,
                prop: "seq_number",
                option: {
                    type: "number",
                },
                required: true,
            },
        ],
    }
}

export function addForm1(id?: string): FormItem[] {
    return [
        {
            label: "招聘会类型：",
            type: FormType.Radio,
            prop: "type",
            needListen: true,
            option: {
                disabled: !!id,
            },
        },
        {
            label: "主题类型：",
            type: FormType.Cascader,
            prop: "theme_type",
            col: { span: 11, offset: 0 },
            needListen: true,
        },
        {
            label: "",
            type: FormType.Tip,
            prop: "empty",
            option: {
                placeholder: "",
            },
            col: { span: 11, offset: 2 },
            handlerDisplay(data) {
                return +data.theme_type !== 20
            },
        },
        {
            label: "主题类型文字说明：",
            type: FormType.Text,
            prop: "theme_type_remark",
            col: { span: 11, offset: 2 },
            handlerDisplay(data) {
                return +data.theme_type === 20
            },
            rules: [
                {
                    required: true,
                    message: "主题类型文字说明不能为空",
                },
            ],
        },
        {
            label: "是否专场：",
            type: FormType.Radio,
            prop: "is_activity_type",
            col: { span: 11, offset: 0 },
            needListen: true,
            option: {
                type: "boolean",
            },
            sourceInputsParameter: buildSelectSource([
                {
                    value: "是",
                    key: 1,
                },
                {
                    value: "否",
                    key: 0,
                },
            ]),
            handlerDisplay() {
                return [EnvProject.荆州项目].includes(config.envProject)
            },
        },
        {
            label: "",
            type: FormType.Tip,
            prop: "empty2",
            option: {
                placeholder: "",
            },
            col: { span: 11, offset: 2 },
            handlerDisplay(data) {
                return (
                    !+data.is_activity_type &&
                    [EnvProject.荆州项目].includes(config.envProject)
                )
            },
        },
        {
            label: "专场类型：",
            type: FormType.Select,
            prop: "activity_type",
            col: { span: 11, offset: 2 },
            needListen: true,
            required: true,
            handlerDisplay(data) {
                return (
                    !!+data.is_activity_type &&
                    [EnvProject.荆州项目].includes(config.envProject)
                )
            },
        },
        {
            label: "专场类型文字说明：",
            type: FormType.Text,
            prop: "activity_type_remark",
            col: { span: 11, offset: 0 },
            handlerDisplay(data) {
                return (
                    +data.activity_type === 10 &&
                    !!+data.is_activity_type &&
                    [EnvProject.荆州项目].includes(config.envProject)
                )
            },
            rules: [
                {
                    required: true,
                    message: "专场类型文字说明不能为空",
                },
            ],
        },
        {
            label: "",
            type: FormType.Tip,
            prop: "empty2",
            option: {
                placeholder: "",
            },
            col: { span: 11, offset: 2 },
            handlerDisplay(data) {
                return (
                    +data.activity_type === 10 &&
                    !!+data.is_activity_type &&
                    [EnvProject.荆州项目].includes(config.envProject)
                )
            },
        },
        {
            label: "招聘会介绍：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 3,
                resize: "none",
            },
            col: { span: 11, offset: 0 },
            prop: "description",
        },
        {
            label: "渠道：",
            type: FormType.Text,
            prop: "type_channel",
            col: { span: 11, offset: 2 },
            required: false,
            hide: !isJz,
        },
        {
            label: "",
            type: FormType.Tip,
            prop: "empty3",
            option: {
                placeholder: "",
            },
            col: { span: 11, offset: 2 },
            hide: isJz,
        },
        {
            label: "",
            prop: "audit_status",
            hide: true,
        },
        // 场地信息
        {
            label: "召开详细地址：",
            type: FormType.Text,
            prop: "place_detail",
            handlerDisplay(data) {
                return +data.type === 2
            },
            rules: [
                {
                    required: true,
                    message: "召开详细地址不能为空",
                },
            ],
            col: { span: 11, offset: 0 },
        },
        {
            label: "",
            type: FormType.Tip,
            option: {
                placeholder: "",
            },
            handlerDisplay(data) {
                return +data.type === 2
            },
            col: { span: 11, offset: 2 },
            prop: "t4",
        },
        {
            label: "招聘会名称：",
            type: FormType.Text,
            prop: "title",
            col: { span: 11, offset: 0 },
            rules: [
                {
                    validator(_rule, value, callback) {
                        if (value.length > 30) {
                            callback(new Error("招聘会名称不能超过30个字符"))
                            return
                        }
                        callback()
                    },
                },
            ],
        },
        {
            label: "招聘会主题：",
            type: FormType.Text,
            prop: "theme",
            col: { span: 11, offset: 2 },
        },
        {
            label: "可容纳企业数：",
            type: FormType.InputNumber,
            col: { span: 11, offset: 0 },
            handlerDisplay(data) {
                return +data.type === 2
            },
            rules: [
                {
                    validator(rule, value, callback) {
                        if (value <= 0) {
                            callback(new Error("可容纳企业数需大于0"))
                        }

                        callback()
                    },
                },
            ],
            prop: "company_count",
        },
        {
            label: "数量已满是否可申请：",
            type: FormType.Switch,
            col: { span: 11, offset: 2 },
            prop: "is_booth_apply",
            handlerDisplay(data) {
                return +data.type === 2
            },
            needListen: true,
            labelWidth: "170px",
        },
        // {
        //     label: "是否为固定场地：",
        //     type: FormType.Switch,
        //     col: { span: 11, offset: 0 },
        //     prop: "is_regular",
        //     labelWidth: "125px",
        //     hide: !!id,
        //     handlerDisplay(data) {
        //         return +data.type === 2
        //     },
        //     option: {
        //         disabled: !!id,
        //     },
        //     needListen: true,
        // },
        {
            label: "活动场地：",
            type: FormType.IntentSearch,
            col: { span: 11, offset: 2 },
            prop: "activity_area_id",
            rules: [
                {
                    required: true,
                    message: "活动场地不能为空",
                },
            ],
            option: {
                dialogProp: {
                    width: "1200px",
                },
                disabled: !!id,
                intentSearchConfig: {
                    tableConfig: () => ({
                        model: sdk.core
                            .model("activity_area")
                            .list("intent_search_list"),
                        predict: {
                            area_name: "",
                            province: "province#region_name",
                            city: "city#region_name",
                            area: "area#region_name",
                            update_time: "label",
                            hold_activity_count: "hold_activity_count",
                            area_file: "",
                        },
                        column: [
                            {
                                label: "序号",
                                prop: "id",
                                showOverflowTip: true,
                            },
                            {
                                label: "场地名称",
                                prop: "area_name",
                                showOverflowTip: true,
                            },
                            {
                                label: "所在区县",
                                prop: "address",
                                formatter(row) {
                                    return getAddress(row)
                                },
                                showOverflowTip: true,
                            },
                            {
                                label: "更新时间",
                                prop: "update_time_label",
                                showOverflowTip: true,
                            },
                            {
                                label: "举办场数",
                                prop: "hold_activity_count",
                                showOverflowTip: true,
                            },
                        ],
                        filter: [
                            {
                                label: "场地名称",
                                type: FormType.Text,
                                prop: "area_name",
                                keyValueFilter: {
                                    match: ListTypes.filterMatchType.fuzzy,
                                },
                            },
                            {
                                label: "所在区县",
                                type: FormType.Cascader,
                                prop: "province_code",
                                option: {
                                    elProps: {
                                        checkStrictly: true,
                                    },
                                },
                            },
                            {
                                label: "更新时间",
                                type: FormType.DatePicker,
                                prop: "update_time",
                                option: {
                                    type: "datetimerange",
                                },
                            },
                        ],
                    }),
                    template: "{area_name}",
                    valueKey: "id",
                },
            },
            handlerDisplay(data) {
                return !!+data.is_regular && +data.type === 2 && !id
            },
        },
        {
            label: "活动场地：",
            prop: "activity_area_name",
            type: FormType.Text,
            col: { span: 11, offset: 2 },
            option: {
                disabled: true,
            },
            handlerDisplay(data) {
                return !!+data.is_regular && +data.type === 2 && !!id
            },
        },
    ]
}

export function addForm2(id?: string): FormItem[] {
    return [
        {
            label: "召开区域：",
            type: FormType.Cascader,
            prop: "place_codes",
            option: { elProps: { checkStrictly: true } },
            col: { span: 11, offset: 0 },
            rules: [
                {
                    validator: (__, value, callback) => {
                        const v = value.split(",")
                        if (v.length > 1) {
                            callback()
                        } else {
                            callback(new Error())
                        }
                    },
                    message: "请选择详细召开区域",
                },
            ],
        },
        config.envProject === EnvProject.黄州项目
            ? {
                  label: "所属管理区域：",
                  type: FormType.Cascader,
                  prop: "mgt_codes",
                  option: {
                      elProps: { checkStrictly: true },
                  },
                  hide: true,
                  defaultValue: config.defaultRegion.region_code,
                  col: { span: 11, offset: 2 },
              }
            : {
                  label: "所属管理区域：",
                  type: FormType.Cascader,
                  prop: "mgt_codes",
                  option: {
                      elProps: { checkStrictly: true },
                  },
                  col: { span: 11, offset: 2 },
              },
        {
            label: "召开时间段：",
            type: FormType.DatePicker,
            option: {
                type: "datetime",
            },
            prop: "start_time",
            col: { span: 7, offset: 0 },
        },
        {
            label: "",
            labelWidth: "0px",
            type: FormType.DatePicker,
            option: {
                type: "datetime",
            },
            prop: "end_time",
            col: { span: 4, offset: 0 },
        },
        {
            label: "申请截止时间：",
            type: FormType.DatePicker,
            option: {
                type: "datetime",
            },
            prop: "apply_time",
            col: { span: 11, offset: 2 },
        },
        {
            label: "主办单位：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 3,
                resize: "none",
                placeholder: "如有联办单位需录入完整单位名称",
            },
            prop: "organizer",
            col: { span: 11, offset: 0 },
        },
        {
            label: "承办单位：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 3,
                resize: "none",
            },
            prop: "hosted_by",
            col: { span: 11, offset: 2 },
        },
        {
            label: "协办单位：",
            type: FormType.Text,
            option: {
                type: "textarea",
                rows: 3,
                resize: "none",
            },
            prop: "co_organizer",
            col: { span: 11, offset: 0 },
        },
    ]
}

export function addForm3(id?: string): FormItem[] {
    return [
        {
            label: "联系人：",
            type: FormType.Text,
            prop: "contact_person",
            col: { span: 11, offset: 0 },
        },
        {
            label: "联系电话：",
            type: FormType.Text,
            prop: "contact_mobile",
            col: { span: 11, offset: 2 },
        },
        {
            label: "办公电话：",
            type: FormType.Text,
            prop: "contact_office_mobile",
            col: { span: 11, offset: 0 },
        },
        {
            label: "电子邮箱：",
            type: FormType.Text,
            prop: "contact_email",
            col: { span: 11, offset: 2 },
        },
        {
            label: "pc端背景：",
            type: FormType.MyUpload,
            prop: "image_pc",
            option: {
                fileType: [FileType.Image],
                listType: "picture-card",
                placeholder: "支持上传jpg、png等图片格式，不超过3M",
                limit: 1,
                limitSize: 3072,
            },
            col: { span: 11, offset: 0 },
            hide: !isJz,
        },
        {
            label: "移动端背景：",
            type: FormType.MyUpload,
            prop: "image_mobile",
            option: {
                fileType: [FileType.Image],
                listType: "picture-card",
                placeholder: "支持上传jpg、png等图片格式，不超过3M",
                limit: 1,
                limitSize: 3072,
            },
            col: { span: 11, offset: isJz ? 2 : 0 },
            notDisplay: [EnvProject.鄂州项目].includes(config.envProject),
        },
    ]
}
