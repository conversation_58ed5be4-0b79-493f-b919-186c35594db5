import { config, EnvProject } from "@/config"
import { IntentSearch } from "@/core-ui/component/form"
import { object2UrlParams } from "@/utils/tools"
import { tableConfig as jobPositionTableConfig } from "@/views/pages/recruit/job"
import { tableConfig as jobFairTableConfig } from "@/views/pages/recruit/job-fair"
import { cloneDeep, filter } from "lodash"
export const carKeyLabelMap = {
    "JOB-RECOMMENDATION-NOTICE-SINGLE": {
        companyName: "受理单位",
        businessContent: "业务内容",
        remark: "备注",
        pagePath: "页面路径",
    },
    "JOB-FAIR-START-NOTICE-COMPLEX": {
        businessContent: "业务内容",
        dateTime: "时间",
        remark: "备注",
        pagePath: "页面路径",
    },
    "RECEIVED-INVITATION-NOTICE": {
        companyName: "受理单位",
        remark: "备注",
        pagePath: "页面路径",
    },
}
export const inCommonUseMap = [
    {
        label: "小程序首页",
        path: "/pages/tabbar/home/<USER>",
    },
    {
        label: "招聘会列表页",
        path: "/pages/sub/job-fair/index",
    },
    {
        label: "岗位列表页",
        path: "/pages/tabbar/job/index",
    },
    {
        label: "市本级培训列表",
        path: `/pages/web/peixun?url=${encodeURIComponent(
            `${
                process.env.VUE_APP_BK_PEIXUN_H5_URL
            }/#/TrainingEnrolment/ClassList/?${object2UrlParams({
                AAB299: "420999",
                dqvalue: "市本级",
            })}`
        )}`,
        hide: config.envProject !== EnvProject.孝感项目,
    },
    {
        label: "其他",
        path: "",
    },
]
export const jobPositionIndentSearchConfig: IntentSearch["intentSearchConfig"] =
    {
        tableConfig: () => {
            const t = cloneDeep(jobPositionTableConfig())
            t.tabPages = ["审核通过已上架"]
            t.filter = filter(t.filter, (i) => i.prop !== "online_status")
            t.oneTab = true
            t.column = filter(t.column, (i) => {
                return (
                    i.type !== "selection" &&
                    i.prop !== "h" &&
                    i.prop !== "status_label" &&
                    i.prop !== "online_status_label"
                )
            })
            return t
        },
        template: "id",
        valueKey: "id",
    }

export const jobFairIndentSearchConfig: IntentSearch["intentSearchConfig"] = {
    tableConfig: () => {
        const t = cloneDeep(jobFairTableConfig())
        t.tabPages = ["已发布"]
        t.filter = filter(t.filter, (i) => i.prop !== "status")
        t.oneTab = true
        t.column = filter(t.column, (i) => {
            return (
                i.type !== "selection" &&
                i.prop !== "h" &&
                i.prop !== "audit_status_label" &&
                i.prop !== "status_label"
            )
        })
        return t
    },
    template: "id",
    valueKey: "id",
}
