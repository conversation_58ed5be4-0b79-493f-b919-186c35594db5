.xg-project-org {

    // 全局阴影
    .shadow {
        box-shadow: 0px 5px 10px 1px rgba(211, 223, 240, 0.5);
    }

    // 列表级页面
    .container-index {
        background: #FEFEFF !important;
        padding-bottom: 50px;

        .filter-container {
            margin-bottom: 0;
            margin-bottom: -20px;
        }
    }

    // 菜单
    .menu-item,
    .submenu:not(.is-active) .el-submenu__title {
        color: #333;
    }


    // 详情级页面
    .detail-index {
        background: #FEFEFF;
        padding-bottom: 40px;

        .detail-row-container {
            border-radius: 5px;
            background: rgba(#F2F7FF, 0.5);
        }

        div:not(.custom-title) {

            .title {
                color: #4E5054 !important;
                font-size: 18px !important;
                line-height: 23px !important;
                background: transparent !important;
                min-height: 25px !important;
                font-weight: bold;

            }

            .label {

                color: #9098A6;
            }
        }

        .detail-row {

            .item {
                line-height: 20px;
            }
        }

    }

    // 标题
    .core-ui-custom-header {
        padding-top: 30px !important;
        padding-bottom: 20px !important;

        .title {
            color: #4E5054 !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            line-height: 22px !important;
            flex: 1;
        }

        .sub-title {
            font-size: 12px;
            font-weight: 500;
            color: #9098A6;
            line-height: 18px;
            padding-right: 60px;
        }
    }

    // single-page
    .container-route {
        background: #F5F6FA;
    }



    // 表格
    .el-table {
        border-radius: 5px;
        border: 1px solid #E0E8F9;
    }

    .core-ui-table-container {
        .filter-container {
            background: transparent;
            box-shadow: none;

            .form {
                margin: 20px;
                margin-bottom: 8px;

                .item {
                    margin-bottom: 12px;

                    .label {
                        color: #9098A6 !important;
                    }
                }
            }
        }

        .el-table__row {
            color: #4E5054;
        }

        .el-table__row--striped .el-table__cell {
            background: rgba(#F0F6FF, 0.5);
        }

        .filter-container-form .label {
            color: #9098A6 !important
        }
    }

    .core-ui-table-container .common-table {
        th.el-table__cell {
            color: #9098A6 !important;
            font-weight: 600 !important;
            font-size: 14px !important;
            background: #F0F6FF !important;
        }
    }

    // tab-view
    .tags-view-container {
        background: #DDE3F0;

        .tags-view-item {
            background: transparent !important;

            &.active {
                background: #FAFCFF !important;
                border-bottom: none;

                &::before {
                    background: #FAFCFF !important;
                }
            }
        }
    }

    // 弹窗
    .el-dialog {
        border-radius: 5px;
        box-shadow: 0px 10px 40px 1px rgba(0, 0, 0, 0.1);
    }

    // 页码
    .el-pagination {
        font-weight: 500;

        .number {
            color: #4E5054;
            background: #F0F6FF;

            &.active {
                background: #598BFF;
                color: #F5FCFF;
            }
        }

        .el-pagination__total {
            color: #9098A6;
        }

        button {
            color: #4E5054 !important;
            background: #F0F6FF !important;
        }
    }

    // 表格
    .el-form-item__label {
        color: #4E5054 !important;
    }

    // 输入框
    .el-input__inner,
    .el-textarea__inner {
        border: 1px solid #C2D5F2;
        background: #FAFCFF;
    }
}