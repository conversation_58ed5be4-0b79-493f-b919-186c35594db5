<template>
    <bk-station-web :srcPath="srcPath" />
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import { routesMap } from "@/router/direction"
    import BkStationWeb from "@/views/components/bk-station-web.vue"
    import { Component, Vue } from "vue-property-decorator"

    @Component({
        name: routesMap.jobStation.manage,
        components: { BkStationWeb },
    })
    export default class Index extends Vue {
        private get srcPath() {
            if (config.envProject === EnvProject.黄州项目) {
                return "/iframe/HuangZhou/settledmanage"
            }
            if (config.envProject === EnvProject.掇刀项目) {
                return "/iframe/DuoDao/settledmanage"
            }
            return "/iframe/XiaoGan/settledmanage"
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
</style>
