import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const base = [
    {
        path: "/base",
        name: routesMap.home.base,
        meta: {
            title: isQj ? "系统基础信息及数据管理" : "基础数据管理",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/base.svg"),
        },
        component: layout,
        children: [
            // 基础数据管理
            {
                path: "/base",
                redirect: "base/org",
                name: routesMap.base.root,
                meta: {
                    title: "基础数据管理",
                    // svgIcon: require("@/assets/icon/menu/base.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "org",
                        name: routesMap.base.org.list,
                        meta: {
                            title: "组织架构",
                            role: "/tablelist/xg_organization/manager",
                        },
                        component: () =>
                            import("@/views/pages/base/org/index.vue"),
                    },
                    {
                        path: "role",
                        name: routesMap.base.role.list,
                        meta: {
                            title: "角色管理",
                            role: "/tablelist/xg_user_role/manager",
                        },
                        component: () =>
                            import("@/views/pages/base/role/index.vue"),
                    },
                    {
                        path: "roleDetail",
                        name: routesMap.base.role.detail,
                        meta: {
                            title: "角色详情",
                            hidden: true,
                            parentMenuName: routesMap.base.role.list,
                        },
                        component: () =>
                            import("@/views/pages/base/role/detail.vue"),
                    },
                    {
                        path: "user",
                        name: routesMap.base.user.list,
                        meta: {
                            title: "用户管理",
                            role: "/tablelist/xg_login_user/manager",
                        },
                        component: () =>
                            import("@/views/pages/base/user/index.vue"),
                    },
                    // {
                    //     path: "grid",
                    //     name: routesMap.base.grid.list,
                    //     meta: {
                    //         title: "网格社工管理",
                    //     },
                    //     component: () => import("@/views/pages/base/grid/index.vue"),
                    // },
                    {
                        path: "mp",
                        name: routesMap.base.mp.list,
                        meta: {
                            title: "客户端管理",
                            role: "/tablelist/g_wx_app/manager",
                        },
                        component: () =>
                            import("@/views/pages/base/mp/index.vue"),
                    },
                    {
                        path: "grid-user-manage",
                        name: routesMap.base.regionUserManage.manage,
                        meta: {
                            title: "采集员管理",
                            role: "/tablelist/grid_user/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/region-user-manage/manage/index.vue"
                            ),
                    },
                    {
                        path: "grid-user-manage-add",
                        name: routesMap.base.regionUserManage.manageAdd,
                        meta: {
                            title: "添加采集员",
                            hidden: true,
                            parentMenuName:
                                routesMap.base.regionUserManage.manage,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/region-user-manage/manage/add.vue"
                            ),
                    },
                    {
                        path: "region-user-manage",
                        name: routesMap.base.regionUserManage.list,
                        meta: {
                            title: "采集员区域管理",
                            role: "/tablelist/grid_user_service_region/manage",
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/region-user-manage/index.vue"
                            ),
                    },
                    {
                        path: "region-user-manage-add",
                        name: routesMap.base.regionUserManage.add,
                        meta: {
                            title: "添加采集员",
                            hidden: true,
                            parentMenuName:
                                routesMap.base.regionUserManage.list,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/region-user-manage/add.vue"
                            ),
                    },
                    {
                        path: "sms-template",
                        name: routesMap.base.smsTemplate.list,
                        meta: {
                            title: "短信模板",
                            role: "/tablelist/sms_template/for_operate",
                        },
                        component: () =>
                            import("@/views/pages/base/sms-template/index.vue"),
                    },
                    {
                        path: "sms-send",
                        name: routesMap.base.smsTemplate.send,
                        meta: {
                            title: "短信发送任务",
                            role: "/tablelist/sms_send_history/for_operate_v2",
                        },
                        component: () =>
                            import("@/views/pages/base/sms-template/send.vue"),
                    },
                    {
                        path: "sms-template-add",
                        name: routesMap.base.smsTemplate.add,
                        meta: {
                            title: "添加短信模板",
                            hidden: true,
                            parentMenuName: routesMap.base.smsTemplate.list,
                        },
                        component: () =>
                            import("@/views/pages/base/sms-template/add.vue"),
                    },
                    {
                        path: "call-list",
                        name: routesMap.base.callList,
                        meta: {
                            title: "外呼坐席管理",
                            role: "/tablelist/callout_seat_setting",
                            hidden: !isDev && !isSaas,
                        },
                        component: () =>
                            import("@/views/pages/base/call-list/index.vue"),
                    },
                    {
                        path: "operate-log",
                        name: routesMap.base.operateLog,
                        meta: {
                            title: "操作日志",
                            role: "/tablelist/work_flow_log/manager_log",
                            hidden: isSaas,
                        },
                        component: () =>
                            import("@/views/pages/base/operate/index.vue"),
                    },
                    {
                        path: "mp-detail",
                        name: routesMap.base.mp.listDetail,
                        meta: {
                            title: "客户端详情",
                            parentMenuName: routesMap.base.mp.list,
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/base/mp/detail.vue"),
                    },
                    {
                        path: "business",
                        name: routesMap.base.mp.business,
                        meta: {
                            title: "业务应用管理",
                            parentMenuName: routesMap.base.mp.list,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/mp/business-app/index.vue"
                            ),
                    },
                    {
                        path: "tools",
                        name: routesMap.base.mp.tools,
                        meta: {
                            title: "常用工具管理",
                            parentMenuName: routesMap.base.mp.list,
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/base/mp/tools/index.vue"),
                    },
                    {
                        path: "group-entrance",
                        name: routesMap.base.mp.groupEntrance,
                        meta: {
                            title: "推荐社群入口管理",
                            parentMenuName: routesMap.base.mp.list,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/mp/group-entrance/index.vue"
                            ),
                    },
                    {
                        path: "advertising-position",
                        name: routesMap.base.mp.advertisingPosition,
                        meta: {
                            title: "广告位管理",
                            parentMenuName: routesMap.base.mp.list,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/mp/advertising-position/index.vue"
                            ),
                    },
                    {
                        path: "mp-role-position",
                        name: routesMap.base.mp.mpRoleManage,
                        meta: {
                            title: "小程序浏览管理",
                            parentMenuName: routesMap.base.mp.list,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/mp/mp-role-manage/index.vue"
                            ),
                    },
                    {
                        path: "sms",
                        name: routesMap.base.sms.list,
                        meta: {
                            title: "短信发送记录",
                            role: "/tablelist/sms_send_history/for_operate",
                        },
                        component: () =>
                            import("@/views/pages/base/sms/index.vue"),
                    },
                    {
                        path: "sms-add",
                        name: routesMap.base.sms.add,
                        meta: {
                            title: "新增短信发送",
                            parentMenuName: routesMap.base.sms.list,
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/base/sms/add.vue"),
                    },
                    {
                        path: "sms-detail",
                        name: routesMap.base.sms.detail,
                        meta: {
                            title: "短信发送详情",
                            parentMenuName: routesMap.base.sms.list,
                            hidden: true,
                        },
                        component: () =>
                            import("@/views/pages/base/sms/detail.vue"),
                    },
                    {
                        path: "/questionnaire-design",
                        name: routesMap.questionnaireDesign.index,
                        meta: {
                            title: "问卷管理",
                            role: "/tablelist/general_questionnaire",
                            hidden: isYD,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/questionnaire-design/index.vue"
                            ),
                    },
                    {
                        path: "/questionnaire-design/detail",
                        name: routesMap.questionnaireDesign.detail,
                        meta: {
                            title: "问卷详情",
                            hidden: true,
                            parentMenuName: routesMap.questionnaireDesign.index,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/questionnaire-design/detail.vue"
                            ),
                    },
                    {
                        path: "register-user-index",
                        name: routesMap.base.registerUser.list,
                        meta: {
                            title: "注册用户管理",
                            role: "/tablelist/system_user_xg",
                            hidden: isSaas,
                        },
                        component: () =>
                            import("@/views/pages/base/register-user/list.vue"),
                    },
                    {
                        path: "register-user-detail",
                        name: routesMap.base.registerUser.detail,
                        meta: {
                            title: "用户详情",
                            parentMenuName: routesMap.base.registerUser.list,
                            hidden: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/register-user/detail.vue"
                            ),
                    },
                    {
                        path: "user-agreement",
                        name: routesMap.base.userAgreement.list,
                        meta: {
                            title: "协议管理",
                            role: "/tablelist/user_agreement_content/user_content_back",
                            hidden: !isDev,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/user-agreement/index.vue"
                            ),
                    },
                    {
                        path: "user-agreement-add",
                        name: routesMap.base.userAgreement.add,
                        meta: {
                            title: "新增用户协议",
                            hidden: true,
                            parentMenuName: routesMap.base.userAgreement.list,
                        },
                        component: () =>
                            import("@/views/pages/base/user-agreement/add.vue"),
                    },
                ],
            },

            {
                path: "equipment",
                redirect: "equipment/automatic",
                name: routesMap.base.equipment,
                meta: {
                    title: "设备管理",
                    // svgIcon: require("@/assets/icon/menu/base.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "automatic",
                        name: routesMap.base.automatic,
                        meta: {
                            title: "终端信息",
                            role: "/redirect/xg_project/automatic",
                        },
                        component: () =>
                            import("@/views/pages/base/automatic/index.vue"),
                    },
                    {
                        path: "equipmentManagement",
                        name: routesMap.base.equipmentManagement,
                        meta: {
                            title: "设备管理",
                            role: "/redirect/xg_project/equipmentManagement",
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/equipmentManagement/index.vue"
                            ),
                    },
                    {
                        path: "statisticalMnalysis",
                        name: routesMap.base.statisticalMnalysis,
                        meta: {
                            title: "统计分析",
                            role: "/redirect/xg_project/statisticalMnalysis",
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/statisticalMnalysis/index.vue"
                            ),
                    },
                ],
            },
            // 政务微信管理
            {
                path: "/wx",
                redirect: "wx/group",
                name: routesMap.wx.root,
                meta: {
                    title: "政务微信管理",
                    // svgIcon: require("@/assets/icon/menu/wx.svg"),
                    hidden: isSaas,
                },
                component: RouteView,
                children: [
                    {
                        path: "statistics",
                        name: routesMap.wx.statistics,
                        meta: {
                            title: "政务微信运营数据统计",
                            role: "/redirect/xg_project/wx_manage1",
                        },
                        component: () =>
                            import("@/views/pages/wx/statistics/index.vue"),
                    },
                    {
                        path: "group-statistics",
                        name: routesMap.wx.groupStatistics,
                        meta: {
                            title: "社群人数统计",
                            role: "wecom_core/client_api/fetch_group_statistics",
                        },
                        component: () =>
                            import(
                                "@/views/pages/wx/group/statistic/index.vue"
                            ),
                    },
                    {
                        path: "statistics-list",
                        name: routesMap.wx.statisticsList,
                        meta: {
                            title: "协管员列表",
                            hidden: true,
                            parentMenuName: routesMap.wx.statistics,
                        },
                        component: () =>
                            import("@/views/pages/wx/statistics/list.vue"),
                    },
                    {
                        path: "wx-friend-list",
                        name: routesMap.wx.wxFriendList,
                        meta: {
                            title: "政务微信好友列表",
                            hidden: true,
                            parentMenuName: routesMap.wx.statistics,
                        },
                        component: () =>
                            import(
                                "@/views/pages/wx/statistics/wx-friend-list.vue"
                            ),
                    },
                    {
                        path: "group",
                        name: routesMap.wx.group,
                        meta: {
                            title: "政务微信群管理",
                            role: "/tablelist/wecom_group_chat/list_for_workbench",
                        },
                        component: () =>
                            import("@/views/pages/wx/group/index.vue"),
                    },
                    {
                        path: "group-detail",
                        name: routesMap.wx.groupDetail,
                        meta: {
                            title: "政务微信群管理详情",
                            hidden: true,
                            parentMenuName: routesMap.wx.group,
                        },
                        component: () =>
                            import("@/views/pages/wx/group/detail.vue"),
                    },
                    {
                        path: "friend",
                        name: routesMap.wx.friend,
                        meta: {
                            title: "居民微信管理",
                            role: "/tablelist/wecom_external_contact/list_for_workbench",
                        },
                        component: () =>
                            import("@/views/pages/wx/friend/index.vue"),
                    },
                    {
                        path: "friend-detail",
                        name: routesMap.wx.friendDetail,
                        meta: {
                            title: "居民微信管理详情",
                            hidden: true,
                            parentMenuName: routesMap.wx.friend,
                        },
                        component: () =>
                            import("@/views/pages/wx/friend/detail.vue"),
                    },
                    {
                        path: "assistant",
                        name: routesMap.wx.assistant,
                        meta: {
                            title: "政务微信内部成员管理",
                            role: "/tablelist/wecom_follow_user/list_for_workbench",
                        },
                        component: () =>
                            import("@/views/pages/wx/assistant/index.vue"),
                    },
                ],
            },
            {
                path: "/messageNotify",
                redirect: "messageNotify/message",
                name: routesMap.messageNotify.root,
                meta: {
                    title: "消息通知管理",
                    showOneChildren: true,
                    role: "/tablelist/serve_task/content_manager",
                    hidden: isYD,
                },
                component: RouteView,
                children: [
                    {
                        path: "message",
                        name: routesMap.messageNotify.message,
                        meta: {
                            title: "消息通知管理",
                            parentMenuName: routesMap.messageNotify.root,
                            role: "/tablelist/serve_task/content_manager",
                            // svgIcon: require("@/assets/icon/menu/message.svg"),
                        },
                        component: () =>
                            import(
                                "@/views/pages/group-service/task-manage-4-message/index.vue"
                            ),
                    },
                    {
                        path: "detail",
                        name: routesMap.messageNotify.detail,
                        meta: {
                            title: "消息通知",
                            parentMenuName: routesMap.messageNotify.root,
                        },
                        component: () =>
                            import(
                                "@/views/pages/group-service/task-manage-4-message/detail.vue"
                            ),
                    },
                ],
            },
            {
                path: "/blacklist",
                name: routesMap.blacklist.root,
                redirect: "/blacklist/index",
                meta: {
                    title: "平台黑名单",
                    showOneChildren: true,
                    // svgIcon: require("@/assets/icon/menu/labourManage.svg"),
                    hidden: [
                        EnvProject.荆州项目,
                        EnvProject.saas项目,
                        EnvProject.黄州项目,
                    ].includes(config.envProject),
                    role: "/tablelist/black_list/manage",
                },
                component: RouteView,
                children: [
                    {
                        path: "index",
                        name: routesMap.blacklist.index,
                        meta: {
                            title: "平台黑名单",
                            // single: true,
                            // svgIcon: require("@/assets/icon/menu/labourManage.svg"),
                        },
                        component: () =>
                            import("@/views/pages/blacklist/index.vue"),
                    },
                ],
            },
            {
                path: "/bi",
                redirect: "bi/setting",
                name: routesMap.biSetting.root,
                meta: {
                    title: "数据大屏管理",
                    showOneChildren: true,
                    role: "/redirect/xg_project/data_screen_bi",
                },
                component: RouteView,
                children: [
                    {
                        path: "setting",
                        name: routesMap.biSetting.setting,
                        meta: {
                            title: "数据大屏管理",
                            parentMenuName: routesMap.biSetting.root,
                            role: "/redirect/xg_project/data_screen_bi",
                            targetPath: `${
                                process.env.VUE_APP_BIGSCREEN_BI_PATH ||
                                "/bigScreen"
                            }/#/`,
                            newPage: true,
                        },
                        component: () =>
                            import(
                                "@/views/pages/base/big-screen-bi/index.vue"
                            ),
                    },
                ],
            },
            // 设备管理
            {
                path: "/device",
                redirect: "device/list",
                name: routesMap.device.root,
                meta: {
                    title: "设备管理",
                    hidden: !isSaas,
                    // svgIcon: require("@/assets/icon/menu/recruit-company.svg"),
                },
                component: RouteView,
                children: [
                    {
                        path: "/list",
                        name: routesMap.device.list,
                        meta: {
                            title: "设备列表",
                            role: "/tablelist/device_base_info/manager_back",
                        },
                        component: () =>
                            import("@/views/pages/device/index.vue"),
                    },
                    {
                        path: "/brand",
                        name: routesMap.device.brand,
                        meta: {
                            title: "品牌管理",
                            role: "/tablelist/device_brand/manager_back",
                        },
                        component: () =>
                            import("@/views/pages/device/brand.vue"),
                    },
                ],
            },
            // 孝感市智慧就业监测大屏入口
            {
                path: "smart-employment-monitor",
                name: routesMap.base.smartEmploymentMonitor,
                meta: {
                    title: "智慧就业监测大屏入口",
                    hidden: ![EnvProject.孝感项目].includes(config.envProject),
                    role: "/redirect/xg_project/data_show",
                    newPage: true,
                    targetPath:
                        `${process.env.BASE_URL}/big-screen/smart-employment-monitor/index`.replace(
                            "//",
                            "/"
                        ),
                },
            },
        ],
    },
]
