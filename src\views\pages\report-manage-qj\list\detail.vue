<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button
                    type="primary"
                    plain
                    @click="toEdit"
                    v-if="showEditBtn"
                >
                    编辑
                </el-button>
                <el-button
                    type="primary"
                    plain
                    @click="toAddTitle"
                    v-if="showAddTitleBtn"
                >
                    新建标题
                </el-button>
                <el-button type="primary" plain @click="preview">
                    预览
                </el-button>
                <el-button
                    type="primary"
                    plain
                    v-if="showDeleteBtn"
                    @click="deleteDetail"
                >
                    删除
                </el-button>
                <el-button
                    type="primary"
                    v-if="showPublishBtn"
                    @click="publish('1')"
                >
                    发布
                </el-button>
                <el-button
                    type="primary"
                    v-if="showCancelPublishBtn"
                    @click="publish('0')"
                >
                    取消发布
                </el-button>
            </div>
        </div>
        <div class="detail-top-box">
            <detail-row-col :list="items" class="u-p-x-20" labelStyle="120px"> </detail-row-col>
        </div>
        <div v-if="row">
            <DetailTable ref="detailTable" :groupId="row.group_id" :reportId="reportId" />
        </div>
        <AddPop v-model="showAddPop" :id="reportId" />
        <PreviewPop v-model="showPreviewPop" :id="reportId" />
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        closeCurrentTap,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import DetailTable from "./components/detail-table.vue"
    import { pageLoading } from "@/views/controller"
    import AddPop from "../../report-manage/list/components/add-pop.vue"
    import PreviewPop from "../creator/components/preview-pop.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { getShowBtn4Page } from "../../collect-task-manage/components/build-table"
    import { MessageBox } from "element-ui"
    import { DetailRow } from "../../report-manage/list/index"

    @Component({
        name: routesMap.reportManage.list.detail,
        components: { DetailTable, AddPop, PreviewPop, DetailRowCol },
    })
    export default class ReportManageListDetail extends Vue {
        private breadcrumbs: BreadcrumbItem[] = []

        private showList = false

        private items: ColItem[] = []

        refreshConfig = {
            fun: this.init,
            name: routesMap.reportManage.list.detail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: `创建报表`,
                    to: {
                        name: routesMap.reportManage.list.index,
                    },
                },
                {
                    label: "模板详情",
                    to: {
                        name: routesMap.reportManage.list.detail,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.reportManage.list.detail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private reportId = ""

        private showAddPop = false
        private showPreviewPop = false

        private showEditBtn = false
        private showAddTitleBtn = false
        private showDeleteBtn = false
        private showPublishBtn = false
        private showCancelPublishBtn = false

        private row: DetailRow | null = null

        created() {
            this.init()
        }

        private init() {
            this.row = null
            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_group_ref")
                    .detail(this.$route.query.id as string, "for_operate_two_dimensional")
                    .query()
                    .then((res) => {
                        this.showEditBtn = getShowBtn4Page(res, "update")
                        this.showAddTitleBtn = getShowBtn4Page(res, "add_indicator")
                        this.showDeleteBtn = getShowBtn4Page(res, "delete")
                        this.showPublishBtn = getShowBtn4Page(
                            res,
                            "change_status_1"
                        )
                        this.showCancelPublishBtn = getShowBtn4Page(
                            res,
                            "change_status_0"
                        )

                        this.row = sdk.buildRow(res.row, {
                            group_id: "",
                        })
                        this.reportId = this.row?.id || ""
                        this.items = res.meta.header.field_groups
                            .map((item) => {
                                return {
                                    label: item.label + "：",
                                    value: item.template,
                                    hide: !item.visible,
                                    span: 8,
                                }
                            })
                            .filter((i) => i) as ColItem[]
                    })
            })
        }

        private toEdit() {
            this.showAddPop = true
        }

        private toAddTitle() {
            ;(this.$refs.detailTable as any).toAdd()
        }

        private preview() {
            this.showPreviewPop = true
        }

        private refreshList() {
            ;(this.$refs.detailTable as any).refresh()
        }

        private deleteDetail() {
            MessageBox.confirm(`确认删除？`, "删除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_indicator_group_ref")
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.reportId }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("删除成功")
                            closeCurrentTap()
                            this.callRefresh(routesMap.reportManage.list.index)
                        })
                })
            })
        }

        private publish(status: "1" | "0") {
            const label = status === "1" ? "发布" : "取消发布"
            MessageBox.confirm(`确认${label}？`, `${label}`).then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model("xg_indicator_group_ref")
                        .action(`change_status_${status}`)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.reportId }],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("操作成功")
                            this.callRefresh(routesMap.reportManage.list.detail)
                        })
                })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
    }
</style>
