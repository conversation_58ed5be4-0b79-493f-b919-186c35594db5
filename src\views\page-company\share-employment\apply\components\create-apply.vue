<template>
    <div class="check-list-container d-flex flex-column" v-loading="loading">
        <div class="core-ui-custom-header">
            <div class="d-flex w-100 justify-content-between">
                <div class="title">{{ titleText }}</div>
            </div>
        </div>
        <div class="content d-flex justify-content-center">
            <div class="w-610">
                <form-builder
                    ref="formBuilder"
                    labelWidth="150px"
                ></form-builder>
                <div class="u-flex u-m-t-20 u-row-center">
                    <el-button type="primary" plain @click="cancel">
                        取消
                    </el-button>
                    <el-button type="primary" @click="confirm" class="u-m-l-40">
                        保存
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import {
        BuildFormConfig,
        buildFormSections,
        FileType,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import { Component, Mixins } from "vue-property-decorator"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { pageLoading } from "@/views/controller"
    import { sdk } from "@/service"
    import { routesMap } from "@/router/direction"
    import { closeCurrentTap } from "@/views/pages/single-page/components/tags-view"
    import { userService } from "@/service/service-user"
    import { rules } from "@/core-ui/component/form/rule"
    import { expiateTimePickerOptions } from "@/views/pages/recruit/job/components/detail"

    @Component({
        name: routesMap.company.shareEmployment.createApply,
        components: { FormBuilder },
    })
    export default class DataComparisonManageAdd extends Mixins(FormController) {
        private createPositionFormSections: BuildFormConfig = {
            forms: [],
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.company.shareEmployment.createApply,
        }

        private id = ""
        private isCreate = false
        private titleText = "创建申请"
        private loading = false

        mounted() {
            this.init()
        }

        private init() {
            this.loading = true
            this.id = ""
            if (this.$route.query.id) {
                this.id = this.$route.query.id as string
            }
            this.titleText = this.id ? "修改申请" : "创建申请"
            if (this.id) {
                this.isCreate = false
            } else {
                this.isCreate = true
            }
            let select_list: any = []
            select_list = this.id ? [{ v: 0, id: +this.id }] : []
            this.createPositionFormSections = {
                forms: [
                    {
                        label: "类型",
                        type: FormType.Select,
                        prop: "type",
                        col: {
                            span: 12,
                        },
                    },
                    {
                        label: "涉及人数",
                        type: FormType.Text,
                        prop: "person_num",
                        option: {
                            append: "人",
                            type: "number",
                        },
                        col: {
                            span: 12,
                        },
                    },
                    {
                        label: "所在地",
                        type: FormType.Cascader,
                        prop: "region",
                        option: { elProps: { checkStrictly: true } },
                        col: {
                            span: 12,
                        },
                        required: true,
                    },
                    // {
                    //     label: "详细地址",
                    //     type: FormType.Text,
                    //     prop: "address",
                    //     col: {
                    //         span: 12,
                    //     },
                    // },
                    {
                        labelWidth: "10px",
                        type: FormType.Text,
                        prop: "address",
                        col: {
                            span: 12,
                        },
                    },
                    {
                        label: "联系人",
                        type: FormType.Text,
                        prop: "contact_person",
                        col: {
                            span: 12,
                        },
                    },
                    {
                        label: "联系方式",
                        type: FormType.Text,
                        prop: "contact_number",
                        col: {
                            span: 12,
                        },
                        rules: rules.mobile,
                    },
                    {
                        label: "有效期",
                        prop: "expired_date",
                        type: FormType.DatePicker,
                        option: {
                            type: "date",
                            pickerOptions: expiateTimePickerOptions,
                        },
                        required: true,
                    },
                    {
                        label: "描述",
                        type: FormType.Text,
                        option: {
                            type: "textarea",
                            rows: 7,
                            placeholder: "请录入工种和岗位说明",
                        },
                        prop: "apply_description",
                    },
                    {
                        label: "上传图片",
                        type: FormType.MyUpload,
                        prop: "images",
                        option: {
                            fileType: [FileType.Image],
                            listType: "picture-card",
                            placeholder:
                                "可上传工作环境、岗位展示，简历等；支持上传jpg、png等图片格式,最多支持三张",
                            limit: 3,
                            limitSize: 3072,
                        },
                    },
                ],
                sdkModel: "share_employee_apply",
                sdkAction: this.isCreate ? "addNewApply" : "edit",
                select_list: select_list,
                needSourceData: true,
            }
            buildFormSections(this.createPositionFormSections).then((r) => {
                this.buildFormFull(r)
                this.loading = false
            })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model("share_employee_apply")
                    .action(this.isCreate ? "addNewApply" : "edit")
                    .updateInitialParams({
                        selected_list: this.id ? [{ v: 0, id: +this.id }] : [],
                    })
                    .addInputs_parameter({
                        ...data,
                        agent_id: userService.getCurAgent()?.data.id,
                    })
                    .execute()
                    .then((r) => {
                        this.$message.success(
                            this.isCreate ? "新增成功！" : "修改成功！"
                        )
                        closeCurrentTap({
                            name: routesMap.company.shareEmployment.applyDetail,
                            query: {
                                id: r.id,
                            },
                        })
                    })
                    .finally(() => {
                        // this.close()
                    })
            })
        }

        private cancel() {
            closeCurrentTap()
        }

        private save() {
            this.$message.success("保存成功")
            closeCurrentTap()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .check-list-container {
        height: 90vh;
    }
    .content {
        background-color: #fff;
        padding: 30px;
        flex: 1;
    }

    .w-610 {
        width: 800px;
    }

    ::v-deep .el-form-item__label {
        text-align: right !important;
    }
    ::v-deep .limit-3 {
        div.el-upload.el-upload--picture-card {
            display: none;
        }
    }
</style>
