<template>
    <div class="detail-container" v-if="row">
        <div class="content">
            <div class="title u-flex">
                <span>基本信息</span>
                <!-- <div class="audit">{{ row.audit_status_label }}</div>
                <div class="status">{{ row.status_label }}</div>
                <div class="tag">{{ row.type_label }}</div> -->
            </div>
            <div class="u-p-20">
                <detail-row-col
                    :list="list"
                    :labelStyle="labelStyle"
                ></detail-row-col>
            </div>
        </div>
        <div class="content">
            <div class="title">图片信息</div>
            <div class="u-p-20 u-p-b-40">
                <detail-row-col
                    :list="list3"
                    :labelStyle="labelStyle"
                ></detail-row-col>
            </div>
        </div>
        <div class="content">
            <div class="title">联系信息</div>
            <div class="u-p-20 u-p-b-40">
                <detail-row-col
                    :list="list2"
                    :labelStyle="labelStyle"
                ></detail-row-col>
            </div>
        </div>
        <div class="content" v-if="row.type !== 1">
            <div class="title">场地信息</div>
            <div class="u-p-20 u-p-b-40">
                <detail-row-col
                    :list="list4"
                    :labelStyle="labelStyle"
                ></detail-row-col>
            </div>
        </div>
        <!-- <div class="content u-p-b-24" v-if="showList">
            <div class="title">操作记录</div>
            <slot></slot>
        </div> -->
    </div>
</template>

<script lang='ts'>
    import { config, EnvProject } from "@/config"
    import { formatDate } from "@/core-ui/helpers/tools"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { getAddress } from "@/utils"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { pageLoading } from "@/views/controller"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { Row } from ".."
    import LivePageUrl from "./live-page-url.vue"
    const isJz = [EnvProject.荆州项目].includes(config.envProject)
    @Component({ components: { DetailRowCol, LivePageUrl } })
    export default class DetailView extends Vue {
        @Prop()
        private row!: Row

        @Prop({
            default: false,
        })
        private showList!: boolean

        private labelStyle = {
            fontSize: "14px",
            minWidth: "112px",
            marginRight: "10px",
            lineHeight: "34px",
            color: "#555",
            flex: "none",
        }

        private get list(): ColItem[] {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "招聘会名称：",
                    value: this.row.title,
                },
                {
                    label: "招聘会类型：",
                    value: this.row.type_label,
                },
                {
                    label: "主题类型：",
                    value: [
                        this.row.theme_type_label,
                        this.row.theme_type2_label,
                        this.row.theme_type_label,
                    ]
                        .filter(Boolean)
                        .join("-"),
                },
                {
                    label: "招聘会主题：",
                    value: this.row.theme,
                },
                {
                    label: "专场类型：",
                    value:
                        this.row.activity_type_label +
                        `${
                            this.row.activity_type_remark
                                ? `-${this.row.activity_type_remark}`
                                : ""
                        }`,
                    hide: !isJz,
                },
                {
                    label: "是否夜市招聘会：",
                    value: this.row.is_evening_label,
                },
                {
                    label: "所属区域：",
                    value: this.row.place_area_remark3,
                },
                {
                    label: "管理区域：",
                    value: getAddress(this.row, [
                        "mgt_province_region_name",
                        "mgt_city_region_name",
                        "mgt_area_region_name",
                    ]),
                    hide: config.envProject === EnvProject.黄州项目,
                },
                {
                    label: "召开时间：",
                    value: `${this.row.start_time_label} 至 ${this.row.end_time_label}`,
                },
                {
                    label: "申请截止时间：",
                    value: formatDate(
                        this.row.apply_time_label,
                        "yyyy-MM-dd HH:mm"
                    ),
                },
                {
                    label: "招聘会规模：",
                    value: this.row.job_fair_size_label,
                },
                {
                    label: "主办单位：",
                    value: this.row.organizer_label,
                },
                {
                    label: "承办单位：",
                    value: this.row.hosted_by_label,
                },
                {
                    label: "协办单位：",
                    value: this.row.co_organizer_label,
                },
                {
                    label: "直播信息：",
                    vNode: h(LivePageUrl, {
                        key: this.row.live_page_url + "" + this.row.id,
                        props: {
                            live_page_url: this.row.live_page_url,
                            jobFairId: this.row.id,
                        },
                    }) as ColItem["vNode"],
                    hide: !isJz,
                },
                {
                    label: "招聘会介绍：",
                    span: 24,
                    value: this.row.description,
                },
                {
                    label: "审核状态：",
                    value: this.row.audit_status_label,
                },
                {
                    label: "发布状态：",
                    value: this.row.status_label,
                },
                {
                    label: "审核信息：",
                    value: this.row.audit_memo,
                },
            ].filter((e) => !e.hide)
        }

        private get list2(): ColItem[] {
            if (!this.row) return []
            const h = this.$createElement
            return [
                {
                    label: "联系人：",
                    value: this.row.contact_person_label,
                },
                {
                    label: "联系电话：",
                    // value: this.row.contact_mobile_label,
                    vNode: renDesensitizationView(h, {
                        value: this.row.contact_mobile_label,
                    }),
                },
                {
                    label: "办公电话：",
                    value: this.row.contact_office_mobile,
                },
                {
                    label: "电子邮箱：",
                    value: this.row.contact_email,
                },
            ]
        }

        private get list3(): ColItem[] {
            if (!this.row) return []
            const h = this.$createElement
            const download = this.download
            console.log(JSON.parse(JSON.stringify(this.row)))
            let arr: any = [
                {
                    label: "pc端背景：",
                    vNode: h("div", { class: "u-flex u-col-top" }, [
                        this.row.image_pc
                            ? h("el-image", {
                                  class: "img",
                                  props: {
                                      "preview-src-list": [
                                          sdk.buildImage(this.row.image_pc || ""),
                                      ],
                                      fit: "contain",
                                      src: sdk.buildImage(this.row?.image_pc || ""),
                                  },
                              })
                            : h("span", "暂无"),
                    ]),
                    hide: ![EnvProject.荆州项目].includes(config.envProject),
                },
                {
                    label: "移动端背景：",
                    vNode: this.row.image_pc
                        ? h("el-image", {
                              class: "img",
                              props: {
                                  "preview-src-list": [
                                      sdk.buildImage(this.row?.image_mobile || ""),
                                  ],
                                  fit: "contain",
                                  src: sdk.buildImage(this.row?.image_mobile || ""),
                              },
                          })
                        : h("span", "暂无"),
                    hide: [EnvProject.鄂州项目].includes(config.envProject),
                },
            ]

            const createImg = this.createImg
            arr = [
                ...arr,
                {
                    label: "企业签到二维码：",
                    vNode: this.row.agent_sign_qr
                        ? h("div", { class: "u-flex u-col-top" }, [
                              h("el-image", {
                                  class: "img",
                                  props: {
                                      "preview-src-list": [
                                          sdk.buildImage(
                                              this.row?.agent_sign_qr || ""
                                          ),
                                      ],
                                      fit: "contain",
                                      src: sdk.buildImage(
                                          this.row?.agent_sign_qr || ""
                                      ),
                                  },
                              }),
                              h(
                                  "span",
                                  {
                                      class: "primary pointer u-m-l-10",
                                      on: {
                                          click: download(
                                              sdk.buildImage(
                                                  this.row?.agent_sign_qr || ""
                                              )
                                          ),
                                      },
                                  },
                                  "下载"
                              ),
                          ])
                        : h(
                              "div",
                              {
                                  class: "primary pointer",
                                  on: { click: createImg },
                              },
                              "生成"
                          ),
                },
            ]

            if (this.row.sign_qr) {
                arr = [
                    ...arr,
                    {
                        label: "个人签到二维码：",
                        vNode: h("div", { class: "u-flex u-col-top" }, [
                            h("el-image", {
                                class: "img",
                                props: {
                                    "preview-src-list": [
                                        sdk.buildImage(this.row?.sign_qr || ""),
                                    ],
                                    fit: "contain",
                                    src: sdk.buildImage(this.row?.sign_qr || ""),
                                },
                            }),
                            h(
                                "span",
                                {
                                    class: "primary pointer u-m-l-10",
                                    on: {
                                        click: download(
                                            sdk.buildImage(this.row?.sign_qr || "")
                                        ),
                                    },
                                },
                                "下载"
                            ),
                        ]),
                    },
                ]
            }
            return arr
        }

        private get list4(): ColItem[] {
            const h = this.$createElement
            return [
                {
                    label: "招聘会地址：",
                    value: [this.row.cal_address_detail].filter(Boolean).join(""),
                },
                {
                    label: "展位已满时是否可申请：",
                    value: this.row.is_booth_apply_label,
                },
                // {
                //     label: "是否为固定场地：",
                //     value: this.row.activity_area_id > 0 ? "是" : "否",
                // },
                this.row.activity_area_id > 0
                    ? false
                    : {
                          label: "可容纳企业数：",
                          value: this.row.company_count || "0",
                      },
            ].filter(Boolean) as ColItem[]
        }

        private createImg() {
            pageLoading(() => {
                return sdk.core
                    .model("job_fair")
                    .action("update_qr_code")
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: this.row.id }],
                    })
                    .execute()
                    .then(() => {
                        this.callRefresh(routesMap.recruit.jobFairDetail)
                    })
            })
        }

        private download(v: string) {
            return () => window.open(v, "_blank")
        }
    }
</script>

<style lang='scss' scoped>
    @import "~@/css/variables.less";

    .detail-container {
        background: #fff;
        .content {
            padding: 20px;
            padding-bottom: 0;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
                div {
                    border-radius: 2px;
                    padding: 0 2px;
                    font-weight: 400;
                }
                .audit {
                    background: #65d2a3;
                    color: #fff;
                    line-height: 16px;
                    font-size: 12px;
                    margin-left: 10px;
                }
                .status {
                    background: #d7f8ea;
                    color: #22bd7a;
                    line-height: 12px;
                    font-size: 12px;
                    margin-left: 10px;
                }
                .tag {
                    background: #e3ecff;
                    color: #4273d9;
                    font-size: 12px;
                    line-height: 12px;
                    margin-left: 10px;
                }
            }
            .label {
                width: 98px;
                margin-right: 10px;
                color: #555;
                flex: none;
                line-height: 28px;
            }
            .value {
                line-height: 28px;
            }
            .info {
                line-height: 34px;
                color: #333;
                font-size: 14px;
            }
        }
    }
    .img {
        width: 80px;
        height: 80px;
    }
</style>
