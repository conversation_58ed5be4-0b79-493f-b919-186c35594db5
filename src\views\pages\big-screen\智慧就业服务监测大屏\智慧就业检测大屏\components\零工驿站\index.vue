<template>
    <div class="content">
        <Header title="零工驿站"></Header>
        <Box :list="list"></Box>
        <div class="title">驿站联盟分析</div>
        <Items :items="items"></Items>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import Header from "../../common/header.vue"
    import Items from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/items.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import { ChartQueryResultItem } from "@/views/pages/big-screen/model"
    import Box from "./box.vue"

    @Component({ components: { Header, Items, Box } })
    export default class Template extends BaseItem {
        private model = [
            {
                label: "使用共享调度驿站数",
                value: 0,
            },
            {
                label: "发起共享岗位总数",
                value: 0,
            },
            {
                label: "发起共享零工总数",
                value: 0,
                remoteKey: "发起零工总数",
            },
            {
                label: "确认完工总数",
                value: 0,
                remoteKey: "确认完工总数-驿站联盟",
            },
        ]

        private items: any[] = this.model

        private list: ChartQueryResultItem[] = []

        protected refresh() {
            this.query<ChartQueryResultItem[]>(
                `lgyz_count`,
                "dashboard_xg_recruit_service_data"
            ).then((r) => {
                this.list = r
                const t = this.list.find((i) => i.key_name === "驿站总数")
                t && (t.key_value = 41)
                this.items = this.getItems(this.list)
            })
        }

        private getItems(r: ChartQueryResultItem[]) {
            return this.formatData(this.model, r)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 613px;
        height: 321px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;
        padding-bottom: 24px;
        .title {
            font-weight: 500;
            font-size: 18px;
            color: #8fbcff;
            line-height: 21px;
            margin: 24px 0 21px;
            padding-left: 16px;
        }
    }
</style>
