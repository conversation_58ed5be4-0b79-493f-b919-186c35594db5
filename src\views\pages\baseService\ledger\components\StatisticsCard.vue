<template>
    <div
        class="card round-3 bg-white"
        style="padding: 30px 40px; gap: 60px; margin-top: 12px"
    >
        <div class="flex w-100" style="gap: 60px">
            <div class="grid grid-cols-3 gap-20 flex-1">
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #f4f7ff"
                >
                    <div class="val text-24 font-600" style="color: #5b86ee">
                        {{ stats.list1.community_total || "--" }}
                    </div>
                    <div class="label text-14 text-777E8E">社区（村）数量</div>
                </div>
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #fef5d780"
                >
                    <div class="val text-24 font-600" style="color: #e6b81e">
                        {{ stats.list1.grid_total }}
                    </div>
                    <div class="label text-14 text-777E8E">人社专员数量</div>
                </div>
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #e1f4f780"
                >
                    <div class="val text-24 font-600" style="color: #57bdec">
                        {{ stats.list1.equipment_total }}
                    </div>
                    <div class="label text-14 text-777E8E">设备数量</div>
                </div>
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #ffead980"
                >
                    <div class="val text-24 font-600" style="color: #f3a162">
                        {{ stats.list1.shequn_total }}
                    </div>
                    <div class="label text-14 text-777E8E">社群数量</div>
                </div>
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #f4f7ff"
                >
                    <div class="val text-24 font-600" style="color: #5b86ee">
                        {{ stats.list1.shequnjumin_total }}
                    </div>
                    <div class="label text-14 text-777E8E">社群居民数量</div>
                </div>
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #e6f3c980"
                >
                    <div class="val text-24 font-600" style="color: #a1c452">
                        {{ stats.list1.labor_force_total }}
                    </div>
                    <div class="label text-14 text-777E8E">劳动力数量</div>
                </div>
            </div>
            <div
                style="width: 1px; background-color: rgba(102, 102, 102, 0.3)"
            ></div>
            <div class="grid grid-cols-3 grid-rows-2 gap-20 flex-1">
                <!-- 政策推广次数 -->
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #e6f3c980"
                >
                    <div class="val text-24 font-600" style="color: #a1c452">
                        {{ stats.list2.policy_form }}
                    </div>
                    <div class="label text-14 text-777E8E">政策推广次数</div>
                </div>
                <!-- 岗位推广次数 -->
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #fef5d780"
                >
                    <div class="val text-24 font-600" style="color: #e6b81e">
                        {{ stats.list2.xg_company_position }}
                    </div>
                    <div class="label text-14 text-777E8E">岗位推广次数</div>
                </div>
                <!-- 劳动力更新数量 -->
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4 row-span-2"
                    style="padding-block: 12px; background-color: #f4f7ff"
                >
                    <div class="val text-24 font-600" style="color: #5b86ee">
                        {{ stats.list2.update_labor_force_total }}
                    </div>
                    <div class="label text-14 text-777E8E">劳动力更新数量</div>
                </div>
                <!-- 招聘会推广次数 -->
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #ffead980"
                >
                    <div class="val text-24 font-600" style="color: #f3a162">
                        {{ stats.list2.job_fair }}
                    </div>
                    <div class="label text-14 text-777E8E">招聘会推广次数</div>
                </div>
                <!-- 培训推广次数 -->
                <div
                    class="flex flex-col items-center justify-center round-3 gap-4"
                    style="padding-block: 12px; background-color: #e1f4f780"
                >
                    <div class="val text-24 font-600" style="color: #57bdec">
                        {{ stats.list2.hb_training }}
                    </div>
                    <div class="label text-14 text-777E8E">培训推广次数</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { sdk } from "@/service"
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"

    @Component({ components: {} })
    export default class StatisticsCard extends Vue {
        @Prop({ type: String, default: "" })
        private region_code!: string

        private stats = {
            list1: {
                equipment_total: "--",
                community_total: "--",
                grid_total: "--",
                labor_force_total: "--",
                shequn_total: "--",
                shequnjumin_total: "--",
            },
            list2: {
                hb_training: "--",
                job_fair: "--",
                policy_form: "--",
                update_labor_force_total: "--",
                xg_company_position: "--",
            },
        }

        @Watch("region_code")
        onRegionCodeChange() {
            this.initStats()
        }

        private initStats() {
            if (!this.region_code) return
            sdk.core
                .domainService(
                    "xg_project",
                    "g_service_register",
                    "service_dashboard_count"
                )
                .post<{
                    list1: {
                        community_total: number
                        equipment_total: number
                        grid_total: number
                        labor_force_total: number
                        shequn_total: number
                        shequnjumin_total: number
                    }
                    list2: {
                        hb_training: number
                        job_fair: number
                        policy_form: number
                        update_labor_force_total: number
                        xg_company_position: number
                    }
                }>({ region_code: this.region_code })
                .then((res: any) => {
                    this.stats = res
                })
        }

        mounted() {
            this.initStats()
        }
    }
</script>

<style lang="less" scoped>
    @import "../style/tail.less";
</style>
