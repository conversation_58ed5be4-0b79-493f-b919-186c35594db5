<template>
    <div
        class="core-ui-table-container container"
        :key="refreshQueryParams"
        v-if="row"
    >
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    @click="showFeedbackPop = true"
                    v-if="!isPublic"
                >
                    企业反馈
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    @click="showContactPop = true"
                >
                    立即联系
                </el-button>

                <template v-if="pass">
                    <!-- <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="showInterviewPop = true"
                        v-if="row.status < status.待面试 && !isDuodao"
                    >
                        约面试
                    </el-button> -->
                    <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="showStatusPop = true"
                        v-if="isShowFeedback"
                    >
                        反馈结果
                    </el-button>
                </template>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    @click="showQualificationPop = true"
                    v-if="row.position_type === positionType.公益岗位"
                >
                    审核资格
                </el-button>
            </div>
        </div>
        <div class="detail-container bg-white">
            <detail-view :row="row" :isPublic="isPublic"></detail-view>
        </div>
        <template>
            <interview-pop
                v-model="showInterviewPop"
                :id="row.id"
                @refresh="init"
            ></interview-pop>
            <contact-pop
                v-model="showContactPop"
                :mobile="row.mobile"
            ></contact-pop>
            <result-pop v-if="isPublic" v-model="showResultPop"></result-pop>
            <status-pop
                v-model="showStatusPop"
                :id="row.id"
                :isBatch="false"
                @refresh="init"
            ></status-pop>
            <qualification-pop
                v-model="showQualificationPop"
                :id="row.id"
                @refresh="init"
            ></qualification-pop>
            <FeedbackPop
                v-model="showFeedbackPop"
                :id="row.id"
                @refresh="init"
            />
        </template>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"
    import { PositionType, predict, Status } from "."
    import ContactPop from "./components/contact-pop.vue"
    import DetailView from "./components/detail-view.vue"
    import InterviewPop from "./components/interview-pop.vue"
    import QualificationPop from "./components/qualification-pop.vue"
    import ResultPop from "./components/result-pop.vue"
    import StatusPop from "./components/status-pop.vue"
    import FeedbackPop from "./components/feedback-pop.vue"
    import { config, EnvProject } from "@/config"

    @Component({
        name: routesMap.recruit.jobDeliverDetail,
        components: {
            DetailView,
            InterviewPop,
            ContactPop,
            ResultPop,
            StatusPop,
            QualificationPop,
            FeedbackPop,
        },
    })
    export default class JobDeliverDetail extends Vue {
        private routesMap = routesMap
        private row: any | null = null
        private id = ""
        private showInterviewPop = false
        private showContactPop = false
        private showResultPop = false
        private showStatusPop = false
        private showQualificationPop = false
        private showFeedbackPop = false

        private positionType = PositionType
        private status = Status

        private isHz = [EnvProject.黄州项目].includes(config.envProject)
        private isYD = [EnvProject.宜都项目].includes(config.envProject)

        private get pass() {
            return this.isPublic ? this.row?.qualification_audit_status === 1 : true
        }

        private get isPublic() {
            return this.row?.position_type === 2
        }

        private get isShowFeedback() {
            return this.row?.status < Status.已入职
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.jobDeliverDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const last = this.from
                ? getCacheBreadcrumbsByRoutePath(this.from)
                : [
                      {
                          label: "岗位投递列表",
                          to: routesMap.recruit.jobDeliver,
                      },
                  ]
            const isPublic = (this.from || "").includes("public")
            const d: BreadcrumbItem[] = [
                ...last,
                {
                    label: "岗位投递详情",
                    to: {
                        name: isPublic
                            ? routesMap.recruit.publicJobDeliverDetail
                            : routesMap.recruit.jobDeliverDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.jobDeliverDetail,
                breadcrumb: d,
            })
            updateTagItem({
                name: routesMap.recruit.publicJobDeliverDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string | undefined
        }

        mounted() {
            this.init()
        }

        private init() {
            this.id = this.$route.query.id as string
            this.row = null
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("xg_candidate_order")
                    .detail(this.id, "manage")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                        console.log(JSON.parse(JSON.stringify(this.row)))
                        if (this.row.status === Status.待处理) {
                            this.showResultPop = true
                        }
                    })
            })
        }

        private toEdit() {}
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .back-btn {
        min-width: 120px;
        height: 40px;
        padding: 0 20px !important;
    }
</style>
