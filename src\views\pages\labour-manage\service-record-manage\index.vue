<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :showExpand="false"
        >
            <div slot="title" class="d-flex-item-center bold">
                我的服务记录处理
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <div class="right-btn-box d-flex justify-content-end u-m-b-20">
                    <el-button
                        class="custom-btn batch-btn"
                        @click="batchDisposePop"
                    >
                        批量处理
                    </el-button>
                    <el-button
                        class="custom-btn batch-btn"
                        @click="batchTurnPop"
                    >
                        批量转给他人
                    </el-button>
                    <el-button class="custom-btn batch-btn" @click="exportData">
                        导出
                    </el-button>
                </div>
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <div class="handler-btn" @click="toDetail(scope.row)">
                            详情
                        </div>
                    </div>
                </common-table>
            </div>
        </table-container>
        <batch-dispose-pop
            :selected="selected"
            @refresh="refreshList"
            v-model="showBatchDisposePop"
        ></batch-dispose-pop>
        <batch-turn-pop
            :selected="selected"
            @refresh="refreshList"
            v-model="showBatchTurnPop"
        ></batch-turn-pop>
    </div>
</template>

<script lang="ts">
    import { SelectOption } from "@/core-ui/component/form"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "."
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { routesMap } from "@/router/direction"
    import BatchDisposePop from "./components/batch-dispose-pop.vue"
    import BatchTurnPop from "./components/batch-turn.vue"

    @Component({
        name: routesMap.labourManage.serviceRecordManage,
        components: { TableContainer, CommonTable, BatchDisposePop, BatchTurnPop },
    })
    export default class Detail extends BaseTableController<Row> {
        tableConfig: TableConfig | null = tableConfig()
        selectOption: SelectOption[] = []

        private checkEdIds: Array<number | string> = []
        private selected: { id: number; v: number }[] = []

        private readonly columns: TableColumn[] = columns

        private showBatchDisposePop = false
        private showBatchTurnPop = false

        private batchDisposePop() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量处理的居民信息！")
            }
            this.showBatchDisposePop = true
        }

        private batchTurnPop() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量处理的居民信息！")
            }
            this.showBatchTurnPop = true
        }

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.ids
            this.selected = d.rows.map((item) => {
                return {
                    id: item.id,
                    v: item.v,
                }
            })
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.labourManage.recordDetail,
                query: { id: row.task_serve_record_access_key || row.task_serve_record_id + "" },
            })
        }

        private exportData() {
            this.exportToExcel()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
