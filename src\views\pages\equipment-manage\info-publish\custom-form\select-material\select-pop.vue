<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="从素材库中选择"
        width="840px"
        top="8vh"
    >
        <div class="content">
            <div class="placeholder" v-show="loading" v-loading="true"></div>
            <table-container
                v-if="tableConfig"
                filedWidth="250"
                ref="table"
                v-model="tableConfig"
            >
                <div slot="table" slot-scope="{ data }">
                    <div>
                        <div class="list">
                            <div
                                v-for="(item, index) in data"
                                :key="index"
                                class="item u-m-b-10"
                            >
                                <div
                                    class="item-box u-flex u-row-center u-col-center"
                                    :class="{
                                        selected: selected.find(
                                            (i) => i === item.id
                                        ),
                                    }"
                                    @click="toSelect(item.id)"
                                >
                                    <img
                                        :src="buildUrl(item)"
                                        alt=""
                                        class="img"
                                    />
                                    <img
                                        src="@/assets/selected.png"
                                        alt=""
                                        class="select-icon"
                                        v-show="
                                            selected.find((i) => i === item.id)
                                        "
                                    />
                                    <img
                                        src="@/assets/video-icon.png"
                                        alt=""
                                        class="video-icon"
                                        v-show="item.type === 'video'"
                                    />
                                    <div
                                        class="manner"
                                        v-if="item.type === 'video'"
                                    ></div>
                                </div>
                                <div class="name">{{ item.name || "" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </table-container>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="confirm"
                    class="custom-btn btn u-m-0"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { Action } from "uniplat-sdk"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { tableConfig } from "."
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { cloneDeep } from "lodash"

    @Component({ components: { TableContainer } })
    export default class SelectPop extends Mixins(
        DialogController,
        BaseTableController
    ) {
        private action?: Action

        @Prop({ default: "" })
        private readonly id!: number

        @Prop({ default: "" })
        private readonly groupId!: string

        @Prop({ default: () => [] })
        private select!: string[]

        @Prop({ default: () => {} })
        private extendData!: any

        private tableConfig: any = null

        private selected: string[] = []

        onOpen() {
            // this.init()
            let type = ""
            if (this.extendData?.type) {
                type = this.extendData.type
            }
            this.tableConfig = tableConfig(type)
            if (this.select?.length) {
                this.selected = cloneDeep(this.select)
            }
        }

        onClosing() {}

        private init() {
            this.loading = true
        }

        private confirm(data: any) {
            if (this.selected.length > 5) {
                return this.$message.error("最多选择5个素材")
            }
            this.$emit("selected", this.selected)
            this.close()
        }

        private isSelected(id: string) {
            return this.selected.some((item) => item === id)
        }

        private toSelect(id: string) {
            if (this.isSelected(id)) {
                this.selected = this.selected.filter((d) => d !== id)
            } else {
                this.selected.push(id)
            }
        }

        private buildUrl(item: any) {
            if (item.type === "image") {
                return sdk.buildImage(item.url)
            }
            return require("@/assets/device-icon/video-default-bg.png")
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
        /deep/.table {
            padding: 0;
        }
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
    .list {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px 20px;
        .item {
            height: 119px;

            .item-box {
                height: 119px;
                position: relative;
                border-radius: 4px 4px 4px 4px;
                border: 2px solid #cecece;
                cursor: pointer;
                &.selected {
                    border: 2px solid #3975ff;
                }
            }
            .img {
                width: 100%;
                height: 100%;
                position: absolute;
            }
            .select-icon {
                width: 32px;
                height: 32px;
                position: absolute;
                top: -2px;
                right: -1px;
                z-index: 2;
            }
            .video-icon {
                width: 40px;
                height: 40px;
                position: relative;
                z-index: 2;
            }
            .manner {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                right: 0;
                background: rgba(0, 0, 0, 0.5);
            }
            .name {
                color: #918f8f;
                text-align: center;
                margin-top: 5px;
            }
        }
    }
    // /deep/.el-tabs__nav {
    //     height: 30px;
    //     margin-bottom: 10px;
    // }
    // /deep/.el-tabs__item {
    //     min-width: 80px !important;
    //     height: 28px !important;
    //     line-height: 28px !important;
    //     border-radius: 4px 0px 0px 4px;
    //     font-size: 12px;
    //     margin-right: 0;
    //     background: #ffffff !important;
    //     border-radius: 4px 0px 0px 4px;
    //     border: 1px solid #598bff;
    //     color: #598bff;
    //     &:last-child {
    //         border-radius: 0px 4px 4px 0px;
    //     }
    // }
    // /deep/.el-tabs__item.is-active {
    //     color: #fff;
    //     background: #5782ec !important;
    // }
</style>
