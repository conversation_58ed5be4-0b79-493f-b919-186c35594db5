<template>
    <div>
        <table-container
            filedWidth="250"
            ref="table"
            v-model="tableConfig"
            :displayLeftTree="true"
            class="container"
        >
            <div slot="title">{{ $route.meta.title }}</div>
            <!-- <el-button
                v-role="'model.xg_organization.action.insert'"
                slot="header-right"
                plain
                type="primary"
                @click="add"
            >
                添加机构
            </el-button> -->

            <div slot="table" class="u-p-x-20 u-p-t-20" slot-scope="{ data }">
                <common-table :data="data" :columns="tableConfig.column">
                    <div slot="h" slot-scope="scope">
                        <el-button
                            v-role="'model.xg_organization.action.update'"
                            type="text"
                            @click="viewDetail(scope.row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-role="[
                                'model.xg_organization.action.disable',
                                'model.xg_organization.action.enable',
                            ]"
                            type="text"
                            @click="changeStatus(scope.row)"
                        >
                            {{ actionLabel(scope.row.status) }}
                        </el-button>
                        <el-button
                            v-role="'model.xg_organization.action.delete'"
                            type="text"
                            @click="deleteRow(scope.row)"
                        >
                            删除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <add v-model="showDetail" :rowId="rowId" />
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { MessageBox } from "element-ui"
    import { Component } from "vue-property-decorator"
    import { Row, tableConfig, Status } from "."
    import Add from "./components/add.vue"

    @Component({
        name: routesMap.base.org.list,
        components: { TableContainer, CommonTable, Add },
    })
    export default class Index extends BaseTableController<Row> {
        private tableConfig = tableConfig()

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.base.org.list,
        }

        private actionLabel(status: Status) {
            return status === Status.已启用 ? "停用" : "启用"
        }

        add() {
            this.rowId = 0
            this.showDetail = true
        }

        deleteRow(row: Row) {
            MessageBox.confirm(`请确认删除${row.name}？`, "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        instance.confirmButtonLoading = true
                        sdk.core
                            .model("xg_organization")
                            .action("delete")
                            .updateInitialParams({
                                selected_list: [{ id: row.id, v: 0 }],
                            })
                            .execute()
                            .then(() => {
                                done()
                                this.refreshList()
                            })
                            .finally(() => {
                                instance.confirmButtonLoading = false
                            })
                    } else {
                        done()
                    }
                },
            })
        }

        changeStatus(row: Row) {
            MessageBox.confirm(`请确认${this.actionLabel(row.status)}？`, "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        const actionName =
                            row.status === Status.已启用 ? "disable" : "enable"
                        instance.confirmButtonLoading = true
                        sdk.core
                            .model("xg_organization")
                            .action(actionName)
                            .updateInitialParams({
                                selected_list: [{ id: row.id, v: 0 }],
                            })
                            .execute()
                            .then(() => {
                                done()
                                this.refreshList()
                            })
                            .finally(() => {
                                instance.confirmButtonLoading = false
                            })
                    }
                    done()
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
</style>
