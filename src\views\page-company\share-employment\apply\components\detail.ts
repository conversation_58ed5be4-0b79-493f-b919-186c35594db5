import { formatTime } from "@/utils/tools"
import { map } from "lodash"

export interface Row {
    agent_name: string
    create_time: string
    type: number
    type_label: string
    person_num: number
    region: number
    region_label: string
    address: string
    contact_person: string
    contact_number: string
    apply_description: string
    images: string
    username: string
    update_time: string
    status: number
    status_label: string
    review_comment: string
    id: number
    v: number
    shelf_status: number
    expired_date: string
    expired_date_label: string
    address_detail: string
    address_detail_label: string
}

export const predict = {
    agent_name: "agentRef#agent_name",
    create_time: "label",
    type: "label",
    person_num: "",
    region: "label",
    address: "",
    contact_person: "",
    contact_number: "",
    apply_description: "",
    images: "",
    username: "create_by#systemUser.username",
    update_time: "label",
    status: "label",
    review_comment: "",
    shelf_status: "label",
    expired_date: "label",
    address_detail: "agentRef#address_detail_label",
}

export const applyBaseInfo = [
    { label: "类型", prop: "type_label", span: 8 },
    {
        label: "涉及人数",
        span: 8,
        vNode: (h: Function, value: any, data: any) => {
            return h(
                "span",
                {
                    class: "u-line-1",
                },
                data.person_num + "人"
            )
        },
    },
    {
        label: "所在地",
        span: 8,
        vNode: (h: Function, value: any, data: any) => {
            return h(
                "span",
                {
                    class: "u-line-1",
                },
                data.address_detail_label
            )
        },
    },
    { label: "联系人", prop: "contact_person", span: 8 },
    { label: "联系方式", prop: "contact_number", span: 8 },
    {
        label: "有效期",
        span: 8,
        vNode: (h: Function, value: any, data: any) => {
            return h("span", {}, formatTime.day(data.expired_date) || "--")
        },
    },
    {
        label: "描述",
        prop: "apply_description",
        span: 24,
        vNode: (h: Function, value: any, data: any) => {
            return h(
                "div",
                {},
                des(data).map((e: any) => {
                    return h("div", {}, e)
                })
            )
        },
    },
]

function des(row: any) {
    const des = (row.apply_description || "").split("\n").filter(Boolean)
    return des.length ? des : ["-"]
}

// function getImage(imgs: string) {
//     if (imgs) {
//         if (imgs.includes(",")) {
//             const str = imgs
//             const arr = str.split(",")
//             return arr
//         }
//         return [imgs]
//     }
//     return [imgs]
// }

export const statusBaseInfo = [
    { label: "创建人", prop: "username", span: 24 },
    { label: "创建时间", prop: "create_time_label", span: 24 },
    { label: "最近操作时间", prop: "update_time_label", span: 24 },
    { label: "审核意见", prop: "review_comment", span: 24 },
]

export function handlerList(data: any, list: any[], h = () => {}, row = 1) {
    return list.map((e: any) => {
        return {
            span: 24 / row,
            ...e,
            value: e.vNode ? undefined : e.prop ? data[e.prop] : undefined,
            vNode: e.vNode ? e.vNode(h, data[e.prop], data) : undefined,
        }
    })
}
