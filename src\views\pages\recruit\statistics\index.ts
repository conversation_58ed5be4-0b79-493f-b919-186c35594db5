import { config, EnvProject } from "@/config"
import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { desensitization } from "@/utils/tools"

const isHz =
    config.envProject === EnvProject.黄州项目 ||
    process.env.VUE_APP_ENV === "test"

const isXg = config.envProject === EnvProject.孝感项目

export const filter: TableFilter[] = [
    {
        label: "统计时间",
        prop: "time",
        type: FormType.DatePicker,
        option: {
            type: "daterange",
        },
        col: { span: 12 },
    },
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_region_code",
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
]

export const list = {
    total: [
        { title: "企业发布累计岗位数", prop: "position_create_num" },
        { title: "企业发布累计招聘人数", prop: "position_all_recruit_num" },
        { title: "市场侧招聘平台发布数量", prop: "position_sync_num" },
        { title: "在线岗位数量", prop: "position_online_num" },
        { title: "预计招聘人数", prop: "position_recruit_num" },
        // { title: "就业局人才推荐人数", prop: "talent_recommend_num" },
        // { title: "人资机构推荐人数", prop: "agent_bidding_recommend_num" },
    ],
    miniProgram: [
        { title: "小程序打开次数", prop: "user_login_num" },
        { title: "实名注册用户数", prop: "user_verified_num" },
        { title: "注册用户数量", prop: "user_register_num" },
        { title: "用户查看岗位总数", prop: "user_browse_position_num" },
        { title: "用户意向岗位总数", prop: "user_apply_position_num" },
        { title: "小程序入职人数", prop: "user_offer_num" },
        { title: "跟踪入职人数", prop: "position_entry_num" },
    ].filter((item) => {
        if (item.prop === "position_entry_num") {
            return isHz || isXg
        }
        return true
    }),
    person: [
        {
            title: "累计申请推荐岗位数量",
            prop: "talent_recommend_num",
        },
        {
            title: "申请企业数量",
            prop: "talent_company_num",
        },
        {
            title: "累计推荐人才数量",
            prop: "talent_profile_num",
        },
        {
            title: "企业联系人数",
            prop: "talent_offer_num",
        },
    ],
    grid: [
        {
            title: "累计申请网格推广岗位",
            prop: "grid_recommend_num",
        },
        {
            title: "分享过岗位的网格员",
            prop: "grid_user_num",
        },
        {
            title: "网格员转发次数",
            prop: "grid_share_num",
        },
        {
            title: "用户浏览次数",
            prop: "grid_browse_num",
        },
        {
            title: "报名次数",
            prop: "grid_apply_num",
            subTitle: "岗位网格推广报名列表",
        },
        {
            title: "入职人数",
            prop: "grid_offer_num",
        },
    ],
    group: [
        {
            title: "累计申请社群推广岗位",
            prop: "group_recommend_num",
        },
        {
            title: "共触达用户总数",
            prop: "group_user_num",
        },
        {
            title: "分享后被用户浏览次数",
            prop: "group_browse_num",
        },
        {
            title: "报名次数",
            prop: "group_apply_num",
        },
        {
            title: "入职人数",
            prop: "group_offer_num",
        },
    ],
    cooperation: [
        {
            title: "申请撮合岗位数量",
            prop: "bidding_recommend_num",
        },
        {
            title: "接单机构数量",
            prop: "bidding_company_num",
        },
        {
            title: "已推荐人才机构数量",
            prop: "recommend_profile_num",
        },
        {
            title: "累计推荐人才数量",
            prop: "recommend_profile_num",
        },
    ],
}

export const detailMap: Record<
    string,
    {
        title: string
        data: Record<string, any>
    }
> = {
    grid_apply_num: {
        title: "网格员报名列表",
        data: { created_from: "grid_user" },
    },
    grid_offer_num: {
        title: "网格员入职列表",
        data: { created_from: "grid_user", status: 20 },
    },
    group_apply_num: {
        title: "社群推广报名列表",
        data: { created_from: "socialize_group" },
    },
    group_offer_num: {
        title: "社群推广入职列表",
        data: { created_from: "socialize_group", status: 20 },
    },
}

export function hasDetail(key: string) {
    return !!detailMap[key]
}

const tableFilter: TableFilter[] = [
    {
        label: "统计时间",
        type: FormType.DatePicker,
        option: { type: "daterange" },
        prop: "time",
    },
    {
        label: "网格",
        type: FormType.Cascader,
        prop: "region_code",
        option: { elProps: { checkStrictly: true } },
    },
    {
        label: "网格员姓名",
        type: FormType.Text,
        prop: "name",
    },
]

export const predict = {
    name: "user#info#name",
    mobile: "user#mobile",
    mobile2: "mobile",
    grid: "countryside#alias_label",
    user_apply_num: "label",
    user_browse_num: "label",
    share_num: "label",
    real_name: "",
}
export function tableConfig(): TableConfig {
    return {
        // model: sdk.core.model("grid_user").list("grid_summary_list"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: predict,
        oneTabFilter: true,
        domainService: sdk.getDomainService(
            "recruit_grid_data",
            "back_api",
            "xg_project"
        ),
        handleFilterData(params) {
            if (params.region_code) {
                params.region_code =
                    (params.region_code || "")
                        .split(",")
                        .filter(Boolean)
                        .reverse()[0] || ""
            }
            if (params.time) {
                params.start_time = params.time[0]
                params.end_time = params.time[1]
            }
            return params
        },
    }
}

export const columns: TableColumn[] = [
    {
        label: "网格名称",
        prop: "grid",
        formatter: (row) => {
            return row.region_name || ""
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "姓名",
        prop: "name",
        width: "100px",
        render(h, row) {
            return h("span", {}, row.name || row.real_name)
        },
        showOverflowTip: true,
    },
    {
        label: "网格员电话",
        prop: "mobile",
        width: "120px",
        render(h, row) {
            return h("span", {}, desensitization(row.mobile2 || row.mobile))
        },
        showOverflowTip: true,
    },
    {
        label: "转发次数",
        prop: "share_num",
        formatter(row) {
            return row.share_num || "0"
        },
        showOverflowTip: true,
    },
    {
        label: "转发后被用户浏览次数",
        prop: "browse_num",
        formatter(row) {
            return row.browse_num || "0"
        },
        showOverflowTip: true,
    },
    { label: "报名次数", prop: "apply_num", showOverflowTip: true },
]

const tableFilter2: TableFilter[] = [
    {
        label: "统计时间",
        type: FormType.DatePicker,
        option: {
            type: "daterange",
        },
        prop: "create_time",
    },
]

export function tableConfig2(data: any): TableConfig {
    return {
        model: sdk.core
            .model("xg_candidate_order")
            .list("position_summary_order_list"),
        filter: tableFilter2,
        preFilter: data,
        predict: {
            name: "",
            mobile_encode: "",
            sex: "label",
            education: "label",
            position_name: "position#name",
            create_time: "label",
        },
        defaultPageSize: 10,
    }
}

export const columns2: TableColumn[] = [
    { label: "姓名", prop: "name", showOverflowTip: true },
    { label: "电话", prop: "mobile_encode", showOverflowTip: true },
    { label: "性别", prop: "sex_label", showOverflowTip: true },
    { label: "学历", prop: "education_label", showOverflowTip: true },
    { label: "报名岗位", prop: "position_name", showOverflowTip: true },
    { label: "报名时间", prop: "create_time_label", showOverflowTip: true },
]
