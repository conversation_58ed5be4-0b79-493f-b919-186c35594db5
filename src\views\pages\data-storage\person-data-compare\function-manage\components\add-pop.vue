<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 content">
            <div class="placeholder" v-show="loading" v-loading="true"></div>
            <form-builder
                ref="formBuilder"
                labelWidth="100px"
                v-show="!loading"
            ></form-builder>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="submitValidate"
                    class="custom-btn btn u-m-0"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FileType,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { Action } from "uniplat-sdk"
    import { routesMap } from "@/router/direction"

    @Component({ components: { FormBuilder } })
    export default class AddTaskPop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: "" })
        private readonly id!: number

        private action?: Action

        private get title() {
            return "新增功能"
        }

        onOpen() {
            this.init()
        }

        onClosing() {
            this.resetFormFields()
        }

        private getAction() {
            return (this.action = sdk.core
                .model("data_comparison")
                .action(
                    this.id ? "update_data_comparison" : "create_data_comparison"
                )
                .updateInitialParams({
                    selected_list: this.id ? [{ v: 0, id: this.id }] : [],
                }))
        }

        private init() {
            this.loading = true
            return buildFormSections({
                action: this.getAction(),
                forms: [],
            }).then((r) => {
                this.buildFormFull(r)
                this.loading = false
            })
        }

        private submitValidate() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                    })
                }
            })
        }

        private submit(data: any) {
            pageLoading(() => {
                return sdk.core
                    .model("data_comparison")
                    .action(
                        this.id
                            ? "update_data_comparison"
                            : "create_data_comparison"
                    )
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: this.id ? [{ v: 0, id: +this.id }] : [],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success(this.id ? "编辑成功" : "新建成功")
                        this.callRefresh(routesMap.dataStorage.personDataCompare)
                        this.callRefresh(
                            routesMap.dataStorage.personDataCompareDetail
                        )
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
