<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            :useTab="true"
            ref="table"
            v-model="tableConfig"
            class="container"
        >
            <div slot="title" class="u-flex u-row-between bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
                <div class="u-flex u-row-right">
                    <el-button type="primary" @click="batchFeedback">
                        批量反馈结果
                    </el-button>
                    <el-button
                        type="primary"
                        @click="
                            exportExcelUniplatV2({
                                template_name: '投递记录导出',
                            })
                        "
                    >
                        导出
                    </el-button>
                </div>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="position_name" slot-scope="scope">
                        <div
                            class="u-line-1 primary pointer"
                            @click="toPositionDetail(scope.row)"
                        >
                            {{ scope.row.position_name }}
                        </div>
                        <!-- <div class="u-flex">
                            <div class="u-m-r-10 color-9">
                                {{ scope.row.create_time_label }}
                            </div>
                            <el-button
                                type="text"
                                @click="toPositionDetail(scope.row)"
                                >查看</el-button
                            >
                        </div> -->
                    </div>
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button type="text" @click="toFeedback(scope.row)">
                            企业反馈
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <FeedbackPop v-model="showFeedbackPop" :id="rowId" />
        <status-pop
            v-model="showStatusPop"
            :selected="selected"
            :isBatch="isBatch"
            @refresh="reloadList"
        ></status-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { Component } from "vue-property-decorator"
    import { tableConfig } from "."
    import { updateTagItem } from "../../single-page/components/tags-view"
    import FeedbackPop from "./components/feedback-pop.vue"
    import StatusPop from "./components/status-pop.vue"
    import { config, EnvProject } from "@/config"

    @Component({
        name: routesMap.recruit.jobDeliver,
        components: { TableContainer, CommonTable, FeedbackPop, StatusPop },
    })
    export default class CompanyBlackList extends BaseTableController<any> {
        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.jobDeliver,
        }

        private id = ""

        tableConfig: TableConfig | null = null

        private showFeedbackPop = false
        private showStatusPop = false
        protected rowId: string | any = ""

        private get columns() {
            return this.tableConfig?.column || []
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "岗位投递列表",
                    to: {
                        name: routesMap.recruit.jobDeliver,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.jobDeliver,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private init() {
            this.tableConfig = tableConfig("manage")
            this.setBreadcrumbs()
        }

        private checkIds: Array<number | string> = []
        private selected: {
            id: number
            v: number
        }[] = []

        private isBatch = false

        private handleSelectionChange(d: { ids: string[]; rows: any[] }) {
            this.checkIds = d.rows.map((e) => e.id)
            this.selected = d.rows.map((item) => {
                return {
                    id: +item.id,
                    v: item.v,
                }
            })
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.recruit.jobDeliverDetail,
                query: {
                    id: row._access_key || row.id,
                    from: routesMap.recruit.jobDeliver,
                },
            })
        }

        private toPositionDetail(row: any) {
            this.$router.push({
                name: routesMap.recruit.jobDetail,
                query: { id: row.position_id, from: routesMap.recruit.jobDeliver },
            })
        }

        private toFeedback(row: any) {
            this.rowId = row.id
            this.showFeedbackPop = true
        }

        private batchFeedback() {
            if (!this.checkIds.length) {
                return this.$message.error("请先勾选需要批量反馈的数据！")
            }
            this.isBatch = true
            this.showStatusPop = true
        }

        mounted() {
            this.init()
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
</style>
