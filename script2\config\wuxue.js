const defaultDomainAllInOne = {
    VUE_APP_UNIPLAT: "/api",
    VUE_APP_UNIPLAT_WEB: "/uniplat",
    VUE_APP_H5: "/h5",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
}
const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "武穴",
    VUE_APP_CITY_SAMPLE_NAME2: "武穴市",
    VUE_APP_DEFAULT_REGION_CODE: "421182",
    VUE_APP_DEFAULT_REGION_NAME: "武穴市",
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "武穴市人民政府",
    VUE_APP_BAIDU_KEY: "Xa338L0G79uVCEmkg7tIXOZtCLEPFfMY",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "LYNBSP96NG248ZHE",
    VUE_APP_requestEncoder: "aes", // 入参加密 aes
    VUE_APP_responseEncoder: "aes", // 返回参加密 aes
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-wuxue",
    VUE_APP_BIGSCREEN_BI_PATH: "/bigScreen",
}
const { config } = require("./wuxue_p.js")
module.exports = {
    name: "武穴项目",
    env: {
        pro: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_UNIPLAT: "https://zhjy.wxggzpw.com/api",
            VUE_APP_UNIPLAT_WEB: "",
            VUE_APP_H5: "",
            VUE_APP_ENTERPRISE_URL: "",
            VUE_APP_HR_URL: "",
            VUE_APP_OPERATE_URL: "",
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                BASE_URL: "platform/",
                VUE_APP_UNIPLAT_ENTRANCE: "武穴市智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "武穴市智慧就业服务工作台",
                VUE_APP_APP_TITLE: "武穴市智慧就业服务工作台",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                pro: {
                    path: "/data/wuxue/web/platform",
                    host: "qqxb-wuxue",
                },
            },
        },
    ],
}
