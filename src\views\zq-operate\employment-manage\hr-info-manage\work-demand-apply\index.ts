import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import { checkColumn1, checkRowPredict } from "../work-demand-audit"

const tableFilter: TableFilter[] = [
    {
        label: "调查名称",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "create_time",
        label: "发布时间",
        type: FormType.DatePicker,
    },
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_province_region_code",
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
]

export const rowPredict = {
    title: "",
    create_time: "",
    task_count: "",
    task_audit_count: "",
    task_notfill_count: "",
    task_over_count: "",
    mgt_province: "mgt_province#region_name",
    mgt_city: "mgt_city#region_name",
    mgt_area: "mgt_area#region_name",
}

export const column: TableColumn[] = [
    {
        label: "任务名称",
        prop: "title",
        showOverflowTip: true,
        width: "300"
    },
    {
        label: "发布时间",
        prop: "create_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "管理区域",
        prop: "mgt_province",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.mgt_province, row.mgt_city, row.mgt_area]
                .filter(Boolean)
                .join("")
        },
        minWidth: "130",
    },
    {
        label: "目标填报企业总数",
        prop: "task_count",
        showOverflowTip: true,
        minWidth: "120",
    },
    {
        label: "审核中企业数",
        prop: "task_audit_count",
        showOverflowTip: true,
    },
    {
        label: "未填报企业数",
        prop: "task_notfill_count",
        showOverflowTip: true,
    },
    {
        label: "已完成企业数",
        prop: "task_over_count",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const column1: TableColumn[] = [
    {
        label: "任务名称",
        prop: "title",
        showOverflowTip: true,
        width: "460"
    },
    {
        label: "创建时间",
        prop: "create_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "管理区域",
        prop: "mgt_province",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.mgt_province, row.mgt_city, row.mgt_area]
                .filter(Boolean)
                .join("")
        },
        minWidth: "130",
    },
    {
        label: "目标填报企业总数",
        prop: "task_count",
        showOverflowTip: true,
        minWidth: "120",
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const column2: TableColumn[] = [
    {
        label: "任务名称",
        prop: "title",
        showOverflowTip: true,
        width: "340"
    },
    {
        label: "发布时间",
        prop: "create_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "结束时间",
        prop: "update_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.update_time)
        },
    },
    {
        label: "管理区域",
        prop: "mgt_province",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.mgt_province, row.mgt_city, row.mgt_area]
                .filter(Boolean)
                .join("")
        },
        minWidth: "130",
    },
    {
        label: "目标填报企业总数",
        prop: "task_count",
        showOverflowTip: true,
        minWidth: "120",
    },
    {
        label: "已完成企业数",
        prop: "task_over_count",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("company_task").list("list_for_req"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        column,
        oneTabFilter: true,
    }
}

export const enum AuthStatus {
    待认证 = 0,
    已认证 = 1,
    认证不通过 = 2,
}

export interface Row {
    /** 任务名称 */
    title: string

    /** 发布时间 */
    create_time: string
    update_time: string

    /** 被调查的企业总数 */
    task_count: number

    /** 审核中企业数 */
    task_audit_count: number

    /** 未填报企业数 */
    task_notfill_count: number

    /** 已完成企业数 */
    task_over_count: number
    id: number
    v: number
}

export const forms = [
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_codes",
        option: {
            elProps: { checkStrictly: true },
        },
        col: {
            span: 14,
            offset: 4,
        },
        required: true,
        disabledUniplatRule: true,
        rules: [{ required: true, message: "管理区域不能为空" }],
    },
    {
        label: "任务名称",
        type: FormType.Text,
        prop: "title",
        col: {
            span: 14,
            offset: 4,
        },
        required: true,
    },
    {
        label: "计划完成时间",
        type: FormType.DatePicker,
        prop: "finish_time",
        col: {
            span: 14,
            offset: 4,
        },
    },
    {
        label: "任务内容描述",
        type: FormType.Text,
        prop: "description",
        option: {
            type: "textarea",
            rows: 7,
        },
        col: {
            span: 14,
            offset: 4,
        },
    },
]

export function getEditForm(id: string): BuildFormConfig {
    return {
        sdkModel: "xg_agent_manager",
        sdkAction: "update",
        id: +id,
        forms: forms as any,
    }
}

const recordTableFilter: TableFilter[] = [
    {
        label: "企业名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "岗位名称",
        type: FormType.Text,
        prop: "position_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "核查状态",
        type: FormType.Select,
        prop: "status",
        option: {
            multiple: true,
        },
    },
]

export function recordTableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("company_task_wroker_require_record")
            .list("list_for_show"),
        filter: recordTableFilter,
        defaultPageSize: 8,
        predict: checkRowPredict,
        column: checkColumn1.filter((i) => {
            return i.prop !== "h"
        }),
        oneTabFilter: true,
    }
}

export const recordColumn: TableColumn[] = [
    {
        label: "岗位名称",
        prop: "agent_name",
        showOverflowTip: true,
    },
    {
        label: "工种类别",
        prop: "company_code",
        showOverflowTip: true,
    },
    {
        label: "用工人数",
        prop: "legal_person",
        showOverflowTip: true,
    },
    {
        label: "工作性质",
        prop: "province_code",
        showOverflowTip: true,
    },
    {
        label: "劳动关系",
        prop: "contact_person",
        showOverflowTip: true,
    },
    {
        label: "学历要求",
        prop: "company_code",
        width: "220",
        showOverflowTip: true,
    },
    {
        label: "年龄要求",
        prop: "legal_person",
        showOverflowTip: true,
    },
    {
        label: "用高峰期",
        prop: "province_code",
        showOverflowTip: true,
    },
    {
        label: "技能要求",
        prop: "contact_person",
        showOverflowTip: true,
    },
]
