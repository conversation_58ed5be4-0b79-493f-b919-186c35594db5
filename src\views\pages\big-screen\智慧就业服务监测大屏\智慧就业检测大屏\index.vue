<template>
    <Container>
        <div class="home-container flex-fill u-flex u-row-between">
            <div class="left">
                <CardJuMin></CardJuMin>
                <CardQiYe></CardQiYe>
                <CardZhengCe></CardZhengCe>
            </div>
            <div>
                <div class="u-flex">
                    <div class="middle">
                        <Card1></Card1>
                        <CardBangFu></CardBangFu>
                    </div>
                    <div class="right">
                        <CardDongTai></CardDongTai>
                        <Card2></Card2>
                    </div>
                </div>
                <Card3></Card3>
            </div>
        </div>
    </Container>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"
    import Container from "./common/container.vue"
    import CardJuMin from "./components/居民/index.vue"
    import CardQiYe from "./components/企业/index.vue"
    import CardZhengCe from "./components/政策/index.vue"
    import CardBangFu from "./components/重点人群帮扶/index.vue"
    import CardDongTai from "./components/动态管理/index.vue"
    import Card1 from "./components/人岗匹配/index.vue"
    import Card2 from "./components/零工驿站/index.vue"
    import Card3 from "./components/七步工作法/index.vue"
    import { getXgRegionMapping } from "./common/regionTree/cache-tree"

    @Component({
        components: {
            Container,
            CardJuMin,
            CardQiYe,
            CardZhengCe,
            CardBangFu,
            CardDongTai,
            Card1,
            Card2,
            Card3,
        },
    })
    export default class Template extends Vue {}
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .home-container {
        padding-left: 20px;
        padding-right: 20px;
        gap: 20px;
    }

    .left,
    .middle,
    .right {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    .middle {
        margin-right: 20px;
        margin-bottom: 10px;
    }
    .right {
        margin-bottom: 10px;
    }
    /deep/ .empty-pie {
        font-size: 14px;
        text-align: center;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #89b9ff;
        opacity: 0.75;
    }
</style>
