<template>
    <div v-if="tableConfig2">
        <div class="core-ui-custom-header" v-if="!hideHeader">
            <div class="u-flex title w-100">
                <bread-crumb :backRoute="from" :items="breadcrumbs" />
                <div class="u-flex-1" />
                <el-button
                    v-if="!isYD && !isEZ"
                    v-role="'model.user_profile_basic.action.recommend_profile'"
                    type="primary"
                    @click="batchRecommend"
                    plain
                    class="custom-btn batch-btn"
                >
                    批量人才推荐
                </el-button>
                <el-button
                    v-if="!isYD && !isXg"
                    v-role="'model.query_user_profile.action.batch_tags'"
                    type="primary"
                    @click="batchSetTag"
                    plain
                    class="custom-btn batch-btn"
                >
                    批量设置标签
                </el-button>
                <el-button
                    type="primary"
                    @click="showPop1 = true"
                    v-if="isXg"
                    plain
                >
                    批量设置标签
                </el-button>
            </div>
        </div>
        <table-filter
            ref="filter"
            :tableFilter="tableConfig2.filter"
            :metaFilters="metaFilters"
            :tagGroups="tagGroups"
            @search="resetPageAndSearch"
            :showExpand="true"
            :defaultFilterIsExpand="false"
        />
        <div
            class="bg-white"
            :class="{
                'u-p-20': !hideHeader,
            }"
            v-loading="loading"
        >
            <common-table
                :data="items"
                :columns="columns"
                @handleSelectionChange="handleSelectionChange"
            >
                <div slot="h" class="u-flex u-row-center" slot-scope="scope">
                    <div class="handler-btn" @click="setTag(scope.row)">
                        标签
                    </div>
                    <div class="handler-btn" @click="toDetail(scope.row)">
                        详情
                    </div>
                </div>
            </common-table>

            <div class="u-flex u-row-center u-m-t-30">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="index"
                    :page-sizes="pageSizes"
                    :page-size="size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <set-tag-dialog
            v-model="showSetTag"
            model="user_profile_basic"
            action="set_labels"
            :ids="row ? [row.id] : []"
            :isBatch="false"
            @refresh="onFilterChanged"
        ></set-tag-dialog>

        <set-tag-dialog
            v-model="showBatchSetTag"
            model="user_profile_basic"
            action="batch_set_labels"
            :ids="checkEdIds"
            :isBatch="true"
            @refresh="onFilterChanged"
        ></set-tag-dialog>

        <batch-recommend
            v-model="showBatchRecommend"
            :selected="selected"
            @refresh="onFilterChanged"
        />

        <excel-import
            v-if="importConfig"
            v-model="showPop1"
            title="批量导入标签"
            placeholder="请点击「确定」将模板上传"
            :importConfig="importConfig"
            @refresh="onFilterChanged"
        />
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { SelectOption } from "@/core-ui/component/form"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTable } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import TableFilter from "@/core-ui/component/table/filter.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { cloneDeep, last, map, split } from "lodash"
    import { metaFilter, TagManagerTypes } from "uniplat-sdk/build/main/def"
    import { Component, Prop, Ref } from "vue-property-decorator"
    import { columns, computeAge, Row, tableConfig } from "."
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import BatchRecommend from "./components/batch-recommend.vue"
    import SetTagDialog from "./components/set-tag.vue"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { needVerifyContent } from "@/core-ui/component/excel-import"
    import { config, EnvProject } from "@/config"

    @Component({
        name: routesMap.labourManage.seekerInfo,
        components: {
            TableContainer,
            CommonTable,
            SetTagDialog,
            BatchRecommend,
            TableFilter,
            ExcelImport,
        },
    })
    export default class SeekerInfoIndex extends BaseTable<Row> {
        @Prop({ default: false })
        private hideHeader!: boolean

        protected pageSizes = [10, 20, 50, 100]
        tableConfig2: TableConfig | null = null
        selectOption: SelectOption[] = []
        public metaFilters: metaFilter[] = []
        row: Row | null = null

        private isYD = config.envProject === EnvProject.宜都项目
        private isEZ = config.envProject === EnvProject.鄂州项目
        private isXg = config.envProject === EnvProject.孝感项目

        importConfig: any = null
        showPop1 = false

        public checkEdIds: Array<number | string> = []
        public selected: {
            id: string
            v: number
            name: string
            mobile: string
        }[] = []

        private get selectedIds() {
            return map(this.selected, "id")
        }

        private showSetTag = false
        private showBatchRecommend = false
        private showBatchSetTag = false
        tagGroups: TagManagerTypes.TagGroup[] = []
        private readonly columns: TableColumn[] = columns

        breadcrumbs: BreadcrumbItem[] = []

        get from() {
            return this.$route.query.from as string | undefined
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from!),
                {
                    label: "居民信息查看",
                    to: {
                        name: routesMap.labourManage.seekerInfo,
                        query: this.from
                            ? {
                                  from: this.from,
                              }
                            : undefined,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.labourManage.seekerInfo,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        mounted() {
            this.tableConfig2 = tableConfig()
            this.setBreadcrumbs()
            this.tableConfig2
                .model!.query({
                    pageIndex: 1,
                    item_size: 0,
                })
                .then((r) => {
                    this.tagGroups = r.pageData.tagGroups
                    this.metaFilters = r.pageData.meta.filters
                })
            this.onFilterChanged()

            this.importConfig = {
                templateUrl:
                    window.location.origin +
                    `/${
                        process.env.BASE_URL === "/" ? "" : process.env.BASE_URL
                    }/file/批量导入标签.xlsx`.replace("//", "/"),
                modelName: "user_profile_basic",
                actionName: "batch_import_labels",
                bigActionImportParams: {
                    inputs_parameters: [],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
        }

        private needVerifyContent(originFile: any) {
            return needVerifyContent({
                originFile,
                fileUniKey: "293c430d80084df48262155633b80ea2",
                vue: this,
                index: 2,
            })
        }

        @Ref("filter")
        private readonly filter!: TableFilter | any

        resetPageAndSearch() {
            this.index = 1
            this.onFilterChanged()
        }

        protected onFilterChanged() {
            const filterData = cloneDeep(this.filter?.getFilterData() || {})
            const v = split(filterData.tags, ",")
            const tags = map(v, (i) => {
                const tag = split(i, ":::")
                return {
                    tagGroup: tag[0],
                    tag: tag[1],
                }
            })
                .filter((i) => i.tag)
                .map((i) => i.tag)
                .join(",")
            filterData.tags = tags
            if (filterData.birth_date) {
                const birth_date = (filterData.birth_date || [])
                    .map((e: number) => computeAge(e))
                    .filter(Boolean)
                    .reverse()
                filterData.birth_date_start = birth_date[0]
                filterData.birth_date_end = birth_date[1]
                delete filterData.birth_date
            }
            if (filterData.household_code) {
                filterData.household_code = last(
                    split(filterData.household_code, ",").filter(Boolean)
                )
            }
            if (filterData.update_time) {
                filterData.update_time_start = filterData.update_time[0]
                filterData.update_time_end = filterData.update_time[1]
                delete filterData.update_time
            }
            this.loading = true
            Promise.all([
                sdk.core
                    .domainService("xg_project", "back_api", "fetch_profile_list")
                    .post({
                        ...filterData,
                        page_index: this.index,
                        page_size: this.size,
                    })
                    .then((r: any) => {
                        this.items = r.data
                    }),
                sdk.core
                    .domainService(
                        "xg_project",
                        "back_api",
                        "fetch_profile_list_total"
                    )
                    .post(filterData)
                    .then((r: any) => {
                        this.total = +r.total_count
                    }),
            ]).finally(() => {
                this.loading = false
            })
        }

        private setTag(row: Row) {
            this.row = row
            this.$nextTick(() => {
                this.showSetTag = true
            })
        }

        /** 批量推广 */
        private batchRecommend() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量人才推荐的居民信息！")
            }
            this.showBatchRecommend = true
        }

        /** 批量设置标签 */
        private batchSetTag() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量设置标签的居民！")
            }
            this.showBatchSetTag = true
        }

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.rows.map((e) => e.profile_id)
            this.selected = d.rows.map((item) => {
                return {
                    profile_id: item.profile_id,
                    id: item.profile_id,
                    v: item.v,
                    mobile: item.mobile_hide,
                    name: item.name_hide,
                }
            })
        }

        private toDetail(row: Row) {
            console.log("jsp", JSON.parse(JSON.stringify(row)))
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: row._access_key || row.profile_id + "",
                    from: routesMap.labourManage.seekerInfo,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
    .tips {
        width: 165px;
        font-size: 12px;
        color: red;
        margin-top: 7px;
        line-height: 1.5;
    }
</style>
