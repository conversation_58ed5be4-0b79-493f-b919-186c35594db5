<template>
    <div class="h-100 d-flex" :class="themeClassName" v-loading="loading">
        <div class="left h-100 u-flex-none" :style="leftStyle">
            <img class="banner" :style="bannerStyle" :src="bannerImg" />
        </div>
        <div class="right u-flex-1 h-100" :style="rightStyle">
            <div class="back-icon" @click="backRoute">
                <span class="el-icon-arrow-left to"></span> 返回登录
            </div>
            <div class="w-600 create-position">
                <div class="title-text">企业入驻申请</div>

                <div class="step-box u-flex">
                    <div
                        v-for="(step, index) in stepArr"
                        :key="index"
                        class="step-item"
                        :class="{ selected: index <= curStep }"
                    >
                        <div class="line" v-if="index"></div>
                        <div class="idx">{{ index + 1 }}</div>
                        <div>{{ step.title }}</div>
                    </div>
                </div>

                <div>
                    <Step1
                        v-show="curStep === 0"
                        v-if="steps.includes(0)"
                        @next="next($event, 0)"
                    ></Step1>
                    <Step2
                        v-show="curStep === 1"
                        :show="curStep >= 1"
                        :info="stepArr[0].value"
                        @next="next($event, 1)"
                        @pre="pre()"
                    ></Step2>
                    <Step3
                        v-show="curStep === 2"
                        :show="curStep === 2"
                        :info="stepArr[1].value"
                        :agree="agree"
                        :curAgreeInfo="curAgreeInfo"
                        @pre="pre()"
                    >
                        <div v-if="curAgreeInfo.length" class="u-flex agree">
                            <el-checkbox v-model="agree"> </el-checkbox>
                            <div class="u-m-l-10">
                                已阅读并同意
                                <span
                                    class="primary pointer"
                                    @click="showAgree(curAgreeInfo[0])"
                                    >《用户协议》</span
                                >
                                和
                                <span
                                    class="primary pointer"
                                    @click="showAgree(curAgreeInfo[1])"
                                    >《隐私政策》</span
                                >
                            </div>
                        </div>
                    </Step3>
                </div>
            </div>
        </div>

        <agree-pop
            v-model="showAgreePop"
            :agreePopInfo="agreePopInfo"
        ></agree-pop>
    </div>
</template>

<script lang="ts">
    import { config } from "@/config"
    import { Component } from "vue-property-decorator"
    import { FormController } from "@/core-ui/component/form"
    import AgreePop from "./components/agree-pop.vue"
    import Step1 from "./components/apply/step1.vue"
    import Step2 from "./components/apply/step2.vue"
    import Step3 from "./components/apply/step3.vue"
    import { cloneDeep } from "lodash"
    import { userService } from "@/service/service-user"

    @Component({
        components: { AgreePop, Step1, Step2, Step3 },
    })
    export default class LoginIndex extends FormController {
        private bannerImg = config.projectConfig.bannerImg
        private bannerStyle = config.projectConfig.bannerStyle || {}
        private title = config.appTitle
        private leftStyle = config.projectConfig.leftStyle || {}
        private rightStyle = config.projectConfig.rightStyle || {}
        private themeClassName = config.projectConfig.themeClass || ""
        private loading = false
        private agree = false
        private agreeInfo: { [key: string]: { name: string; src: string }[] } = {
            荆州市企业智慧就业服务平台: [
                {
                    name: "“荆州创业就业企业服务工作台”用户服务协议",
                    src: "/file/用户协议-荆州创业就业企业服务工作台.md",
                },
                {
                    name: "“荆州就业创业企业服务工作台”隐私政策",
                    src: "/file/隐私政策-荆州就业创业企业服务工作台.md",
                },
            ],
        }

        private stepArr = [
            {
                title: "统一社会信用代码",
                component: "step1",
                value: null,
            },
            {
                title: "确认企业信息",
                component: "step2",
                value: null,
            },
            {
                title: "提交申请",
                component: "step3",
                value: null,
            },
        ]

        private curStep = 0
        private steps = [0]

        private next(info: any, idx: number) {
            // console.log("out", info)
            this.stepArr[idx].value = cloneDeep(info)
            // console.log("stepArr", this.stepArr)
            this.curStep = idx + 1
            this.steps.push(this.curStep)
        }

        private pre() {
            this.curStep = this.curStep - 1
        }

        private get curAgreeInfo() {
            return (this.agreeInfo[config.appTitle] || []).map((e) => {
                return {
                    ...e,
                    src: window.location.origin + e.src,
                }
            })
        }

        private showAgreePop = false
        private agreePopInfo: any = null

        private showAgree(agreePopInfo: any) {
            this.agreePopInfo = agreePopInfo
            this.$nextTick(() => {
                this.showAgreePop = true
            })
        }

        created() {
            this.init()
        }

        private backRoute() {
            userService.logout()
        }

        private init() {}
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .left {
        width: 50%;
        background: linear-gradient(360deg, #457cee 0%, #305dcc 100%);
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }
    .right {
        display: flex;
        justify-content: center;
        overflow: auto;
        position: relative;

        .container {
            margin-bottom: 10%;
            .title {
                font-size: 24px;
                font-weight: 600;
                color: #000000;
                line-height: 33px;
                margin-bottom: 29px;
                text-align: center;
            }
        }

        .back-icon {
            position: absolute;
            left: 15px;
            top: 40px;
            font-size: 16px;
            cursor: pointer;
            color: #000000;

            &:hover {
                color: var(--primary);
            }
        }
    }

    .title-text {
        color: #36383b;
        font-size: 22px;
        font-weight: 600;
        text-align: center;
        margin-bottom: 30px;
    }

    .w-600 {
        width: 600px;
        padding: 40px 0;
        // min-height: 1066px;
        min-height: 100%;
        margin-top: auto;
        margin-bottom: auto;
    }

    .create-position {
        ::v-deep .el-form-item__label {
            text-align: left;
            color: #4e5054;
            font-size: 14px;
        }
    }

    ::v-deep .el-form-item__label {
        text-align: right !important;
    }
    ::v-deep .limit-1 {
        div.el-upload.el-upload--picture-card {
            display: none;
        }
    }

    ::v-deep .upload {
        display: flex;
        align-items: center;

        .loader {
            .icon {
                margin-bottom: 0;
            }
            div {
                display: none;
            }
        }
        .upload-tip {
            flex: none;
            margin-left: 10px;
            color: #9098a6;
        }
    }

    .btns {
        .btn {
            width: 353px;
            height: 40px;
        }
    }
    .agree {
        margin-left: 125px;
        margin-bottom: 20px;
    }

    .step-box {
        justify-content: center;
        margin-bottom: 20px;

        .step-item {
            flex: none;
            display: flex;
            align-items: center;

            font-weight: bold;
            font-size: 14px;
            color: #bec0c2;
            line-height: 16px;

            .idx {
                width: 34px;
                height: 34px;
                border-radius: 50%;
                border: 1px solid rgba(153, 153, 153, 0.6);
                color: rgba(153, 153, 153, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16px;
            }

            .line {
                width: 105px;
                height: 1px;
                background-color: #ccc;
                margin-left: 20px;
                margin-right: 20px;
            }

            &.selected {
                color: #598bff;
                font-weight: 700;

                .idx {
                    background-color: #598bff;
                    border: 1px solid #598bff;
                    color: #fff;
                }

                .line {
                    background-color: #598bff;
                }
            }
        }
    }
</style>
