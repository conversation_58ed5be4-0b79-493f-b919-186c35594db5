<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="titleText"
        width="680px"
        :closeOnClickModal="false"
        top="8vh"
    >
        <div class="" v-if="showForm" v-loading="loading">
            <div class="form-content-box">
                <form-builder
                    ref="formBuilder"
                    label-position="right"
                    label-width="120px"
                    modelName="collect_task_order_detail"
                    actionName="base_insert"
                    :useSameAction="true"
                    :showTips="true"
                    :useCustomInput="true"
                    :forceUpdateValue="false"
                    :immediateValidate="true"
                    :transConfig="transConfig"
                    :transCustomConfigByProperty="transCustomConfigByProperty"
                    :needHideProp="[
                        '_tip_collector_change_status',
                        'collector_change_status',
                    ]"
                    @updateQueryRemote="updateQueryRemote"
                ></form-builder>
            </div>
            <div class="u-flex u-m-t-30 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="confirm"
                    class="custom-btn btn u-m-0"
                >
                    提交
                </el-button>
            </div>
        </div>

        <DialogConfirm
            @confirm="confirmStatus"
            v-model="displayDialogConfirm"
            :curChangeStatus="curChangeStatus"
        ></DialogConfirm>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FileType,
        FormController,
        FormItem,
        FormType,
    } from "@/core-ui/component/form"
    import { sdk } from "@/service"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { Action } from "uniplat-sdk"
    import { cloneDeep, flatMap, isEqual } from "lodash"
    import DialogConfirm from "./dialog-confirm.vue"
    import { config, EnvProject } from "@/config"

    const isDev = process.env.VUE_APP_ENV === "test"

    const enum ChangeStatus {
        已更新 = "0",
        居民信息无更新 = "1",
        居民信息联系不上 = "2",
    }

    @Component({ components: { FormBuilder, DialogConfirm } })
    export default class DialogAllocation extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: () => {} })
        private detail!: any

        @Prop({ default: "" })
        private preId!: any

        @Prop({ default: "" })
        private curId!: any

        @Prop({ default: "collect_task_order_detail" })
        private intentModelName!: string

        @Prop({ default: () => [] })
        private requiredConfig!: string[]

        @Prop({ default: () => [] })
        private schemaInfo!: any[]

        private displayDialogConfirm = false

        private transConfig = {
            image: {
                type: FormType.MyUpload,
                option: {
                    fileType: [FileType.Image],
                    listType: "picture-card",
                    placeholder: "",
                    limit: 1,
                },
            },
        }

        private transCustomConfigByProperty = {
            mobile: {
                type: FormType.TextCustom,
                option: {
                    useAutoBlur2Change: true,
                },
            },
            ...((isDev || [EnvProject.孝感项目].includes(config.envProject))
                ? {
                      education_school_name: {
                          type: FormType.Select,
                          option: {
                              remote: true,
                              filterable: true,
                              domainService: {
                                  request(searchText: string) {
                                      return sdk.core
                                          .domainService(
                                              "data_products",
                                              "fixed_column_api",
                                              "get_school_name_v2"
                                          )
                                          .post({
                                              school_name: searchText,
                                          })
                                          .then((r: any) => {
                                              return r.map((i: any) => {
                                                  return {
                                                      key: i.map_value,
                                                      value: i.map_value,
                                                  }
                                              })
                                          })
                                  },
                              },
                          },
                      },
                      work_company_name: {
                          type: FormType.Select,
                          option: {
                              remote: true,
                              filterable: true,
                              domainService: {
                                  request(searchText: string) {
                                      return sdk.core
                                          .domainService(
                                              "data_products",
                                              "fixed_column_api",
                                              "get_market_entity_name_v2"
                                          )
                                          .post({
                                              market_entity_name: searchText,
                                          })
                                          .then((r: any) => {
                                              return r.map((i: any) => {
                                                  return {
                                                      key: i.market_entity_name,
                                                      value: i.market_entity_name,
                                                  }
                                              })
                                          })
                                  },
                              },
                          },
                      },
                  }
                : {}),
        }

        onOpen() {
            this.showForm = true
            this.init()
        }

        private curChangeStatus = ChangeStatus.居民信息无更新

        private get titleText() {
            return this.curId ? "编辑采集" : "新增采集"
        }

        private action: Action | null = null

        private cacheData: Record<string, string> | null = null

        private showForm = false

        private cacheForms: any[] = []

        protected onClosing() {
            setTimeout(() => {
                this.showForm = false
            }, 300)
        }

        private init() {
            if (this.curId) {
                this.initUpdateAction()
            } else {
                this.initCreatedAction()
            }
        }

        private isPhone(str: string) {
            if (/^\d{11}$/.test(str)) {
                return true
            }
        }

        // collect_task_order_wait_detail
        // update_by_village_back_intent

        private initUpdateAction() {
            this.loading = true
            return buildFormSections({
                sdkModel: "collect_task_order_detail",
                sdkAction: "base_insert",
                select_list: [{ id: this.curId, v: 0 }],
                others: {
                    intentContext: {
                        modelName:
                            this.intentModelName || "collect_task_order_detail",
                        name: "update_by_village_back_intent",
                        id: this.curId,
                    },
                },
                forms: [],
                requiredConfig: this.requiredConfig,
                sortFormItem: (formItems: FormItem[]) => {
                    return this.sortFormItems(formItems)
                },
                needSourceData: true,
            })
                .then((r) => {
                    this.handelBuildForm(r)
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private initCreatedAction() {
            this.loading = true
            return buildFormSections({
                sdkModel: "collect_task_order_detail",
                sdkAction: "base_insert",
                select_list: [],
                others: {
                    intentContext: {
                        modelName: "collect_task_order_detail_pre",
                        name: "insert_by_village_back_intent",
                        id: this.preId,
                    },
                },
                forms: [],
                requiredConfig: this.requiredConfig,
                sortFormItem: (formItems: FormItem[]) => {
                    return this.sortFormItems(formItems)
                },
                needSourceData: true,
            })
                .then((r) => {
                    this.handelBuildForm(r)
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private cacheFormConfig: any[] = []

        private handelBuildForm(r: any) {
            console.log(r)

            this.action = r.action
            this.cacheData = r.data

            const inputs_parameters =
                r.inputsParameters?.parameters.inputs_parameters || {}
            this.cacheForms = r.forms
            this.updateQueryRemote(undefined, {
                masters: inputs_parameters,
            })

            this.buildForm(this.cacheForms, r)
        }

        private sortFormItems(formItems: FormItem[]) {
            console.log("this.schemaInfo")
            console.log(this.schemaInfo)
            const schemaInfo = cloneDeep(this.schemaInfo)
            const schemaInfo2 = cloneDeep(this.schemaInfo)
            const forms = cloneDeep(formItems)
            schemaInfo.forEach((i) => {
                if (i.fields && i.fields.length) {
                    i.fields.unshift({
                        enabled: true,
                        field: i.code,
                        label: i.title,
                        required: true,
                        isTips: true,
                    })
                }
            })

            this.cacheFormConfig = schemaInfo2.map((i) => {
                return {
                    tip: i.code,
                    keys: i.fields.map((j: any) => {
                        return {
                            key: j.field,
                            hide: false,
                        }
                    }),
                    hide: false,
                }
            })

            console.log("cacheFormConfig")
            console.log(this.cacheFormConfig)

            const fields = flatMap(schemaInfo, (i) => i.fields)
            const newForms = this.sortArr2BasedOnArr1(fields, forms)
            return newForms
        }

        private sortArr2BasedOnArr1(
            arr1: { field: string; isTips: boolean; label: string }[],
            arr2: { prop: string }[]
        ) {
            const result: any[] = []
            arr1.forEach((i) => {
                if (i.isTips) {
                    result.push({
                        type: FormType.None,
                        option: {
                            disabled: true,
                            placeholder: "请输入",
                        },
                        label: i.label,
                        prop: i.field,
                        col: {
                            span: 12,
                        },
                        sourceInputsParameter: {},

                        notDisplay: true,
                        emptyDisplay: "",
                        rules: [],
                    })
                    return
                }

                const t = arr2.find((j) => j.prop === i.field)
                if (t) {
                    result.push(t)
                }
            })

            console.log("result")
            console.log(result)

            return result
        }

        private updateQueryRemote(_property: any, r: any) {
            console.log("r")
            console.log(r)

            r.masters.forEach((i: any) => {
                this.cacheFormConfig.forEach((j) => {
                    j.keys.forEach((k: any) => {
                        if (k.key === i.property) {
                            k.hide = i.type === "hidden"
                        }
                    })
                    j.hide = !j.keys.filter((k: any) => !k.hide).length
                })
            })

            this.checkTipNeedHide(this.cacheForms)
            console.log("this.cacheFormConfig")
            console.log(this.cacheFormConfig)
        }

        private checkTipNeedHide(forms: any) {
            this.cacheFormConfig.forEach((j) => {
                const res = forms.find((x: any) => x.prop === j.tip)
                if (res) {
                    Object.assign(res, {
                        hide: j.hide,
                    })
                }
            })
        }

        private checkStatusAndSetData(
            cacheData: Record<string, string>,
            data: Record<string, string>
        ) {
            const cacheStatus = cacheData.collector_change_status
            cacheData.collector_change_status = data.collector_change_status
            const result = isEqual(cacheData, data)
            console.log("checkStatusAndSetData")
            console.log(result)
            console.log(data.collector_change_status)
            if (result) {
                if (!data.collector_change_status) {
                    data.collector_change_status = ChangeStatus.居民信息无更新
                    this.formBuilder?.onInput(
                        { prop: "collector_change_status", needListen: true },
                        ChangeStatus.居民信息无更新
                    )
                    return true
                }
                // if (data.collector_change_status === ChangeStatus.已更新) {
                //     return false
                // }
                return false
            } else {
                data.collector_change_status = ChangeStatus.已更新
                this.formBuilder?.onInput(
                    { prop: "collector_change_status", needListen: true },
                    ChangeStatus.已更新
                )
            }
            cacheData.collector_change_status = cacheStatus
            return data
        }

        private confirm() {
            if (!this.cacheData) {
                console.log("this.cacheData")
                console.log(this.cacheData)
                return
            }
            const data = this.getFormValues()

            const result = this.checkStatusAndSetData(this.cacheData, data)

            console.log("confirm: data")
            console.log(cloneDeep(data))
            this.validateForm((v: boolean, obj: any) => {
                console.log("this.confirm")
                console.log(v)
                console.log("result")
                console.log(result)
                if (v) {
                    if (!result) {
                        const t = (data.collector_change_status +
                            "") as ChangeStatus
                        this.curChangeStatus =
                            t !== ChangeStatus.已更新
                                ? t
                                : ChangeStatus.居民信息无更新
                        this.displayDialogConfirm = true
                        console.log("this.displayDialogConfirm")
                        console.log(this.displayDialogConfirm)
                        return
                    }

                    this.submit(data)
                } else {
                    this.openTooltipsOnce(obj)
                }
            })
        }

        private openTooltipsOnce(obj: any) {
            let showMessage = false
            Object.keys(obj).forEach((key) => {
                const t = obj[key][0]
                if (t && !showMessage) {
                    showMessage = true
                    this.$message.error(t.message)
                }
            })
        }

        private confirmStatus(status: any) {
            this.formBuilder?.onInput(
                { prop: "collector_change_status", needListen: true },
                status
            )

            const data = this.getFormValues()

            this.validateForm((v: boolean, obj: any) => {
                console.log("this.confirmStatus")
                console.log(v)
                if (v) {
                    this.submit(data)
                } else {
                    this.openTooltipsOnce(obj)
                }
            })
        }

        private submit(d: any) {
            const data = cloneDeep(d)
            if (!this.action) {
                console.log("缺失action")
                return
            }
            const displayForms = ((this.formBuilder as any).getForms() || [])
                ?.filter((i: any) => !i.hide)
                .map((i: any) => i.prop)

            console.log("data")
            console.log(cloneDeep(data))
            console.log("displayForms")
            console.log(displayForms)

            Object.keys(data).forEach((key) => {
                if (["collector_change_status"].includes(key)) {
                    return
                }
                if (!displayForms.includes(key)) {
                    // data[key] = "" // 置空逻辑
                    delete data[key] // 不传逻辑（暂时采用这个）
                    return
                }
                if (data[key] === 0) {
                    data[key] = "0"
                }
            })

            console.log("data", data)
            this.action
                .addInputs_parameter(data)
                .execute()
                .then((r) => {
                    if (this.curId) {
                        this.$message.success("提交修改成功！")
                        this.$emit("refresh")
                    } else {
                        this.$message.success("提交成功！")
                        this.$emit("reload")
                    }
                    this.close()
                    console.log(r)
                })
            return data
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .form-content-box {
        height: 60vh;
        overflow-y: auto;
        padding-left: 0px;
        padding-right: 15px;
        margin-top: -15px;

        // /deep/ .el-col.el-col-12

        /deep/ .el-col-12 {
            width: 100%;
            padding-right: 20px;
        }

        /deep/ .core-ui-form-tips {
            font-weight: 700;
            font-size: 18px;
            color: #000;
            padding-bottom: 10px;
            padding-left: 0px;
            padding-top: 15px;
            border-bottom: 1px solid #eee;
            margin-bottom: 15px;
            position: relative;
            padding-left: 10px;

            &::before {
                content: "";
                display: block;
                position: absolute;
                left: 0px;
                width: 5px;
                height: 16px;
                border-radius: 2px;
                top: 17px;
                background-color: rgba(87, 130, 236, 1);
            }
        }

        &.need-hide {
            /deep/ .el-col {
                &:nth-last-child(2) {
                    display: none;
                }

                &:last-child {
                    display: none;
                }
            }
        }
    }

    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .select {
        width: 320px;
    }
    .btn {
        width: 100px;
        height: 36px;
    }
    .create-position {
        ::v-deep .el-form-item__label {
            text-align: left;
            color: #555;
            font-size: 14px;
        }
    }
</style>
