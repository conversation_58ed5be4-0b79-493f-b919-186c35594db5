
<template>
    <div>
        <table-container
            v-if="tableConfig"
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
        >
            <div slot="title">任务管理</div>

            <div slot="table" class="u-p-x-20 u-p-t-20" slot-scope="{ data }">
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="viewDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn as TableColumnImpl } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { checkRole } from "@/installer/role"
    import { routesMap } from "@/router/direction"
    import { userService } from "@/service/service-user"
    import { Component } from "vue-property-decorator"
    import { columns, tableConfig } from "."
    import { ServiceTaskRow } from "../service-manage/service-detail/task"

    @Component({
        name: routesMap.groupService.taskManage,
        components: { TableContainer, CommonTable },
    })
    export default class CheckList extends BaseTableController<ServiceTaskRow> {
        private tableConfig: any = null
        private readonly columns: TableColumnImpl[] = columns

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.groupService.taskManage,
        }

        viewDetail(row: ServiceTaskRow) {
            this.$router.push({
                name: routesMap.groupService.serviceManageDetail.task,
                query: {
                    id: row.id + "",
                    from: routesMap.groupService.taskManage,
                },
            })
        }

        async mounted() {
            const t = await tableConfig()
            if (checkRole("/tablelist/user_profile_basic/manage_v3")) {
                t.preFilter = {
                    org_name: await userService.setup().then((r) => {
                        console.log("userService", JSON.parse(JSON.stringify(r)))
                        return r.label
                    }),
                }
                console.log("table", JSON.parse(JSON.stringify(t)))
            }
            this.tableConfig = t
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
