import { BuildFormConfig, FileType, FormType } from "@/core-ui/component/form"
import { getAddress } from "@/utils"

export interface InjectObj {
    uniplat_uid_calc: number
    groups: string
    friends: string
}

export function companyFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "user_profile_basic",
        sdkAction: "back_job_info_2_edit",
        id,
        forms: [
            {
                label: "就业状态:",
                type: FormType.Select,
                required: true,
                prop: "employment_status",
            },
            {
                label: "就业方式:",
                type: FormType.Select,
                required: true,
                prop: "employment_type",
            },
            {
                label: "就业地点:",
                type: FormType.Cascader,
                required: true,
                prop: "work_citys",
                option: {
                    elProps: { checkStrictly: true },
                },
            },
            {
                label: "就业单位名称:",
                type: FormType.Text,
                prop: "company_name",
            },
            {
                label: "从事行业:",
                type: FormType.Select,
                prop: "job_industry",
            },
            {
                label: "就业工种:",
                type: FormType.Select,
                required: true,
                prop: "job_type_work",
            },
            {
                label: "月工资收入:",
                type: FormType.InputNumber,
                required: true,
                prop: "salary",
                option: {
                    type: "number",
                },
            },
            {
                label: "是否有就业意愿:",
                type: FormType.Switch,
                prop: "is_job_willing",
            },
            {
                label: "择业地域意愿:",
                type: FormType.Cascader,
                required: true,
                prop: "job_city_codes",
                option: {
                    elProps: { checkStrictly: true },
                },
            },
            {
                label: "月薪要求:",
                type: FormType.Text,
                required: true,
                prop: "job_willing_salary",
            },
            {
                label: "意向行业:",
                type: FormType.Select,
                required: true,
                prop: "job_willing_industry",
                option: {
                    multiple: true,
                },
            },
            {
                label: "意向工种:",
                type: FormType.Select,
                required: true,
                prop: "job_willing_type_work",
                option: {
                    multiple: true,
                },
            },
            {
                label: "是否有创业意愿:",
                type: FormType.Switch,
                prop: "is_start_job",
            },
            {
                label: "创业意向行业:",
                type: FormType.Select,
                required: true,
                prop: "start_job_industry",
                option: {
                    multiple: true,
                },
            },
            {
                label: "创业意向工种:",
                type: FormType.Select,
                required: true,
                prop: "start_job_type_work",
                option: {
                    multiple: true,
                },
            },
            // {
            //     label: "查无此人原因:",
            //     type: FormType.Select,
            //     required: true,
            //     prop: "undefined_person_remark",
            // },
            {
                label: "简历附件:",
                type: FormType.MyUpload,
                prop: "file_url",
                option: {
                    fileType: [FileType.Pdf, FileType.Image],
                    drag: true,
                    placeholder: "",
                },
            },
        ],
    }
}

export function buildItems(rows: any | {}, vue: Vue) {
    const row = (rows || {}) as any
    console.log("row")
    console.log(row)
    const arr1 =
        row.employment_status_label === "已就业创业"
            ? [
                  {
                      label: "就业方式：",
                      value: row.employment_type_label || "",
                  },
                  { label: "就业地点：", value: row.work_city_label || "" },
                  { label: "就业单位：", value: row.company_name || "" },
                  { label: "从事行业：", value: row.job_industry_label || "" },
                  { label: "就业工种：", value: row.job_type_work_label || "" },
                  { label: "月薪收入：", value: row.salary_label || "" },
              ]
            : ([] as any)
    const arr2 =
        row.is_jobwilling_label === "是"
            ? [
                  {
                      label: "择业地域意愿：",
                      vNode: handleAddress(vue, row, [
                          "jobwilling_province",
                          "jobwilling_city",
                          "jobwilling_region",
                      ]),
                  },
                  {
                      label: "月薪要求：",
                      value: +row.jobwilling_salary || "",
                  },
                  {
                      label: "就业意向行业：",
                      value: row.job_willing_industry_label || "",
                  },
                  {
                      label: "就业意向工种：",
                      value: row.job_willing_type_work_display || "",
                      span: 16,
                  },
              ]
            : ([] as any)

    const arr3 =
        row.is_start_job_label === "是"
            ? [
                  {
                      label: "创业意向行业：",
                      value: row.start_job_industry_label || "",
                  },
                  {
                      label: "创业意向工种：",
                      value: row.start_job_type_work_label || "",
                  },
              ]
            : []
    return [
        {
            label: "就业状态：",
            value: row.employment_status_label || "",
            span: 24,
        },
        ...arr1,
        {
            label: "是否有就业意愿：",
            value: row.is_jobwilling_label || "",
        },
        ...arr2,
        {
            label: "是否有创业意愿：",
            value: row.is_start_job_label || "",
        },
        ...arr3,
        // {
        //     label: "查无此人原因：",
        //     value: row.undefined_person_remark_label || "",
        // },
        {
            label: "简历附件：",
            vNode: handleFile(vue, row.resume_label),
        },
    ].map((i) => {
        return { ...i, span: i.span || 8 }
    })
}

function handleFile(vue: Vue, value: string) {
    const h = vue.$createElement
    return h("div", {
        domProps: {
            innerHTML: value || "--",
        },
    })
}

function handleAddress(vue: Vue, row: any, arr: string[]) {
    const h = vue.$createElement
    return h(
        "span",
        {
            class: "u-line-1",
        },
        getAddress(row, arr) || "--"
    )
}

export const enum EmploymentStatus {
    已就业创业 = 1,
    未就业 = 2,
    务农 = 3,
    入伍 = 4,
    入学 = 5,
    退出农村劳动力范围 = 6,
    查无此人 = 7,
}

// 荆州的枚举，与上面一致，不区分处理了，此注释为当前正式线枚举，参考用 20240514
// const enum JzEmploymentStatus {
//     已就业创业 = "1",
//     未就业 = "2",
//     务农 = "3",
//     入伍 = "4",
//     入学 = "5",
// }
