<template>
    <div class="core-ui-table-container" :key="id">
        <div class="core-ui-custom-header">
            <!-- <div class="title">人力资源机构申请</div> -->
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="bg-white u-p-30" v-if="id">
            <form-builder ref="formBuilder" labelWidth="130px"></form-builder>
            <div class="u-flex btns">
                <el-button
                    type="primary"
                    class="custom-btn btn"
                    plain
                    @click="closeCurrentTap"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    class="u-m-l-30 custom-btn btn"
                    @click="confirm"
                >
                    提交申请
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import { Component } from "vue-property-decorator"
    import { createPositionFormSections, editCooperationFormConfig } from "."
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { userService } from "@/service/service-user"
    import { closeCurrentTap } from "@/views/pages/single-page/components/tags-view"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import { sdk } from "@/service"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"

    @Component({
        name: routesMap.company.companyManage.applyDetail,
        components: { FormBuilder },
    })
    export default class ApplyDetail extends FormController {
        private id = ""
        private key = ""
        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "企业信息",
                    to: {
                        name: routesMap.company.companyManage.manage,
                    },
                },
                {
                    label: "人力资源机构申请",
                },
            ]
            this.breadcrumbs = d
        }

        private init() {
            this.id = (this.$route.query.id as string) || "none"
            this.setBreadcrumbs()

            return pageLoading(async () => {
                let fn = editCooperationFormConfig
                if (this.id === "none") {
                    this.key = await userService.setup().then((r) => r?.key || "")
                    fn = createPositionFormSections
                }
                return buildFormSections(fn(this.key || this.id)).then((r) => {
                    this.buildForm(r.forms)
                })
            })
        }

        mounted() {
            this.init()
        }

        private closeCurrentTap() {
            closeCurrentTap({ name: routesMap.company.companyManage.manage })
        }

        private confirm() {
            this.validateForm((v: boolean) => {
                if (v) {
                    const data = this.getFormValues()
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            MessageBox.confirm("确认提交申请？", "提交申请").then(() => {
                let model = sdk.core
                    .model("xg_human_agent")
                    .action("edit_human_agent")
                if (this.id === "none") {
                    model = sdk.core.model("xg_agent").action("open_human_agent")
                }
                return pageLoading(() => {
                    return model
                        .addInputs_parameter(data)
                        .updateInitialParams({
                            selected_list: [
                                {
                                    id: this.key || this.id,
                                    v: 0,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.callRefresh("refreshCompanyManage")
                            this.$message.success("提交成功")
                            this.closeCurrentTap()
                        })
                })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .btns {
        padding-left: 135px;
        .btn {
            width: 136px;
            height: 40px;
        }
    }
    ::v-deep .upload {
        display: flex;
        align-items: center;

        .loader {
            .icon {
                margin-bottom: 0;
            }
            div {
                display: none;
            }
        }
        .upload-tip {
            flex: none;
            margin-left: 10px;
            color: #9098a6;
        }
    }
    ::v-deep .limit-0 {
        ul.el-upload-list {
            display: none;
        }
    }
    ::v-deep .limit-1 {
        div.el-upload.el-upload--picture-card {
            display: none;
        }
    }
</style>
