<template>
    <div class="container-box core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    type="primary"
                    plain
                    class="custom-btn btn"
                    @click="toEdit"
                    v-if="!isTaskWaiting"
                >
                    编辑
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn btn"
                    @click="toAudit"
                    v-if="!isShowEdit && isAuditing"
                >
                    审核
                </el-button>
            </div>
        </div>
        <div class="contain-box u-p-b-40">
            <detail-view ref="view" :row="detail"></detail-view>
            <div v-if="detail">
                <DetailTable
                    :tableColumns="tableColumns"
                    :tableRows="tableRows"
                    :rows="rows"
                    :detail="detail"
                    :isEditing="isShowEdit"
                    ref="detailTable"
                />
            </div>
            <div class="u-flex u-m-t-40 u-row-center" v-if="isShowEdit">
                <el-button
                    type="primary"
                    @click="cancel"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn btn u-m-r-30"
                    @click="toDraft"
                >
                    保存至草稿状态
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn btn u-m-0"
                    @click="toSubmit"
                >
                    提交
                </el-button>
            </div>
        </div>
        <!-- <list-view :detail="detail" v-if="detail" ref="listView" /> -->

        <AuditPop
            v-model="showChangePop"
            @refresh="init"
            :id="detail && detail.id"
        />
    </div>
</template>

<script lang="ts">
    import { Component, Vue, Watch } from "vue-property-decorator"
    import { routesMap } from "@/router/direction"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        DetailRow,
        ReportItem,
        Status,
        detailPredict,
        flattenObjectArray,
    } from "./"
    import DetailView from "./components/detail-view.vue"
    import AuditPop from "./components/audit-pop.vue"
    import DetailTable from "./components/detail-table.vue"
    import { TableColumn } from "@/core-ui/component/table"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"
    import { Status as TaskStatus } from "../work-info-apply/detail"

    @Component({
        name: routesMap.employmentManage.hrInfoManage.workInfoAuditCheck,
        components: {
            DetailRowCol,
            DetailView,
            AuditPop,
            DetailTable,
        },
    })
    export default class WorkInfoAuditCheck extends Vue {
        private detail: DetailRow | null = null

        private id = ""

        breadcrumbs: BreadcrumbItem[] = [
            {
                label: `企业用工信息审核`,
                to: {
                    name: routesMap.employmentManage.hrInfoManage.workInfoAudit,
                },
            },
            {
                label: "审核查看",
            },
        ]

        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `企业用工信息审核`,
                    to: {
                        name: routesMap.employmentManage.hrInfoManage.workInfoAudit,
                    },
                },
            ]
            if (this.from) {
                d = [...getCacheBreadcrumbsByRoutePath(this.from)]
            }
            d = [
                ...d,
                {
                    label: "审核查看",
                    to: {
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoAuditCheck,
                        query: {
                            key: this.id,
                        },
                    },
                },
            ]
            this.breadcrumbs = d
            updateTagItem({
                name: routesMap.employmentManage.hrInfoManage.workInfoAuditCheck,
                breadcrumb: d,
            })
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.employmentManage.hrInfoManage.workInfoAuditCheck,
        }

        private isShowEdit = false
        private showChangePop = false
        private isEditng = false

        private tableColumns: TableColumn[] | any[] = []
        private tableRows: any[] = []
        private rows: any[] = []

        @Watch("$route", { immediate: true })
        private onRouteChanged() {
            this.isShowEdit = false
        }

        private get title() {
            return this.$route.meta?.title || ""
        }

        private get from() {
            return this.$route.query.from as string
        }

        private get isAuditing() {
            return this.detail?.status === Status.审核中
        }

        private get isTaskWaiting() {
            return this.detail?.task_status === TaskStatus.待发布
        }

        mounted() {
            this.init()
        }

        init() {
            this.getDetail()
        }

        private getDetail() {
            if (this.id !== this.$route.query.key) {
                this.detail = null
            }

            this.id = this.$route.query.key as string
            this.setBreadcrumbs()
            const model = sdk.core
                .model("company_task_record")
                .detail(this.id, "detail_report_for_review")
            pageLoading(() => {
                return model.query().then((r) => {
                    const row = sdk.buildRow<any>(r.row, detailPredict)
                    this.detail = row
                    pageLoading(() => {
                        return Promise.all([
                            this.getColumns(),
                            this.getData(),
                        ]).then((r: any[]) => {
                            this.tableColumns = r[0].schema?.map(
                                (i: ReportItem) => {
                                    return {
                                        label: i.display_name,
                                        prop: i.p_union_code,
                                        value_type: i.value_type,
                                        showOverflowTip: true,
                                        column_type: i.type,
                                        default_value: i.default_value,
                                        computer_item: i.computer_item,
                                        limit: i.limit,
                                        text_component_type: i.text_component_type,
                                        minWidth: "150px",
                                        children:
                                            i.children?.map((j: ReportItem) => {
                                                return {
                                                    label: j.display_name,
                                                    prop: j.p_union_code,
                                                    showOverflowTip: true,
                                                    children: j.children || [],
                                                    value_type: j.value_type,
                                                    column_type: j.type,
                                                    default_value: j.default_value,
                                                    computer_item: j.computer_item,
                                                    limit: j.limit,
                                                    text_component_type:
                                                        j.text_component_type,
                                                    minWidth:
                                                        j.display_name.length > 10
                                                            ? `${
                                                                  j.display_name
                                                                      .length * 13
                                                              }px`
                                                            : "110px",
                                                }
                                            }) || [],
                                    }
                                }
                            )
                            this.rows = r[1]
                            this.tableRows = [
                                this.rows.reduce((acc: any, cur: any) => {
                                    const t = flattenObjectArray(
                                        this.tableColumns
                                    ).find(
                                        (i) => i.prop === cur.p_union_code
                                    ) as any
                                    acc[cur.p_union_code] =
                                        t?.value_type === 3
                                            ? cur.indicator_value
                                            : cur.indicator_value || ""
                                    return acc
                                }, {}),
                            ]
                        })
                    })
                    return row
                })
            })
        }

        private getColumns() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_report_indicator"
                )
                .post<{ schema: any[] }>({
                    task_id: this.detail?.task_id,
                    tg_enterprise_id: this.detail?.tg_enterprise_id,
                })
        }

        private getData() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_company_record_report_this_term"
                )
                .post<{ schema: any[] }>({
                    task_id: this.detail?.task_id,
                    tg_enterprise_id: this.detail?.tg_enterprise_id,
                })
        }

        private cancel() {
            this.init()
            this.isShowEdit = false
        }

        private toEdit() {
            this.isShowEdit = true
        }

        private toAudit() {
            this.showChangePop = true
        }

        private toDraft() {
            ;(this.$refs.detailTable as any).toDraft(6).then(() => {
                this.$message.success("保存成功")
                this.isShowEdit = false
                this.init()
                this.callRefresh(
                    routesMap.employmentManage.hrInfoManage.workInfoAudit
                )
                this.callRefresh(
                    routesMap.employmentManage.hrInfoManage.workInfoApply
                )
                this.callRefresh(
                    routesMap.employmentManage.hrInfoManage.workInfoApplyDetail
                )
            })
        }

        private toSubmit() {
            ;(this.$refs.detailTable as any).toDraft(1).then(() => {
                return sdk.core
                    .model("company_task_record")
                    .action("audit_report")
                    .addInputs_parameter({ status: "3" })
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: +(this.detail?.id || 0) }],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("审核成功")
                        this.isShowEdit = false
                        this.init()
                        this.callRefresh(
                            routesMap.employmentManage.hrInfoManage.workInfoAudit
                        )
                        this.callRefresh(
                            routesMap.employmentManage.hrInfoManage.workInfoApply
                        )
                        this.callRefresh(
                            routesMap.employmentManage.hrInfoManage
                                .workInfoApplyDetail
                        )
                    })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .contain-box {
        background-color: #fff;
        margin-bottom: 24px;
    }

    .title {
        width: 100%;
        height: 36px;
        font-size: 16px;
        background: #f8f8f8;
        padding-left: 20px;
    }

    .container-box {
        height: 100%;
    }

    .component {
        background-color: #fff;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
