import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import router from "@/router"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"
export const ModelConfig = {
    model: "xg_company_position_recommend",
    list: "manage_apply7_list",
    action1: "end_recommend",
    action2: "recommend_apply_7",
    detail: "manage_apply_7_detail",
}
export const modelTitle = "招聘会推广服务"

export const predict = {
    job_fair_title: "job_fair#title",
    job_fair_type: "job_fair#type_label",
    job_fair_access_key: "job_fair#_access_key",
    apply_region: "",
    recommend_region: "",
    status_memo: "",
    start_time: "label",
    end_time: "label",

    audit_status: "label",
    audit_time: "label",
    audit_memo: "label",
    audit_user: "auditor_name",
    status: "label",
}

export function canEnd(status: RecommendStatusMemo) {
    return [RecommendStatusMemo.推广中, RecommendStatusMemo.待推广].includes(
        status
    )
}

export function canEdit(status: RecommendStatusMemo) {
    return ![RecommendStatusMemo.已结束].includes(status)
}

export interface Row {
    job_fair_title: string
    job_fair_type: string
    job_fair_type_label: string
    job_fair_access_key: string
    status: RecommendStatus
    status_label: string
    apply_region: string
    recommend_region: string
    status_memo: RecommendStatusMemo
    start_time: string
    start_time_label: string
    end_time: string
    end_time_label: string
    id: number
    _access_key: string
    v: number
}

export enum RecommendStatus {
    申请中,
    待推广,
    推广中,
    已结束,
    取消申请,
}

export enum RecommendStatusMemo {
    申请中 = "申请中",
    待推广 = "待推广",
    推广中 = "推广中",
    已结束 = "已结束",
    取消申请 = "取消申请",
}

const tableFilter: TableFilter[] = [
    {
        label: "招聘会名称",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "招聘会类型",
        type: FormType.Select,
        prop: "type",
        option: {
            multiple: true,
        },
    },
    {
        label: "推广开始时间",
        type: FormType.DatePicker,
        prop: "start_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "推广结束时间",
        type: FormType.DatePicker,
        prop: "end_time",
        option: {
            type: "daterange",
        },
    },
]
const column: TableColumn[] = [
    {
        label: "招聘会名称",
        prop: "job_fair_title",
        showOverflowTip: true,
        minWidth: "200",
        render: (h, row: Row) =>
            h(
                "span",
                {
                    on: {
                        click: () => {
                            router.push({
                                name: routesMap.recruit.jobFairDetail,
                                query: {
                                    id: row.job_fair_access_key,
                                },
                            })
                        },
                    },
                    class: "pointer primary",
                },
                row.job_fair_title
            ),
    },
    {
        label: "招聘会类型",
        prop: "job_fair_type_label",
        showOverflowTip: true,
        minWidth: "100",
    },
    {
        label: "政策状态",
        prop: "status_label",
        showOverflowTip: true,
        minWidth: "100",
    },
    {
        label: "推广区域",
        prop: "recommend_region",
        showOverflowTip: true,
        minWidth: "220",
    },
    {
        label: "推广状态",
        prop: "status_memo",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "推广时间",
        prop: "apply_time_label",
        minWidth: "200",
        showOverflowTip: true,
        formatter: (row: Row) =>
            row.start_time_label + " ~ " + row.end_time_label,
    },
    {
        label: "操作",
        prop: "h",
        width: "110",
        fixed: "right",
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model(ModelConfig.model).list(ModelConfig.list),
        filter: tableFilter,
        oneTabFilter: true,
        defaultPageSize: 10,
        predict,
        column,
    }
}
