import { config, EnvProject } from "@/config"
import { FileType, FormItem, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { formatDate } from "@/core-ui/helpers/tools"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"

export const tableFilter: TableFilter[] = [
    {
        label: "AppID",
        type: FormType.Text,
        prop: "app_id",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "创建时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "客户端名称",
        type: FormType.Text,
        prop: "app_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "客户端主体",
        type: FormType.Text,
        prop: "app_main_part",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "管理员信息",
        type: FormType.Text,
        prop: "manager_wx",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "是否上线",
        type: FormType.Select,
        prop: "status",
    },
]

export const listredict = {
    id: "",
    app_id: "",
    app_name: "",
    app_main_part: "",
    manager_wx: "",
    manager_mobile: "",
    create_time: "",
    status: "label",
    type: "label",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("g_wx_app").list("manager"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: listredict,
    }
}

export interface Row {
    id: number
    v: number
    app_id: string
    app_name: string
    app_main_part: string
    manager_wx: string
    manager_mobile: string
    create_time: string
    status: Status
    status_label: string
    type: ClientType
    type_label: string
}

export const columns: TableColumn[] = [
    {
        label: "AppID",
        prop: "app_id",
        showOverflowTip: true,
        minWidth: "180px",
    },
    {
        label: "客户端名称",
        prop: "app_name",
        showOverflowTip: true,
        minWidth: "200px",
    },
    {
        label: "客户端类型",
        prop: "type_label",
        minWidth: "100px",
    },
    {
        label: "客户端主体",
        prop: "app_main_part",
        showOverflowTip: true,
        minWidth: "140px",
    },
    {
        label: "客服信息",
        prop: "organizationName",
        showOverflowTip: true,
        render(h, row) {
            return h("div", { class: "u-flex-col" }, [
                h("span", row.manager_wx),
                // h("span", row.manager_mobile),
                renDesensitizationView(h, {
                    value: row.manager_mobile,
                }),
            ])
        },
        minWidth: "160px",
    },
    {
        label: "创建时间",
        prop: "create_time",
        showOverflowTip: true,
        render(h, row) {
            return h(
                "span",
                {},
                formatDate(row.create_time, "yyyy.MM.dd") || "-"
            )
        },
    },
    {
        label: "是否已上线",
        prop: "status_label",
        showOverflowTip: true,
        width: "120px",
    },
    {
        label: "操作",
        prop: "h",
        minWidth: "100px",
        fixed: "right",
    },
]

export enum Status {
    未上线,
    已上线,
}

export enum ClientType {
    pc = "0",
    mobile = "1",
}

export interface DetailRow {
    app_id: string
    app_name: string
    type_label: string
    cs_images: string
    app_main_part: string
    manager_wx: string
    manager_mobile: string
    status: Status
    status_label: string
    display_name: string
    guide_images: string
    id: string
    v: number
    type: ClientType
    manager_name: string
}

export function getEditForm(): FormItem[] {
    return [
        {
            label: "客户端类型",
            type: FormType.Select,
            prop: "type",
            needListen: true,
        },
        {
            label: "是否上线",
            type: FormType.Radio,
            prop: "status",
        },
        {
            label: "AppID",
            type: FormType.Text,
            prop: "app_id",
            option: {
                placeholder: "请输入AppID，确保正确",
            },
        },
        {
            label: "客户端名称",
            type: FormType.Text,
            prop: "app_name",
            option: {
                placeholder: "请输入客户端名称",
            },
        },
        {
            label: "客户端主体",
            type: FormType.Text,
            prop: "app_main_part",
            option: {
                placeholder: "请输入客户端的申请主体",
            },
        },
        {
            label: "客服姓名",
            type: FormType.Text,
            prop: "manager_name",
        },
        {
            label: "客服微信号",
            type: FormType.Text,
            prop: "manager_wx",
        },
        {
            label: "客服手机号",
            type: FormType.Text,
            prop: "manager_mobile",
        },

        {
            label: "客服二维码",
            type: FormType.MyUpload,
            option: {
                fileType: [FileType.Image],
                listType: "picture-card",
                placeholder: "",
                limit: 1,
            },
            prop: "cs_images",
        },
        {
            label: "首页显示名称",
            type: FormType.Text,
            prop: "display_name",
            option: {
                placeholder: "请输入名称，用于客户端首页标题显示，限8个字",
                limit: 8,
            },
            handlerDisplay: (data) => {
                return data.type === ClientType.mobile
            },
        },
        {
            label: "注册引导图",
            type: FormType.MyUpload,
            option: {
                fileType: [FileType.Image],
                listType: "picture-card",
                placeholder: "",
                limit: 1,
            },
            handlerDisplay: (data) => {
                return data.type === ClientType.mobile
            },
            prop: "guide_images",
        },
    ]
}

export const enum ActionType {
    业务应用,
    小工具,
    // 移除
    推荐社群,
    广告位,
    // 未使用
    弹窗,
    小程序浏览配置,
}

export const actionConfig = [
    {
        label: "业务应用",
        route: routesMap.base.mp.business,
        type: ActionType.业务应用,
    },
    {
        label: "小工具",
        route: routesMap.base.mp.tools,
        type: ActionType.小工具,
    },
    // {
    //     label: "推荐社群",
    //     route: routesMap.base.mp.groupEntrance,
    //     type: ActionType.推荐社群,
    // },
    {
        label: "广告位管理",
        route: routesMap.base.mp.advertisingPosition,
        type: ActionType.广告位,
    },
    {
        label: "小程序浏览配置",
        route: routesMap.base.mp.mpRoleManage,
        type: ActionType.小程序浏览配置,
        hide: true,
        showKeyByPage: "app_browse",
    },
].filter((item) => {
    if (item.type === ActionType.推荐社群) {
        return [EnvProject.咸丰项目].includes(config.envProject)
    }
    return true
})

export const enum EntranceActionName {
    BusinessApp = "app_entrance_insert",
    Tools = "tool_entrance_insert",
    Group = "community_entrance_insert",
    Advertising = "announce_entrance_insert",
    Popup = "popup_entrance_insert",
}
