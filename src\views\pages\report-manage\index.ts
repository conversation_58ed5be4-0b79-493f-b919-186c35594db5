import { TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"

export const enum Status {
    未开始 = 0,
    进行中 = 1,
    已完成 = 2,
}

export const enum FillInStatus {
    未填报 = -1,
    待填报 = 0,
    已完成 = 1,
}

export interface Row {
    name: string
    description: string
    start_date: string
    end_date: string
    finish_num: number
    target_num: string
    status: Status
    status_label: string
    _access_key: string
    id: number
    v: number
}

export interface IndicatorDataRows {
    id: string
    indicator_data: any
    region_code: string
    region_name: string
    status: Status
}

export interface IndicatorColumn {
    display_name: string
    union_code: string
    value_type?: string
    description?: string
    p_union_code?: string
    children?: IndicatorColumn[]
}

export interface ListDetailMeta {
    id: string
    indicator_meta: IndicatorColumn[]
    name: string
}

export const enum DetailItemType {
    Single = 1,
    Multiple,
}
export interface DetailRow {
    type: DetailItemType
    task_id: string
    region_name: string
    id: number
    v: number
    name?: string
}

export function detailTableConfig(id: string): TableConfig {
    return {
        model: sdk.core
            .model("xg_indicator_task_instance_ref")
            .list("for_task"),
        defaultPageSize: 10,
        predict: {
            indicator_data: "",
            region_name: "sys_region#region_name",
            isEdit: false,
        },
        preFilter: {
            task_id: id,
        },
    }
}

export const primaryRows = [
    "填写须知：",
    "1、红色字段为填写说明；",
    "2、请勿修改表头和填写说明；",
]
