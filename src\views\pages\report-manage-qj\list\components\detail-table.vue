<template>
    <div>
        <div class="fill-box">
            <div class="core-ui-table-container">
                <div class="d-flex flex-column table-tabs">
                    <el-tabs v-model="currentPageName" @tab-click="handleClick">
                        <el-tab-pane
                            v-for="item in tabs"
                            :key="item.label"
                            :label="item.label"
                            :name="item.name"
                        >
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <div v-if="groupId" class="bg-white u-p-b-20" v-loading="loading">
                <table-container
                    v-if="tableConfig"
                    filedWidth="200"
                    ref="table"
                    v-model="tableConfig"
                    class="container"
                    :showExpand="false"
                >
                    <div slot="table" slot-scope="{ data }" class="u-p-20">
                        <common-table :data="data" :columns="columns">
                            <div
                                slot="h"
                                class="u-flex u-row-center"
                                slot-scope="scope"
                            >
                                <el-button
                                    type="text"
                                    @click="toEdit(scope.row)"
                                >
                                    编辑
                                </el-button>
                                <el-button
                                    type="text"
                                    @click="toDelete(scope.row)"
                                >
                                    删除
                                </el-button>
                            </div>
                        </common-table>
                    </div>
                </table-container>
                <AddTitlePop
                    v-model="showAddTitlePop"
                    :id="rowId"
                    :modelName="addModelName"
                    :actionName="actionName"
                    :groupId="groupId"
                    @refresh="refresh"
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { buildConfig4RemoteMeta } from "@/views/pages/collect-task-manage/components/build-table"
    import { MessageBox } from "element-ui"
    import { pageLoading } from "@/views/controller"
    import { sdk } from "@/service"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { DetailListRow, columns } from "../../../report-manage/list/index"
    import { columns2 } from "../index"
    import AddTitlePop from "./add-title-pop.vue"

    @Component({
        components: { TableContainer, CommonTable, AddTitlePop },
    })
    export default class DetailTable extends BaseTableController<DetailListRow> {
        tableConfig: TableConfig | null = null

        @Prop({ default: "" })
        private readonly groupId!: string

        @Prop({ default: "" })
        private readonly reportId!: string

        private showAddTitlePop = false

        private columns: TableColumn[] = columns

        protected rowId: string | any = ""

        private currentPageName = "indicator_name_dict"
        private listName = "list_in_group"
        private actionName = ""
        private prefilters: any = {}
        private loading = false

        private get tabs() {
            return [
                {
                    label: "横行",
                    name: "indicator_name_dict",
                },
                {
                    label: "纵列",
                    name: "xg_indicator_name_y_dict",
                },
            ]
        }

        private get addModelName() {
            return this.currentPageName
        }

        created() {
            this.init()
        }

        refresh() {
            this.reloadList()
        }

        private init(prefilters?: any) {
            const buildPrefilters = prefilters || { group_id: this.groupId }
            return buildConfig4RemoteMeta(this.currentPageName, this.listName, {
                prefilters: buildPrefilters,
                useLabelWidth: true,
                disabledFilter: true,
                optColumn: {
                    label: "操作",
                    prop: "h",
                    fixed: "right",
                    minWidth: "150px",
                },
            })
                .then((r) => {
                    this.buildConfig(r)
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            tableConfig.predict = {
                ...r.tableConfig.predict,
                parent_display_name: "p_indicator_dict#display_name",
                actions: "actions",
            }
            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
        }

        toAdd() {
            this.rowId = ""
            this.actionName = "insert"
            this.showAddTitlePop = true
        }

        private toEdit(row: DetailListRow) {
            this.rowId = row.id
            this.actionName = "update"
            this.showAddTitlePop = true
        }

        private toDelete(row: DetailListRow) {
            MessageBox.confirm(`确认删除？`, "删除").then(() => {
                return pageLoading(() => {
                    return sdk.core
                        .model(this.addModelName)
                        .action("delete")
                        .updateInitialParams({
                            selected_list: [{ id: row.id, v: 0 }],
                            prefilters: [
                                {
                                    property:
                                        this.currentPageName ===
                                        "xg_indicator_name_y_dict"
                                            ? "group_ref_id"
                                            : "group_id",
                                    value: this.groupId,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success("删除成功")
                            this.refresh()
                        })
                })
            })
        }

        private handleClick() {
            this.loading = true
            if (this.currentPageName === "xg_indicator_name_y_dict") {
                this.listName = "for_operate_list"
                this.columns = columns2
                return this.init({ group_ref_id: this.groupId })
            }
            this.listName = "list_in_group"
            this.columns = columns
            return this.init()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
</style>
