<template>
    <Container>
        <div class="home-container flex-fill" v-if="show">
            <list1 v-if="curList === 0"></list1>
        </div>
    </Container>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { Component, Vue } from "vue-property-decorator"
    import Container from "../common/container.vue"
    import List1 from "./components/返乡人员.vue"

    @Component({
        name: routesMap.bigScreen.smartEmploymentMonitor.list1,
        components: {
            Container,
            List1,
        },
    })
    export default class Template extends Vue {
        private curList = 0
        private show = false

        private get type(): string {
            return (this.$route.query?.type as string) || ""
        }

        mounted() {
            if (this.type === "返乡总人数") {
                this.curList = 0
            }
            this.show = true
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .home-container {
        width: 1860px;
        height: 935px;
        background: rgba(16, 66, 180, 0.6);
        border-radius: 16px 16px 16px 16px;
        margin: auto;
        position: fixed;
        left: 0;
        right: 0;
        top: 120px;
        padding: 28px 20px;
    }
    /deep/ td {
        height: 62px;
    }
</style>
