const config = {
    局方端: {
        bannerImg: `/img/wuhanshucai/banner.png`,
        bannerStyle: {
            maxWidth: "650px",
            minWidth: "477px",
            marginTop: "50px",
            position: "relative",
            "z-index": "9",
            "margin-right": "20px",
        },
        extraImgs: [
            {
                bgImg: `/img/wuhanshucai/bg-img1.png`,
                bgImgStyle: {
                    height: "307px",
                    width: "251px",
                    position: "absolute",
                    right: "0px",
                    top: "0px",
                },
            },
            {
                bgImg: `/img/wuhanshucai/bg-img2.png`,
                bgImgStyle: {
                    height: "278px",
                    width: "189px",
                    position: "absolute",
                    left: "0px",
                    top: "8%",
                },
            },
            {
                bgImg: `/img/wuhanshucai/bg-img3.png`,
                bgImgStyle: {
                    height: "372px",
                    width: "376px",
                    position: "absolute",
                    right: "0px",
                    bottom: "0px",
                },
            },
        ],

        theme: "#5782EC",
    },
    网格员端: {
        bannerImg: `img/common-login-grid/wangge.webp`,
        bannerStyle: {
            maxWidth: "875px",
            minWidth: "477px",
            marginTop: "145px",
            position: "relative",
            "z-index": "9",
            "margin-right": "20px",
        },
        extraImgs: [
            {
                bgImg: `img/common-login-grid/bg-img1.png`,
                bgImgStyle: {
                    height: "307px",
                    width: "251px",
                    position: "absolute",
                    left: "0px",
                    top: "8%",
                },
            },
            {
                bgImg: `img/common-login-grid/bg-img2.png`,
                bgImgStyle: {
                    height: "278px",
                    width: "189px",
                    position: "absolute",
                    right: "0px",
                    top: "0px",
                },
            },
            {
                bgImg: `img/common-login-grid/bg-img3.png`,
                bgImgStyle: {
                    height: "372px",
                    width: "376px",
                    position: "absolute",
                    right: "0px",
                    bottom: "0px",
                },
            },
        ],
        theme: "#5782EC",
    },
}

function stringifyValue(config) {
    Object.keys(config).forEach((i) => {
        config[i] = JSON.stringify(config[i])
    })
    return config
}

module.exports = {
    config: { ...stringifyValue(config) },
}
