<template>
    <div>
        <table-container
            filedWidth="250"
            ref="table"
            v-model="tableConfig"
            :displayLeftTree="true"
            class="container"
            @tabName="getTabName"
        >
            <div slot="title">
                <div class="d-flex-item-center bold">
                    <bread-crumb :backRoute="false" :items="breadcrumbs" />
                </div>
            </div>
            <div slot="header-right">
                <el-button type="primary" @click="batchEdit" v-if="showAction">
                    批量修改设备状态
                </el-button>
                <el-button
                    type="primary"
                    plain
                    @click="batchDelete"
                    v-if="showAction"
                >
                    批量删除
                </el-button>
            </div>

            <div slot="table" class="u-p-x-20 u-p-t-20" slot-scope="{ data }">
                <common-table
                    :data="data"
                    :columns="tableConfig.column"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div
                        slot="status_label"
                        slot-scope="scope"
                        class="status"
                        :class="'status-' + scope.row.status"
                    >
                        {{ scope.row.status_label }}
                    </div>
                    <div
                        slot="status_now_label"
                        slot-scope="scope"
                        class="status"
                        :class="'status-' + scope.row.status_now"
                    >
                        {{ scope.row.status_now_label }}
                    </div>
                    <div slot="h" slot-scope="scope">
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button
                            type="text"
                            @click="changeStatus(scope.row)"
                            v-if="showActionRow(scope.row)"
                        >
                            {{ actionLabel(scope.row.status) }}
                        </el-button>
                        <el-button
                            type="text"
                            @click="deleteRow(scope.row)"
                            v-if="showActionRow(scope.row)"
                        >
                            删除
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <BatchEditPop
            v-model="showBatchEditPop"
            :rowId="checkEdIds"
            :selectList="selected"
            @refresh="reloadList"
        ></BatchEditPop>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { MessageBox } from "element-ui"
    import { Component } from "vue-property-decorator"
    import { Row, tableConfig, Status, batchDelete } from "./"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import BatchEditPop from "./components/batch-edit-pop.vue"
    import { pageLoading } from "@/views/controller"
    import { DeviceType } from "./detail"

    @Component({
        name: routesMap.equipmentManage.hardwareManage,
        components: { TableContainer, CommonTable, BatchEditPop },
    })
    export default class Index extends BaseTableController<Row> {
        private tableConfig = tableConfig()

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.equipmentManage.hardwareManage,
        }

        private checkEdIds: Array<number | string> = []
        private selected: {
            id: string
            v: number
        }[] = []

        private showBatchEditPop = false
        private deviceType = DeviceType

        private tabName = ""

        private get showAction() {
            return (
                this.tabName !== "就业小知" &&
                this.tabName !== "智能移动公共服务工作站"
            )
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "硬件设备管理",
                    to: {
                        name: routesMap.equipmentManage.hardwareManage,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.equipmentManage.hardwareManage,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        created() {
            this.setBreadcrumbs()
        }

        private handleSelectionChange(d: { ids: string[]; rows: any[] }) {
            this.checkEdIds = d.ids
            this.selected = d.rows
        }

        private actionLabel(status: Status) {
            return status === Status.启用 ? "停用" : "启用"
        }

        add() {
            this.rowId = 0
            this.showDetail = true
        }

        deleteRow(row: Row) {
            MessageBox.confirm(`请确认删除${row.device_name}？`, "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        instance.confirmButtonLoading = true
                        return pageLoading(() => {
                            return sdk.core
                                .model("device_base_info")
                                .action("delete_device")
                                .updateInitialParams({
                                    selected_list: [{ id: row.id, v: 0 }],
                                })
                                .execute()
                                .then(() => {
                                    done()
                                    this.reloadList()
                                })
                                .finally(() => {
                                    instance.confirmButtonLoading = false
                                })
                        })
                    } else {
                        done()
                    }
                },
            })
        }

        batchEdit() {
            if (!this.selected.length) {
                return this.$message.warning("请选择数据")
            }
            this.showBatchEditPop = true
        }

        changeStatus(row: Row) {
            MessageBox.confirm(`请确认${this.actionLabel(row.status)}？`, "提示", {
                beforeClose: (action, instance, done) => {
                    if (action === "confirm") {
                        const actionName =
                            row.status === Status.启用 ? "disable" : "enable"
                        instance.confirmButtonLoading = true
                        sdk.core
                            .model("device_base_info")
                            .action(actionName)
                            .addInputs_parameter({
                                status: row.status === 0 ? "1" : "0",
                            })
                            .updateInitialParams({
                                selected_list: [{ id: row.id, v: 0 }],
                            })
                            .execute()
                            .then(() => {
                                done()
                                this.reloadList()
                            })
                            .finally(() => {
                                instance.confirmButtonLoading = false
                            })
                    }
                    done()
                },
            })
        }

        private batchDelete() {
            batchDelete(this.checkEdIds, this.selected).then(() => {
                this.reloadList()
            })
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.equipmentManage.hardwareManageDetail,
                query: {
                    id: row.access_key,
                    from: this.$route.name,
                },
            })
        }

        private getTabName(name: string) {
            this.tabName = name
        }

        private showActionRow(row: Row) {
            return (
                +row.device_type !== DeviceType.就业小知 &&
                +row.device_type !== DeviceType.智能移动公共服务工作站
            )
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    /deep/.filter-container-form {
        .field {
            display: flex;
            align-items: center;
        }
    }
    .status {
        color: #e87005;
        &.status-0 {
            color: #d0021b;
        }
        &.status-1 {
            color: #22bd7a;
        }
    }
</style>
