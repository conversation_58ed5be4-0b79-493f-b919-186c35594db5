<template>
    <Card label="春风行动人岗匹配数据统计">
        <div class="stats-grid">
            <div
                v-for="(stat, index) in items"
                :key="index"
                class="stat-hexagon"
            >
                <template v-if="index !== 3">
                    <div class="stat-label">{{ stat.label }}</div>
                    <div class="stat-value">
                        <number-scroll
                            :value="stat.value"
                            :digit-height="36"
                            :digitWidth="18"
                            :size="36"
                            :duration="1500"
                        />
                        <div class="unit" v-if="stat.unit">{{ stat.unit }}</div>
                    </div>
                </template>
                <template v-else>
                    <div class="stat-label">{{ stat.label }}</div>
                    <div class="stat-value-custom">
                        <span>{{ getInfo(stat.value, 0) }}</span>
                        <span>:</span>
                        <span>{{ getInfo(stat.value, 1) }}</span>
                    </div>
                </template>
            </div>
        </div>
    </Card>
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import Card from "../../common/card.vue"
    import NumberScroll from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/number-scroll.vue"

    @Component({ components: { Card, NumberScroll } })
    export default class Template extends Vue {
        @Prop({ default: {} })
        private info!: any

        @Watch("info", { immediate: true })
        private onInfoChange() {
            if (!this.info) {
                return
            }
            this.items = this.buildInfo()
        }

        private items = this.buildInfo()

        private getInfo(value: string, index: number) {
            const t = value.replace("：", ":").split(":")
            return t[index] || 0
        }

        private buildInfo() {
            return [
                {
                    label: "招聘企业",
                    value: this.info["招聘企业"] || 0,
                    unit: "",
                },
                {
                    label: "招聘岗位",
                    value: this.info["招聘岗位"] || 0,
                    unit: "",
                },
                {
                    label: "岗位类型",
                    value: this.info["岗位类型"] || 0,
                    unit: "",
                },
                { label: "人岗比", value: this.info["人岗比"], unit: "" },
                {
                    label: "参与总人数",
                    value: this.info["参与总人数"] || 0,
                    unit: "",
                },
                {
                    label: "线上参与",
                    value: this.info["线上参与"] || 0,
                    unit: "",
                },
                {
                    label: "线下参与",
                    value: this.info["线下参与"] || 0,
                    unit: "",
                },
                // {
                //     label: "招聘会\n浏览人次",
                //     value: this.info["招聘会浏览人次"] || 0,
                //     unit: "",
                // },
                {
                    label: "岗位\n浏览人次",
                    value: this.info["岗位浏览人次"] || 0,
                    unit: "",
                },
                {
                    label: "投递人次",
                    value: this.info["投递人次"] || 0,
                    unit: "",
                },
            ]
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 944px;
        height: 362px;
        /deep/.label {
            text-align: center;
        }
    }

    .stats-grid {
        display: flex;
        gap: 20px 18px;
        flex-wrap: wrap;
        justify-content: center;

        .stat-hexagon {
            width: 110px;
            height: 126px;
            background-image: url("../../assets/home-hexagon.png");
            background-size: 110px 126px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 1px;

            &:nth-child(4) {
                background-image: none;
            }

            .stat-label {
                font-size: 20px;
                color: #89b9ff;
                line-height: 23px;
                text-align: center;
                white-space: pre-wrap;
            }

            .stat-value {
                color: #fdc850;
                display: flex;
                align-items: flex-end;
                justify-content: center;

                .unit {
                    font-size: 14px;
                    margin-left: 2px;
                    position: relative;
                    top: -3px;
                }

                /deep/ .number-scroll-container {
                    font-weight: 400;
                }
            }

            .stat-value-custom {
                font-size: 36px;
                color: #fdc850;
                line-height: 42px;
                margin-top: 13px;
                display: flex;
                font-weight: 600;
                gap: 8px;
            }
        }
    }
</style>
