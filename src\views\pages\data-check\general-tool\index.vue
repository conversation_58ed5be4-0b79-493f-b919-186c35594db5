<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            :alwaysShowPageIndex="false"
        >
            <div slot="title" class="d-flex-item-center bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <div class="item-table">
                    <div v-for="item in data" :key="item.id" class="item">
                        <div class="item-title u-line-1">{{ item.name }}</div>
                        <div class="u-p-x-20 content w-100">
                            <div class="descreption">
                                {{ item.note }}
                            </div>

                            <div class="top u-flex w-100 u-col-center">
                                <div class="u-flex u-flex-1 u-row-around">
                                    <div>
                                        <div class="num">
                                            {{ item.source_count || 0 }}个
                                        </div>
                                        <div class="num-text">关联数据库</div>
                                    </div>
                                    <div>
                                        <div class="num">
                                            {{ item.task_count || 0 }}个
                                        </div>
                                        <div class="num-text">下属核查任务</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bottom u-flex">
                            <div class="btn detail" @click="toDetail(item)">
                                查看详情
                            </div>
                            <div class="btn" @click="toAdd(item)">
                                创建核查任务
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="!data.length" class="color-8 u-flex u-row-center">
                    暂无数据
                </div>
            </div>
        </table-container>
        <AddPop :id="itemId" :policyId="policyId" v-model="showAddPop" />
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { Component } from "vue-property-decorator"
    import { updateTagItem } from "../../single-page/components/tags-view"
    import { buildConfig4RemoteMeta } from "../../collect-task-manage/components/build-table"
    import AddPop from "./components/add-pop.vue"
    import { Row } from "./index"
import { MessageBox } from "element-ui"

    @Component({
        name: routesMap.dataCheck.generalToolIndex,
        components: { TableContainer, CommonTable, AddPop },
    })
    export default class Template extends BaseTableController<Row> {
        tableConfig: TableConfig | null = null

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.dataCheck.generalToolIndex,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "通用数据核查工具",
                    to: {
                        name: routesMap.dataCheck.generalToolIndex,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.dataCheck.generalToolIndex,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private showAddPop = false
        private itemId = 0
        private policyId = ""

        private get from() {
            return this.$route.query.from as string
        }

        toDetail(row: Row) {
            this.$router.push({
                name: routesMap.dataCheck.generalToolDetail,
                query: { id: row._access_key + "", from: this.$route.name },
            })
        }

        toAdd(item: Row) {
            if (!item.is_create_task) {
                return MessageBox.alert(
                `核查关联数据源和规则在调整中，暂停创建功能。`,
                "提示",
                {
                    closeOnClickModal: false,
                    showClose: false,
                }
            )
            }
            this.itemId = item.id
            this.policyId = item.policy_id
            this.showAddPop = true
        }

        mounted() {
            this.init()
        }

        private init() {
            this.setBreadcrumbs()
            pageLoading(() => {
                return buildConfig4RemoteMeta("common_check_tool", "back_list", {
                    disabledOpt: false,
                    disabledFilter: true,
                    useLabelWidth: true,
                    // optColumn: {
                    //     label: "操作",
                    //     prop: "h",
                    //     fixed: "right",
                    //     minWidth: "120px",
                    // },
                    // customLabelWidths: {
                    //     任务名称: 270,
                    //     所属区域: 360,
                    //     采集模板: 170,
                    //     任务起止时间: 200,
                    // },
                    useTabs: true,
                    useRowFieldGroups: true,
                }).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            tableConfig.predict = {
                ...r.tableConfig.predict,
                policy_id: "",
                is_create_task: false,
                actions: "actions",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .date-picker {
        height: 40px;
        /deep/ .el-date-editor {
            width: 100px !important;
            .el-input__icon {
                color: #077aec !important;
            }
            .el-input__inner {
                border-color: #077aec;
                color: #077aec !important;
                line-height: 36px !important;
                height: 36px !important;
            }
        }
    }
    /deep/ .filter-container-out {
        display: none;
    }
    .item-table {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        .item {
            height: 300px;
            border-radius: 6px;
            border: 1px solid #c7d0d9;
            overflow: hidden;
            position: relative;
            padding-bottom: 50px;
            .item-title {
                padding: 0 20px;
                line-height: 50px;
                font-size: 16px;
                background: #ecf2ff;
                color: #154cc0;
                font-weight: 600;
            }
            .num {
                font-weight: 600;
                font-size: 20px;
                color: #00a25c;
                line-height: 20px;
                margin-bottom: 10px;
                text-align: center;
                &.un {
                    color: #f05246;
                }
            }
            .num-text {
                color: #7998b8;
                text-align: center;
            }

            .content {
                overflow: scroll;
                height: 198px;
                .descreption {
                    color: #555555;
                    line-height: 24px;
                    padding: 20px 0;
                }
            }
            .top {
                height: 83px;
            }
            .bottom {
                height: 49px;
                border-top: 1px solid #f0f0f0;
                color: #5782ec;
                font-size: 14px;
                position: absolute;
                bottom: 0;
                width: 100%;
                background: #fff;
                z-index: 1;
                .btn {
                    width: 50%;
                    text-align: center;
                    line-height: 49px;
                    cursor: pointer;
                    &.detail {
                        border-right: 1px solid #f0f0f0;
                    }
                }
            }
        }
    }
</style>
