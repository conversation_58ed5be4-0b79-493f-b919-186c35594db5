import {
    Table<PERSON>olumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { FormType } from "@/core-ui/component/form"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"
import { config, EnvProject } from "@/config"
import { ExcelGenerator } from "@/utils/excel-generator"
import { get, map } from "lodash"
import { pageLoading } from "@/views/controller"

export const queryParamsKey = "policy-apply-record-params-key"

export const enum Status {
    待提交 = -1,
    已提交审核 = 0,
    通过 = 1,
    不通过 = 2,
}

export interface Row {
    /** 政策名称 */
    policy_name: string

    /** 处理人 */
    real_name: number

    /** 申报时间 */
    create_time: string

    /** 联系方式 */
    contact_mobile: string

    /** 处理时间 */
    dealer_time: number

    /** 状态 */
    status: Status

    /** 状态[文本] */
    status_label: string
    serve_target_type: ServeTargetType
    form_access_key: string
    profile_access_key: string
    agent_access_key: string
    contact_person: string
    id: number
    v: number
}

const tableFilter: TableFilter[] = [
    {
        label: "政策名称",
        type: FormType.Text,
        prop: "policy_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "申报人",
        type: FormType.Text,
        prop: "contact_person",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "状态",
        type: FormType.Select,
        prop: "status",
    },
    {
        label: "申报企业",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "处理人",
        type: FormType.Text,
        prop: "dealer.real_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "申报时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "经办单位",
        type: FormType.Cascader,
        prop: "xg_org_id",
        option: { elProps: { checkStrictly: true } },
        hide: config.envProject !== EnvProject.荆州项目,
    },
]

const commonColumn: TableColumn[] = [
    {
        label: "申报时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "contact_mobile",
        showOverflowTip: true,
        minWidth: "150px",
    },
    {
        label: "经办单位",
        prop: "organization_name",
        width: "120px",
        showOverflowTip: true,
        hide: config.envProject !== EnvProject.荆州项目,
    },
    {
        label: "处理人",
        prop: "dealer_name",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "处理时间",
        prop: "dealer_time_label",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "状态",
        prop: "status_label",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        showOverflowTip: true,
    },
]

export const columns1: TableColumn[] = [
    {
        label: "序号",
        prop: "order",
        showOverflowTip: true,
    },
    {
        label: "政策名称",
        prop: "policy_name",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "申报人",
        prop: "real_name",
        showOverflowTip: true,
    },
    ...commonColumn,
]

export const columns2: TableColumn[] = [
    {
        label: "序号",
        prop: "order",
        showOverflowTip: true,
    },
    {
        label: "政策名称",
        prop: "policy_name",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "申报人",
        prop: "real_name_company",
        showOverflowTip: true,
    },
    {
        label: "申报企业",
        prop: "agent_name",
        showOverflowTip: true,
        minWidth: "120px",
    },
    ...commonColumn,
]

export const predict = {
    policy_name: "form#policy_name",
    real_name: "user#real_name",
    real_name_company: "creator#real_name",
    agent_name: "agent#agent_name",
    create_time: "label",
    contact_mobile: "",
    dealer_name: "dealer#real_name",
    dealer_time: "label",
    status: "label",
    serve_target_type: "form#serve_target_type",
    form_access_key: "form#_access_key",
    profile_access_key: "profile#_access_key",
    agent_access_key: "agent#_access_key",
    contact_person: "",
    showMobile: false,
    xg_org_id: "label",
    organization_name: "organization#name",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("policy_form_apply").list("apply_manage"),
        filter: tableFilter,
        defaultPageSize: 10,
        column: columns1,
        predict,
    }
}

export const enum ServeTargetType {
    居民 = 1,
    企业 = 2,
}

export const serviceTypeMapping = new Map<ServeTargetType, string>([
    [ServeTargetType.居民, "居民"],
    [ServeTargetType.企业, "企业"],
])

const enum RegResidenceProperty {
    农业户口 = 1,
    非农业户口 = 2,
    未知 = 0,
}

const enum PoliticalOutlook {
    "中共党员/预备党员" = 1,
    共青团员 = 2,
    群众 = 3,
    其他 = 4,
}

const enum Education {
    博士研究生 = 1,
    硕士研究生 = 2,
    大学本科 = 3,
    大学专科 = 4,
    中等专科 = 5,
    职业高中 = 6,
    技工学校 = 7,
    普通高中 = 8,
    初中 = 9,
    小学 = 10,
    其他 = 11,
    不限学历 = 0,
}

export interface DetailRow {
    policy_name: string
    real_name: string
    apply_memo: string
    create_time: string
    create_time_label: string
    contact_mobile: string
    attachments: string
    name: string
    id_card: string
    id_card_hide: string
    mobile: string
    getAge: string
    reg_residence_property: RegResidenceProperty
    reg_residence_property_label: string
    political_outlook: PoliticalOutlook
    political_outlook_label: string
    region_name: string
    education: Education
    education_label: string
    household_province: string
    household_city: string
    household_area: string
    permanent_province: string
    permanent_city: string
    permanent_area: string
    agent_name: string
    company_code: string
    legal_person: string
    legal_card_open_id: string
    legal_card_open_id_hide: string
    industory_catalog: string
    industory_catalog_label: string
    province_region_company: string
    city_region_company: string
    area_region_company: string
    address_detail: string
    reg_origin: string
    pay_amount: number
    oper_status: string
    found_time: string
    reg_authority: string
    profile_access_key: string
    agent_access_key: string
    /** 状态 */
    status: Status
    form_id: string
    form_questionnaire_id: string
    industory_catalog_display: string
    audit_memo: string
    organization_name: string

    /** 状态[文本] */
    status_label: string
    id: number
    v: number
}

export const detailPredict = {
    name: "apply_type#name",
    apply_submit_time: "label",
    contact_person: "apply_type#contact_person",
    contact_mobile: "apply_type#contact_mobile",
    company_name: "company#company_name",
    apply_type_serve_type: "apply_type#serve_type_label",
    create_time: "label",
    apply_model_id: "",
    file: "company#summary_table_file",
    status: "label",
    apply_status: "label",
    person_name: "personal#name",
    person_mobile: "personal#mobile",
    id_card: "personal#id_card",
    city: "personal#city#region_name",
    area: "personal#area#region_name",
    province: "personal#province#region_name",
    town: "personal#town#region_name",
    district: "personal#district#region_name",
    education: "personal#education_label",
    graduation_school: "personal#graduation_school",
    major: "personal#major",
    graduation_date: "personal#graduation_date_label",
    contract_start_date: "personal#contract_start_date_label",
    contract_end_date: "personal#contract_end_date_label",
    social_region_names: "personal#social_region_names",
    bank_image: "personal#bank_card_image",
    bank_card_no: "personal#bank_card_no",
    bank_name: "personal#bank_name",
    bank_no: "personal#bank_no",
    audit_memo: "personal#memo",
    personal_audit_status: "personal#audit_status_label",
    personal_status: "personal#status_label",
    company_contact_person: "company#contact_person",
    company_contact_mobile: "company#contact_mobile",
    memo: "",
    task_name: "task#task_name",
    no: "apply_type#no",
    object_data: "object_data",
    request_table_file: "company#request_table_file",
    household_region_names: "personal#household_region_names_label",
    permanent_area_names: "personal#permanent_area_names_label",
    mgt_area_names: "personal#mgt_area_names_label",
    agent_name: "personal#com_name",
    agent_mobile: "personal#com_contact_mobile",
    the_end_date: "personal#the_end_date_label",
    share_date: "personal#share_date_label",
    the_begin_date: "personal#the_begin_date_label",
    share_month_number: "personal#share_month_number",
    personal_type: "personal#personal_type_label",
    lhjy_personal_type: "personal#lhjy_personal_type_label",

    social_butie_begin_date: "personal#social_butie_begin_date_label",
    social_butie_end_date: "personal#social_butie_end_date_label",
    social_pay_begin_date: "personal#social_pay_begin_date_label",
    social_pay_end_date: "personal#social_pay_end_date_label",
    yiliao_butie_begin_date: "personal#yiliao_butie_begin_date_label",
    yiliao_butie_end_date: "personal#yiliao_butie_end_date_label",
    yiliao_pay_begin_date: "personal#yiliao_pay_begin_date_label",
    yiliao_pay_end_date: "personal#yiliao_pay_end_date_label",
    gongshang_pay_begin_date: "personal#gongshang_pay_begin_date_label",
    gongshang_pay_end_date: "personal#gongshang_pay_end_date_label",
    social_region_codes: "personal#social_region_codes_label",
    social_butie_month: "personal#social_butie_month_label",
    yiliao_butie_month: "personal#yiliao_butie_month_label",
}

export function getColumns(questionnaire_id: string, type = "") {
    const obj = {
        questionnaire_id,
    }
    if (type) {
        Object.assign(obj, {
            type: type,
        })
    }
    return sdk.core
        .domainService(
            "xg_project",
            "general_questionnaire",
            "get_answer_statistic_list_meta"
        )
        .get<{
            columns: { label: string; type: "text " | "area" | "date" }[]
        }>(obj)
        .then((r) => {
            return map(r.columns, (i) => {
                return i.label.replace("答题", "申报")
            })
        })
}
const jzColumns = [
    { label: "经办单位", prop: "org_name" },
    { label: "申报时间", prop: "apply_create_time" },
    { label: "处理时间", prop: "apply_dealer_time" },
    { label: "处理人", prop: "apply_dealer" },
    { label: "审批备注", prop: "audit_memo" },
    { label: "状态", prop: "apply_status" },
]
export function getFormData(
    questionnaire_id: string,
    type = "",
    columns: any,
    id = ""
) {
    pageLoading(() => {
        return sdk.core
            .getAxios()
            .post<any>(
                `${config.uniplatApi}/general/project/xg_project/service/general_questionnaire/get_answer_statistic_list?questionnaire_id=${questionnaire_id}&type=${type}&form_id=${id}`,
                {
                    export: 1,
                },
                {
                    timeout: 0,
                }
            )
            .then((r) => {
                ExcelGenerator.execute({
                    primaryRows: [],
                    columns: [...columns, ...map(jzColumns, (i) => i.label)],
                    rows: map(r.data || [], (e) => [
                        ...e.values,
                        ...map(jzColumns, (i) => e[i.prop] || ""),
                    ]),
                    fileName: "申报表单",
                })
            })
    })
}

export function batchDownload(id: string) {
    return sdk.core
        .model("zc_apply_attach_uploaded")
        .action("download")
        .updateInitialParams({
            prefilters: [
                {
                    property: "attach_model_name",
                    value: "zc_apply_personal",
                },
                {
                    property: "attach_model_id",
                    value: id,
                },
            ],
        })
        .execute()
        .then((r) => {
            console.log("r", JSON.parse(JSON.stringify(r)))
            const url = get(r, "data.file_url") as string
            url && window.open(url, "_blank")
        })
}
