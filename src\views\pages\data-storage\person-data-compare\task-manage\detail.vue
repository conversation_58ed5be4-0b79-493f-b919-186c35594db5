<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button
                    type="primary"
                    :disabled="!isWait"
                    @click="toCheck"
                    v-if="!isFinish"
                >
                    {{ isWait ? "开始核查" : "核查中" }}
                </el-button>
            </div>
        </div>
        <div class="detail-top-box u-p-20">
            <detail-view :detailRow="row" v-if="row"></detail-view>
            <div>
                <div class="title">比对结果</div>
                <div class="u-p-20">
                    <detail-row-col
                        :labelStyle="{ width: '100px' }"
                        :list="items1"
                    ></detail-row-col>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../../single-page/components/tags-view"
    import { pageLoading } from "@/views/controller"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailView from "./components/detail-view.vue"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { Status } from "./index"

    @Component({
        name: routesMap.dataStorage.personDataCompareTaskDetail,
        components: { DetailView, DetailRowCol },
    })
    export default class CompareTaskManageDetail extends Vue {
        private breadcrumbs: BreadcrumbItem[] = []

        private showList = false

        private items: ColItem[] = []

        private showAddPop = false
        private showCheckPop = false

        refreshConfig = {
            fun: this.init,
            name: routesMap.dataStorage.personDataCompareTaskDetail,
        }

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from as string),
                {
                    label: "数据比对任务详情",
                    to: {
                        name: routesMap.dataStorage.personDataCompareTaskDetail,
                        query: {
                            id: this.$route.query.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.dataStorage.personDataCompareTaskDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private detailId = ""

        private row: any | null = null

        private get items1() {
            if (!this.row) return []
            const h = this.$createElement
            const item: ColItem[] = [
                {
                    label: "比对结果：",
                    vNode: this.row.result_url
                        ? h(
                              "a",
                              {
                                  class: "primary",
                                  attrs: {
                                      href: sdk.buildFilePath(
                                          this.row.result_url || ""
                                      ),
                                      target: "__blank",
                                  },
                              },
                              this.row.task_name + "__比对结果.xlsx" ||
                                  this.row.result_url ||
                                  ""
                          )
                        : h("span", undefined, "--"),
                    span: 24,
                },
            ]
            return item.map((i) => {
                return { ...i, span: i.span }
            })
        }

        private get isWait() {
            return this.row?.task_status === Status.待开始
        }

        private get isFinish() {
            return this.row?.task_status === Status.已完成
        }

        created() {
            this.init()
        }

        private init() {
            this.row = null
            this.setBreadcrumbs()

            this.showList = false
            this.$nextTick(() => {
                this.showList = true
            })
            pageLoading(() => {
                return sdk.core
                    .model("data_comparison_task")
                    .detail(
                        this.$route.query.id as string,
                        "data_comparison_task_list_detail"
                    )
                    .query()
                    .then((res) => {
                        this.row = sdk.buildRow(res.row, {
                            task_status: "label",
                            task_name: "",
                            func_name: "data_comparison#func_name",
                            description: "",
                            real_name: "user_account#real_name",
                            create_time: "label",
                            source_url: "",
                            result_url: "",
                        })
                        this.detailId = this.row?.id + "" || ""
                    })
            })
        }

        private toAdd() {
            ;(this.$refs.detailTable as any).toAdd()
        }

        private refreshList() {
            ;(this.$refs.detailTable as any).refresh()
        }

        private toCheck() {
            pageLoading(() => {
                return sdk.core
                    .model("data_comparison_task")
                    .action("start_task")
                    .updateInitialParams({
                        selected_list: [{ v: 0, id: +this.detailId }],
                    })
                    .execute()
                    .then(() => {
                        this.callRefresh(
                            routesMap.dataStorage.personDataCompareTaskDetail
                        )
                        this.callRefresh(
                            routesMap.dataStorage.personDataCompareTask
                        )
                    })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .detail-top-box {
        background-color: #fff;
        margin-bottom: 20px;
        padding-top: 20px;
        width: 100%;
        padding-bottom: 20px;
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
        }
    }
</style>
