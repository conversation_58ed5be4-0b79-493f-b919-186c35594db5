import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import { config, EnvProject } from "@/config"

const isXg = config.envProject === EnvProject.孝感项目

const rowPredict = {
    parent_name: "parent#name",
    name: "",
    full_region_name_display: "",
    update_time: "",
    status: "label",
    category: "label",
}

export const enum Status {
    已启用 = 0,
    已停用 = 1,
}

export interface Row {
    name: string
    parent_name: string
    full_region_name_display: string
    update_time: string
    status: Status
    status_label: string
    id: number
    v: number
}

const tableFilter: TableFilter[] = [
    {
        prop: "name",
        label: "机构名称",
        type: FormType.Text,
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    !isXg
        ? {
              prop: "parent_id",
              label: "上级机构",
              type: FormType.Tree,
          }
        : {
              prop: "parent_id",
              label: "上级机构",
              type: FormType.Select,
              option: {
                  filterable: true,
              },
          },
    {
        prop: "status",
        label: "状态",
        type: FormType.Select,
    },
    {
        prop: "category",
        label: "机构类型",
        type: FormType.Select,
    },
    {
        prop: "update_time",
        label: "修改时间",
        type: FormType.DatePicker,
        option: {
            type: "daterange",
        },
    },
]
const column: TableColumn[] = [
    {
        label: "机构名称",
        prop: "name",
        minWidth: "120px",
    },
    {
        label: "上级机构",
        prop: "parent_name",
        minWidth: "120px",
    },
    {
        label: "所属区域",
        prop: "full_region_name_display",
        minWidth: "130px",
    },
    {
        label: "机构类型",
        prop: "category_label",
        minWidth: "130px",
    },
    {
        label: "修改时间",
        prop: "update_time",
        minWidth: "120px",
        formatter: (row: Row) => {
            return formatTime.seconds(row.update_time)
        },
    },
    {
        label: "状态",
        prop: "status_label",
        minWidth: "80px",
    },
    {
        label: "操作",
        prop: "h",
        minWidth: "80px",
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("xg_organization").list("manager"),
        filter: tableFilter,
        defaultPageSize: 8,
        predict: rowPredict,
        column,
    }
}
