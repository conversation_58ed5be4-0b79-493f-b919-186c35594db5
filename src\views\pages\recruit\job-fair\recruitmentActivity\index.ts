import {
    buildSelectSource,
    defaultTimePickerOptions,
    FormType,
} from "@/core-ui/component/form"
import { TableColumn, TableConfig } from "@/core-ui/component/table"
import { sdk } from "@/service"
import { sysConfigService } from "@/service/sys-config"
import { cloneDeep } from "lodash"
import { getFilters } from "../specialRecruitment"

export interface Row {
    region_code: string // 区县行政区划代码
    code_remark: string // 区县行政区划代码释义
    activity_type_account: number // 专场招聘会总场次数
    // activity_type_1: number // 高校毕业生专场场次数
    // activity_type_2: number // 退捕渔民专场场次数
    // activity_type_3: number // 退捕渔民专场场次数
    // activity_type_4: number // 退役军人专场场次数
    // activity_type_5: number // 脱贫人员专场场次数
    // activity_type_6: number // 失业人员专场场次数
    // activity_type_7: number // 返乡农民工专场场次数
    // activity_type_8: number // 纺织服装行业专场场次数
    // activity_type_9: number // 综合类场次数
    // activity_type_10: number // 其他场次数
    // 专场招聘会统计列表
    activity_list: {
        key: string // 专场招聘会类型
        value: number // 专场招聘会场次数
    }[]
    account: number // 合计
    type2: number // 现场
    type1: number // 网络
    type3: number // 直播
    agents: number // 参与企业
    positions: number // 提供岗位
    job_will_count: number // 达成就业意向
    pub_file_count: number // 发放宣传资料
    live_person_count: number // 观看直播
    make_work_count: number // 开展职业指导
    go_online_count: number // 进场求职人数
    other_job_fair: {
        title: string
        activity_type_remark: number
    }[]
    [key: string]: any
}

export async function tableConfig(): Promise<TableConfig> {
    return {
        filter: await getFilters(),
        predict: {},
        domainService: sdk.core.domainService(
            "xg_project",
            "back_api",
            "get_job_fair_activity"
        ),
        column: columns,
        handleFilterData(d) {
            const params = cloneDeep(d)
            if (params?.time) {
                params.start_date = params.time[0]
                params.end_date = params.time[1]
                delete params.time
            }
            return {
                start_date: "",
                end_date: "",
                ...params,
            }
        },
    }
}

export const columns: TableColumn[] = [
    {
        label: "地区",
        prop: "code_remark",
        minWidth: "140",
        showOverflowTip: true,
        fixed: "left",
    },
    {
        label: "专场招聘会数量（场）",
        prop: "专场招聘会数量（场）",
        children: [],
    },
    {
        label: "招聘形式（场）",
        prop: "招聘形式（场）",
        children: [
            {
                label: "合计",
                prop: "account",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "现场",
                prop: "type2",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "网络",
                prop: "type1",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "直播",
                prop: "type3",
                minWidth: "100",
                showOverflowTip: true,
            },
        ],
    },
    {
        label: "招聘会综合信息",
        prop: "招聘会综合信息",
        children: [
            {
                label: "参与企业（家）",
                prop: "agents",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "参与企业（去重）",
                prop: "distinct_agents",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "提供岗位（个）",
                prop: "positions",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "进场求职人数",
                prop: "go_online_count",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "达成就业意向（人）",
                prop: "job_will_count",
                minWidth: "120",
                showOverflowTip: true,
            },
            {
                label: "发放宣传资料（份）",
                prop: "pub_file_count",
                minWidth: "120",
                showOverflowTip: true,
            },
            {
                label: "网络参与人数",
                prop: "live_person_count",
                minWidth: "100",
                showOverflowTip: true,
            },
            {
                label: "开展职业指导（人次）",
                prop: "make_work_count",
                minWidth: "120",
                showOverflowTip: true,
            },
        ],
    },
]
