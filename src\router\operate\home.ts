import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const homeRoute =
    (
        {
            潜江项目: routesMap.home.qjPage.index,
        } as any
    )[config.envProject] || routesMap.home.page
export const home = [
    {
        path: `/qj-home`,
        name: routesMap.home.qjPage.root,
        meta: {
            showOneChildren: true,
            hideTag: true,
            hideMenu: true,
            hidden: ![EnvProject.潜江项目].includes(config.envProject),
        },
        component: RouteView,
        children: [
            {
                path: "qj-page",
                name: routesMap.home.qjPage.index,
                meta: {
                    title: "潜江首页",
                    svgIcon: require("@/assets/icon/menu2/home.svg"),
                    hideTag: true,
                    hideTagView: true,
                    single: true,
                },
                component: () => import("@/views/pages/home/<USER>"),
            },
        ],
    },
    {
        path: `/home`,
        redirect: "home/page",
        name: routesMap.home.root,
        meta: { showOneChildren: true, hideTag: true },
        component: layout,
        children: [
            {
                path: "page",
                name: routesMap.home.page,
                meta: {
                    title: "首页",
                    svgIcon: require("@/assets/icon/menu2/home.svg"),
                    hideTag: true,
                    parentMenuName: routesMap.home.root,
                    hideTagView: true,
                    customPageClassName: isXg ? "home-page-cus-class" : "",
                    // single: true,
                },
                component: () => {
                    if (isXg) {
                        return import("@/views/pages/home/<USER>")
                    }
                    return import("@/views/pages/home/<USER>")
                },
            },
        ],
    },
]
