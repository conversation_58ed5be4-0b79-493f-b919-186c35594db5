import {
    Table<PERSON><PERSON>umn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { FormType } from "@/core-ui/component/form"
import { sdk } from "@/service"
import { ListTypes } from "uniplat-sdk"
import { config, EnvProject } from "@/config"
import { ExcelGenerator } from "@/utils/excel-generator"
import { map } from "lodash"
import { pageLoading } from "@/views/controller"

export const queryParamsKey = "policy-apply-record-params-key"

export const enum Status {
    待提交 = -1,
    已提交审核 = 0,
    通过 = 1,
    不通过 = 2,
}

export interface Row {
    /** 政策名称 */
    policy_name: string

    /** 处理人 */
    real_name: number

    /** 申报时间 */
    create_time: string

    /** 联系方式 */
    contact_mobile: string

    /** 处理时间 */
    dealer_time: number

    /** 状态 */
    status: Status

    /** 状态[文本] */
    status_label: string
    serve_target_type: ServeTargetType
    form_access_key: string
    profile_access_key: string
    agent_access_key: string
    contact_person: string
    id: number
    v: number
}

export const tableFilter: TableFilter[] = [
    {
        label: "政策名称",
        type: FormType.Text,
        prop: "policy_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "申报人",
        type: FormType.Text,
        prop: "contact_person",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "状态",
        type: FormType.Select,
        prop: "status",
    },
    {
        label: "所属科室",
        prop: "parent_id",
        type: FormType.Cascader,
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "申报企业",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "处理人",
        type: FormType.Text,
        prop: "dealer.real_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "申报时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "经办单位",
        type: FormType.Cascader,
        prop: "xg_org_id",
        option: { elProps: { checkStrictly: true } },
        hide: config.envProject !== EnvProject.荆州项目,
    },
]

const commonColumn: TableColumn[] = [
    {
        label: "申报时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "contact_mobile",
        showOverflowTip: true,
        minWidth: "150px",
    },
    {
        label: "经办单位",
        prop: "organization_name",
        width: "120px",
        showOverflowTip: true,
        hide: config.envProject !== EnvProject.荆州项目,
    },
    {
        label: "处理人",
        prop: "dealer_name",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "处理时间",
        prop: "dealer_time_label",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "状态",
        prop: "status_label",
        width: "120px",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "120px",
        showOverflowTip: true,
    },
]

export const columns1: TableColumn[] = [
    {
        label: "序号",
        prop: "order",
        showOverflowTip: true,
    },
    {
        label: "政策名称",
        prop: "policy_name",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "申报人",
        prop: "real_name",
        showOverflowTip: true,
    },
    ...commonColumn,
]

export const columns2: TableColumn[] = [
    {
        label: "序号",
        prop: "order",
        showOverflowTip: true,
    },
    {
        label: "政策名称",
        prop: "policy_name",
        showOverflowTip: true,
        minWidth: "120px",
    },
    {
        label: "申报人",
        prop: "real_name_company",
        showOverflowTip: true,
    },
    {
        label: "申报企业",
        prop: "agent_name",
        showOverflowTip: true,
        minWidth: "120px",
    },
    ...commonColumn,
]

export const predict = {
    policy_name: "form#policy_name",
    real_name: "user#real_name",
    real_name_company: "creator#real_name",
    agent_name: "agent#agent_name",
    create_time: "label",
    contact_mobile: "",
    dealer_name: "dealer#real_name",
    dealer_time: "label",
    status: "label",
    serve_target_type: "form#serve_target_type",
    form_access_key: "form#_access_key",
    profile_access_key: "profile#_access_key",
    agent_access_key: "agent#_access_key",
    contact_person: "",
    showMobile: false,
    xg_org_id: "label",
    organization_name: "organization#name",
}

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("policy_form_apply").list("apply_manage"),
        filter: tableFilter,
        defaultPageSize: 10,
        column: columns1,
        predict,
    }
}

export const enum ServeTargetType {
    居民 = 1,
    企业 = 2,
}

export const serviceTypeMapping = new Map<ServeTargetType, string>([
    [ServeTargetType.居民, "居民"],
    [ServeTargetType.企业, "企业"],
])

const enum RegResidenceProperty {
    农业户口 = 1,
    非农业户口 = 2,
    未知 = 0,
}

const enum PoliticalOutlook {
    "中共党员/预备党员" = 1,
    共青团员 = 2,
    群众 = 3,
    其他 = 4,
}

const enum Education {
    博士研究生 = 1,
    硕士研究生 = 2,
    大学本科 = 3,
    大学专科 = 4,
    中等专科 = 5,
    职业高中 = 6,
    技工学校 = 7,
    普通高中 = 8,
    初中 = 9,
    小学 = 10,
    其他 = 11,
    不限学历 = 0,
}

export interface DetailRow {
    policy_name: string
    real_name: string
    apply_memo: string
    create_time: string
    create_time_label: string
    contact_mobile: string
    attachments: string
    name: string
    id_card: string
    id_card_hide: string
    mobile: string
    getAge: string
    reg_residence_property: RegResidenceProperty
    reg_residence_property_label: string
    political_outlook: PoliticalOutlook
    political_outlook_label: string
    region_name: string
    education: Education
    education_label: string
    household_province: string
    household_city: string
    household_area: string
    permanent_province: string
    permanent_city: string
    permanent_area: string
    agent_name: string
    company_code: string
    legal_person: string
    legal_card_open_id: string
    legal_card_open_id_hide: string
    industory_catalog: string
    industory_catalog_label: string
    province_region_company: string
    city_region_company: string
    area_region_company: string
    address_detail: string
    reg_origin: string
    pay_amount: number
    oper_status: string
    found_time: string
    reg_authority: string
    profile_access_key: string
    agent_access_key: string
    /** 状态 */
    status: Status
    form_id: string
    form_questionnaire_id: string
    industory_catalog_display: string
    audit_memo: string
    organization_name: string

    /** 状态[文本] */
    status_label: string
    id: number
    v: number
}

export const detailPredict = {
    policy_name: "form#policy_name",
    real_name: "creator#real_name",
    apply_memo: "",
    create_time: "label",
    contact_mobile: "",
    attachments: "",
    name: "profile#name",
    id_card: "profile#id_card",
    id_card_hide: "profile#id_card_hide",
    mobile: "profile#mobile",
    getAge: "profile#getAge",
    reg_residence_property: "profile#basic_info#reg_residence_property_label",
    political_outlook: "profile#basic_info#political_outlook_label",
    region_name: "profile#basic_info#permanent_area#region_name",
    education: "profile#basic_info#education_label",
    household_province: "profile#basic_info#household_province#region_name",
    household_city: "profile#basic_info#household_city#region_name",
    household_area: "profile#basic_info#household_area#region_name",
    permanent_province: "profile#basic_info#permanent_province#region_name",
    permanent_city: "profile#basic_info#permanent_city#region_name",
    permanent_area: "profile#basic_info#permanent_area#region_name",
    agent_name: "agent#agent_name",
    company_code: "agent#company_code",
    legal_person: "agent#legal_person",
    legal_card_open_id: "agent#legal_card_open_id",
    legal_card_open_id_hide: "agent#legal_card_open_id_hide",
    industory_catalog: "agent#industory_catalog_label",
    industory_catalog_display: "agent#industory_catalog_display",
    province_region_company: "agent#province#region_name",
    city_region_company: "agent#city#region_name",
    area_region_company: "agent#area#region_name",
    address_detail: "agent#address_detail",
    reg_origin: "agent#enterprise_info#reg_origin",
    pay_amount: "agent#enterprise_info#pay_amount",
    oper_status: "agent#enterprise#oper_status",
    found_time: "agent#enterprise#found_time",
    reg_authority: "agent#enterprise#reg_authority",
    profile_access_key: "profile#_access_key",
    agent_access_key: "agent#_access_key",
    status: "label",
    form_id: "form#id",
    form_questionnaire_id: "form#questionnaire_id",
    audit_memo: "label",
    organization_name: "organization#name",
}

export function getColumns(questionnaire_id: string, type = "") {
    const obj = {
        questionnaire_id,
    }
    if (type) {
        Object.assign(obj, {
            type: type,
        })
    }
    return sdk.core
        .domainService(
            "xg_project",
            "general_questionnaire",
            "get_answer_statistic_list_meta"
        )
        .get<{
            columns: { label: string; type: "text " | "area" | "date" }[]
        }>(obj)
        .then((r) => {
            return map(r.columns, (i) => {
                return i.label.replace("答题", "申报")
            })
        })
}
const jzColumns = [
    { label: "经办单位", prop: "org_name" },
    { label: "申报时间", prop: "apply_create_time" },
    { label: "处理时间", prop: "apply_dealer_time" },
    { label: "处理人", prop: "apply_dealer" },
    { label: "审批备注", prop: "audit_memo" },
    { label: "状态", prop: "apply_status" },
]
export function getFormData(
    questionnaire_id: string,
    type = "",
    columns: any,
    id = ""
) {
    pageLoading(() => {
        return sdk.core
            .getAxios()
            .post<any>(
                `${config.uniplatApi}/general/project/xg_project/service/general_questionnaire/get_answer_statistic_list?questionnaire_id=${questionnaire_id}&type=${type}&form_id=${id}`,
                {
                    export: 1,
                },
                {
                    timeout: 0,
                }
            )
            .then((r) => {
                ExcelGenerator.execute({
                    primaryRows: [],
                    columns: [...columns, ...map(jzColumns, (i) => i.label)],
                    rows: map(r.data || [], (e) => [
                        ...e.values,
                        ...map(jzColumns, (i) => e[i.prop] || ""),
                    ]),
                    fileName: "申报表单",
                })
            })
    })
}
