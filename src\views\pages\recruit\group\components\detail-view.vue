<template>
    <div class="detail-container">
        <div class="content u-flex-none" v-if="row">
            <div class="title">申请基本信息</div>
            <div class="u-p-t-10 u-p-x-20 u-p-b-20 info">
                <div class="u-flex u-col-top">
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">申请时间：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.apply_time | time2String }}
                            </div>
                        </div>
                        <div class="u-flex">
                            <div class="label">申请公司：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.agent_name || "-" }}
                            </div>
                        </div>
                        <div class="u-flex">
                            <div class="label">关联岗位：</div>
                            <div class="value u-flex">
                                <div>{{ row.name || "-" }}</div>
                                <div
                                    class="u-m-l-10 pointer primary u-flex-none"
                                    @click="viewPosition"
                                    v-if="row.source_page_url"
                                >
                                    查看岗位
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">推广范围：</div>
                            <div class="w-100 u-line-1 value2 u-flex-1">
                                <!-- {{ row.apply_region }} -->
                                <span :title="row.apply_region">
                                    {{ row.apply_region }}
                                </span>
                            </div>
                        </div>
                        <div class="u-flex">
                            <div class="label">岗位负责人：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.contact_person || "-" }}
                            </div>
                        </div>
                        <div class="u-flex">
                            <div class="label">岗位联系方式：</div>
                            <div class="u-flex u-flex-none">
                                <desensitization-view
                                    :value="row.contact_mobile"
                                ></desensitization-view>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="u-flex u-col-top">
                    <div class="label">需求描述：</div>
                    <div class="value">
                        {{ row.recommend_desc || "-" }}
                    </div>
                </div>
            </div>
            <div class="title">审核信息</div>
            <div class="u-p-t-10 u-p-x-20 u-p-b-20 info">
                <div class="u-flex">
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">审核状态：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.audit_status_label || "-" }}
                            </div>
                        </div>
                        <div class="u-flex u-col-top">
                            <div class="label">审核意见：</div>
                            <div class="value">{{ row.audit_memo || "-" }}</div>
                        </div>
                    </div>
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">审核人：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.audit_user || "-" }}
                            </div>
                        </div>
                        <div class="u-flex">
                            <div class="label">审核时间：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.audit_time | time2String }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="title">推广设置及推广效果</div>
            <div class="u-p-t-10 u-p-x-20 u-p-b-20 info">
                <div class="u-flex">
                    <div class="u-flex u-flex-1">
                        <div class="label">推广时间段：</div>
                        <div class="selector u-flex u-flex-none">
                            <div
                                class="picker u-flex-none"
                                v-if="editTime && show"
                            >
                                <el-date-picker
                                    v-model="timeData.start_time"
                                    :default-value="timeData.start_time"
                                    placeholder="请设置开始时间"
                                    type="date"
                                    value-format="yyyy.MM.dd"
                                ></el-date-picker>
                                至
                                <el-date-picker
                                    v-model="timeData.end_time"
                                    :default-value="timeData.end_time"
                                    placeholder="请设置结束时间"
                                    type="date"
                                    value-format="yyyy.MM.dd"
                                ></el-date-picker>
                            </div>
                            <div class="picker u-flex" v-else>
                                <div class="display u-m-r-10">
                                    {{ timeData.start_time || "-" }}
                                </div>
                                <div>至</div>
                                <div class="display u-m-l-10">
                                    {{ timeData.end_time || "-" }}
                                </div>
                            </div>
                            <div
                                class="primary pointer u-m-l-20"
                                @click="changeTime"
                                v-if="show"
                            >
                                {{ editTime ? "确定" : "设置" }}
                            </div>
                        </div>
                    </div>
                    <div class="u-flex u-flex-1">
                        <div class="label">推广区域：</div>
                        <div class="u-flex u-flex-none">
                            <div class="u-flex-1 u-flex">
                                <v-multiple-cascader
                                    class="city u-line-1"
                                    v-if="showAudit"
                                    :value="city"
                                    :mark="''"
                                    :isEdit="editCity"
                                    :sourceInputsParameter="cityParameters"
                                    :elProps="{
                                        checkStrictly: true,
                                        multiple: true,
                                    }"
                                    :option="option"
                                    @input="inputCity"
                                ></v-multiple-cascader>
                            </div>
                            <div
                                class="primary pointer u-m-l-20 u-flex-none"
                                @click="changeCity"
                                v-if="show"
                            >
                                {{ editCity ? "确定" : "设置" }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="u-flex u-col-top">
                    <div
                        class="u-flex u-flex-1"
                        v-if="row.audit_status === audit_status.审核通过"
                    >
                        <div class="label">推广状态：</div>
                        <div class="u-flex u-flex-none">
                            {{ row.status_memo || "-" }}
                        </div>
                    </div>
                    <div
                        class="u-flex u-flex-1 u-col-top"
                        v-if="row.socialize_group_share_url"
                    >
                        <div class="label">推广二维码：</div>
                        <div class="u-flex u-flex-none">
                            <el-image
                                :src="row.socialize_group_share_url"
                                :preview-src-list="[
                                    row.socialize_group_share_url,
                                ]"
                                class="code"
                            ></el-image>
                            <span class="u-m-l-5">用企业微信扫描二维码</span>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="row && row.audit_status === audit_status.审核通过">
                <chart-view :row="row"></chart-view>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    // import { DrawerBox } from "@/core-ui/component/drawer-box"
    import VCascader from "@/core-ui/component/form/filed/v-cascader.vue"
    import VMultipleCascader from "@/core-ui/component/form/filed/v-multiple-cascader.vue"
    import { getAddress } from "@/utils"
    import DesensitizationView from "@/views/components/common-comps/desensitization-view.vue"
    import { pageLoading } from "@/views/controller"
    import { cloneDeep, get } from "lodash"
    import { ActionTypes } from "uniplat-sdk"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { AuditStatus, Row } from ".."
    import {
        changeCity,
        changeTime,
        getCityInfo,
        getTimeInfo,
    } from "../../grid/components/detail"
    import ChartView from "./chart-view.vue"

    @Component({
        components: {
            VCascader,
            ChartView,
            VMultipleCascader,
            DesensitizationView,
        },
    })
    export default class GridDetailView extends Vue {
        @Prop()
        private row!: Row

        @Prop()
        private show!: boolean

        private showAudit = false
        private audit_status = AuditStatus

        private editTime = false
        private timeData: Record<string, any> = {
            recommend_duration: 0,
            end_time: "",
            start_time: "",
        }

        private get option() {
            return {
                handlerDisplay: this.handlerDisplay,
            }
        }

        private handlerDisplay(v: string) {
            return (v || "").split("-").filter(Boolean).join("、")
        }

        private changeTime() {
            if (this.editTime) {
                if (!this.timeData.start_time) {
                    return this.$message.warning("请设置开始时间")
                }
                if (
                    !!this.timeData.end_time &&
                    +new Date(this.timeData.start_time) >
                        +new Date(this.timeData.end_time)
                ) {
                    return this.$message.warning("开始时间需小于结束时间")
                }
                pageLoading(() => {
                    return changeTime(this.row.id, this.timeData).then(() => {
                        this.$emit("getDetail")
                        this.editTime = false
                    })
                })
            } else {
                this.editTime = !this.editTime
            }
        }

        private editCity = false
        private city = ""
        private cityParameters: ActionTypes.inputsParameter | null = null
        private changeCity() {
            if (this.editCity) {
                return pageLoading(() => {
                    return changeCity(this.row.id, this.city).then(() => {
                        this.editCity = !this.editCity
                        this.$emit("getDetail")
                    })
                })
            }
            this.editCity = !this.editCity
        }

        private inputCity(v: string) {
            this.city = v
        }

        mounted() {
            this.init()
        }

        async init() {
            await pageLoading(() => {
                return Promise.resolve([
                    getCityInfo(this.row.id).then((r) => {
                        this.cityParameters = r
                        this.city = get(r, "default_value") as string
                    }),
                    getTimeInfo(this.row.id).then((r) => {
                        this.timeData = cloneDeep(r)
                    }),
                ])
            })
            this.showAudit = true
        }

        private viewPosition() {
            console.log("viewPosition")
            window.open(this.row.source_page_url, "_blank")
            // DrawerBox.open({
            //     url: "http://localhost:8085/recruit/job",
            //     title: "职位详情",
            // })
        }

        private getAddress(row: Row) {
            return getAddress(row)
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        background: #fff;
        .content {
            padding: 20px;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
            .label {
                width: 98px;
                margin-right: 10px;
                color: #555;
                flex: none;
                line-height: 28px;
            }
            .value {
                line-height: 28px;
            }
            .value2 {
                width: 0;
            }
            .info {
                line-height: 34px;
                color: #333;
                font-size: 14px;
            }
            .selector {
                position: relative;
            }
        }
    }
    .picker {
        .display {
            flex: none;
            line-height: 40px;
        }
        ::v-deep .el-date-editor {
            width: 120px;
            .el-input__prefix {
                display: none;
            }
        }
        ::v-deep .el-input__inner {
            padding: 0 10px;
        }
    }
    .group-view {
        width: 510px;
        height: 384px;
    }
    .code {
        width: 50px;
        height: 50px;
    }
    .city {
        max-width: calc(100vw * 400 / 1920);
    }
</style>
