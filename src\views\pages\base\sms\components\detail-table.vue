<template>
    <div>
        <table-container
            filedWidth="250"
            ref="table"
            v-model="tableConfig"
            class="container"
        >
            <div slot="table" class="u-p-x-20 u-p-t-20" slot-scope="{ data }">
                <common-table :data="data" :columns="tableConfig.column">
                    <div slot="h" slot-scope="scope">
                        <el-button
                            v-if="scope.row.profile_access_key"
                            type="text"
                            @click="viewDetail(scope.row)"
                        >
                            详情
                        </el-button>
                        <span v-else>--</span>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { Component } from "vue-property-decorator"
    import { tableConfig, Row } from "./detail-table"

    @Component({
        components: { TableContainer, CommonTable },
    })
    export default class Index extends BaseTableController<Row> {
        private tableConfig = tableConfig(this.$route.query.id as string)

        viewDetail(row: Row) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: row.profile_access_key || row.user_account_id + "",
                    from: routesMap.base.sms.detail,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
</style>
