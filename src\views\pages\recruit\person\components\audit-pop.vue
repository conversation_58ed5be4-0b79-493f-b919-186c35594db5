<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
    >
        <div class="u-p-x-20" v-if="row">
            <div class="u-flex">
                <div class="label">申请时间</div>
                <div class="value">{{ row.apply_time | time2String }}</div>
            </div>
            <div class="u-flex u-m-t-10">
                <div class="label">申请公司</div>
                <div class="value">{{ row.agent_name }}</div>
            </div>
            <!-- <div class="u-flex u-m-t-10">
                <div class="label">招聘时间</div>
                <div class="value">
                    {{ row.position_start_time | time2String }} 至
                    {{ row.position_end_time | time2String }}
                </div>
            </div> -->
            <div class="u-flex u-m-t-10">
                <div class="label">关联岗位</div>
                <div class="value u-flex">
                    <div>{{ row.name || "-" }}</div>
                    <div
                        class="u-m-l-10 pointer primary u-flex-none"
                        v-if="row.source_page_url"
                        @click="viewPosition"
                    >
                        查看岗位
                    </div>
                </div>
            </div>
            <div class="u-flex u-m-t-10 u-col-top">
                <div class="label">联系人</div>
                <div class="value">
                    {{ row.contact_person || "-" }}
                </div>
            </div>
            <div class="u-flex u-m-t-10 u-col-top">
                <div class="label">联系方式</div>
                <div class="value">
                    {{ row.contact_mobile || "-" }}
                </div>
            </div>
            <div class="u-flex u-m-t-10 u-col-top" v-if="row.position_description">
                <div class="label">岗位描述</div>

                <el-popover
                    placement="top-start"
                    width="400"
                    trigger="hover"
                    :content="row.position_description"
                >
                    <div slot="reference" class="value u-line-1 u-line-2">
                        {{ row.position_description || "-" }}
                    </div>
                </el-popover>
            </div>
            <div class="u-flex u-m-t-10 u-col-top">
                <div class="label">需求描述</div>
                <div class="value">
                    {{ row.recommend_desc || "-" }}
                </div>
            </div>
            <div class="u-flex u-m-t-20">
                <div class="label">审核状态</div>
                <div>
                    <el-select
                        size="medium"
                        v-model="status"
                        placeholder="请选择审核状态"
                        class="select"
                    >
                        <el-option value="1" label="通过">通过</el-option>
                        <el-option value="2" label="不通过">不通过</el-option>
                    </el-select>
                </div>
            </div>
            <div class="u-flex u-m-t-20 u-col-top">
                <div class="label u-m-t-12">审核意见</div>
                <div>
                    <el-input
                        placeholder="请输入"
                        v-model="memo"
                        resize="none"
                        rows="3"
                        type="textarea"
                        class="input"
                    ></el-input>
                </div>
            </div>
            <div class="u-flex u-row-center u-m-t-20">
                <el-button
                    @click="close"
                    class="btn custom-btn u-m-r-30"
                    type="primary"
                    plain
                    >取消</el-button
                >
                <el-button
                    class="btn custom-btn"
                    type="primary"
                    @click="confirm"
                    >确定</el-button
                >
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    // import { DrawerBox } from "@/core-ui/component/drawer-box"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Prop } from "vue-property-decorator"
    import { Row } from ".."

    @Component({ components: {} })
    export default class AuditPop extends DialogController {
        @Prop({ default: "智能人才推荐审核" })
        private title!: string

        @Prop({ default: null })
        private row!: Row

        private status = "2"
        private time = ""
        private memo = ""

        private confirm() {
            console.log(this.status, this.memo)
            if (!this.status) return this.$message.error("请选择审核状态")
            pageLoading(() => {
                return sdk.core
                    .model("xg_company_position_recommend")
                    .action("audit")
                    .updateInitialParams({
                        selected_list: [
                            {
                                v: 0,
                                id: this.row.id,
                            },
                        ],
                    })
                    .addInputs_parameter({
                        status: this.status,
                        audit_memo: this.memo,
                    })
                    .execute()
                    .then(() => {
                        this.close()
                        this.$emit("refresh")
                    })
            })
        }

        onOpen() {
            this.status = "2"
            this.memo = ""
            this.time = ""
            console.log(JSON.parse(JSON.stringify(this.row)))
        }

        private viewPosition() {
            console.log("viewPosition")
            window.open(this.row.source_page_url, "_blank")
            // DrawerBox.open({
            //     url: "http://localhost:8085/recruit/job",
            //     title: "职位详情",
            // })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
        flex: none;
        line-height: 24px;
        width: 56px;
    }
    .value {
        margin-left: 20px;
        color: #333;
        line-height: 24px;
        width: 400px;
        white-space: normal;
    }
    .select {
        width: 400px;
    }
    .input {
        width: 400px;
    }
    .btn {
        width: 100px;
        height: 36px;
    }
</style>
