import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes, TagManagerTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    {
        label: "用户信息",
        type: FormType.Text,
        prop: "name",
    },
    {
        label: "所在城市",
        type: FormType.MultipleCascader,
        prop: "permanent_province_code",
        option: {
            filterable: true,
            elProps: { checkStrictly: true },
        },
    },
    {
        label: "服务类型",
        type: FormType.Select,
        prop: "serve_type",
    },
    {
        label: "服务名称",
        type: FormType.Text,
        prop: "p_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "就业状态",
        type: FormType.Select,
        prop: "employment_status",
        option: {
            multiple: true,
        },
    },
    {
        label: "发布时间",
        type: FormType.DatePicker,
        prop: "create_time",
        option: {
            type: "daterange",
        },
    },
    {
        label: "标签",
        type: FormType.Cascader,
        prop: "tags",
        useTag: "*",
        option: {
            filterable: true,
            collapseTags: true,
            elProps: { multiple: true },
        },
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    {
        label: "出生日期",
        type: FormType.DatePicker,
        option: {
            type: "daterange",
        },
        prop: "birth_date",
    },
]

const rowPredict = {
    userId: "task_serve_target#user_profile#id",
    name: "task_serve_target#user_profile#name",
    sex: "task_serve_target#user_profile#sex_label",
    birth_date: "task_serve_target#user_profile#birth_date",
    mobile_hide: "task_serve_target#user_profile#mobile_hide",
    idcard_hide: "task_serve_target#user_profile#id_card_hide",
    region_name:
        "task_serve_target#user_profile#basic_info#permanent_city#region_name",
    employment_status:
        "task_serve_target#user_profile#user_profile_current_job_info#employment_status_label",
    province_name:
        "task_serve_target#user_profile#basic_info#permanent_province#region_name",
    city_name:
        "task_serve_target#user_profile#basic_info#permanent_city#region_name",
    update_time: "",
    create_time: "",
    tags: "tags",
    p_name: "serve_task#p_name",
    status: "label",
    _access_key: "",
}

export function tableConfig(preFilter: { project_id?: string }): TableConfig {
    const model = preFilter.project_id ? "task_serve_record" : ""
    const list = preFilter.project_id ? "for_operate2" : ""
    return {
        model: sdk.core.model(model).list(list),
        filter: tableFilter,
        defaultPageSize: 8,
        predict: rowPredict,
        preFilter,
    }
}
const enum Status {
    未读 = 0,
    已读 = 1,
    已完成 = 10,
}
export interface Row {
    userId: string
    name: string
    sex_label: string
    birth_date: string
    mobile_hide: string
    idcard_hide: string
    region_name: number
    employment_status_label: string
    province_name: string
    city_name: string
    update_time: string
    create_time: string
    tags: { [key: string]: TagManagerTypes.TagInfo[] }
    id: number
    v: number
    p_name: string
    status: Status
    status_label: string
    _access_key: string
}

export const columns: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        width: "70",
        showOverflowTip: true,
    },
    {
        label: "性别",
        width: "50",
        align: "center",
        prop: "sex_label",
        showOverflowTip: true,
    },
    {
        label: "出生日期",
        prop: "birth_date",
        showOverflowTip: true,
    },
    {
        label: "身份证号",
        prop: "idcard_hide",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "手机号",
        prop: "mobile_hide",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "当前城市",
        prop: "address",
        width: "150",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.province_name, row.city_name].filter(Boolean).join("")
        },
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "任务名称",
        prop: "p_name",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "服务记录状态",
        prop: "status_label",
        width: "100",
        showOverflowTip: true,
    },
    {
        label: "状态更新时间",
        prop: "update_time",
        width: "180",
        align: "center",
        render: (h, row) => {
            return h("div", [
                h("div", formatTime.day(row.update_time)),
                h("div", formatTime.default(row.update_time, "HH:mm")),
            ])
        },
    },
    {
        label: "服务发布时间",
        prop: "create_time",
        width: "180",
        align: "center",
        render: (h, row) => {
            return h("div", [
                h("div", formatTime.day(row.create_time)),
                h("div", formatTime.default(row.create_time, "HH:mm")),
            ])
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]
