<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            @getRows="getRows"
            :showExpand="false"
        >
            <div slot="title" class="d-flex-item-center bold">
                <div class="d-flex-item-center bold">
                    <bread-crumb :backRoute="false" :items="breadcrumbs" />
                </div>
            </div>

            <div slot="header-right">
                <el-button type="primary" plain @click="toExport">
                    导出
                </el-button>

                <el-button
                    type="primary"
                    @click="toCreate"
                    v-if="showBtn"
                    class="custom-btn batch-btn"
                >
                    创建网点
                </el-button>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="联系方式" slot-scope="scope">
                        <div>{{ scope.row.contact_person }}</div>
                        <div>{{ scope.row.mobile_hide }}</div>
                    </div>
                    <div slot="创建/更新时间" slot-scope="scope">
                        <div>创建 {{ formatTime(scope.row.create_time) }}</div>
                        <div>更新 {{ formatTime(scope.row.update_time) }}</div>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button
                            type="text"
                            v-if="getBtnInfo(scope.row, 'online')"
                            @click="toEdit(scope.row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            type="text"
                            v-if="getBtnInfo(scope.row, 'offline')"
                            @click="toOperate(scope.row, 'offline')"
                        >
                            下架
                        </el-button>
                        <el-button
                            type="text"
                            v-if="getBtnInfo(scope.row, 'online')"
                            @click="toOperate(scope.row, 'online')"
                        >
                            上架
                        </el-button>
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { cloneDeep } from "lodash"
    import { Component } from "vue-property-decorator"
    import { Row, Status, tableFilter } from "./index"
    import { updateTagItem } from "../../single-page/components/tags-view"
    import { buildConfig4RemoteMeta } from "../../collect-task-manage/components/build-table"
    import { formatTime } from "@/utils/tools"

    @Component({
        name: routesMap.branch.service.index,
        components: {
            TableContainer,
            CommonTable,
        },
    })
    export default class PublishPolicyIndex extends BaseTableController<Row> {
        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []

        private checkEdIds: Array<number | string> = []
        private selectList: Row[] = []
        showRecommendApply = false

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.rows.map((i) => i.id)
            this.selectList = d.rows
        }

        toApplyRecommend() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要推广的招聘会")
            }
            this.showRecommendApply = true
        }

        refreshConfig = {
            name: routesMap.branch.service.index,
            fun: this.reloadList,
        }

        private showBtn = false

        private rows: any[] = []

        breadcrumbs: BreadcrumbItem[] = []

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "服务网点管理",
                    to: {
                        name: this.$route.name as string,
                    },
                },
            ]
            updateTagItem({
                name: this.$route.name as string,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        created() {
            this.setBreadcrumbs()
            this.init()
        }

        private formatTime(time: string) {
            if (!time) {
                return ""
            }
            return formatTime.seconds(time)
        }

        private init() {
            return buildConfig4RemoteMeta("server_stage", "", {
                useLabelWidth: true,
                useTabs: true,
                disabledFilter: true,
                customLabelWidths: {
                    "创建/更新时间": 190,
                },
                optColumn: {
                    label: "操作",
                    prop: "h",
                    fixed: "right",
                    minWidth: "160px",
                },
            }).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig = r.tableConfig as TableConfig
            tableConfig.filter = tableFilter
            if (r.meta.actions && r.meta.actions.length > 0) {
                this.showBtn = !!r.meta.actions.find(
                    (i: any) => i.action_name === "add"
                )
            } else {
                this.showBtn = false
            }

            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            this.columns = r.columns
            // this.columns = [
            //     {
            //         prop: "select",
            //         width: "58",
            //         type: "selection",
            //         selectable: (row: any) => {
            //             return !row.is_recommend
            //         },
            //     },
            //     ...r.columns,
            // ]
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private getBtnInfo(row: Row, key: string) {
            if (key === "online") {
                return row.status === Status.未上架
            }
            if (key === "offline") {
                return row.status === Status.已上架
            }
        }

        private refresh(force?: boolean) {
            if (force) {
                return this.reloadList(true)
            }
            this.refreshList()
        }

        private toEdit(row: any) {
            this.$router.push({
                name: routesMap.branch.service.add,
                query: { id: row?.id + "", from: this.$route.name },
            })
        }

        private toCreate() {
            this.$router.push({
                name: routesMap.branch.service.add,
                query: { from: this.$route.name },
            })
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.branch.service.detail,
                query: {
                    id: row._access_key + "",
                    from: this.$route.name,
                },
            })
        }

        private toOperate(row: any, actionName: string) {
            MessageBox.confirm(
                `确认${actionName === "online" ? "上架" : "下架"}？`,
                "提示"
            ).then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("server_stage")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private toFormList() {
            this.$router.push({
                name: routesMap.publishPolicy.formList,
                query: { from: routesMap.branch.service.index },
            })
        }

        private toExport() {
            this.exportToExcel()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
