import { object2SortStr } from "@/utils/tools"
import { Component, Vue } from "vue-property-decorator"

@Component({
    components: {},
})
export default class RefreshMixin extends Vue {
    refreshQueryParams = ""
    mounted() {
        if (this.refreshConfig) {
            this.$root.$on(this.refreshConfig.name, this.refreshConfig.fun)
            this.refreshQueryParams = object2SortStr(this.$route?.query)
        }
    }

    activated() {
        if (this.refreshConfig) {
            const newQueryParams = object2SortStr(this.$route?.query)
            if (this.refreshQueryParams !== newQueryParams) {
                this.refreshQueryParams = newQueryParams
                this.refreshConfig?.fun()
            }
        }
    }

    deactivated() {}

    destroyed() {
        if (this.refreshConfig) {
            this.$root.$off(this.refreshConfig.name, this.refreshConfig.fun)
        }
    }

    callRefresh(name: string, ...args: any[]) {
        this.$root.$emit(name, args)
    }
}
