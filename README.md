# 公共就业项目

包含局方端(xg_project_operate)；企业端(xg_project_org)；人力机构端(xg_project_org_hr)

## 开发要求

公共就业这边开发的主分支是 master，荆州正式线是 jz-release 孝感正式线 release。我们各自开发分支可以根据业务新建分支，或者直接用个人开发分支（ytl-master），尽量不要直接在 master 上和 release 上改，如果为了快速需要在 release 上改东西，改完后记得合并到 master。

环境判断统一使用 config 中的 envProject（项目）、envApplication（应用）、isPro（是否是正式线）、isBuild（build 打包）

> 尽量少使用 `config.isOrg` 等使用 is 判断
> 如果公共多端使用但是部分端不使用，建议使用 `[EnvApplication.培训机构端, EnvApplication.局方端].includes(config.envApplication)` 这种写法。不同项目也类似使用 `config.envProject`

## 安装依赖

install时会自动使用ssh初始化submodules请确保有submodules权限

`npm install`

### 启动

```bash
npm run serve
// 选择启动端
```

### 编译

```bash
npm run build-base xg_project_operate test
// 其中xg_project_operate为对应的端
```

# 页面缓存机制

1、在路由配置上配置 name

```ts
{
    path: "/labourManage",
    redirect: "labourManage/seekerInfo",
    name: routesMap.labourManage.seekerDetail,
    meta: {
        title: "居民管理",
        svgIcon: require("@/assets/icon/menu/labourManage.svg"),
    },
    component: () => import("@/views/pages/labour-manage/seeker-info/index.vue"),
    // ...
}
```

2、组件名称与路由名称保持一致

```ts
@Component({
    name: routesMap.labourManage.seekerDetail,
    components: { TableContainer, CommonTable, SetTagDialog },
})
```

# 缓存页面刷新数据机制

刷新页面基础逻辑由 `src/installer/mixin-refresh.ts` 提供
如果配置了 `refreshConfig.fun` 方法，它会在 `callRefresh` 和 `$route.query` 对象数据发生变化时执行

## 列表页

列表都会继承 `BaseTableController` 其中 提供了两种刷新函数 `refreshList(只刷新列表，相当于点击搜索)`； `reloadList(resetPage = true)(会刷新tab页签数据，同时支持是否重置分页选项)`
1、指定刷新函数和刷新操作的名字

```ts
refreshConfig = {
    fun: this.refreshList,
    name: routesMap.groupService.taskManage,
}
```

2、调用

```ts
this.callRefresh(routesMap.groupService.taskManage)
```

```ts
// refreshConfig,callRefresh 签名如下；通过mixin混入到所有vue组件中
refreshConfig?: {
    fun: Function
    name: string | string[]
}
callRefresh(name: string, ...args: any[]): void
```

### 其他 example：如果列表页面需要依赖 URL 参数，刷新函数可这样写

```ts
refreshConfig = {
    fun: this.refresh,
    name: routesMap.groupService.serviceManage,
}

refresh() {
    this.defaultType = +(this.$route.query.type || 0)
    this.tableConfig!.preFilter = this.defaultType
        ? {
                serve_type: this.defaultType,
            }
        : {}
    this.$nextTick(() => {
        this.reloadList(true)
    })
}
```

## 详情页

跟列表一样，定义一个刷新函数去获取详情
如果详情页面中有子列表刷新逻辑如上列表页刷新
如果想直接刷新整个子组件，可以直接在子组件上绑定一个 `:key="queryParams"` 让框架去直接重新渲染组件，同时刷新组件中的数据

### demo

list: `src/views/pages/group-service/service-manage/index.vue`
detail: `src/views/pages/group-service/service-manage/service-detail/index.vue`
create: `src/views/pages/group-service/service-manage/create/index.vue`

```ts
// 刷新
this.callRefresh(routesMap.groupService.serviceManage)
// 关闭当前页面并跳转新页面
closeCurrentTap({
    name: routesMap.groupService.serviceManageDetail.detail,
    query: {
        id: r.id,
    },
})
```

`detail-row-col`: `src/views/pages/group-service/service-manage/service-detail/task-detail/components/task-question.vue`

# 面包屑

面包屑组件 `src/views/components/breadcrumb/index.vue`

```html
// 在table-container中
<table-container
    v-if="tableConfig"
    filedWidth="200"
    ref="table"
    v-model="tableConfig"
    class="container"
    :defaultFilterIsExpand="expand"
>
    <div slot="title">
        <breadcrumb :backRoute="true" :items="breadcrumbs" />
    </div>
    ... // 在普通页面如详情中
    <div class="core-ui-custom-header">
        <div class="title">
            <breadcrumb :backRoute="true" :items="breadcrumbs" />
        </div></div
></table-container>
```

| props     | 类型                                                                              | require |
| --------- | --------------------------------------------------------------------------------- | ------- |
| items     | BreadcrumbIte[]                                                                   | N       |
| backRoute | Boolean ｜ BreadcrumbItem["to"]<br />为 true 时，范围路由为 items[items.length-2] |         |

如果当前页面还会跳转子页面需要

1、将当前页面的面包屑缓存

2、并且指定当前页面的路由

> 注意 ⚠️：页面跳转在各自页面通过 `watch` 和 `computed` 去监控 `$route.query` 参数行为是未知的，所以不能使用这两种方式去生成面包屑数据

示例

```ts
private breadcrumbs:BreadcrumbItem[] = []
private setBreadcrumbs() {
    const d: BreadcrumbItem[] = [
        // 从缓存中获取上级页面面包屑
        ...getCacheBreadcrumbsByRoutePath(
            routesMap.groupService.serviceManageList
        ),
        // 当前页面的
        {
            label: this.item?.p_name || "--",
            // 如果当前页面时最后一级可以不用配置to
            to: {
                name: routesMap.groupService.serviceManageDetail.detail,
                query: {
                    ...this.$route.query,
                },
            },
        },
    ]
    // 如果当前页面时最后一级可以不用缓存
    updateTagItem({
        name: routesMap.groupService.serviceManageDetail.detail,
        breadcrumb: d,
    })
    this.breadcrumbs:BreadcrumbItem = d
}
```

如果当前页面会从多个上级页面跳转，可以这样写，跳转时带一个 from 参数

```ts
private breadcrumbs:BreadcrumbItem[] = []
private setBbreadcrumbs() {
    const d: BreadcrumbItem[] = [
        {
            label: "服务对象",
        },
    ]
    const routeMap = {
        task: routesMap.groupService.serviceManageDetail.task,
        project: routesMap.groupService.serviceManageDetail.detail,
        pc: routesMap.groupService.serviceManageDetail.item,
    }
    if (this.from) {
        d.unshift(...getCacheBreadcrumbsByRoutePath(routeMap[this.from]))
    }
    this.breadcrumbs = d
}
```

`detail-row-col`: `src/views/pages/group-service/service-manage/service-detail/task-detail/components/task-question.vue`

# 菜单权限和功能权限

核心逻辑代码 `src/installer/role.ts` 提供了三种方式进行权限验证

## 通过 `v-role="roleConfig"`

其中 `roleConfig` 类型为

```ts
export type roleConfig =
    | string
    | (() => boolean)
    | ((() => boolean) | string)[]
    | undefined
```

### 操作权限规则

```
操作权限key规则：
action的规则：model.{{model_name}}.action.{{action_name}}
intent的规则：model.{{model_name}}.intent.{{intent_name}}
非匿名的领域服务的规则：domain.{{project_name}}.service.{{service_name}}.{{function_name}}
默认列表的导出规则：model.{{model_name}}.export.list
指定列表的导出规则：model.{{model_name}}.export.list.{{list_name}}
```

### demo

```html
<el-button
    @click="clickClose"
    v-role="[
        'model.serve_project_submit_record.action.update',
        'model.serve_project_submit_record.action.insert',
    ]"
    type="primary"
    plain
>
    取消
</el-button>
```

## 通过路由配置

在路由 `meta`中加入 `role:roleConfig`

### demo1

```
meta: {
    title: "企业列表",
    role: "/tablelist/xg_agent/company_back_list",
}
```

### demo2

```
const companyAdminRole: roleConfig = () => {
    return userService.getCurAgent()?.data.sys_role === CompanyRole.超级管理员
}
meta: {
    title: "管理员列表",
    role: companyAdminRole,
},
```

## 在逻辑代码中使用

### demo1

```ts
if (checkRole("model.serve_project.action.update_status")) {
    this.showStatusEdit = !this.showStatusEdit
}
```

### demo2

```ts
const checkRouteRoleAndNext = () => {
    if (checkRole(to.meta?.role)) {
        next()
    } else {
        router.replace({ name: routesMap.role.no })
    }
}
```
