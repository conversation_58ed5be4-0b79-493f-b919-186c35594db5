<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 content">
            <div class="placeholder" v-show="loading" v-loading="true"></div>
            <form-builder
                ref="formBuilder"
                labelWidth="100px"
                v-show="!loading"
            ></form-builder>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="submitValidate"
                    :loading="btnLoading"
                    class="custom-btn btn u-m-0"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import {
        buildFormSections,
        FileType,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { Action } from "uniplat-sdk"
    import { routesMap } from "@/router/direction"

    @Component({ components: { FormBuilder } })
    export default class AddComparePop extends Mixins(
        DialogController,
        FormController
    ) {
        @Prop({ default: "" })
        private readonly id!: number

        private action?: Action

        private btnLoading = false

        private get title() {
            return "新增比对任务"
        }

        onOpen() {
            this.init()
        }

        onClosing() {
            setTimeout(() => {
                this.resetFormFields()
                this.clearForm()
            }, 300)
        }

        private getAction() {
            return (this.action = sdk.core
                .model("data_comparison_task")
                .action("add_data_comparison_task")
                .updateInitialParams({
                    selected_list: this.id ? [{ v: 0, id: this.id }] : [],
                }))
        }

        private init() {
            this.loading = true
            return buildFormSections({
                action: this.getAction(),
                forms: [
                    {
                        label: "任务名称",
                        type: FormType.Text,
                        prop: "task_name",
                    },
                    {
                        label: "需求描述",
                        type: FormType.Text,
                        prop: "description",
                        option: {
                            type: "textarea",
                            rows: 4,
                        },
                    },
                    this.id
                        ? {
                              label: "数据比对功能",
                              type: FormType.Select,
                              prop: "data_comparison_id",
                              defaultValue: this.id,
                          }
                        : {
                              label: "数据比对功能",
                              type: FormType.Select,
                              prop: "data_comparison_id",
                          },
                    {
                        label: "比对项",
                        type: FormType.Select,
                        prop: "comparison_type",
                    },
                    {
                        label: "比对数据",
                        type: FormType.MyUpload,
                        prop: "source_url",
                        option: {
                            fileType: [FileType.Excel],
                            drag: true,
                            placeholder:
                                "导入表格中的身份证号和手机号码需要是文本格式",
                            limit: 1,
                        },
                        col: {
                            span: 24,
                            offset: 0,
                        },
                    },
                    {
                        label: "",
                        prop: "t",
                        type: FormType.Tip,
                        option: {
                            placeholder: "单次比对数据不超过1万条",
                        },
                        itemStyle: {
                            color: "#606266",
                            'margin-top': "-8px",
                            'font-size': '12px'
                        },
                    },
                ],
            }).then((r) => {
                this.buildFormFull(r)
                this.loading = false
            })
        }

        private submitValidate() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                    })
                }
            })
        }

        private submit(data: any) {
            this.btnLoading = true
            pageLoading(() => {
                return sdk.core
                    .model("data_comparison_task")
                    .action("add_data_comparison_task")
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: this.id ? [{ v: 0, id: +this.id }] : [],
                    })
                    .execute()
                    .then(() => {
                        this.$message.success("新建成功")
                        this.callRefresh(routesMap.dataStorage.personDataCompare)
                        this.callRefresh(
                            routesMap.dataStorage.personDataCompareDetail
                        )
                        this.callRefresh(
                            routesMap.dataStorage.personDataCompareTask
                        )
                        this.close()
                    })
                    .finally(() => (this.btnLoading = false))
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
