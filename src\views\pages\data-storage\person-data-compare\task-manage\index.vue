<template>
    <div class="content">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            @getRows="getRows"
        >
            <div
                slot="title"
                class="d-flex-item-center bold justify-content-between"
            >
                <bread-crumb :backRoute="false" :items="breadcrumbs" />

                <div>
                    <el-button
                        type="primary"
                        v-if="showBtn"
                        @click="showAddPop = true"
                        >新建比对任务</el-button
                    >
                </div>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div
                        slot="h"
                        class="u-flex u-row-center flex-wrap"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <AddPop v-model="showAddPop" />
    </div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { pageLoading } from "@/views/controller"
    import {
        buildConfig4RemoteMeta,
        getShowBtn4Page,
    } from "@/views/pages/collect-task-manage/components/build-table"
    import { cloneDeep } from "lodash"
    import AddPop from "../function-manage/components/add-compare-pop.vue"

    @Component({
        name: routesMap.dataStorage.personDataCompareTask,
        components: {
            TableContainer,
            CommonTable,
            AddPop,
        },
    })
    export default class CompareTaskManage extends BaseTableController<any> {
        tableConfig: TableConfig | null = null

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.dataStorage.personDataCompareTask,
        }

        private columns: TableColumn[] = []

        private routesName = routesMap.dataStorage.personDataCompareTask

        private displayResetPassword = false

        private displayBindUser = false

        private displayServiceRegion = false

        private showAddPop = false

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "数据比对任务列表",
                    to: {
                        name: this.routesName,
                    },
                },
            ]
            updateTagItem({
                name: this.routesName,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private currentRow = {}

        private showBtn = false

        private rows: any[] = []

        private curId = ""

        mounted() {
            this.setBreadcrumbs()
            this.init()
        }

        private bindUser(id: string) {
            this.curId = id
            this.displayBindUser = true
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private init() {
            pageLoading(() => {
                return buildConfig4RemoteMeta(
                    "data_comparison_task",
                    "data_comparison_task_list",
                    {
                        disabledFilter: true,
                        useLabelWidth: true,
                        useRowFieldGroups: true,
                        useTabs: true,
                        optColumn: {
                            label: "操作",
                            prop: "h",
                            fixed: "right",
                            minWidth: "110px",
                        },
                        // customLabelWidths: {
                        //     身份证号: 180,
                        //     所属区域: 420,
                        // },
                    }
                ).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            this.showBtn = getShowBtn4Page(r, "add_data_comparison_task")

            tableConfig.filter = r.filter
            tableConfig.predict = {
                ...r.tableConfig.predict,
                access_key: "_access_key",
                actions: "actions",
                intents: "intents",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = r.columns.filter((i: any) => i.prop !== "id")
        }

        private toDetail(item: any) {
            this.$router.push({
                name: routesMap.dataStorage.personDataCompareTaskDetail,
                query: {
                    id: item.access_key,
                    taskId: item.id,
                    from: this.routesName,
                },
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
