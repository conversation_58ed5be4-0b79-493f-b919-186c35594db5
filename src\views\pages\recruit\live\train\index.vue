<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container-index container"
        >
            <div slot="title" class="u-flex u-row-between">
                <div>培训管理</div>
                <div>
                    <el-button type="primary" @click="edit('')">
                        创建培训
                    </el-button>
                </div>
            </div>
            <div slot="table" slot-scope="{ data }" class="bg-white">
                <common-table :data="data" :columns="columns">
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                        <template
                            v-if="
                                scope.row.train_audit_status ===
                                liveAuditStatus.审核通过
                            "
                        >
                            <el-button
                                v-if="
                                    scope.row.train_shelf_status ===
                                    liveShelfStatus.未发布
                                "
                                type="text"
                                @click="toggleShell(scope.row)"
                            >
                                上架
                            </el-button>
                            <el-button
                                v-else
                                type="text"
                                @click="toggleShell(scope.row)"
                            >
                                下架
                            </el-button>
                        </template>
                        <el-button
                            v-if="
                                scope.row.train_audit_status !==
                                    liveAuditStatus.审核通过 ||
                                scope.row.train_shelf_status ===
                                    liveShelfStatus.未发布
                            "
                            type="text"
                            @click="edit(scope.row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-if="
                                scope.row.train_audit_status ===
                                liveAuditStatus.待审核
                            "
                            type="text"
                            @click="audit(scope.row)"
                        >
                            审核
                        </el-button>
                        <el-button
                            v-if="
                                scope.row.train_audit_status ===
                                liveAuditStatus.草稿
                            "
                            type="text"
                            @click="toAudit(scope.row)"
                        >
                            提交审核
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <CommonPop
            labelWidth="140px"
            v-model="showAudit"
            title="审核"
            sdkModel="training_record"
            sdkAction="setAudit"
            @refresh="reloadList"
            :id="auditId"
        />
    </div>
</template>

<script lang='ts'>
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { routesMap } from "@/router/direction"
    import { Component } from "vue-property-decorator"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
    import { LiveAuditStatus, LiveShelfStatus, toAudit, toggleShell } from "."
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import CommonPop from "@/views/components/common-pop/index.vue"

    @Component({
        name: routesMap.recruit.live.train,
        components: {
            CommonPop,
            TableContainer,
            CommonTable,
        },
    })
    export default class Template extends BaseTableController<any> {
        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []
        private liveAuditStatus = LiveAuditStatus
        private liveShelfStatus = LiveShelfStatus
        private showAudit = false
        private auditId = ""

        mounted() {
            this.init()
        }

        private init() {
            return buildConfig4RemoteMeta("training_record", "list_operator", {
                useLabelWidth: true,
                disabledOpt: false,
                useTabs: true,
                optColumn: {
                    label: "操作",
                    prop: "h",
                    fixed: "right",
                    width: "200px",
                },
            }).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig
            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            this.columns = r.columns
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.recruit.live.trainDetail,
                query: {
                    id: row?._access_key || undefined,
                    from: this.$route.name,
                },
            })
        }

        private toggleShell(row: any) {
            toggleShell(row).then(() => {
                this.reloadList()
            })
        }

        private toAudit(row: any) {
            toAudit(row).then(() => {
                this.reloadList()
            })
        }

        private audit(row: any) {
            this.auditId = row.id
            this.showAudit = true
        }

        private edit(row: any) {
            this.$router.push({
                name: routesMap.recruit.live.trainAdd,
                query: {
                    id: row?.id || undefined,
                    from: this.$route.name,
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
