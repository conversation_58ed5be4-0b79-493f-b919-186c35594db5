<template>
    <div ref="chart" class="chart" />
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import * as echarts from "echarts/core"
    import { graphic, ComposeOption } from "echarts/core"
    import { <PERSON>vasRenderer } from "echarts/renderers"
    import { LineChart, LineSeriesOption } from "echarts/charts"
    import {
        TooltipComponent,
        LegendComponent,
        GridComponent,
        TooltipComponentOption,
        LegendComponentOption,
        GridComponentOption,
    } from "echarts/components"

    // 按需引入 ECharts 组件
    echarts.use([
        <PERSON>vas<PERSON>enderer,
        LineChart,
        TooltipComponent,
        LegendComponent,
        GridComponent,
    ])

    type EChartsOption = ComposeOption<
        | GridComponentOption
        | TooltipComponentOption
        | LegendComponentOption
        | LineSeriesOption
    >

    // 定义数据接口
    interface ChartData {
        date: string
        share_count: number
    }

    @Component({
        name: "<PERSON><PERSON><PERSON><PERSON>",
    })
    export default class Ledger<PERSON><PERSON> extends Vue {
        @Prop({
            type: Array,
            default: () =>
                Array.from({ length: 30 }, (_, i) => ({
                    time: `2024-01-${i + 1}`,
                    count: Math.floor(Math.random() * 200) + 200,
                })),
        })
        readonly data!: ChartData[]

        @Prop({ type: String, default: "#1770E566" })
        readonly lineColor!: string

        @Prop({ type: String, default: "推荐次数" })
        readonly seriesName!: string

        private chart: echarts.ECharts | null = null
        private throttledResizeHandler!: () => void

        get xAxisData(): string[] {
            return this.data.map((item: ChartData) => item.date)
        }

        get seriesData(): number[] {
            return this.data.map((item: ChartData) => item.share_count)
        }

        get option(): EChartsOption {
            return {
                tooltip: {
                    trigger: "axis",
                    formatter: (params: any) => {
                        const xAxisValue = params[0].axisValue
                        let result = `<div style="font-size: 14px; color: #747EB2; margin-bottom: 2px; text-align: center; width: 100%;">${xAxisValue}</div>`
                        params.forEach((item: { seriesName: any; value: any }) => {
                            result += `<div style="font-size: 14px; color: #293366;display: flex; align-items: center; justify-content: center; margin-top: 2px; margin-right: 2px; text-align: center; width: 100%;">${item.seriesName} <span style="display: inline-block; margin-left: 15px; color: ${this.lineColor}; font-size: 16px; font-weight: bold;"> ${item.value}</span></div>`
                        })
                        return result
                    },
                    backgroundColor: "rgba(255, 255, 255, 0.50)",
                    borderRadius: 10,
                    padding: [15, 15],
                    confine: true,
                    extraCssText:
                        " width: 115.5px !important; height: 60px !important; flex-shrink: 0; box-shadow: 0px 5px 20px 0px rgba(107, 96, 168, 0.20); backdrop-filter: blur(12.5px); overflow: hidden; display: flex !important; flex-direction: column !important; justify-content: center !important; align-items: center !important;",
                },
                grid: {
                    left: "3%",
                    right: "6%",
                    bottom: "3%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    boundaryGap: false,
                    data: this.xAxisData,
                    axisLabel: {
                        fontSize: 16,
                        margin: 20,
                        color: "#767676",
                    },
                    axisLine: {
                        lineStyle: {
                            color: "#E5E5E5",
                        },
                    },
                },
                yAxis: {
                    type: "value",
                    axisLabel: {
                        fontSize: 16,
                        margin: 20,
                        color: "#767676",
                    },
                    axisLine: {
                        lineStyle: {
                            color: "#E5E5E5",
                        },
                    },
                    splitLine: {
                        lineStyle: {
                            color: "#E5E5E5",
                        },
                    },
                },
                series: [
                    {
                        name: this.seriesName,
                        type: "line",
                        smooth: true,
                        data: this.seriesData,
                        lineStyle: {
                            color: this.lineColor,
                            width: 1,
                        },
                        showSymbol: false,
                        areaStyle: {
                            color: new graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: this.lineColor,
                                },
                                {
                                    offset: 1,
                                    color: "rgba(255, 255, 255, 0)",
                                },
                            ]),
                        },
                    },
                ],
            }
        }

        @Watch("option", { deep: true })
        private onOptionChanged(newOption: EChartsOption) {
            if (this.chart) {
                this.chart.setOption(newOption)
            }
        }

        created() {
            this.throttledResizeHandler = this.throttle(this.handleResize, 200)
        }

        mounted() {
            this.initChart()
            window.addEventListener("resize", this.throttledResizeHandler)
        }

        beforeDestroy() {
            window.removeEventListener("resize", this.throttledResizeHandler)
            if (this.chart) {
                this.chart.dispose()
                this.chart = null
            }
        }

        private initChart() {
            const chartDom = this.$refs.chart as HTMLElement
            if (chartDom) {
                this.chart = echarts.init(chartDom)
                this.chart.setOption(this.option)
            }
        }

        private handleResize() {
            if (this.chart) {
                this.chart.resize()
            }
        }

        private throttle(func: () => void, delay: number) {
            let timeout: ReturnType<typeof setTimeout> | null = null
            return () => {
                if (timeout) {
                    return
                }
                timeout = setTimeout(() => {
                    func.apply(this)
                    timeout = null
                }, delay)
            }
        }
    }
</script>

<style scoped>
    .chart {
        height: 400px;
    }
</style>
