<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            @getRows="getRows"
            :showExpand="false"
        >
            <div slot="title" class="d-flex-item-center bold">
                <div class="d-flex-item-center bold">
                    <bread-crumb :backRoute="false" :items="breadcrumbs" />
                </div>
            </div>

            <div slot="header-right">
                <el-button
                    type="primary"
                    plain
                    @click="toApplyRecommend"
                    v-role="[
                        'model.xg_company_position_recommend.action.recommend_apply_6',
                    ]"
                    v-if="!isEZ"
                >
                    申请推广
                </el-button>
                <el-button
                    type="primary"
                    @click="toFormList"
                    class="custom-btn batch-btn"
                    role="/tablelist/general_questionnaire/policy_form"
                >
                    表单维护
                </el-button>
                <el-button
                    type="primary"
                    @click="toCreate"
                    v-if="showBtn"
                    class="custom-btn batch-btn"
                >
                    创建政策
                </el-button>
            </div>

            <div
                slot="table"
                slot-scope="{ data, index }"
                class="u-p-20 bg-white"
            >
                <common-table
                    :data="data"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div slot="序号" slot-scope="scope">
                        {{ (index - 1) * 10 + scope.index + 1 }}
                    </div>
                    <div slot="政策名称" slot-scope="scope">
                        <span
                            class="primary pointer"
                            @click="toDetail(scope.row)"
                        >
                            {{ scope.row.policy_name }}
                        </span>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center u-flex-wrap"
                        slot-scope="scope"
                    >
                        <el-button
                            type="text"
                            v-if="getBtnInfo(scope.row.id, 'update_form')"
                            @click="toEdit(scope.row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            type="text"
                            v-if="getBtnInfo(scope.row.id, 'offline')"
                            @click="toOperate(scope.row, 'offline')"
                        >
                            下架
                        </el-button>
                        <el-button
                            type="text"
                            v-if="getBtnInfo(scope.row.id, 'online')"
                            @click="toOperate(scope.row, 'online')"
                        >
                            上架
                        </el-button>
                        <el-button
                            type="text"
                            v-if="getBtnInfo(scope.row.id, 'delete')"
                            @click="toOperate(scope.row, 'delete')"
                        >
                            删除
                        </el-button>
                        <el-button
                            type="text"
                            @click="changeOrder(scope.row)"
                            v-role="['model.policy_form.action.update_order']"
                        >
                            修改排序
                        </el-button>
                        <el-button type="text" @click="toDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <RecommendApply
            :isInJobFair="true"
            v-model="showRecommendApply"
            :checkEdIds="checkEdIds"
            @refresh="reloadList"
        />
        <common-pop
            v-model="showOrder"
            title="修改排序"
            sdkModel="policy_form"
            sdkAction="update_order"
            :id="pid"
            @refresh="reloadList"
            :labelWidth="'160px'"
        ></common-pop>
    </div>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { SelectOption } from "@/core-ui/component/form"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { pageLoading } from "@/views/controller"
    import UpdateSingleType from "@/views/pages/policy/manage/components/update-single-type.vue"
    import UpdateTypePop from "@/views/pages/policy/manage/components/update-type-pop.vue"
    import { MessageBox } from "element-ui"
    import { cloneDeep } from "lodash"
    import { Component } from "vue-property-decorator"
    import RecommendApply from "../baseService/policy/components/recommend-apply.vue"
    import { buildConfig4RemoteMeta } from "../collect-task-manage/components/build-table"
    import { updateTagItem } from "../single-page/components/tags-view"
    import { publishTableFilter, Row } from "./index"

    @Component({
        name: routesMap.publishPolicy.list,
        components: {
            TableContainer,
            CommonTable,
            UpdateTypePop,
            UpdateSingleType,
            RecommendApply,
            CommonPop,
        },
    })
    export default class PublishPolicyIndex extends BaseTableController<Row> {
        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []

        private checkEdIds: Array<number | string> = []
        private selectList: Row[] = []
        showRecommendApply = false
        private isEZ = config.envProject === EnvProject.鄂州项目
        private pid = ""
        private showOrder = false

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.rows.map((i) => i.id)
            this.selectList = d.rows
        }

        toApplyRecommend() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要推广的招聘会")
            }
            this.showRecommendApply = true
        }

        refreshConfig = {
            name: routesMap.publishPolicy.list,
            fun: this.reloadList,
        }

        private showBtn = false

        private rows: any[] = []

        breadcrumbs: BreadcrumbItem[] = []

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "政策管理",
                    to: {
                        name: this.$route.name as string,
                    },
                },
            ]
            updateTagItem({
                name: this.$route.name as string,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        created() {
            this.setBreadcrumbs()
            this.init()
        }

        private init() {
            return buildConfig4RemoteMeta("policy_form", "manage_list", {
                useLabelWidth: true,
                useTabs: true,
                disabledFilter: true,
                optColumn: {
                    label: "操作",
                    prop: "h",
                    fixed: "right",
                    minWidth: "160px",
                },
            }).then((r) => {
                this.buildConfig(r)
            })
        }

        private buildConfig(r: any) {
            const tableConfig = r.tableConfig as TableConfig
            tableConfig.filter = publishTableFilter
            tableConfig.filter?.forEach((i) => {
                if (i.label === "对外发布单位") {
                    i.label = "所属科室"
                }
            })
            const serveTargetType = (r.meta.filters || []).find(
                (item: { property: string }) => {
                    return item.property === "serve_target_type"
                }
            )
            if (serveTargetType) {
                const o = _.get(
                    serveTargetType,
                    "ext_properties.mapping.mapping_values",
                    []
                ) as SelectOption[]
                const hasOrg =
                    o.findIndex((item) => {
                        return item.value === "企业"
                    }) !== -1

                if (!hasOrg) {
                    tableConfig.oneTab = true
                }
            }

            if (r.meta.actions && r.meta.actions.length > 0) {
                this.showBtn = !!r.meta.actions.find(
                    (i: any) => i.action_name === "create_form"
                )
            } else {
                this.showBtn = false
            }

            this.tableConfig = null
            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })
            let defaultColumns = r.columns
            if ([EnvProject.潜江项目].includes(config.envProject)) {
                defaultColumns = defaultColumns.filter(
                    (item: { label: string }) => {
                        return (
                            item.label !== "政策找人人数" &&
                            item.label !== "政策找人服务次数" &&
                            item.label !== "政策找人办结数"
                        )
                    }
                )
            }
            this.columns = [
                {
                    prop: "select",
                    width: "58",
                    type: "selection",
                    selectable: (row: any) => {
                        return !row.is_recommend
                    },
                },
                ...defaultColumns,
            ]
        }

        private getRows(
            rows: { intents: { name: string }[]; id: { value: string } }[]
        ) {
            const cacheRows = cloneDeep(this.rows)
            const resArr = cacheRows.filter((i) => {
                return !(rows || []).find((j) => j.id.value === i.id.value)
            })
            resArr.push(...(rows || []))
            this.rows = resArr
        }

        private getBtnInfo(id: string, key: string) {
            const t = this.rows.find((i) => i.id.value === id)
            if (!t) {
                return false
            }
            return (
                (t.actions || []).find((i: any) => i.action_name === key) ||
                (t.intents || []).find((i: any) => i.name === key)
            )
        }

        private refresh(force?: boolean) {
            if (force) {
                return this.reloadList(true)
            }
            this.refreshList()
        }

        private toEdit(row: any) {
            this.$router.push({
                name: routesMap.publishPolicy.create,
                query: { id: row.id + "", from: this.$route.name },
            })
        }

        private toCreate() {
            this.$router.push({
                name: routesMap.publishPolicy.create,
                query: { from: this.$route.name },
            })
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.publishPolicy.policyDetail,
                query: {
                    id: row._access_key + "",
                    from: this.$route.name,
                },
            })
        }

        private toOperate(row: any, actionName: string) {
            const t = this.getBtnInfo(row.id, actionName)
            MessageBox.confirm(`确认${t.label}？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("policy_form")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row.id }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            this.reloadList()
                        })
                })
            })
        }

        private toFormList() {
            this.$router.push({
                name: routesMap.publishPolicy.formList,
                query: { from: routesMap.publishPolicy.list },
            })
        }

        changeOrder(row: any) {
            this.pid = row.id
            this.showOrder = true
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
