<template>
    <div class="question-container">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :showExpand="false"
        >
            <div slot="title" class="d-flex-item-center bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div slot="header-right">
                <el-button type="primary" @click="toCreate">
                    新增问卷
                </el-button>
            </div>

            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <el-button @click="goDetail(scope.row)" type="text">
                            详情
                        </el-button>
                        <el-button
                            v-if="!scope.row.is_using"
                            type="text"
                            @click="opt('enable_questionnaire', scope.row)"
                            >启用</el-button
                        >
                        <el-button
                            v-else
                            type="text"
                            @click="opt('disable_questionnaire', scope.row)"
                            >停用</el-button
                        >
                        <el-button @click="openEdit(scope.row.id)" type="text">
                            编辑
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>

        <EditQuestionDialog
            v-model="displayQuestionDialog"
            :curId="curId"
            @refresh="refreshList"
        ></EditQuestionDialog>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { SelectOption } from "@/core-ui/component/form"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import UpdateSingleType from "@/views/pages/policy/manage/components/update-single-type.vue"
    import UpdateTypePop from "@/views/pages/policy/manage/components/update-type-pop.vue"
    import { Component } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "."
    import EditQuestionDialog from "@/views/common/form/edit-question-dialog.vue"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { updateTagItem } from "../../single-page/components/tags-view"

    @Component({
        name: routesMap.questionnaireDesign.index,
        components: {
            TableContainer,
            CommonTable,
            UpdateTypePop,
            UpdateSingleType,
            EditQuestionDialog,
        },
    })
    export default class Job extends BaseTableController<Row> {
        tableConfig: TableConfig | null = tableConfig()
        selectOption: SelectOption[] = []

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "问卷模板",
                    to: {
                        name: routesMap.questionnaireDesign.index,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.questionnaireDesign.index,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private curId = ""

        private readonly columns: TableColumn[] = columns
        private index = 1

        private displayQuestionDialog = false

        refreshConfig = {
            name: routesMap.questionnaireDesign.index,
            fun: this.reloadList,
        }

        private toCreate() {
            this.curId = ""
            this.displayQuestionDialog = true
        }

        private openEdit(id: string) {
            this.curId = id
            this.displayQuestionDialog = true
        }

        private refresh(force?: boolean) {
            if (force) {
                return this.reloadList(true)
            }
            this.refreshList()
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.questionnaireDesign.detail,
                query: { id: row.id + "" },
            })
        }

        private goDetail(row: any) {
            this.$router.push({
                name: routesMap.questionnaireDesign.detail,
                query: {
                    id: row.id,
                    from: routesMap.questionnaireDesign.index,
                },
            })
        }

        private opt(actionName: string, value: any) {
            console.log("value")
            console.log(value)
            sdk.core
                .model("general_questionnaire")
                .action(actionName)
                .updateInitialParams({
                    selected_list: [{ id: value.id, v: value.v }],
                })
                .execute()
                .then(() => {
                    this.refresh()
                })
        }

        mounted() {
            this.setBreadcrumbs()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
