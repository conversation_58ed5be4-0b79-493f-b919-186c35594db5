import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { getAddress, getSalary } from "@/utils"
import { predict } from "@/views/page-company/recruit/job/index"
import { ListTypes } from "uniplat-sdk"
const itemStyle = {
    color: "#4E5054",
    marginLeft: "-60px",
    fontSize: "18px",
    fontWeight: "bold",
}
export function createFormConfig(
    id: number,
    agent_id: string,
    agentRow: any
): BuildFormConfig {
    return {
        sdkModel: "job_fair",
        sdkAction: "agent_apply",
        id,
        forms: [
            {
                type: FormType.Tip,
                prop: "t",
                label: "",
                defaultValue: "789",
                option: {
                    placeholder: "企业信息",
                },
                itemStyle,
            },
            {
                label: "企业名称",
                type: FormType.Text,
                prop: "agent_name",
                col: {
                    span: 11,
                },

                noEdit: true,
            },
            {
                label: "统一社会信用代码",
                type: FormType.Text,
                prop: "company_code",
                col: {
                    span: 11,
                    offset: 2,
                },

                noEdit: true,
            },
            {
                label: "企业法人",
                type: FormType.Text,
                prop: "legal_person",
                col: {
                    span: 11,
                },

                noEdit: true,
            },
            {
                label: "企业法人",
                type: FormType.Text,
                prop: "legal_person",
                option: {
                    placeholder: "请输入企业法人姓名",
                    disabled: !!agentRow.legal_person,
                },
                required: true,
                col: {
                    span: 11,
                    offset: 2,
                },
            },
            {
                label: "法人身份证",
                type: FormType.Text,
                prop: "legal_card_open_id",
                option: {
                    disabled: !!agentRow.legal_card_open_id,
                    placeholder:
                        "请输入企业法人身份证号(支持港澳台身份证和护照)",
                },
                required: true,
                col: {
                    span: 11,
                },
                rules: [
                    {
                        validator(rule, value, callback) {
                            if (
                                !_.isIdentityNumberWidthPassportAndHongkongAndTaiwan(
                                    value
                                )
                            ) {
                                callback(new Error("请填写正确的身份证号"))
                                return
                            }
                            callback()
                        },
                        message: "请填写正确的身份证号",
                    },
                ],
            },
            {
                label: "",
                type: FormType.Tip,
                prop: "a",
                option: {
                    placeholder: "",
                },
                col: {
                    span: 11,
                    offset: 2,
                },

                noEdit: true,
            },
            {
                label: "所属地区",
                type: FormType.Text,
                prop: "city",
                col: {
                    span: 24,
                },
                noEdit: true,
            },
            {
                label: "企业简介",
                type: FormType.Text,
                prop: "remark",
                col: {
                    span: 24,
                },
                noEdit: true,
            },
            {
                label: "所属行业",
                type: FormType.Cascader,
                prop: "industory_catalog",
                option: {
                    elProps: { checkStrictly: true },
                    disabled: !!agentRow.industory_catalog,
                },
                col: {
                    span: 11,
                },
                required: true,
            },
            {
                label: "人员规模",
                type: FormType.Select,
                prop: "company_size",
                col: {
                    span: 11,
                    offset: 2,
                },
                option: {
                    disabled: !!agentRow.company_size,
                },
                required: true,
            },
            {
                label: "公司简介",
                type: FormType.Text,
                option: { type: "textarea", disabled: !!agentRow.remark },
                required: true,
                defaultValue: agentRow.remark,
                prop: "remark",
            },
            {
                type: FormType.Tip,
                prop: "t1",
                label: "",
                defaultValue: "789",
                option: {
                    placeholder: "联系信息",
                },
                itemStyle,
            },
            {
                label: "企业联系人",
                type: FormType.Text,
                prop: "contact_person",
                col: {
                    span: 11,
                },
                required: true,
            },
            {
                label: "联系手机号",
                type: FormType.Text,
                prop: "contact_mobile",
                col: {
                    span: 11,
                    offset: 2,
                },
                required: true,
            },
            {
                label: "企业办公地址",
                type: FormType.Cascader,
                prop: "region_code",
                option: { elProps: { checkStrictly: true } },
                col: {
                    span: 11,
                },
                required: true,
            },
            {
                label: "办公地址详情",
                type: FormType.Text,
                prop: "address_detail",
                col: {
                    span: 11,
                    offset: 2,
                },
                required: true,
            },
            {
                label: "选择职位",
                type: FormType.IntentSearch,
                prop: "position_id",
                hide: false,
                option: {
                    dialogProp: {
                        width: "1200px",
                    },
                    intentSearchConfig: {
                        multi: true,
                        tableConfig: () => ({
                            model: sdk.core
                                .model("xg_company_position")
                                .list("intent_search_list"),
                            preFilter: { status: "1", agent_id },
                            predict: positionPpredict,
                            filter: [
                                {
                                    label: "岗位名称",
                                    type: FormType.Text,
                                    prop: "name",
                                    keyValueFilter: {
                                        match: ListTypes.filterMatchType.fuzzy,
                                    },
                                },
                            ],
                            column: [
                                {
                                    label: "岗位名称",
                                    prop: "name",
                                    width: "200px",
                                    align: "left",
                                    showOverflowTip: true,
                                },
                                {
                                    label: "薪资待遇",
                                    prop: "salary",
                                    render(h, row) {
                                        return h(
                                            "span",
                                            {},
                                            getSalary(row.salary_desc)
                                        )
                                    },
                                    showOverflowTip: true,
                                },
                                {
                                    label: "工作地区",
                                    prop: "cal_address_detail",
                                    showOverflowTip: true,
                                },
                                {
                                    label: "招聘人数",
                                    prop: "recruit_count",
                                    showOverflowTip: true,
                                },
                                {
                                    label: "来源",
                                    prop: "source_from_type_label",
                                    showOverflowTip: true,
                                },
                                {
                                    label: "年龄要求",
                                    prop: "age_require_label",
                                    showOverflowTip: true,
                                },
                                {
                                    label: "岗位状态",
                                    prop: "status_label",
                                    showOverflowTip: true,
                                },
                                {
                                    label: "上架状态",
                                    prop: "online_status_label",
                                    showOverflowTip: true,
                                },
                                {
                                    label: "发布时间",
                                    prop: "create_time_label",
                                    showOverflowTip: true,
                                    width: "160px",
                                },
                            ],
                        }),
                        template: "{name}",
                        valueKey: "id",
                    },
                },
            },
            // {
            //     type: FormType.Tip,
            //     prop: "t3",
            //     label: "",
            //     defaultValue: "789",
            //     option: {
            //         placeholder: "选择岗位",
            //     },
            //     itemStyle,
            // },
        ],
    }
}

const tableFilter: TableFilter[] = [
    {
        label: "岗位名称",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
]

export const positionPpredict = predict
export function tableConfig(
    agent_id: string,
    selected: string[] = []
): TableConfig {
    return {
        model: sdk.core.model("xg_company_position").list("intent_search_list"),
        filter: tableFilter,
        defaultPageSize: 5,
        predict: positionPpredict,
        preFilter: {
            // online_status: 1,
            agent_id,
            status: 1,
            id__nin: selected.length ? JSON.stringify(selected) : "",
        },
    }
}

export const columns: TableColumn[] = [
    { prop: "select", width: "55px", align: "center" },
    { label: "岗位名称", prop: "name", align: "left", showOverflowTip: true },
    {
        label: "薪资待遇",
        prop: "salary",
        render(h, row) {
            return h("span", {}, row.salary_desc)
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "工作地区",
        prop: "city",
        render: (h, row) => {
            return h("span", {}, getAddress(row))
        },
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "招聘人数",
        prop: "recruit_count",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "来源",
        prop: "source_from_type_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "年龄要求",
        prop: "age_require_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "岗位状态",
        prop: "status_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "上架状态",
        prop: "online_status_label",
        align: "left",
        showOverflowTip: true,
    },
    {
        label: "发布时间",
        prop: "create_time_label",
        align: "left",
        showOverflowTip: true,
    },
    { label: "操作", prop: "h", align: "left", showOverflowTip: true },
]

export function add_position(id: string, list: string[]) {
    return sdk.core
        .model("job_fair_agent_apply")
        .action("add_position")
        .addInputs_parameter({ position_id: JSON.stringify(list) })
        .updateInitialParams({
            selected_list: [{ id, v: 0 }],
        })
        .execute()
}

export function getSelectedPosition(id: string) {
    return sdk.core
        .model("job_fair_agent_apply")
        .action("add_position")
        .updateInitialParams({
            selected_list: [{ id, v: 0 }],
        })
        .query()
        .then((r) => {
            let arr = []
            try {
                arr = JSON.parse(
                    r.parameters.inputs_parameters[0].default_value || ""
                )
            } catch {}
            return arr
        })
}
