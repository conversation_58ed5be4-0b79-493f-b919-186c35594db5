<template>
    <div class="container-box core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>

        <div class="page-detail u-p-t-30 u-p-b-30">
            <form-builder
                v-if="show"
                ref="formBuilder"
                label-position="right"
                label-width="140px"
            ></form-builder>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button type="primary" plain @click="cancel">
                    取消
                </el-button>
                <el-button type="primary" @click="confirm"> 确定 </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import { sdk } from "@/service"
    import { closeCurrentTap } from "../../single-page/components/tags-view"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { forms } from "./index"
    import { cloneDeep, find } from "lodash"
    import { sleep } from "@/utils"

    interface CheckRes {
        masters: { default_value: string; property: string }[]
    }

    @Component({
        name: routesMap.branch.service.add,
        components: { FormBuilder },
    })
    export default class Template extends FormController {
        private show = false
        breadcrumbs: BreadcrumbItem[] = [
            {
                label: `服务网点管理`,
                to: {
                    name: routesMap.branch.service.index,
                },
            },
            {
                label: this.$route.query.id ? "编辑网点" : "新增网点",
                to: {
                    name: routesMap.branch.service.add,
                    query: {
                        id: this.$route.query.id || "",
                    },
                },
            },
        ]

        refreshConfig = {
            fun: this.init,
            name: routesMap.branch.service.add,
        }

        private forms = cloneDeep(forms)

        private timer = 0
        private curCode = ""

        private get title() {
            return this.$route.meta?.title || ""
        }

        mounted() {
            this.init()
        }

        private async init() {
            this.show = false
            await sleep(0).then(() => {
                this.show = true
            })
            return buildFormSections({
                ...this.forms,
                action: sdk.core
                    .model("server_stage")
                    .action(this.$route.query.id ? "update" : "add")
                    .updateInitialParams({
                        selected_list: this.$route.query.id
                            ? [{ id: this.$route.query.id + "", v: 0 }]
                            : [],
                    }),
            }).then((r) => {
                const addressEditFiled = find(r.forms, {
                    prop: "addressEditFiled",
                })
                if (addressEditFiled) {
                    const data: any = {}
                    const subForms: any = {}
                    ;["address_detail", "lat", "lng"].forEach((i) => {
                        const d = find(r.forms, { prop: i })
                        data[i] = r.data[i]
                        subForms[i] = d
                    })
                    addressEditFiled.extendData = {
                        subForms,
                        displayValue: data.address_detail,
                    }
                    addressEditFiled.defaultValue = data
                }
                this.buildFormFull(r)
            })
        }

        private confirm() {
            const result = this.getFormValues() as any
            this.validateForm((v: boolean) => {
                if (v) {
                    sdk.core
                        .model("server_stage")
                        .action(this.$route.query.id ? "update" : "add")
                        .addInputs_parameter({
                            ...result,
                            ...result.addressEditFiled,
                            mobile: result.mobile?.trim(),
                        })
                        .updateInitialParams({
                            selected_list: this.$route.query.id
                                ? [{ id: this.$route.query.id + "", v: 0 }]
                                : [],
                        })
                        .execute()
                        .then(() => {
                            this.$message.success(
                                this.$route.query.id ? "修改成功" : "创建成功"
                            )
                            this.callRefresh(routesMap.branch.service.index)
                            this.callRefresh(routesMap.branch.service.detail)
                            this.cancel()
                        })
                }
            })
        }

        private cancel() {
            closeCurrentTap()
        }

        private handleChange(fn: Function) {
            clearTimeout(this.timer)
            this.timer = setTimeout(() => {
                fn && fn()
            }, 500)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .page-detail {
        background-color: #fff;
        padding-left: 20px;
        padding-right: 20px;
        min-height: 50%;
    }
</style>
