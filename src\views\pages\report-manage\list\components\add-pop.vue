<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        :title="title"
        width="600px"
        top="8vh"
    >
        <div class="u-p-x-20 content" :class="{ 'u-p-r-30': !id }">
            <div class="placeholder" v-show="loading" v-loading="true"></div>
            <form-builder
                ref="formBuilder"
                labelWidth="160px"
                v-show="!loading"
            ></form-builder>
            <div class="u-flex u-m-t-20 u-row-center">
                <el-button
                    type="primary"
                    @click="close"
                    plain
                    class="custom-btn btn u-m-r-30"
                >
                    取消
                </el-button>
                <el-button
                    type="primary"
                    @click="submitValidate"
                    class="custom-btn btn u-m-0"
                >
                    {{ id ? "确定" : "提交" }}
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins, Prop } from "vue-property-decorator"
    import { Action } from "uniplat-sdk"
    import { routesMap } from "@/router/direction"

    @Component({ components: { FormBuilder } })
    export default class AddPop extends Mixins(
        DialogController,
        FormController
    ) {
        private action?: Action

        @Prop({ default: "" })
        private readonly id!: number

        private get title() {
            return this.id ? "编辑报表" : "新建报表"
        }

        onOpen() {
            this.init()
        }

        onClosing() {
            this.resetFormFields()
        }

        private getAction() {
            return (this.action = sdk.core
                .model("xg_indicator_group_ref")
                .action(this.id ? "update" : "insert")
                .updateInitialParams({
                    selected_list: this.id ? [{ v: 0, id: this.id }] : [],
                }))
        }

        private init() {
            this.loading = true
            return buildFormSections({
                action: this.getAction(),
                forms: [],
            }).then((r) => {
                this.buildFormFull(r)
                this.loading = false
            })
        }

        private submitValidate() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                    })
                }
            })
        }

        private submit(data: any) {
            const id = this.id
            pageLoading(() => {
                return sdk.core
                    .model("xg_indicator_group_ref")
                    .action(id ? "update" : "insert")
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: id ? [{ v: 0, id: +id }] : undefined,
                    })
                    .execute()
                    .then(() => {
                        this.$message.success(id ? "编辑成功" : "新建成功")
                        this.callRefresh(routesMap.reportManage.list.index)
                        this.callRefresh(routesMap.reportManage.list.detail)
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        position: relative;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
</style>
