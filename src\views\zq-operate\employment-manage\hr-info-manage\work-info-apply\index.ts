import { BuildFormConfig, FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
import { checkRowPredict, commonColumn } from "../work-info-audit"

const tableFilter: TableFilter[] = [
    {
        label: "调查名称",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        prop: "create_time",
        label: "发布时间",
        type: FormType.DatePicker,
    },
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_province_region_code",
        option: {
            elProps: {
                checkStrictly: true,
            },
            filterable: true,
        },
    },
]

export const rowPredict = {
    push_time: "",
    title: "",
    create_time: "",
    update_time: "",
    task_count: "",
    task_audit_count: "",
    task_notfill_count: "",
    task_over_count: "",
    mgt_province: "mgt_province#region_name",
    mgt_city: "mgt_city#region_name",
    mgt_area: "mgt_area#region_name",
}

export const detailPredict = {
    title: "",
    status: "label",
    push_time: "",
    username: "creator#real_name",
    finish_time: "",
    description: "",
    task_count: "",
    task_fill_count: "",
    task_notfill_count: "",
    task_notpass_count: "",
    yonggong_fill_count: "",
    yonggong_audit_count: "",
    yonggong_audit_pass_count: "",
    yonggong_audit_notpass_count: "",
    xuqiu_fill_count: "",
    xuqiu_audit_count: "",
    xuqiu_audit_pass_count: "",
    xuqiu_audit_notpass_count: "",
    mgt_province: "mgt_province#region_name",
    mgt_city: "mgt_city#region_name",
    mgt_area: "mgt_area#region_name",
}

export const column: TableColumn[] = [
    {
        label: "任务名称",
        prop: "title",
        showOverflowTip: true,
        width: "340",
    },
    {
        label: "发布时间",
        prop: "push_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.push_time)
        },
    },
    {
        label: "管理区域",
        prop: "mgt_province",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.mgt_province, row.mgt_city, row.mgt_area]
                .filter(Boolean)
                .join("")
        },
        minWidth: "130",
    },
    {
        label: "目标填报企业总数",
        prop: "task_count",
        showOverflowTip: true,
        minWidth: "120",
    },
    {
        label: "审核中企业数",
        prop: "task_audit_count",
        showOverflowTip: true,
    },
    {
        label: "未填报企业数",
        prop: "task_notfill_count",
        showOverflowTip: true,
    },
    {
        label: "已完成企业数",
        prop: "task_over_count",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const column1: TableColumn[] = [
    {
        label: "任务名称",
        prop: "title",
        showOverflowTip: true,
        width: "460",
    },
    {
        label: "创建时间",
        prop: "create_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "管理区域",
        prop: "mgt_province",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.mgt_province, row.mgt_city, row.mgt_area]
                .filter(Boolean)
                .join("")
        },
        minWidth: "130",
    },
    {
        label: "目标填报企业总数",
        prop: "task_count",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export const column2: TableColumn[] = [
    {
        label: "任务名称",
        prop: "title",
        showOverflowTip: true,
        width: "340",
    },
    {
        label: "发布时间",
        prop: "create_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.create_time)
        },
    },
    {
        label: "结束时间",
        prop: "update_time",
        showOverflowTip: true,
        formatter: (row) => {
            return formatTime.day(row.update_time)
        },
    },
    {
        label: "管理区域",
        prop: "mgt_province",
        showOverflowTip: true,
        formatter: (row) => {
            return [row.mgt_province, row.mgt_city, row.mgt_area]
                .filter(Boolean)
                .join("")
        },
        minWidth: "130",
    },
    {
        label: "目标填报企业总数",
        prop: "task_count",
        showOverflowTip: true,
    },
    {
        label: "已完成企业数",
        prop: "task_over_count",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "100",
        showOverflowTip: true,
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("company_task").list(),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: rowPredict,
        column,
        oneTabFilter: true,
    }
}

export const enum AuthStatus {
    待认证 = 0,
    已认证 = 1,
    认证不通过 = 2,
}

export const enum Status {
    待发布 = 0,
    已发布 = 1,
    已结束 = 2,
}

export interface Row {
    /** 任务名称 */
    title: string

    /** 发布时间 */
    create_time: string
    update_time: string

    /** 被调查的企业总数 */
    task_count: number

    /** 审核中企业数 */
    task_audit_count: number

    /** 未填报企业数 */
    task_notfill_count: number

    /** 已完成企业数 */
    task_over_count: number
    id: number
    v: number
}

export interface DetailRow {
    /** 任务名称 */
    title: string

    /** 任务状态 */
    status: Status

    /** 任务状态[文本] */
    status_label: string

    /** 发布时间 */
    push_time: string

    /** 任务创建人 */
    username: string

    /** 计划完成时间时间 */
    finish_time: string

    /** 描述 */
    description: string

    /** 被调查的企业总数 */
    task_count: number

    /** 已填报的企业 */
    task_fill_count: number

    /** 未填报企业数 */
    task_notfill_count: number

    /** 未通过企业数 */
    task_notpass_count: number

    /** 已填报的用工数量 */
    yonggong_fill_count: number

    /** 待核查的用工数量 */
    yonggong_audit_count: number

    /** 核查通过的用工数量 */
    yonggong_audit_pass_count: number

    /** 核查不通过的用工数量 */
    yonggong_audit_notpass_count: number
    mgt_province: number
    mgt_city: number
    mgt_area: number
    id: number
    v: number
}

export interface AgentRow {
    agent_name: string
    yonggong_fill_count: string
    xuqiu_fill_count: string
    status: number
    status_label: string
    id: string
    v: number
}

export const forms = [
    {
        label: "管理区域",
        type: FormType.Cascader,
        prop: "mgt_codes",
        option: {
            elProps: { checkStrictly: true },
        },
        col: {
            span: 14,
            offset: 4,
        },
        required: true,
        disabledUniplatRule: true,
        rules: [{ required: true, message: "管理区域不能为空" }],
    },
    {
        label: "任务名称",
        type: FormType.Text,
        prop: "title",
        col: {
            span: 14,
            offset: 4,
        },
        required: true,
    },
    {
        label: "计划完成时间",
        type: FormType.DatePicker,
        prop: "finish_time",
        col: {
            span: 14,
            offset: 4,
        },
    },
    {
        label: "任务内容描述",
        type: FormType.Text,
        prop: "description",
        defaultValue: "",
        option: {
            type: "textarea",
            rows: 7,
        },
        col: {
            span: 14,
            offset: 4,
        },
    },
]

export function getEditForm(id: string): BuildFormConfig {
    return {
        sdkModel: "company_task",
        sdkAction: "update",
        id: +id,
        forms: [
            {
                label: "任务名称",
                type: FormType.Text,
                prop: "title",
                col: {
                    span: 18,
                    offset: 2,
                },
                required: true,
            },
            {
                label: "计划完成时间",
                type: FormType.DatePicker,
                prop: "finish_time",
                col: {
                    span: 18,
                    offset: 2,
                },
            },
            {
                label: "任务内容描述",
                type: FormType.Text,
                prop: "description",
                option: {
                    type: "textarea",
                    rows: 7,
                },
                col: {
                    span: 18,
                    offset: 2,
                },
            },
        ],
    }
}

const recordTableFilter: TableFilter[] = [
    {
        label: "企业名称",
        type: FormType.Text,
        prop: "agent_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "员工信息",
        type: FormType.Text,
        prop: "person_name",
        option: {
            placeholder: "请输入姓名/身份证号/手机号",
        },
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "核查状态",
        type: FormType.Select,
        prop: "status",
        option: {
            multiple: true,
        },
    },
]

export const recordColumn2: TableColumn[] = [
    ...commonColumn,
    {
        label: "审核反馈",
        prop: "error_message",
        showOverflowTip: true,
        width: "120",
        render: (h, row) => {
            return h(
                "span",
                {
                    class: "color-red",
                },
                row.error_message
            )
        },
    },
]

export function recordTableConfig(): TableConfig {
    return {
        model: sdk.core
            .model("company_task_emprequire_record")
            .list("list_for_show"),
        filter: recordTableFilter,
        defaultPageSize: 8,
        predict: checkRowPredict,
        column: commonColumn,
        oneTabFilter: true,
    }
}

export function companyTableConfig(
    task_id: number,
    listName?: string
): TableConfig {
    return {
        model: sdk.core
            .model("company_task_record")
            .list(listName || "list_for_yonggong_task_detail"),
        filter: [
            {
                label: "公司名称",
                type: FormType.Text,
                prop: "agent_name",
                keyValueFilter: {
                    match: ListTypes.filterMatchType.fuzzy,
                },
            },
            {
                prop: "status",
                label: "任务状态",
                type: FormType.Select,
                option: {
                    multiple: true,
                },
            },
        ],
        defaultPageSize: 10,
        predict: {
            agent_name: "tg_enterprise#name",
            yonggong_fill_count: "",
            xuqiu_fill_count: "",
            status: "label",
        },
        preFilter: { task_id },
        column: [
            {
                label: "企业名称",
                prop: "agent_name",
                showOverflowTip: true,
            },
            {
                label: "已填报总用工数量",
                prop: listName ? "xuqiu_fill_count" : "yonggong_fill_count",
                showOverflowTip: true,
            },
            {
                label: "任务状态",
                prop: "status_label",
                showOverflowTip: true,
            },
            {
                label: "操作",
                prop: "h",
                showOverflowTip: true,
            },
        ],
    }
}
