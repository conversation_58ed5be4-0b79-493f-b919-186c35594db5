<template>
    <div v-if="item">
        <base-info :item="item" @refresh="init">
            <breadcrumb :backRoute="true" :items="breadcrumbs" />
        </base-info>
        <div class="u-m-t-30" />
        <div class="bg-white u-p-20" v-if="item.status">
            <div class="title-container u-flex u-p-l-20 bold u-m-b-10">
                任务内容
            </div>
            <task-collect :item="item" v-if="item.task_type === 1" />
            <task-question :item="item" v-if="item.task_type === 2" />
            <task-job :item="item" v-if="item.task_type === 3" />
            <task-notify :item="item" v-if="item.task_type === 4" />
            <task-policy :item="item" v-if="item.task_type === 5" />
        </div>
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Breadcrumb from "@/views/components/breadcrumb/index.vue"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"
    import { getServiceTaskDetail, ServiceTaskRow } from "../task"
    import BaseInfo from "./components/base-info.vue"
    import TaskCollect from "./components/task-collect.vue"
    import TaskJob from "./components/task-job.vue"
    import TaskNotify from "./components/task-notify.vue"
    import TaskPolicy from "./components/task-policy.vue"
    import TaskQuestion from "./components/task-question.vue"

    @Component({
        name: routesMap.groupService.serviceManageDetail.task,
        components: {
            BaseInfo,
            TaskCollect,
            TaskJob,
            TaskPolicy,
            TaskNotify,
            TaskQuestion,
            Breadcrumb,
        },
    })
    export default class ServiceTaskDetail extends Vue {
        private id = 0

        private get from() {
            return this.$route.query.from as "" | "taskList" | "project" | "pc"
        }

        breadcrumbs: BreadcrumbItem[] = []

        private setbreadcrumbs() {
            if (!this.item) {
                return
            }
            const d: BreadcrumbItem[] = [
                {
                    label: this.item?.p_name || "--",
                    to: {
                        name: routesMap.groupService.serviceManageDetail.task,
                        query: {
                            id: this.item.id + "",
                            from: this.from,
                        },
                    },
                },
            ]
            if (this.from === routesMap.groupService.taskManage) {
                d.unshift({
                    label: "重点人群任务管理",
                    to: {
                        name: routesMap.groupService.taskManage,
                    },
                })
                // 设置面包屑的父级菜单
                ;(this.$route.meta as any).parentMenuName =
                    routesMap.groupService.taskManage
            } else {
                ;(this.$route.meta as any).parentMenuName =
                    routesMap.groupService.serviceManage
                this.from && d.unshift(...getCacheBreadcrumbsByRoutePath(this.from))
            }

            updateTagItem({
                name: routesMap.groupService.serviceManageDetail.task,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private item: ServiceTaskRow | null = null

        refreshConfig = {
            name: routesMap.groupService.serviceManageDetail.task,
            fun: this.init,
        }

        mounted() {
            this.init()
        }

        init() {
            if (
                this.$route.name === routesMap.groupService.serviceManageDetail.task
            ) {
                this.id = +this.$route.query.id
            }

            pageLoading(() => {
                return getServiceTaskDetail(this.id).then((r) => {
                    this.item = r
                    this.setbreadcrumbs()
                })
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .title-container {
        width: 100%;
        height: 36px;
        background: #f8f8f8;
        color: #222;
    }
</style>
