import { config, EnvProject } from "@/config"
import { RouteConfig } from "@/router"
import { routesMap } from "@/router/direction"
import { bigScreenPolicyRouters } from "./潜江政策大屏/routers"
import { bigScreenEmploymentRouters } from "./就业地图大屏/routers"
import { getWhiteRegionConfig } from "./service"

function getRegionUuid() {
    try {
        const urlParams = new URLSearchParams(window.location.search)
        const regionUuid = urlParams.get("regionUuid")
        return regionUuid || ""
    } catch (e) {
        console.error("Error getting regionUuid:", e)
        return ""
    }
}

const openCheck = [EnvProject.咸丰项目].includes(config.envProject)
const whiteRegionConfig = getWhiteRegionConfig(getRegionUuid() as string)

const needLogin = !(
    openCheck &&
    whiteRegionConfig &&
    whiteRegionConfig.anonymous
)

export const bigScreenRouters: RouteConfig[] = [
    {
        path: `/big-screen/nav`,
        name: routesMap.bigScreen.nav,
        meta: {},
        component: () => import("./views/entrance.vue"),
    },
    {
        path: `/big-screen`,
        name: routesMap.bigScreen.home,
        meta: {},
        component: () => import("./views/home/<USER>"),
    },
    {
        path: `/big-screen/emp`,
        name: routesMap.bigScreen.emp,
        meta: {},
        component: () => import("./views/企业用工结构分析/index.vue"),
    },
    {
        path: `/big-screen/emp2`,
        name: routesMap.bigScreen.emp2.index,
        meta: { needLogin: false },
        component: () => import("./views/房县企业用工结构分析/index.vue"),
    },
    {
        path: `/big-screen/emp2-person-list`,
        name: routesMap.bigScreen.emp2.personList,
        meta: { needLogin: false },
        component: () =>
            import("./views/房县企业用工结构分析/房县员工样本列表.vue"),
    },
    {
        path: `/big-screen/emp2-company-list`,
        name: routesMap.bigScreen.emp2.companyList,
        meta: { needLogin: false },
        component: () =>
            import("./views/房县企业用工结构分析/房县企业样本列表.vue"),
    },
    {
        path: `/big-screen/emp2-temp-list`,
        name: routesMap.bigScreen.emp2.tempList,
        meta: { needLogin: false },
        component: () =>
            import("./views/房县企业用工结构分析/房县企业缺工统计表.vue"),
    },
    {
        path: `/big-screen/returns-monitor`,
        name: routesMap.bigScreen.returnHome,
        meta: {},
        component: () => import("./views/返乡返岗监测/index.vue"),
    },
    {
        path: `/big-screen/report`,
        name: routesMap.bigScreen.report,
        meta: {
            needLogin: needLogin,
        },
        component: () => import("./views/劳动力总况/index.vue"),
    },
    {
        path: `/big-screen/report1`,
        name: routesMap.bigScreen.report1,
        meta: {
            needLogin: needLogin,
        },
        component: () => import("./views/劳动力总况/index1.vue"),
    },
    {
        path: `/big-screen/focus`,
        meta: {
            needLogin: needLogin,
        },
        redirect: "/big-screen/focus/has-job",
        component: () => import("./views/重点人群/index.vue"),
        children: [
            {
                path: `graduate`,
                name: routesMap.bigScreen.important.graduate,
                meta: {
                    needLogin: [
                        EnvProject.潜江项目,
                        EnvProject.十堰项目,
                        EnvProject.武汉数采项目,
                        EnvProject.红安项目,
                    ].includes(config.envProject),
                },
                component: () => {
                    if (
                        [EnvProject.荆州项目].includes(config.envProject) &&
                        !localStorage.getItem("isXianFenEnv")
                    ) {
                        return import("./views/重点人群/高校毕业生-荆州.vue")
                    }
                    return import("./views/重点人群/高校毕业生.vue")
                },
            },
            {
                path: `important-type1`,
                name: routesMap.bigScreen.important.importantType1,
                meta: {
                    needLogin: needLogin,
                },
                component: () => {
                    return import("./views/重点人群/残疾人.vue")
                },
            },
            {
                path: `important-type2`,
                name: routesMap.bigScreen.important.importantType2,
                meta: {
                    needLogin: needLogin,
                },
                component: () => {
                    return import("./views/重点人群/退捕渔民.vue")
                },
            },
            {
                path: `important-type3`,
                name: routesMap.bigScreen.important.importantType3,
                meta: {
                    needLogin: needLogin,
                },
                component: () => {
                    return import("./views/重点人群/脱贫人口.vue")
                },
            },
            {
                path: `important-type4`,
                name: routesMap.bigScreen.important.importantType4,
                meta: {
                    needLogin: needLogin,
                },
                component: () => {
                    return import("./views/重点人群/退役军人.vue")
                },
            },
            {
                path: `hard`,
                name: routesMap.bigScreen.important.hard,
                meta: {
                    needLogin: needLogin,
                },
                component: () => import("./views/重点人群/困难就业.vue"),
            },
            {
                path: `out`,
                name: routesMap.bigScreen.important.out,
                meta: {
                    needLogin: [
                        EnvProject.潜江项目,
                        EnvProject.十堰项目,
                        EnvProject.武汉数采项目,
                        EnvProject.红安项目,
                    ].includes(config.envProject),
                },
                component: () => import("./views/重点人群/外出务工.vue"),
            },
            {
                path: `important-type5`,
                name: routesMap.bigScreen.important.importantType5,
                meta: {
                    needLogin: needLogin,
                },
                component: () =>
                    import("./views/重点人群/市外省内务工人员.vue"),
            },
            {
                path: `important-type6`,
                name: routesMap.bigScreen.important.importantType6,
                meta: {
                    needLogin: needLogin,
                },
                component: () => import("./views/重点人群/劳动力流向总况.vue"),
            },
            {
                path: `important-type7`,
                name: routesMap.bigScreen.important.importantType7,
                meta: {
                    needLogin: needLogin,
                },
                component: () => import("./views/重点人群/脱贫劳动力.vue"),
            },
            {
                path: `important-type8`,
                name: routesMap.bigScreen.important.importantType8,
                meta: {
                    needLogin: needLogin,
                },
                component: () => import("./views/重点人群/长江禁捕和还湖.vue"),
            },
            {
                path: `in`,
                name: routesMap.bigScreen.important.in,
                meta: {
                    needLogin: needLogin,
                },
                component: () => import("./views/重点人群/外来务工.vue"),
            },
            {
                path: `no-job`,
                name: routesMap.bigScreen.important.noJob,
                meta: {
                    needLogin: needLogin,
                },
                component: () => import("./views/重点人群/未就业.vue"),
            },
            {
                path: `has-job`,
                name: routesMap.bigScreen.important.hasJob,
                meta: {
                    needLogin: needLogin,
                },
                component: () => import("./views/重点人群/已就业.vue"),
            },
        ],
    },
    {
        path: `/big-screen/task-warning`,
        name: routesMap.bigScreen.task.warning,
        meta: {},
        component: () => import("./潜江就业指标预警大屏/首页/index.vue"),
    },
    ...bigScreenPolicyRouters,
    ...bigScreenEmploymentRouters,
]
