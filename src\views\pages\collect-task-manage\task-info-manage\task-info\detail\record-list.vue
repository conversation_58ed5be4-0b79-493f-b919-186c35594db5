<template>
    <div class="detail-container cus-table-tab-ui">
        <div class="d-flex justify-content-end opt-btn">
            <!-- <el-button v-if="showActiveName" @click="exportCurrent"
                >导出未通过数据</el-button
            > -->

            <el-button type="primary" @click="onBatchCheck">批量审核</el-button>
        </div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            :customPageSize="[10, 20, 50]"
            @tabName="onTabNameChange"
            @getData="getCurrentPageRows"
            class="container-index container shadow"
        >
            <div
                slot="table"
                class="u-p-20 bg-white"
                :class="{
                    'custom-selector-table': ['待审核', '审核未通过'].includes(
                        activeName
                    ),
                }"
            >
                <div class="select-each-num">
                    <el-checkbox
                        @click.stop.prevent
                        @focus.stop.prevent
                        class="table-select-chechbox"
                        :disabled="!tableSelectedVisible"
                        v-model="tableSelected"
                        :indeterminate="indeterminate"
                        @change="selectedChanged"
                    />
                    <el-select
                        ref="typeSelect"
                        v-model="selectType"
                        class="header-select"
                        popper-class="header-select-popper"
                        @change="selectTypeChange"
                        :title="selectTopTitle"
                    >
                        <el-option label="本页" :value="0" />
                        <!-- <el-option label="全部" :value="1" /> -->
                        <el-option :label="topItemText" :value="2">
                            前
                            <input
                                v-model="top"
                                class="top-input"
                                @click.stop
                                @focus.stop
                                @input="onInputChange"
                            />
                            条
                        </el-option>
                    </el-select>
                </div>
                <common-table
                    ref="commonTable"
                    :data="curData"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <template slot="selectV2" slot-scope="scope">
                        <el-checkbox
                            @change="change($event, scope.row)"
                            v-model="scope.row.selected"
                            :disabled="scope.row.selectedDisabled"
                        ></el-checkbox>
                    </template>
                    <div slot="h" slot-scope="scope">
                        <el-button
                            v-if="[0, 2].includes(scope.row.audit_status)"
                            type="text"
                            @click="goAudit(scope.row.id)"
                        >
                            审核
                        </el-button>
                        <el-button
                            type="text"
                            @click="goDetail(scope.row.access_key)"
                        >
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>

        <DialogAudit
            v-model="displayDialogAudit"
            :curId="curId"
            :selectedIds="checkIds"
            @refresh="refresh"
            :topNum="top"
            :selectType="selectType"
            :activeName="activeName"
        >
        </DialogAudit>
    </div>
</template>

<script lang="ts">
    import { routesMap } from "@/router/direction"
    import { Component, Prop, Ref, Vue } from "vue-property-decorator"
    import { TableConfig } from "@/core-ui/component/table"
    import { tableConfig, columns, columns2 } from "../record-list"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { DetailController, getBtnViewsByMetaActions } from "../base"
    import { action } from "uniplat-sdk"
    import DialogAudit from "../components/dialog-audit.vue"
    import { without } from "lodash"
    import { DetailRow } from "../model"

    enum SelectType {
        Page = 0,
        All = 1,
        Top = 2,
    }

    @Component({
        components: {
            TableContainer,
            CommonTable,
            DialogAudit,
        },
    })
    export default class TemplateView extends BaseTableController<any> {
        @Prop({ default: () => {} })
        private detailRow!: DetailRow

        @Ref()
        private commonTable!: CommonTable

        tableConfig: TableConfig | null = null

        private columns = columns()

        private selectType = SelectType.Page

        private displayDialogAudit = false

        private tableSelectedVisible = true

        private tableSelected = false

        private curData: any = []

        private middleStatus = false
        private indeterminate = false

        private curId = ""

        private detailId = ""

        private viewActions: action[] = []

        private checkIds: string[] = []

        private detailName = ""

        private showActiveName = false

        private activeName = ""

        private top = 100

        refreshConfig = {
            fun: this.refresh,
            name: "task-info-record-list",
        }

        private get rootTaskId() {
            return this.detailRow.root_task_id || ""
        }

        private refresh() {
            this.reloadList()
        }

        private handlerData(data: any[]) {
            return data.map((e) => {
                return {
                    ...e,
                    selected: this.checkIds.includes(e.id),
                    selectedDisabled: false,
                }
            })
        }

        private change(selected: any, row: any) {
            if (selected && !this.checkIds.includes(row.id)) {
                this.checkIds.push(row.id)
            } else if (!selected) {
                this.checkIds = without(this.checkIds, row.id)
            }
            this.setAllSelectedStatus()
        }

        private goAudit(id: string) {
            this.curId = id
            this.displayDialogAudit = true
        }

        private onTabNameChange(activeName: string) {
            console.log("console.log(activeName)")
            console.log(activeName)

            this.activeName = activeName
            this.showActiveName = activeName === "审核未通过"

            this.checkIds = []
            this.resetTopSelectedStatus()
            this.getCurrentData(activeName)

            this.$nextTick(() => {
                if (["待审核", "审核未通过"].includes(activeName)) {
                    this.columns = columns2()
                } else {
                    this.columns = columns()
                }
            })
        }

        private resetTopSelectedStatus() {
            this.tableSelectedVisible = true
            this.tableSelected = false
            this.indeterminate = false
            this.selectType = SelectType.Page
        }

        private exportCurrent() {
            this.exportToExcel()
        }

        private onBatchCheck() {
            if (this.selectType === SelectType.Page) {
                this.curId = ""
                if (!this.checkIds.length) {
                    return this.$message.warning("请选择数据")
                }

                this.displayDialogAudit = true
            } else {
                this.curId = ""
                this.displayDialogAudit = true
            }
        }

        private get selectTopTitle() {
            return this.selectType === SelectType.Top ? `前${this.top}条` : ""
        }

        private get topItemText() {
            if (this.top > 999) {
                return "前N条"
            }
            return `前${this.top}条`
        }

        private pageDatas: any[] = []

        private getCurrentPageRows(pageDatas: any[], name: string) {
            console.log("pageDatas")
            console.log(pageDatas)
            this.pageDatas = pageDatas
            this.getCurrentData(name)
        }

        private getCurrentData(name: string) {
            const t = this.pageDatas.find((e: any) => e.name === name)
            const rows = t?.rows || []

            const item_index = t?.item_index || 1
            const item_size = t?.item_size || 10
            const preLength = (item_index - 1) * item_size
            const baseIndexLine =
                this.top - preLength > 0 ? this.top - preLength : -999

            this.curData = rows.map((e: any, index: number) => {
                return {
                    ...e,
                    selected:
                        this.selectType === SelectType.Top
                            ? baseIndexLine > index
                            : false,
                    selectedDisabled: this.selectType === SelectType.Top,
                }
            })
            this.checkIds = []
            this.setAllSelectedStatus()
        }

        private onInputChange() {
            let num: any = this.top * 1

            if (isNaN(num)) {
                num = (this.top + "").replace(/[^\d]/g, "") || 0
            }

            if (num * 1 > 999) {
                this.top = 999
                this.selectTypeChange()
                return
            }
            this.top = num
            this.selectTypeChange()

            // console.log("num")
            // console.log(num)
            // console.log(this.top)
        }

        private selectTypeChange() {
            this.tableSelected = false
            if (this.selectType === SelectType.All) {
                this.tableSelectedVisible = false
                this.curData.forEach((item: any) => {
                    item.selected = true
                    item.selectedDisabled = true
                })
                return
            }
            if (this.selectType === SelectType.Top) {
                console.log("top toptop")
                console.log(this.top)
                this.tableSelectedVisible = false
                this.curData.forEach((item: any, index: number) => {
                    if (index < this.top) {
                        item.selected = true
                    } else {
                        item.selected = false
                    }
                    item.selectedDisabled = true
                })
                return
            }
            this.curData.forEach((item: any) => {
                item.selected = false
                item.selectedDisabled = false
            })
            this.tableSelectedVisible = true
        }

        private selectedChanged(checked: boolean) {
            this.curData.forEach((item: any) => (item.selected = checked))
            this.checkIds = this.curData
                .filter((e: any) => e.selected)
                .map((e: any) => e.id)

            this.setAllSelectedStatus()
        }

        private setAllSelectedStatus() {
            const cLength = this.checkIds.length
            if (!cLength) {
                this.tableSelected = false
                this.indeterminate = false
            } else if (cLength === this.curData.length) {
                this.tableSelected = true
                this.indeterminate = false
            } else {
                this.tableSelected = false
                this.indeterminate = true
            }
        }

        private goDetail(id: number) {
            this.$router.push({
                name: routesMap.collectTaskManage.taskInfoManage.taskInfo
                    .recordListDetail,
                query: {
                    id: id + "",
                    type: "pre",
                    detailName: this.detailName,
                    from: this.$route.name,
                },
            })
            return id
        }

        private handleSelectionChange(d: { ids: string[]; rows: any[] }) {
            this.checkIds = d.rows.map((e) => e.id)
        }

        public noticeRegionIdUpdate(init: boolean) {
            this.detailId = this.$route.query.id as string
            if (init) {
                return this.init()
            }
            this.tableConfig = null
            this.$nextTick(() => {
                this.init()
            })
        }

        private init() {
            DetailController.getDetail(this.detailId).then(() => {
                const pageListName = "collect_task_order_detail"
                const c =
                    DetailController.getPagePreFilterMetaInDetail(pageListName)
                this.detailName = c.listName
                this.tableConfig = tableConfig("collect_task_order_detail")
            })
        }

        private getViewActionDisplayInfo(key: string) {
            return !!getBtnViewsByMetaActions(this.viewActions, key)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";

    .opt-btn {
        top: 20px;
        right: 20px;
        position: absolute;
        z-index: 999;
    }

    .detail-container {
        gap: 20px;
        min-height: 610px;
        position: relative;
        padding-top: 15px;

        .tree-box {
            background-color: #fff;
        }

        /deep/ .el-button {
            min-width: auto !important;
        }
    }

    .ml-auto {
        margin-left: auto;
    }

    .cus-table-tab-ui {
        margin-left: 20px;

        /deep/ .el-tabs__item {
            background-color: transparent !important;
            position: relative;
            min-width: 0px !important;
            padding-left: 0px !important;
            padding-right: 0px !important;
            margin-right: 40px;

            &.is-active {
                border-bottom: 3px solid #5782ec;
                color: #5782ec !important;

                &::after {
                    content: "";
                    position: absolute;
                    bottom: 0px;
                    left: 0px;
                    width: 100%;
                }
            }
        }
    }

    .select-each-num {
        position: absolute;
        display: none;
        left: 20px;
        width: 120px;
        top: 20px;
        cursor: pointer;
        z-index: 99;
        height: 64px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .custom-selector-table {
        position: relative;

        .select-each-num {
            display: flex !important;
        }

        /deep/ table {
            thead {
                & > tr {
                    &:first-child {
                        & > th {
                            padding: 20px 0;

                            &:first-child {
                                .cell {
                                    position: relative;
                                    top: -10px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .header-select {
        /deep/ .el-input__suffix {
            display: none;
        }
        /deep/ .el-input__inner {
            padding: 0;
            height: 16px;
            line-height: 16px;
            border: none;
            background-color: transparent;
            width: 58px;
            text-align: center;
            font-size: 13px;
            color: #5780ab;
            border-bottom: 1px solid #ccc;
            border-radius: 0;
        }
        &.no-checkbox /deep/ .el-input__inner {
            width: 50px;
            text-align: center;
        }
    }

    /deep/ .el-table__row {
        .el-checkbox__input.is-checked .el-checkbox__inner {
            background-color: var(--primary, #0bcc27) !important;
            border-color: var(--primary, #0bcc27) !important;

            &::after {
                border-color: #fff !important;
            }
        }
    }

    .top-input {
        width: 50px;
        height: 22px;
        line-height: 20px;
        padding: 0 2px;
        text-align: center;
        border-top: none;
        border-left: none;
        border-right: none;
        border-radius: 0;
        background-color: transparent;
        border-bottom: #5780ab 1px solid;
        color: #5780ab;

        &:focus {
            outline: none;
        }
    }
</style>
