<template>
    <div class="check-list-container">
        <table-container
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            @getData="getData"
        >
            <div
                v-if="!hideHeader"
                slot="title"
                class="d-flex-item-center bold"
            >
                {{ title }}
            </div>

            <div v-if="!hideHeader" slot="header-right">
                <el-button
                    type="primary"
                    plain
                    :loading="exportLoading"
                    @click="
                        exportExcelUniplatV2({
                            template_name: '导出企业',
                        })
                    "
                >
                    导出
                </el-button>

                <el-button
                    @click="exportTable"
                    v-if="isUseDataMode"
                    type="primary"
                    plain
                >
                    导出
                </el-button>
                <el-button
                    v-role="'model.xg_agent.action.batch_tags'"
                    type="primary"
                    @click="batchSetTag"
                    plain
                    v-if="!isYD && !isEZ"
                >
                    批量设置标签
                </el-button>
                <el-button
                    v-role="'model.xg_agent.action.add_company'"
                    @click="goAdd"
                    type="primary"
                >
                    新增企业
                </el-button>
            </div>

            <div slot="table" slot-scope="{ data }">
                <common-table
                    :data="data"
                    :tableConfig="tableConfig"
                    :columns="columns"
                    @handleSelectionChange="handleSelectionChange"
                >
                    <div
                        slot="contact_person"
                        slot-scope="scope"
                        class="d-flex flex-column align-items-start"
                    >
                        <div>{{ scope.row.contact_person }}</div>
                        <div>{{ scope.row.contact_mobile }}</div>
                    </div>
                    <div
                        slot="h"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-button
                            v-if="!isYD && !isEZ"
                            type="text"
                            @click="setTag(scope.row)"
                        >
                            标签
                        </el-button>
                        <el-button type="text" @click="viewDetail(scope.row)">
                            详情
                        </el-button>
                    </div>
                </common-table>
            </div>
        </table-container>
        <set-tag-dialog
            v-model="showSetTag"
            :ids="row ? [row.id] : []"
            :isBatch="false"
            @refresh="refreshList"
        ></set-tag-dialog>
        <excel-import
            v-if="importConfig"
            v-model="showBatchSetTag"
            title="批量设置标签"
            placeholder="请点击「确定」上传"
            :importConfig="importConfig"
            :needVerifyContent="needVerifyContent"
            @refresh="refreshList"
        >
            <template v-slot:tips>
                <div class="tips">
                    <div>1.模板已于2024年9月5日更新</div>
                    <div>2.请下载最新模版填写上传</div>
                </div>
            </template>
        </excel-import>
    </div>
</template>

<script lang="ts">
    import { config, EnvProject } from "@/config"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { needVerifyContent } from "@/core-ui/component/excel-import"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { TableColumn as TableColumnImpl } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { ExcelGenerator } from "@/core-ui/component/table/excel-generator"
    import { routesMap } from "@/router/direction"
    import { Component, Prop } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "."
    import SetTagDialog from "./components/set-tag.vue"

    @Component({
        name: routesMap.employmentManage.companyManage,
        components: { TableContainer, CommonTable, SetTagDialog, ExcelImport },
    })
    export default class CompanyManageIndex extends BaseTableController<Row> {
        @Prop({ default: false })
        private hideHeader!: boolean

        private isYD = config.envProject === EnvProject.宜都项目
        private isEZ = config.envProject === EnvProject.鄂州项目

        private tableConfig = tableConfig()
        private columns: TableColumnImpl[] = []
        private importConfig: any = null

        private isUseDataMode = false
        public checkEdIds: Array<number | string> = []
        private info: any[] = []
        showSetTag = false

        private get title() {
            return this.$route.meta?.title || ""
        }

        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.employmentManage.companyManage,
        }

        viewDetail(row: Row) {
            this.$router.push({
                name: routesMap.employmentManage.companyManageDetail,
                query: { id: row._access_key || row.id + "" },
            })
        }

        private setTag(row: Row) {
            this.row = row
            this.showSetTag = true
        }

        created() {
            this.isUseDataMode = !!this.$route.query.opt
            if (this.isUseDataMode || this.hideHeader) {
                this.columns.push({
                    type: "selection",
                    prop: "select",
                })
            }
            this.columns = [...this.columns, ...columns]
        }

        private handleSelectionChange(d: { ids: string[]; rows: any[] }) {
            this.checkEdIds = d.rows.map((e) => e.id)
        }

        private exportTable() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量导出的数据！")
            }

            const fileName = "企业列表数据"
            const target = _.filter(this.columns, (i) => i.prop !== "select")
            const columnsLabel = target.map((t) => t.label)
            const columns = target.map((t) => t.prop)
            const rows: string[][] = []
            this.info.forEach((i) => {
                if (!this.checkEdIds.includes(i.id)) {
                    return
                }
                const row: any[] = []

                columns.forEach((t) => {
                    const val = i[t]
                    row.push(val)
                })

                rows.push(row)
            })

            ExcelGenerator.execute({
                fileName: fileName,
                columns: columnsLabel,
                rows: rows,
            })
            return rows
        }

        private goAdd() {
            this.callRefresh(routesMap.employmentManage.companyManageAdd)
            this.$router.push({
                name: routesMap.employmentManage.companyManageAdd,
            })
        }

        private getData(info: any) {
            this.info = info
        }

        private showBatchSetTag = false
        /** 批量设置标签 */
        private batchSetTag() {
            this.showBatchSetTag = true
        }

        mounted() {
            this.importConfig = {
                templateUrl:
                    window.location.origin +
                    `${process.env.BASE_URL}/file/企业批量设置标签导入文件模板0905.xlsx`.replace(
                        "//",
                        "/"
                    ),
                modelName: "xg_agent",
                actionName: "batch_tags",
                bigActionImportParams: {
                    inputs_parameters: [],
                    selected_list: [],
                    batchSchema: "默认导入",
                    batchSchemaTable: {
                        schema: "默认导入",
                        sheet: 1,
                        startRow: 1,
                    },
                },
            }
        }

        private needVerifyContent(originFile: any) {
            return needVerifyContent({
                originFile,
                fileUniKey: "293c430d80084df48262155633b80ea2",
                vue: this,
                index: 2,
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .check-list-container {
        width: 100%;
    }

    .header {
        padding-top: 15px;
        padding-bottom: 15px;

        .title {
            font-size: 22px;
            color: #000000;
        }
    }
    .tips {
        width: 165px;
        font-size: 12px;
        color: red;
        margin-top: 7px;
        line-height: 1.5;
    }
</style>
