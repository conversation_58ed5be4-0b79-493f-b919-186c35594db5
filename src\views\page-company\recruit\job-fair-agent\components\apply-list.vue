<template>
    <div class="core-ui-table-container list-view">
        <div class="title u-flex u-row-between u-m-b-20">
            <div>投递信息</div>
        </div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            :alwaysShowPageIndex="false"
        >
            <div slot="table" slot-scope="{ data }">
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <template
                            v-if="
                                [status.已报名, status.沟通中].includes(
                                    scope.row.status
                                )
                            "
                        >
                            <el-button
                                v-if="
                                    [status.已报名].includes(scope.row.status)
                                "
                                type="text"
                                @click="changeStatus(scope.row, status.沟通中)"
                            >
                                沟通中
                            </el-button>
                            <el-button
                                type="text"
                                @click="changeStatus(scope.row, status.已入职)"
                            >
                                已入职
                            </el-button>
                            <el-button
                                type="text"
                                @click="changeStatus(scope.row, status.已淘汰)"
                            >
                                已淘汰
                            </el-button>
                        </template>
                        <span v-else>--</span>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { Component, Prop } from "vue-property-decorator"
    import { columns2, Status, tableConfig2 } from "."
    import { Row } from ".."

    @Component({ components: { TableContainer, CommonTable } })
    export default class ApplyList extends BaseTableController<{ id: number }> {
        @Prop()
        id!: string

        @Prop()
        detail!: Row

        @Prop()
        isOver!: boolean

        private status = Status

        private tableConfig: TableConfig | null = null
        private columns = columns2

        mounted() {
            this.tableConfig = tableConfig2(
                this.detail.agent_id,
                this.detail.job_fair_id
            )
        }

        private toDetail(row: any) {
            this.$router.push({
                name: routesMap.company.recruit.jobDetail,
                query: {
                    from: routesMap.company.recruit.jobFairDetail,
                    id: row.position_access_key || row.position_id,
                },
            })
        }

        private changeStatus(row: Row, status: Status) {
            console.log(row, status, JSON.parse(JSON.stringify(this.detail)))
            MessageBox.confirm(
                "确认更改状态为" + Status[status] + "?",
                "提示"
            ).then(() => {
                pageLoading(() => {
                    return sdk!.core
                        .model("xg_candidate_order")
                        .action("update_status")
                        .addInputs_parameter({
                            status,
                        })
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row!.id }],
                            prefilters: [
                                {
                                    property: "create_from",
                                    value: "job_fair",
                                },
                                {
                                    property: "create_from_id",
                                    value: this.detail!.job_fair_id,
                                },
                                {
                                    property: "position.agent_id",
                                    value: this.detail!.agent_id,
                                },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.refreshList()
                        })
                })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
