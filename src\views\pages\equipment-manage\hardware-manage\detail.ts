import { Status, StatusNow } from "./index"

export enum DeviceType {
    招聘机 = 1,
    智能电视 = 2,
    导视机 = 3,
    查询机 = 4,
    LED发布终端 = 5,
    广告机 = 6,
    就业小知 = 7,
    智能移动公共服务工作站
}

export const predict = {
    id: "",
    device_name: "",
    device_sn: "",
    brand: "device_brand#brand",
    version: "device_brand#version",
    status: "label",
    onlineDay: "",
    update_time: "",
    status_now: "bind_info#status_now_label",
    online_time: "drl#update_time",
    active_time: "bind_info#active_time",
    device_remark: "",
    region_name: "bind_info#full_region_name_display",
    device_type: "label",
    sim_card_no: "",
    device_open_id: "",
}

export interface Row {
    id: number
    device_name: string
    device_sn: string
    brand: string
    version: string
    status: Status
    status_label: string
    onlineDay: string
    update_time: string
    status_now: StatusNow
    status_now_label: string
    online_time: string
    active_time: string
    device_remark: string
    region_name: string
    [key: string]: any
}
