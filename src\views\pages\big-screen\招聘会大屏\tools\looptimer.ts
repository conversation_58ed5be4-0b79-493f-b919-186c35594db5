/**
 *
 * @param time 循环间隔的时间
 * @param isRelate4Promise 是否与被装饰函数返回结果相关联，等待Promise的返回，然后再开始计时，用于短时间的循环请求
 * @returns
 */
export function loopTimer(time = 1000 * 60 * 5, isRelate4Promise = false) {
    return function (target: any, name: string): any {
        const func = target[name]
        let timer: number | null = null

        const newFunc = function (this: any, ...params: any[]) {
            const log = (t: string) => {
                // console.log(t)
                return t
            }

            // 清理之前的定时器
            if (timer) {
                clearTimeout(timer)
                timer = null
            }
            const loop = () => {
                log("exec:pre")
                const v = func.apply(this, params)
                const loopExec = () => {
                    timer = setTimeout(() => {
                        loop()
                    }, time)
                }
                if (!isRelate4Promise) {
                    log("not:relate")
                    loopExec()
                } else {
                    if (!v || !v.finally) {
                        log("then:null")
                        return loopExec()
                    }
                    v?.finally(() => {
                        log("then")
                        loopExec()
                    })
                }
            }
            loop()
            this.$once &&
                this.$once("hook:beforeDestroy", () => {
                    log("remove:listener")
                    if (timer) {
                        clearTimeout(timer)
                        timer = null
                    }
                })
        }
        target[name] = newFunc
        return target
    }
}
