<template>
    <div class="page-detail">
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            @setTotal="setTotal"
            :showExpand="false"
        >
            <div slot="btn-right">
                <el-button class="u-m-l-10" @click="exportToExcel"
                    >导出</el-button
                >
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="getData(data)" :columns="columns">
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang="ts">
    import { buildSelectSource, FormType } from "@/core-ui/component/form"
    import { sdk } from "@/service"
    import { Component, Prop, Watch } from "vue-property-decorator"
    import { TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { cloneDeep, map } from "lodash"
    import { DrawerBox } from "@/core-ui/component/drawer-box"
    import { config } from "@/config"
    import { formatTime } from "@/utils/tools"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { ExcelGenerator } from "@/utils/excel-generator"
    import Axios from "axios"

    interface FilterListItem {
        question: string
        question_id: string
        question_type: 1 | 2 | 3 | 4
        options: {
            option_id: string
            option: string
        }[]
    }

    @Component({ components: { TableContainer, CommonTable } })
    export default class StatisticsTable extends BaseTableController<any> {
        @Prop()
        private statisticsId!: string

        @Prop()
        private type!: "" | "agent" | "wecom_group"

        private FormTypeObj = {
            1: FormType.Select,
            2: FormType.Select,
            3: FormType.Text,
            4: FormType.Cascader,
        }

        private tableConfig: TableConfig | null = null

        private columns: any[] = []
        private tableFilter: any[] = []

        private addressList: any[] = []

        @Watch("statisticsId", { immediate: true })
        private onStatisticsIdChange() {
            this.init()
        }

        private init() {
            console.log("this", this)
            this.getCityList().finally(() => {
                this.getConfig()
            })
        }

        private toDetail() {
            DrawerBox.open({
                title: "问卷模版",
                url: `${config.h5}/pages/sub/questionnaire/preview?verify=false&questionnaire_id=${this.statisticsId}`,
            })
        }

        private getCityList() {
            const cache = sessionStorage.getItem("DOMAIN_SERVICE_CACHE_DATA")
            if (cache) {
                this.addressList = JSON.parse(cache)
                return Promise.resolve()
            }
            return sdk.core
                .domainService(
                    "xg_project",
                    "anonymous/client_api",
                    "fetch_city_list"
                )
                .get<any[]>()
                .then((res) => {
                    this.addressList = res
                    sessionStorage.setItem(
                        "DOMAIN_SERVICE_CACHE_DATA",
                        JSON.stringify(res)
                    )
                })
        }

        private filterCache = {}
        async exportToExcel() {
            const columns = this.columns.map((i) => i.label)
            this.table!.exportV2()
            sdk.core
                .getAxios()
                .post<any>(
                    `${config.uniplatApi}/general/project/xg_project/service/general_questionnaire/get_answer_statistic_list?questionnaire_id=${this.statisticsId}&type=${this.type}`,
                    {
                        ...this.filterCache,
                        export: 1,
                    },
                    {
                        timeout: 0,
                    }
                )
                .then((r) => {
                    ExcelGenerator.execute({
                        primaryRows: [],
                        columns,
                        rows: map(r.data || [], (e) => e.values),
                        fileName: "答卷统计",
                    })
                })
        }

        private total = 0
        setTotal(total: number) {
            console.log("setTotal", total)
            this.total = total
        }

        private getConfig() {
            const obj = {
                questionnaire_id: this.statisticsId,
            }
            if (this.type) {
                Object.assign(obj, {
                    type: this.type,
                })
            }
            return sdk.core
                .domainService(
                    "xg_project",
                    "general_questionnaire",
                    "get_answer_statistic_list_meta"
                )
                .get<{
                    filters: FilterListItem[]
                    columns: { label: string; type: "text " | "area" | "date" }[]
                }>(obj)
                .then((res) => {
                    console.log(res)
                    const tableFilter = res.filters.map((i) => {
                        const o: any = {
                            label: i.question,
                            prop: i.question_id,
                            type: this.FormTypeObj[i.question_type],
                            self: i,
                        }
                        if (i.options) {
                            Object.assign(o, {
                                sourceInputsParameter: buildSelectSource(
                                    i.options.map((j) => {
                                        return {
                                            value: j.option,
                                            key: j.option_id,
                                        }
                                    })
                                ),
                            })
                        }

                        if (o.type === FormType.Cascader) {
                            Object.assign(o, {
                                sourceInputsParameter: buildSelectSource(
                                    this.addressList
                                ),
                                option: {
                                    elProps: {
                                        checkStrictly: true,
                                    },
                                    filterable: true,
                                },
                            })
                        }
                        return o
                    })
                    tableFilter.unshift({
                        label: "用户信息",
                        prop: "userInfo",
                        type: FormType.Text,
                    })
                    if (this.type === "agent") {
                        tableFilter.unshift({
                            label: "企业名称",
                            prop: "company_name",
                            type: FormType.Text,
                        })
                    }
                    const columns = res.columns.map((r, index) => {
                        const label = (r.label || r) as string
                        const w = this.calcWidth(label)
                        const obj = {
                            label: label,
                            prop: "a" + index,
                            showOverflowTip: true,
                            align: "center",
                            formatter: (row: any) => {
                                if (r.type === "date" && row[`a${index}`]) {
                                    return formatTime.seconds(row[`a${index}`])
                                }
                                if (r.type === "area" && row[`a${index}`]) {
                                    return this.formatCity(row[`a${index}`])
                                }
                                return row[`a${index}`]
                            },
                        }
                        if (w) {
                            Object.assign(obj, {
                                minWidth: w,
                            })
                        }
                        return obj
                    })
                    console.log(columns)
                    console.log(columns)
                    // columns.push({
                    //     label: "操作",
                    //     prop: "h",
                    //     showOverflowTip: true,
                    //     align: "center",
                    // })
                    this.tableFilter = cloneDeep(tableFilter)
                    this.columns = cloneDeep(columns)
                    console.log(tableFilter)
                    console.log(columns)
                    let suffix = ""
                    if (this.type) {
                        suffix = `&type=${this.type}`
                    }
                    this.tableConfig = {
                        domainService: sdk.core.domainService(
                            "xg_project",
                            "general_questionnaire",
                            `get_answer_statistic_list?questionnaire_id=${this.statisticsId}${suffix}`
                        ),
                        filter: tableFilter,
                        defaultPageSize: 10,
                        handleFilterData: (params) => {
                            const filters: any[] = []

                            console.log(params)
                            console.log("params")

                            Object.keys(params || {}).forEach((key: any) => {
                                const cur = this.tableFilter.find(
                                    (i) => i.prop + "" === key + ""
                                )
                                const value = params[key]
                                if (!cur || !value || !cur.self) {
                                    return ""
                                }
                                const obj = {
                                    question_id: cur.self.question_id,
                                    question_type: cur.self.question_type,
                                }

                                const type =
                                    this.FormTypeObj[
                                        cur.self
                                            .question_type as FilterListItem["question_type"]
                                    ]

                                if (
                                    [FormType.Text, FormType.Cascader].includes(
                                        type
                                    )
                                ) {
                                    Object.assign(obj, {
                                        value: value,
                                    })
                                }

                                if ([FormType.Select].includes(type)) {
                                    Object.assign(obj, {
                                        options: [value],
                                    })
                                }
                                filters.push(obj)
                            })
                            this.filterCache = {
                                ...params,
                                filters: filters,
                            }
                            return {
                                ...this.filterCache,
                            }
                        },
                    }
                })
        }

        private formatCity(value: string) {
            const arr = value.split(",")
            const result = this.handelCity(this.addressList, arr, 0, "")
            return result
        }

        private handelCity(
            addressList: any[],
            arr: string[],
            cur: number,
            str: string
        ) {
            if (!arr[cur]) {
                return str
            }
            let str1 = str
            for (let i = 0; i < addressList.length; i++) {
                const item = addressList[i]
                if (item && item.key === arr[cur]) {
                    str1 = str + item.value
                    console.log("str1 ")
                    console.log(str1)

                    if (item.children) {
                        const c = item.children as any[]
                        const res = this.handelCity(c, arr, cur + 1, str1) as string
                        return res
                    }
                }
            }
            return str1
        }

        private calcWidth(label: string) {
            if (label.length < 4) {
                return ""
            }
            const w = label.length > 9 ? 9 : label.length
            return `${w * 20}px`
        }

        private getData(info: any) {
            const data = info.map((i: any) => {
                const o = {
                    id: i.id,
                }
                i.values.map((t: any, idx: number) => {
                    const v = t || ""
                    const isObj = typeof v === "object"
                    return Object.assign(o, {
                        [`a${idx}`]: isObj ? v.toString() : v,
                    })
                })
                return o
            })
            return data
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .page-detail {
        background-color: #fff;
    }
</style>
