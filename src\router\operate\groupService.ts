import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
const school = [
    {
        path: "school",
        name: routesMap.groupService.school.index,
        meta: {
            title: "重点人群管理", // 高校
            role: "/tablelist/user_profile_basic/manage_v3",
        },
        component: () => import("@/views/pages/group-service/school/index.vue"),
    },
    {
        path: "batch-add-service",
        name: routesMap.groupService.school.add,
        meta: {
            title: "新建帮扶", // 高校
            hidden: true,
        },
        component: () => import("@/views/pages/group-service/school/add.vue"),
    },
    {
        path: "service-record-manage",
        name: routesMap.groupService.school.serviceRecordManage,
        meta: {
            title: "我的服务记录处理",
            role: "/tablelist/user_profile_basic/manage_v3",
        },
        component: () =>
            import(
                "@/views/pages/group-service/school/service-record-manage/index.vue"
            ),
    },
    {
        path: "recordDetail",
        name: routesMap.groupService.school.serviceRecordManageDetail,
        meta: {
            title: "服务记录处理详情",
            hidden: true,
            parentMenuName: routesMap.groupService.school.serviceRecordManage,
        },
        component: () =>
            import("@/views/pages/labour-manage/record-list/detail.vue"),
    },
]
export const groupService = [
    {
        path: "/groupService1",
        name: routesMap.home.groupService,
        meta: {
            title: "重点人群服务跟踪管理",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/laborInfoBase.svg"),
            hidden:
                [EnvProject.宜都项目, EnvProject.鄂州项目].includes(
                    config.envProject
                ) || isQj,
        },
        component: layout,
        children: [
            ...school,
            {
                path: "service-manage",
                name: routesMap.groupService.serviceManage,
                meta: {
                    title: "重点人群就业服务",
                    role: "/tablelist/serve_project/for_operate",
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-statistics/index.vue"
                    ),
            },
            {
                path: "service-list",
                name: routesMap.groupService.serviceManageList,
                meta: {
                    title: "服务列表",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/index.vue"
                    ),
            },
            {
                path: "service-manage-edit",
                name: routesMap.groupService.serviceManageEdit,
                meta: {
                    title: "更改帮扶服务",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/index.vue"
                    ),
            },
            {
                path: "service-manage-create",
                name: routesMap.groupService.serviceManageCreate,
                meta: {
                    title: "新增帮扶服务",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/index.vue"
                    ),
            },
            {
                path: "service-create",
                name: routesMap.groupService.serviceCreate,
                meta: {
                    title: "新增服务内容项",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                    noCache: true,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/service.vue"
                    ),
            },
            {
                path: "service-task-create",
                name: routesMap.groupService.serviceTaskCreate,
                meta: {
                    title: "新增任务",
                    hidden: true,
                    parentMenuName: routesMap.groupService.serviceManage,
                    noCache: true,
                },
                component: () =>
                    import(
                        "@/views/pages/group-service/service-manage/create/task.vue"
                    ),
            },
            {
                path: "serviceManageDetail",
                redirect: "serviceManageDetail/detail",
                name: routesMap.groupService.serviceManageDetail.root,
                meta: {
                    hidden: true,
                },
                component: RouteView,
                children: [
                    {
                        path: "detail",
                        name: routesMap.groupService.serviceManageDetail.detail,
                        meta: {
                            title: "重点人群帮扶服务详情",
                            parentMenuName:
                                routesMap.groupService.serviceManage,
                        },
                        component: () =>
                            import(
                                "@/views/pages/group-service/service-manage/service-detail/index.vue"
                            ),
                    },
                    {
                        path: "item",
                        name: routesMap.groupService.serviceManageDetail.item,
                        meta: {
                            title: "服务内容项详情",
                            parentMenuName:
                                routesMap.groupService.serviceManage,
                        },
                        component: () =>
                            import(
                                "@/views/pages/group-service/service-manage/service-detail/item.vue"
                            ),
                    },
                    {
                        path: "task",
                        name: routesMap.groupService.serviceManageDetail
                            .taskRoot,
                        meta: {
                            title: "任务详情",
                            parentMenuName:
                                routesMap.groupService.serviceManage,
                        },
                        component: RouteView,
                        children: [
                            {
                                path: "d",
                                name: routesMap.groupService.serviceManageDetail
                                    .task,
                                meta: {
                                    title: "任务详情",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/index.vue"
                                    ),
                            },
                            {
                                path: "collect",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.collect,
                                meta: {
                                    title: "编辑任务-采集工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-collect/index.vue"
                                    ),
                            },
                            {
                                path: "job",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.job,
                                meta: {
                                    title: "编辑任务-岗位推荐工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-job/index.vue"
                                    ),
                            },
                            {
                                path: "policy",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.policy,
                                meta: {
                                    title: "编辑任务-政策推荐工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/policy/index.vue"
                                    ),
                            },
                            {
                                path: "question",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.question,
                                meta: {
                                    title: "编辑任务-问卷调查工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-question/index.vue"
                                    ),
                            },
                            {
                                path: "notify",
                                name: routesMap.groupService.serviceManageDetail
                                    .taskContent.notify,
                                meta: {
                                    title: "编辑任务-内容通知工具",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/content-notify/index.vue"
                                    ),
                            },
                            {
                                path: "object",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.serve_target_count,
                                meta: {
                                    title: "服务对象",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/object/index.vue"
                                    ),
                            },
                            {
                                path: "served",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.served_target_count,
                                meta: {
                                    title: "已服务对象",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/object-served/index.vue"
                                    ),
                            },
                            {
                                path: "serve-record",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.serve_record_count,
                                meta: {
                                    title: "已服务人次",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/serve-record/index.vue"
                                    ),
                            },
                            {
                                path: "questionnaire",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.answered_questionnaire_count,
                                meta: {
                                    title: "已填写问卷",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/questionnaire/index.vue"
                                    ),
                            },
                            {
                                path: "apply_job_count",
                                name: routesMap.groupService.serviceManageDetail
                                    .result.apply_job_count,
                                meta: {
                                    title: "已申请岗位",
                                    parentMenuName:
                                        routesMap.groupService.serviceManage,
                                },
                                component: () =>
                                    import(
                                        "@/views/pages/group-service/service-manage/service-detail/task-detail/result/job/index.vue"
                                    ),
                            },
                        ],
                    },
                ],
            },
            {
                path: "task-manage",
                name: routesMap.groupService.taskManage,
                meta: {
                    title: "重点人群任务管理",
                    role: "/tablelist/serve_task/manage_for_operate",
                },
                component: () =>
                    import("@/views/pages/group-service/task-manage/index.vue"),
            },
        ],
        // children: [
        //     // 重点人群跟踪服务
        //     {
        //         path: "/group-service",
        //         redirect: "group-service/service-manage",
        //         name: routesMap.groupService.groupService,
        //         meta: {
        //             title: "重点人群服务跟踪",
        //             // svgIcon: require("@/assets/icon/menu/group-service.svg"),
        //         },
        //         component: RouteView,
        //         children: [
        //             {
        //                 path: "service-manage",
        //                 name: routesMap.groupService.serviceManage,
        //                 meta: {
        //                     title: "重点人群就业服务",
        //                     role: "/tablelist/serve_project/for_operate",
        //                 },
        //                 component: () =>
        //                     import(
        //                         "@/views/pages/group-service/service-statistics/index.vue"
        //                     ),
        //             },
        //             {
        //                 path: "service-list",
        //                 name: routesMap.groupService.serviceManageList,
        //                 meta: {
        //                     title: "服务列表",
        //                     hidden: true,
        //                     parentMenuName:
        //                         routesMap.groupService.serviceManage,
        //                 },
        //                 component: () =>
        //                     import(
        //                         "@/views/pages/group-service/service-manage/index.vue"
        //                     ),
        //             },
        //             {
        //                 path: "service-manage-edit",
        //                 name: routesMap.groupService.serviceManageEdit,
        //                 meta: {
        //                     title: "更改帮扶服务",
        //                     hidden: true,
        //                     parentMenuName:
        //                         routesMap.groupService.serviceManage,
        //                 },
        //                 component: () =>
        //                     import(
        //                         "@/views/pages/group-service/service-manage/create/index.vue"
        //                     ),
        //             },
        //             {
        //                 path: "service-manage-create",
        //                 name: routesMap.groupService.serviceManageCreate,
        //                 meta: {
        //                     title: "新增帮扶服务",
        //                     hidden: true,
        //                     parentMenuName:
        //                         routesMap.groupService.serviceManage,
        //                 },
        //                 component: () =>
        //                     import(
        //                         "@/views/pages/group-service/service-manage/create/index.vue"
        //                     ),
        //             },
        //             {
        //                 path: "service-create",
        //                 name: routesMap.groupService.serviceCreate,
        //                 meta: {
        //                     title: "新增服务内容项",
        //                     hidden: true,
        //                     parentMenuName:
        //                         routesMap.groupService.serviceManage,
        //                     noCache: true,
        //                 },
        //                 component: () =>
        //                     import(
        //                         "@/views/pages/group-service/service-manage/create/service.vue"
        //                     ),
        //             },
        //             {
        //                 path: "service-task-create",
        //                 name: routesMap.groupService.serviceTaskCreate,
        //                 meta: {
        //                     title: "新增任务",
        //                     hidden: true,
        //                     parentMenuName:
        //                         routesMap.groupService.serviceManage,
        //                     noCache: true,
        //                 },
        //                 component: () =>
        //                     import(
        //                         "@/views/pages/group-service/service-manage/create/task.vue"
        //                     ),
        //             },
        //             {
        //                 path: "serviceManageDetail",
        //                 redirect: "serviceManageDetail/detail",
        //                 name: routesMap.groupService.serviceManageDetail.root,
        //                 meta: {
        //                     hidden: true,
        //                 },
        //                 component: RouteView,
        //                 children: [
        //                     {
        //                         path: "detail",
        //                         name: routesMap.groupService.serviceManageDetail
        //                             .detail,
        //                         meta: {
        //                             title: "重点人群帮扶服务详情",
        //                             parentMenuName:
        //                                 routesMap.groupService.serviceManage,
        //                         },
        //                         component: () =>
        //                             import(
        //                                 "@/views/pages/group-service/service-manage/service-detail/index.vue"
        //                             ),
        //                     },
        //                     {
        //                         path: "item",
        //                         name: routesMap.groupService.serviceManageDetail
        //                             .item,
        //                         meta: {
        //                             title: "服务内容项详情",
        //                             parentMenuName:
        //                                 routesMap.groupService.serviceManage,
        //                         },
        //                         component: () =>
        //                             import(
        //                                 "@/views/pages/group-service/service-manage/service-detail/item.vue"
        //                             ),
        //                     },
        //                     {
        //                         path: "task",
        //                         name: routesMap.groupService.serviceManageDetail
        //                             .taskRoot,
        //                         meta: {
        //                             title: "任务详情",
        //                             parentMenuName:
        //                                 routesMap.groupService.serviceManage,
        //                         },
        //                         component: RouteView,
        //                         children: [
        //                             {
        //                                 path: "",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.task,
        //                                 meta: {
        //                                     title: "任务详情",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "collect",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.taskContent
        //                                     .collect,
        //                                 meta: {
        //                                     title: "编辑任务-采集工具",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/content-collect/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "job",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.taskContent
        //                                     .job,
        //                                 meta: {
        //                                     title: "编辑任务-岗位推荐工具",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/content-job/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "policy",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.taskContent
        //                                     .policy,
        //                                 meta: {
        //                                     title: "编辑任务-政策推荐工具",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/policy/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "question",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.taskContent
        //                                     .question,
        //                                 meta: {
        //                                     title: "编辑任务-问卷调查工具",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/content-question/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "notify",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.taskContent
        //                                     .notify,
        //                                 meta: {
        //                                     title: "编辑任务-内容通知工具",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/content-notify/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "object",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.result
        //                                     .serve_target_count,
        //                                 meta: {
        //                                     title: "服务对象",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/result/object/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "served",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.result
        //                                     .served_target_count,
        //                                 meta: {
        //                                     title: "已服务对象",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/result/object-served/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "serve-record",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.result
        //                                     .serve_record_count,
        //                                 meta: {
        //                                     title: "已服务人次",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/result/serve-record/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "questionnaire",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.result
        //                                     .answered_questionnaire_count,
        //                                 meta: {
        //                                     title: "已填写问卷",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/result/questionnaire/index.vue"
        //                                     ),
        //                             },
        //                             {
        //                                 path: "apply_job_count",
        //                                 name: routesMap.groupService
        //                                     .serviceManageDetail.result
        //                                     .apply_job_count,
        //                                 meta: {
        //                                     title: "已申请岗位",
        //                                     parentMenuName:
        //                                         routesMap.groupService
        //                                             .serviceManage,
        //                                 },
        //                                 component: () =>
        //                                     import(
        //                                         "@/views/pages/group-service/service-manage/service-detail/task-detail/result/job/index.vue"
        //                                     ),
        //                             },
        //                         ],
        //                     },
        //                 ],
        //             },
        //             {
        //                 path: "task-manage",
        //                 name: routesMap.groupService.taskManage,
        //                 meta: {
        //                     title: "重点人群任务管理",
        //                     role: "/tablelist/serve_task/manage_for_operate",
        //                 },
        //                 component: () =>
        //                     import(
        //                         "@/views/pages/group-service/task-manage/index.vue"
        //                     ),
        //             },
        //         ],
        //     },
        // ],
    },
]
