<template>
    <div>
        <div class="core-ui-custom-header">
            <div class="title">
                <breadcrumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>

        <div class="container u-p-t-30 u-p-b-30">
            <div class="content">
                <div>
                    帮扶服务：是指响应“国家保障城乡就业，并采取措施促进重点群体和困难人员就业”的政策，在平台上开展的一系列帮扶服务项目。
                </div>
                <div>
                    包含但不限于通过数据采集、社群管理、问卷调查、智能招聘、就业培训、直播待岗、就业惠补、人才咨询等数字化智慧就业服务。
                </div>
            </div>
            <div
                v-if="formLoading"
                v-loading="formLoading"
                class="u-p-60"
            ></div>
            <form-builder
                class="form"
                ref="formBuilder"
                label-position="right"
                label-width="140px"
            ></form-builder>
            <div class="u-flex u-m-t-20 u-row-center u-p-l-80">
                <el-button type="primary" plain @click="cancel">
                    取消
                </el-button>
                <el-button
                    type="primary"
                    class="u-m-l-30"
                    :loading="loading"
                    @click="confirm"
                >
                    确定
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import {
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Breadcrumb from "@/views/components/breadcrumb/index.vue"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component } from "vue-property-decorator"

    @Component({
        name: routesMap.groupService.serviceManageCreate,
        components: { FormBuilder, Breadcrumb },
    })
    export default class Template extends FormController {
        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(
                    (this.$route.query.from as string) ||
                        routesMap.groupService.serviceManageList
                ),
                {
                    label: this.id ? "修改帮扶服务" : "新增帮扶服务",
                },
            ]
            updateTagItem({
                name: routesMap.groupService.serviceManageDetail.item,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        formLoading = false
        private get id() {
            return this.$route.query.id
        }

        private getAction() {
            return sdk.core
                .model("serve_project")
                .action(this.id ? "update" : "insert")
                .updateInitialParams({
                    selected_list: +this.id ? [{ id: +this.id, v: 0 }] : [],
                })
        }

        loading = false

        refreshConfig = {
            fun: this.init,
            name: routesMap.groupService.serviceManageCreate,
        }

        mounted() {
            this.init()
        }

        cancel() {
            closeCurrentTap()
        }

        confirm() {
            this.validateForm((isValid) => {
                if (!isValid) {
                    return
                }
                this.loading = true
                const action = this.getAction()
                action.addInputs_parameter(this.getFormValues())
                action
                    .execute()
                    .then((r) => {
                        this.callRefresh(routesMap.groupService.serviceManage)

                        closeCurrentTap({
                            name: routesMap.groupService.serviceManageDetail.detail,
                            query: {
                                id: r.id,
                            },
                        })
                        this.callRefresh(
                            routesMap.groupService.serviceManageDetail.detail
                        )
                    })
                    .finally(() => {
                        this.loading = false
                    })
            })
        }

        private init() {
            this.setBreadcrumbs()
            this.formLoading = true
            return buildFormSections({
                action: this.getAction(),
                forms: [
                    {
                        label: "服务名称",
                        type: FormType.Text,
                        prop: "p_name",
                    },
                    {
                        label: "服务类型",
                        type: FormType.Select,
                        prop: "serve_type",
                        defaultValue: +this.$route.query.serve_type || undefined,
                    },
                    {
                        label: "开始时间",
                        type: FormType.DatePicker,
                        option: {
                            type: "date",
                        },
                        prop: "start_date",
                    },
                    {
                        label: "结束时间",
                        type: FormType.DatePicker,
                        option: {
                            type: "date",
                        },
                        prop: "end_date",
                    },
                    {
                        label: "服务城市",
                        type: FormType.Cascader,
                        option: {
                            filterable: true,
                            elProps: { checkStrictly: true },
                        },
                        prop: "area",
                    },
                    {
                        label: "服务描述",
                        type: FormType.Text,
                        option: {
                            type: "textarea",
                        },
                        prop: "description",
                    },
                    {
                        label: "发起单位",
                        type: FormType.Text,
                        prop: "initiate_unit",
                    },
                    {
                        label: "跟进人员所属部门",
                        type: FormType.Cascader,
                        prop: "xg_org_id",
                        option: {
                            filterable: true,
                            elProps: { checkStrictly: true },
                        },
                        needListen: true,
                    },
                    {
                        label: "跟进人员",
                        type: FormType.Select,
                        prop: "follow_ups",
                        option: {
                            filterable: true,
                            multiple: true,
                        },
                        handlerDisplay(d: any) {
                            return d.xg_org_id
                        },
                    },
                ],
            })
                .then((r) => {
                    this.buildFormFull(r)
                })
                .finally(() => {
                    this.formLoading = false
                })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .container {
        background-color: #fff;
        min-height: 50%;
        .el-button + .el-button {
            margin-left: 40px;
        }
        .content {
            width: 720px;
            padding: 10px 20px;
            background: #f5f9ff;
            margin: 0 auto;
            color: #555555;
            font-size: 14px;
            line-height: 24px;
            text-indent: 2em;
            text-align: justify;
        }
        .form {
            width: 460px;
            margin: 0 auto;
            margin-top: 40px;
            min-height: 400px;
        }
    }
</style>
