<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="d-flex">
                <el-button
                    type="primary"
                    plain
                    v-if="isXg"
                    v-role="['model.policy_form.action.set_depement']"
                    @click="showPop1 = true"
                    >设置管理科室</el-button
                >
                <el-button
                    type="primary"
                    plain
                    @click="toOperate('delete')"
                    class="custom-btn batch-btn"
                    v-if="showdeleteBtn"
                >
                    删除
                </el-button>
                <el-button
                    type="primary"
                    plain
                    @click="toOperate('online')"
                    class="custom-btn batch-btn"
                    v-if="showOnlineBtn"
                >
                    上架
                </el-button>
                <el-button
                    type="primary"
                    plain
                    @click="toOperate('offline')"
                    class="custom-btn batch-btn"
                    v-if="showOfflineBtn"
                >
                    下架
                </el-button>
                <el-button
                    type="primary"
                    @click="toEdit"
                    class="custom-btn batch-btn"
                    v-if="showEditBtn"
                >
                    编辑
                </el-button>
            </div>
        </div>
        <div class="detail-container" v-if="row">
            <div class="content u-flex-1 u-p-20">
                <div class="title">基本信息</div>
                <div class="u-p-20">
                    <detail-row-col
                        :labelStyle="{ width: '106px' }"
                        :list="items"
                    ></detail-row-col>

                    <div class="u-flex u-p-t-14 u-col-top w-100">
                        <div class="label flex-none">附件模版：</div>
                        <FileList :detail="row"></FileList>
                    </div>
                    <div
                        class="u-flex u-p-t-14 u-col-top w-100"
                        v-if="row.question_id"
                    >
                        <div class="label flex-none">关联表单：</div>
                        <div @click="toQuestionDetail" class="primary pointer">
                            {{ row.question_title }}
                        </div>
                    </div>
                    <div class="u-flex u-p-t-22 u-col-top">
                        <div class="label flex-none">政策内容：</div>
                        <div v-html="row.content" class="content-row"></div>
                    </div>
                </div>
            </div>
            <div class="u-p-20 bg-white">
                <div class="title u-p-l-20 apply-title u-flex u-row-between">
                    居民申报
                    <div class="u-flex">
                        <template v-if="applyTabName === '离线申报'">
                            <template v-if="isNew">
                                <!-- <el-button
                                    v-role="[
                                        'model.policy_form_apply.action.add_offline_apply_person',
                                    ]"
                                    @click="showPop = true"
                                >
                                    离线申报录入
                                </el-button> -->
                                <!-- <el-button
                                    @click="showImport1"
                                    type="primary"
                                    v-role="[
                                        'model.policy_form_apply.action.import_offline_apply_person',
                                    ]"
                                >
                                    导入政策办理人员
                                </el-button> -->
                            </template>
                            <!-- <el-button
                                v-else
                                @click="showImport"
                                type="primary"
                            >
                                导入政策办理人员
                            </el-button> -->
                        </template>
                        <el-button
                            v-else
                            type="primary"
                            @click="exportCurrent"
                            plain
                        >
                            导出
                        </el-button>
                    </div>
                </div>
            </div>
            <tab-list
                v-if="isPerson"
                :id="row.id"
                :type="row.serve_target_type"
                :policyName="row.policy_name"
                ref="tabList"
                @onTabChange="onTabChange"
            ></tab-list>
            <ApplyList
                v-else
                :id="row.id"
                :type="row.serve_target_type"
                :policyName="row.policy_name"
                ref="applyList"
                @onTabChange="onTabChange"
            />
        </div>
        <common-pop
            v-if="row"
            v-model="showPop"
            sdkModel="policy_form_apply"
            sdkAction="add_offline_apply_person"
            :prefilters="[
                {
                    property: 'apply_type',
                    value: '1',
                },
                {
                    property: 'form_id',
                    value: row.id,
                },
            ]"
            @refresh="init"
            title="离线申报录入"
        ></common-pop>
        <common-pop
            v-if="row"
            v-model="showPop1"
            sdkModel="policy_form"
            sdkAction="set_depement"
            :id="row.id"
            @refresh="init"
            :afterBuildFormSections="afterBuildFormSections"
            :submiteDataHandler="submiteDataHandler"
            title="设置管理科室"
        ></common-pop>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { ColItem } from "@/views/components/detail-row-col"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../single-page/components/tags-view"
    import {
        detailPredict,
        DetailRow,
        ServeTargetType,
        TemplateItem,
    } from "./index"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { getShowBtn4Page } from "../collect-task-manage/components/build-table"
    import ApplyList from "./components/apply-list.vue"
    import { MessageBox } from "element-ui"
    import FileList from "@/views/page-company/policy/components/file-list.vue"
    import { getColumns, getFormData } from "../policy-apply-record"
    import { config, EnvProject } from "@/config"
    import { spPolicy } from "./components"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import TabList from "./components/tab-list.vue"
    import { sleep } from "@/utils"
    import { FormType } from "@/core-ui/component/form"
    import { get, join, split } from "lodash"

    @Component({
        name: routesMap.publishPolicy.policyDetail,
        components: { DetailRowCol, ApplyList, FileList, CommonPop, TabList },
    })
    export default class PublishPolicyDetail extends Vue {
        breadcrumbs: BreadcrumbItem[] = []
        private showPop = false
        isXg = [EnvProject.孝感项目].includes(config.envProject)
        showPop1 = false

        private templateList: TemplateItem[] = []

        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.from as string),
                {
                    label: "政策详情",
                    to: {
                        name: routesMap.publishPolicy.policyDetail,
                        query: {
                            id: this.id,
                            from: this.from,
                        },
                    },
                },
            ]

            updateTagItem({
                name: routesMap.publishPolicy.policyDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private id = ""
        private row: DetailRow | null = null
        private showdeleteBtn = false
        private showOnlineBtn = false
        private showOfflineBtn = false
        private showEditBtn = false

        private applyTabName = "离线申报"

        isQianJiang = config.envProject === EnvProject.潜江项目

        private get isPerson() {
            if (this.isQianJiang) {
                return false
            }
            const type = this.row?.serve_target_type
            return type && type === ServeTargetType.居民
        }

        private get items() {
            if (!this.row) return []
            const item: ColItem[] = [
                { label: "政策名称：", value: this.row.policy_name || "" },

                {
                    label: "服务对象：",
                    value: this.row.serve_target_type_label || "",
                },
                { label: "状态：", value: this.row.status_label || "" },
                { label: "对外发布单位：", value: this.row.org_name_display || "" },
                { label: "所属科室：", value: this.row.name || "" },
                { label: "申报限制：", value: this.row.limit_times_display || "" },
                {
                    label: "上传附件说明：",
                    value: this.row.upload_desc || "",
                },
            ]
            return item.map((i) => {
                return { ...i, span: 12 }
            })
        }

        refreshConfig = {
            name: routesMap.publishPolicy.policyDetail,
            fun: this.init,
        }

        mounted() {
            this.init()
        }

        private get isNew() {
            return spPolicy.includes(this.row?.policy_name || "")
        }

        async init() {
            this.row = null
            this.id = ""
            await sleep(0)
            if (this.$route.name === routesMap.publishPolicy.policyDetail) {
                this.id = this.$route.query.id as string
                this.setBreadcrumbs()
            }
            if (!this.id) return
            pageLoading(() => {
                return sdk.core
                    .model("policy_form")
                    .detail(this.id, "manage")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, detailPredict)
                        this.showdeleteBtn = getShowBtn4Page(r, "delete")
                        this.showOnlineBtn = getShowBtn4Page(r, "online")
                        this.showOfflineBtn = getShowBtn4Page(r, "offline")
                        this.showEditBtn = getShowBtn4Page(r, "update_form")
                        // this.browser()
                        console.log("row", JSON.parse(JSON.stringify(this.row)))
                    })
            })
        }

        private browser() {
            return sdk.core
                .model("user_browse")
                .action("client_browse")
                .addInputs_parameter({
                    model_name: "policy_form",
                    object_id: this.row!.id,
                })
                .execute()
        }

        private toEdit() {
            this.$router.push({
                name: routesMap.publishPolicy.create,
                query: { id: this.row?.id + "", from: this.$route.name },
            })
        }

        private toOperate(actionName: string) {
            const label =
                actionName === "online"
                    ? "上架"
                    : actionName === "offline"
                    ? "下架"
                    : "删除"
            MessageBox.confirm(`确认${label}？`, "提示").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("policy_form")
                        .action(actionName)
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: this.row?.id + "" }],
                        })
                        .addInputs_parameter({})
                        .execute()
                        .then(() => {
                            if (actionName === "delete") {
                                closeCurrentTap({
                                    name: routesMap.publishPolicy.list,
                                })
                                return
                            }
                            this.init()
                        })
                })
            })
        }

        isJz = config.envProject === EnvProject.荆州项目

        private async exportCurrent() {
            if (this.isJz) {
                const type = +this.row!.serve_target_type === 2 ? "agent" : ""
                const col = await getColumns(this.row!.question_id, type)
                getFormData(this.row!.question_id, type, col, this.id + "")
            } else {
                ;(this.$refs.applyList as any)?.exportToExcel2({
                    template_name: "默认模板",
                })
                ;(this.$refs.tabList as any)?.exportExcelUniplatV2(
                    this.applyTabName
                )
            }
        }

        private toQuestionDetail() {
            this.$router.push({
                name: routesMap.publishPolicy.formDetail,
                query: {
                    id: this.row?.question_access_key + "",
                    from: this.$route.name,
                },
            })
        }

        private showImport() {
            ;(this.$refs.applyList as any).showImportPop = true
        }

        private showImport1() {
            ;(this.$refs.tabList as any).applyList0.showImportPop2 = true
        }

        private onTabChange(tabName: string) {
            this.applyTabName = tabName
        }

        afterBuildFormSections(r: any) {
            // r.forms = [
            //     {
            //         prop: "manage_department",
            //         type: FormType.Tree,
            //         label: "管理科室",
            //         option: {
            //             setDefaultValue2RootCode: false,
            //         },
            //     },
            // ]
            const f = r?.forms[0]
            const dv = r.data?.manage_department
                ?.split(",")
                .filter(Boolean)
                .map((e: any) => +e)
            console.log("r", JSON.parse(JSON.stringify(r)))
            f.option.treeMulti = true
            f.default_value = dv
            f.option.treeConfig = {
                computeIcon: (node: any, cur: any) => {
                    const category = +node.data?.data?.category
                    const active =
                        `${node.data?.data?.id}` === `${get(cur, "0.data.id")}`
                    return `${process.env.BASE_URL || ""}/img/tree/${
                        category === 1 ? "org" : "department"
                    }${active ? "-active" : ""}.png`.replace("//", "/")
                },
                disabled(node: any) {
                    return +node?.data?.category !== 2
                },
                handlerSelectedKeys() {
                    if (dv.length) {
                        return dv
                    }
                },
            }
            return r
        }

        submiteDataHandler(d: any) {
            d.manage_department = join(d.manage_department, ",")
            return d
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
    .detail-container {
        .title {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            color: #222;
            font-size: 18px;
            font-weight: 600;
            line-height: 40px;
            padding: 0 20px;
            &.apply-title {
                padding-left: 0;
                padding-right: 0;
                margin: 10px 0;
            }
        }
        .content {
            background: #fff;
            .label {
                width: 98px;
                margin-right: 10px;
                color: #555;
            }
        }
        .content-row {
            color: #333;
            width: 100%;
            overflow: hidden;
            /deep/ img {
                max-width: 100% !important;
                height: auto !important;
            }
        }
        /deep/.attachment-template {
            width: 50%;
        }
    }

    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
        margin-left: 30px;
    }
</style>
