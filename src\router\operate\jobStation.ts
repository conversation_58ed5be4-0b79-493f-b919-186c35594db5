import { config, EnvProject } from "@/config"
import RouteView from "@/views/components/route-view.vue"
import layout from "@/views/pages/single-page/index.vue"
import { routesMap } from "../direction"

const isXg = config.envProject === EnvProject.孝感项目
const isQj =
    config.envProject === EnvProject.潜江项目 ||
    config.envProject === EnvProject.武汉数采项目 ||
    config.envProject === EnvProject.红安项目 ||
    config.envProject === EnvProject.十堰项目
const isDev = process.env.VUE_APP_ENV === "test"
const isHz = config.envProject === EnvProject.黄州项目
const isYD = config.envProject === EnvProject.宜都项目

const isJz = config.envProject === EnvProject.荆州项目
const isSaas = config.envProject === EnvProject.saas项目
export const jobStation = [
    {
        path: "/jobStation",
        name: routesMap.home.jobStation,
        meta: {
            title: "零工驿站管理",
            homeIcon: "/img/xiaogan/home/<USER>",
            svgIcon: require("@/assets/icon/menu2/laborInfoBase.svg"),
        },
        component: layout,
        children: [
            {
                path: "/job-station",
                redirect: "job-station/manage",
                name: routesMap.jobStation.root,
                meta: {
                    title: "零工驿站管理",
                    // svgIcon: require("@/assets/icon/menu/job-station.svg"),
                    role: "/tablelist/xg_odd_job/manage",
                },
                component: RouteView,
                children: [
                    {
                        path: "statistics",
                        name: routesMap.jobStation.statistics,
                        meta: {
                            title: "驿站统计",
                            hidden: [EnvProject.荆州项目].includes(
                                config.envProject
                            ),
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/statistics/index.vue"
                            ),
                    },
                    {
                        path: "jobstation-statistics-detail",
                        name: routesMap.jobStation.jobStationStatisticsDetail,
                        meta: {
                            title: "零工驿站统计详情",
                            hidden: true,
                            parentMenuName: routesMap.jobStation.statistics,
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/statistics/jobstastion-detail.vue"
                            ),
                    },
                    {
                        path: "recommend-statistics-detail",
                        name: routesMap.jobStation.recommendStatisticsDetail,
                        meta: {
                            title: "用工推荐统计详情",
                            hidden: true,
                            parentMenuName: routesMap.jobStation.statistics,
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/statistics/recommend-detail.vue"
                            ),
                    },
                    {
                        path: "share-statistics-detail",
                        name: routesMap.jobStation.shareStatisticsDetail,
                        meta: {
                            title: "驿站共享统计详情",
                            hidden: true,
                            parentMenuName: routesMap.jobStation.statistics,
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/statistics/share-detail.vue"
                            ),
                    },
                    {
                        path: "manage",
                        name: routesMap.jobStation.manage,
                        meta: {
                            title: "驿站信息",
                        },
                        component: () =>
                            import("@/views/pages/job-station/page/manage.vue"),
                    },
                    {
                        path: "ban",
                        name: routesMap.jobStation.ban,
                        meta: {
                            title: "驿站封禁",
                        },
                        component: () =>
                            import("@/views/pages/job-station/page/ban.vue"),
                    },
                    {
                        path: "invite-log",
                        name: routesMap.jobStation.inviteLog,
                        meta: {
                            title: "邀约记录",
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/page/invite-log.vue"
                            ),
                    },
                    {
                        path: "seek-info",
                        name: routesMap.jobStation.publishSeekInfo,
                        meta: {
                            title: "零工求职信息管理",
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/page/seek-info.vue"
                            ),
                    },
                    {
                        path: "recruitment-info",
                        name: routesMap.jobStation.publishRecruitmentInfo,
                        meta: {
                            title: "雇主招工信息管理",
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/page/recruitment-info.vue"
                            ),
                    },
                    {
                        path: "device-manage",
                        name: routesMap.jobStation.deviceMange,
                        meta: {
                            title: "设备管理",
                            role: "/tablelist/device_base_info/manager_back_v2",
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/device/index.vue"
                            ),
                    },
                    {
                        path: "device-manage-detail",
                        name: routesMap.jobStation.deviceDetail,
                        meta: {
                            title: "设备管理详情",
                            hidden: true,
                            parentMenuName: routesMap.jobStation.deviceMange,
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/device/detail.vue"
                            ),
                    },
                    {
                        path: "brand-manage",
                        name: routesMap.jobStation.brandManage,
                        meta: {
                            title: "品牌管理",
                            role: "/tablelist/device_brand/manager_back",
                        },
                        component: () =>
                            import(
                                "@/views/pages/job-station/device/brand.vue"
                            ),
                    },
                    {
                        path: "hz-jobstation-bigscreen",
                        name: routesMap.jobStation.hzBigScreen,
                        meta: {
                            title: "零工驿站大屏",
                            newPage: true,
                            targetPath:
                                EnvProject.黄州项目 === config.envProject
                                    ? `https://rtd.hzjycy.cn/shareDashboard/f2e7f632700e47798c235488bd28afea?type=NONE`
                                    : EnvProject.咸丰项目 === config.envProject
                                    ? "https://rtd.xfzhhjy.cn/shareDashboard/3007f5ea9ff44331b5640051ef8140fb?type=NONE"
                                    : "",
                            hidden: ![
                                EnvProject.黄州项目,
                                EnvProject.咸丰项目,
                            ].includes(config.envProject),
                        },
                    },
                ],
            },
            {
                path: "xg-employment",
                name: routesMap.bigScreen.xgEmployment.screen,
                meta: {
                    title: "就业地图大屏",
                    newPage: true,
                    // svgIcon: require("@/assets/icon/menu/screen.svg"),
                    targetPath:
                        `${process.env.BASE_URL}/big-screen/employment/index`.replace(
                            "//",
                            "/"
                        ),
                    role: "/redirect/xg_project/device_map_screen",
                },
            },
        ],
    },
]
