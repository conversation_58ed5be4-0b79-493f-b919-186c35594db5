<template>
    <div v-if="tableConfig2">
        <div class="w-100">
            <div class="core-ui-custom-header w-100" v-if="!hideHeader">
                <div class="u-flex u-row-between bold title">注册居民管理</div>
                <div class="u-flex">
                    <el-button
                        v-role="'model.user_account.action.recommend_profile'"
                        type="primary"
                        @click="batchRecommend"
                        plain
                        v-if="!isYD"
                    >
                        批量人才推荐
                    </el-button>
                </div>
            </div>
        </div>
        <table-filter
            ref="filter"
            :tableFilter="tableConfig2.filter"
            :metaFilters="metaFilters"
            :tagGroups="tagGroups"
            @search="resetPageAndSearch"
            :showExpand="true"
            :defaultFilterIsExpand="false"
        />
        <!-- <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :showExpand="true"
            :defaultFilterIsExpand="false"
        > -->
        <div class="bg-white u-p-20">
            <div v-if="!hideHeader" class="u-flex u-row-right u-m-b-15">
                <el-button
                    type="primary"
                    plain
                    size="mini"
                    @click="batch"
                    v-role="['model.user_account.action.set_tags']"
                >
                    批量设置标签
                </el-button>
                <el-button
                    type="primary"
                    plain
                    size="mini"
                    @click="exportExcel"
                    :loading="exportLoading"
                >
                    导出
                </el-button>
            </div>
            <common-table
                :data="items"
                :columns="columns"
                @handleSelectionChange="handleSelectionChange"
                v-loading="loading"
            >
                <div slot="info" slot-scope="scope">
                    <div v-if="!scope.row.is_verified" class="color-8">
                        — 未实名 —
                    </div>
                    <div class="info" v-else>
                        <div class="u-flex u-row-between">
                            <div class="u-flex">
                                <span
                                    :class="{
                                        'u-m-r-10': scope.row.name,
                                    }"
                                >
                                    {{ scope.row.name }}
                                </span>
                                <span
                                    :class="{
                                        'u-m-r-10': scope.row.age,
                                    }"
                                >
                                    {{ scope.row.age }}
                                </span>
                                <span
                                    :class="{
                                        'u-m-r-10': scope.row.sex_label,
                                    }"
                                >
                                    {{ scope.row.sex_label }}
                                </span>
                            </div>
                            <div
                                class="info-right"
                                :class="[
                                    scope.row.employment_status_display
                                        ? 'emp emp-' +
                                          scope.row.employment_status
                                        : 'color-9',
                                ]"
                            >
                                {{ scope.row.employment_status_display || "-" }}
                            </div>
                        </div>
                        <div class="u-flex u-row-between">
                            <div class="u-flex">
                                {{ scope.row.id_card }}
                            </div>
                            <div class="info-right">
                                <div
                                    class="primary pointer"
                                    v-if="
                                        scope.row.profile_access_key ||
                                        scope.row.profile_id
                                    "
                                    @click="
                                        toProfile(
                                            scope.row.profile_access_key ||
                                                scope.row.profile_id
                                        )
                                    "
                                >
                                    查看档案
                                </div>
                                <div v-else class="color-9">-</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div slot="h" class="u-flex u-row-center" slot-scope="scope">
                    <el-button type="text" @click="toDetail(scope.row)">
                        详情
                    </el-button>
                </div>
            </common-table>
            <div class="u-flex u-row-center u-m-t-30">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="index"
                    :page-sizes="pageSizes"
                    :page-size="size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <!-- </table-container> -->
        <set-tag-dialog
            v-model="showSetTag"
            :ids="checkEdIds"
            @refresh="onFilterChanged"
            model="user_account"
            action="batch_set_labels"
        ></set-tag-dialog>
        <batch-recommend
            v-model="showBatchRecommend"
            :selected="selected"
            @refresh="onFilterChanged"
        />
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import {
        BaseTable,
        BaseTableController,
    } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { Component, Prop, Ref } from "vue-property-decorator"
    import { columns, Row, tableConfig } from "."
    import { updateTagItem } from "../../single-page/components/tags-view"
    import BatchRecommend from "./components/batch-recommend.vue"
    import SetTagDialog from "../seeker-info/components/set-tag.vue"
    import { config, EnvProject } from "@/config"
    import {
        ListTypes,
        metaFilter,
        TagManagerTypes,
    } from "uniplat-sdk/build/main/def"
    import { sdk } from "@/service"
    import { cloneDeep, forEach, map, split } from "lodash"
    import TableFilter from "@/core-ui/component/table/filter.vue"
    import ExcelImport from "@/core-ui/component/excel-import/index.vue"
    import { computeAge } from "../seeker-info"
    import moment from "moment"

    @Component({
        name: routesMap.labourManage.userAccount,
        components: {
            TableContainer,
            CommonTable,
            BatchRecommend,
            SetTagDialog,
            TableFilter,
            ExcelImport,
        },
    })
    export default class UserAccount extends BaseTable<{ id: number }> {
        @Prop({ default: false })
        private hideHeader!: boolean

        @Prop({ default: false })
        private is_verified!: boolean

        private isYD = config.envProject === EnvProject.宜都项目
        exportLoading = false

        get table() {
            return {
                size: this.size,
                total: this.total,
                handleCurrentChange: async (index: number) => {
                    this.index = index
                    return this.onFilterChanged()
                },
                items: this.items,
            }
        }

        tableConfig2: TableConfig | null = null
        public metaFilters: metaFilter[] = []
        tagGroups: TagManagerTypes.TagGroup[] = []

        private readonly columns: TableColumn[] = columns
        private checkEdIds: Array<number | string> = []
        private showSetTag = false

        @Ref("filter")
        private readonly filter!: TableFilter | any

        public selected: {
            id: string
            v: number
            profile_id: string
        }[] = []

        resetPageAndSearch() {
            this.index = 1
            this.onFilterChanged()
        }

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.rows.map((e) => e.uniplat_uid)
            this.selected = d.rows.map((item) => {
                return {
                    profile_id: item.profile_id,
                    id: item.uniplat_uid,
                    v: item.v,
                }
            })
        }

        private batch() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量设置标签的居民！")
            }
            this.showSetTag = true
        }

        private showBatchRecommend = false

        private batchRecommend() {
            if (!this.checkEdIds.length) {
                return this.$message.error("请先勾选需要批量人才推荐的居民信息！")
            }
            this.showBatchRecommend = true
        }

        mounted() {
            this.tableConfig2 = tableConfig(this.is_verified)
            updateTagItem({
                name: routesMap.labourManage.userAccount,
                breadcrumb: [
                    {
                        label: "注册居民管理",
                        to: routesMap.labourManage.userAccount,
                    },
                ],
            })
            this.tableConfig2
                .model!.query({
                    pageIndex: 1,
                    item_size: 0,
                })
                .then((r) => {
                    this.tagGroups = r.pageData.tagGroups
                    this.metaFilters = r.pageData.meta.filters
                })
            this.onFilterChanged()
        }

        private toDetail(row: Row) {
            console.log("row", JSON.parse(JSON.stringify(row)))
            this.$router.push({
                name: routesMap.labourManage.userAccountDetail,
                query: {
                    id: row.access_key || row.uniplat_uid + "",
                    from: routesMap.labourManage.userAccount,
                },
            })
        }

        private toProfile(id: string) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: { id, from: routesMap.labourManage.userAccount },
            })
        }

        private async exportExcel() {
            this.exportLoading = true
            const model = this.tableConfig2?.model
            model?.clearFilter()
            forEach(this.curFilterData, (e: any, p) => {
                if (e) {
                    let v: any = e
                    if (
                        [
                            "household_province_code",
                            "permanent_province_code",
                        ].includes(p)
                    ) {
                        v = split(e, ",")
                    }
                    if (p === "birth_date") {
                        v = (e || []).map((e: string) => computeAge(+e)).reverse()
                        v[0] = moment(v[0]).format("YYYY-01-01")
                        v[1] = moment(v[1]).format("YYYY-12-31")
                    }
                    model?.addFilter({
                        property: p,
                        value: v,
                        match: ListTypes.filterMatchType.fuzzy,
                    })
                }
            })
            if (this.is_verified) {
                model?.addFilter({
                    property: "is_verified",
                    value: "1",
                })
            }
            await model?.query({
                item_size: 0,
                pageIndex: 1,
            })
            model!
                .postParamsForExcel({
                    template_name: "注册用户导出",
                })
                .then((eid) => {
                    const process = model!.exportToExcelV2(eid)((p, status) => {
                        this.loadingMsg = {
                            percent: p,
                            status,
                            process,
                        }
                    })
                    return process.awaiting.then((r) => {
                        window.open(r)
                    })
                })
                .catch((e) => {
                    this.$message.error(e)
                })
                .finally(() => {
                    this.exportLoading = false
                })
            // this.exportExcelUniplatV2({ template_name: "注册用户导出" })
        }

        private curFilterData = {}

        protected onFilterChanged() {
            const filterData = cloneDeep(this.filter?.getFilterData() || {})
            console.log("onFilterChanged", JSON.parse(JSON.stringify(filterData)))
            const v = split(filterData.tags, ",")
            const tags = map(v, (i) => {
                const tag = split(i, ":::")
                return {
                    tagGroup: tag[0],
                    tag: tag[1],
                }
            })
                .filter((i) => i.tag)
                .map((i) => i.tag)
                .join(",")
            filterData.tags = tags
            // if (filterData.update_time) {
            //     filterData.update_time_start = filterData.update_time[0]
            //     filterData.update_time_end = filterData.update_time[1]
            //     delete filterData.update_time
            // }
            this.curFilterData = cloneDeep(filterData)
            this.loading = true
            return Promise.all([
                sdk.core
                    .domainService(
                        "xg_project",
                        "operate_api",
                        "get_user_account_list"
                    )
                    .post({
                        ...filterData,
                        page_index: this.index,
                        page_size: this.size,
                    })
                    .then((r: any) => {
                        this.items = r.data
                        this.total = +r.total
                    }),
            ]).finally(() => {
                this.loading = false
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .info {
        width: 200px;
        .info-right {
            width: 60px;
            text-align: left;
            &.emp {
                color: #3bbc6b;
                &.emp-2 {
                    color: #ff8b16;
                }
            }
        }
    }
</style>
