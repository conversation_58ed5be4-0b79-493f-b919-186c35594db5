import { config, EnvProject } from "@/config"
import { BuildFormConfig, FormType } from "@/core-ui/component/form"

export function createFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_candidate_order",
        sdkAction: "interview",
        id,
        forms: [
            {
                label: "面试时间",
                type: FormType.DatePicker,
                option: {
                    type: "datetime",
                },
                prop: "interview_time",
                required: true,
                rules: [
                    {
                        validator: (rule, value, callback) => {
                            if (+new Date(value) < +new Date()) {
                                callback(new Error("面试时间需大于当前时间"))
                            } else {
                                callback()
                            }
                        },
                    },
                ],
            },
            {
                label: "面试地点",
                type: FormType.Text,
                prop: "interview_address",
                required: true,
            },
        ],
    }
}

export function statusFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_candidate_order",
        sdkAction: "audit_feedback",
        id,
        forms: [
            {
                label: "投递状态",
                type: FormType.Select,
                prop: "status",
                required: true,
                needListen: true,
            },
            {
                label: "面试类型",
                type: FormType.Select,
                prop: "interview_type",
                required: true,
                needListen: true,
            },
            {
                label: "面试时间",
                type: FormType.DatePicker,
                option: {
                    type: "datetime",
                },
                prop: "interview_time",
                required: true,
                rules: [
                    {
                        validator: (rule, value, callback) => {
                            if (+new Date(value) < +new Date()) {
                                callback(new Error("面试时间需大于当前时间"))
                            } else {
                                callback()
                            }
                        },
                    },
                ],
                needListen: true,
            },
            {
                label: "面试地点",
                type: FormType.Text,
                prop: "interview_address",
                needListen: true,
            },
        ],
    }
}

export function qualificationFormConfig(id: number): BuildFormConfig {
    return {
        sdkModel: "xg_candidate_order",
        sdkAction: "qualification_audit",
        id,
        forms: [
            {
                label: "审核资格",
                type: FormType.Select,
                prop: "qualification_audit_status",
                required: true,
            },
        ],
    }
}

export const seekerInfoPredict = {
    name: "",
    name_hide: "name_hide",
    sex: "label",
    getAge: "",
    id_card: "id_card_openid#user_profile_basic_joint.id_card",
    id_card_hide: "",
    mobile: "",
    mobile_hide: "",
    uniplat_uid_calc: "label",
    uniplat_uid: "",
    black_id: "black#id",
    black_is_del: "black#is_del_label",
    blacktype: "black#type_label",
    black_memo: "black#memo",
    black_update_time: "black#update_time_label",
    black_update_by: "black#update_by",
    black_creator_real_name: "black#creator#real_name",
    residence_property: "basic_info#residence_property_label",
    // residence_property: "residence_property_label",
    // education: "basic_info#education_label",
    education: "basic_info#education_label",
    update_time: "update_time_label",
    household: "basic_info#household_address_full_name",
    id_card_openid: "",

    // 常住地址
    permanent_province: "basic_info#permanent_province#region_name",
    permanent_city: "basic_info#permanent_city#region_name",
    permanent_area: "basic_info#permanent_area#region_name",
    permanent_countryside: "basic_info#permanent_countryside#region_name",
    permanent_village: "basic_info#permanent_village#region_name",
    permanent_detail: "basic_info#permanent_detail",

    // 户籍地址

    household_province: "basic_info#household_province#region_name",
    household_city: "basic_info#household_city#region_name",
    household_area: "basic_info#household_area#region_name",
    household_countryside: "basic_info#household_area#region_name",
    household_village: "basic_info#household_village#region_name",

    household_detail: "basic_info#permanent_detail",
    resume: "basic_info#file_url_label",
    is_start_job: "user_profile_current_job_info#is_start_job_label",
    nation: "basic_info#nation",
    reg_residence_property: "basic_info#reg_residence_property_label",
    political_outlook: "basic_info#political_outlook_label",
    graduate_date: "basic_info#graduate_date_label",
    graduate_school: "basic_info#graduate_school",
    study_speciality: "basic_info#study_speciality",

    employment_status:
        "id_card_openid#user_profile_current_job_info.employment_status_label",
    employment_type:
        "id_card_openid#user_profile_current_job_info.employment_type_label",
    work_city: "id_card_openid#user_profile_current_job_info.work_city_label",
    company_name: "id_card_openid#user_profile_current_job_info.company_name",
    // job_type_work: "id_card_openid#user_profile_current_job_info.job_type_label",
    salary: "id_card_openid#user_profile_current_job_info.salary_label",
    // job_industry: "user_profile_current_job_info#start_job_industry_label",
    // id_card_openid#user_profile_current_job_info.job_industry
    // job_willingness#job_willing_industry  星系
    job_industry:
        "id_card_openid#user_profile_current_job_info.job_industry_label",
    job_willing_industry: "job_willingness#job_willing_industry_label",
    job_willing_type_work: "job_willingness#job_willing_type_work_label",
    start_job_industry:
        "user_profile_current_job_info#start_job_industry_label",
    start_job_type_work:
        "user_profile_current_job_info#start_job_type_work_display_label",
    undefined_person_remark:
        "user_profile_current_job_info#undefined_person_remark_label",
    job_type_work: "user_profile_current_job_info#job_type_work_label",
    is_jobwilling: "job_willingness#is_job_willing_label",
    jobwilling_province: "job_willingness#job_province#region_name_label",
    jobwilling_city: "job_willingness#job_city#region_name_label",
    jobwilling_region: "job_willingness#job_county#region_name_label",

    jobwilling_salary: "job_willingness#job_willing_salary_label",
    jobWilling_type: "job_willing_type_label",
    jobWilling_type_work: "job_willing_type_work_label",
    is_train_job: "profile_current_train_info#is_train_job_label",
    is_training_willingess:
        "profile_current_train_info#is_training_willingness_label",
    level: "profile_current_train_info#qualification_level_label",
    speciality_detail:
        "profile_current_train_info#train_speciality_detail_label",
    train_speciality: "profile_current_train_info#train_speciality_label",
    training_willingness_work_type:
        "profile_current_train_info#training_willingness_work_type_label",
    training_start_time:
        "profile_current_train_info#train_start_datetime_label",
    training_end_time: "profile_current_train_info#train_end_datetime_label",
    flexible_employment:
        "profile_current_train_info#is_tran_flexible_employment_label",
    flexible_employment_month:
        "profile_current_train_info#flexible_employment_month",
    uid: "uniplat_uid",
    tags: "tags",

    basic_info_id: "basic_info#id",
    jon_info_id: "id_card_openid#user_profile_current_job_info.id",
    train_info_id: "profile_current_train_info#id",
    willingness_joint_id: "id_card_openid#job_willingness_joint.id",
}
