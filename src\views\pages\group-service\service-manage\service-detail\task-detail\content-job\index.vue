<template>
    <div class="core-ui-table-container">
        <div class="core-ui-custom-header">
            <div class="title">编辑任务内容</div>
        </div>
        <div class="page-detail">
            <div class="form-title u-flex">
                您正在使用 <span class="tips">岗位推荐工具</span>
            </div>
            <div class="form-content">
                <form-builder
                    class="form"
                    ref="formBuilder"
                    label-position="top"
                    label-width="140px"
                ></form-builder>
                <div class="u-m-b-30">
                    <div class="u-flex justify-content-between u-m-b-10">
                        <div>请选择招聘岗位，以便进行定向推广</div>
                        <el-button @click="displayJopApp = true">
                            添加岗位
                        </el-button>
                    </div>
                    <div>
                        <job-item
                            v-for="(item, index) in jobs"
                            :key="index"
                            :detail="item"
                        >
                            <div class="slot-right">
                                <el-button
                                    @click="onDelete(item)"
                                    type="text"
                                    class="u-p-0"
                                >
                                    删除
                                </el-button>
                            </div>
                        </job-item>
                        <div v-if="!jobs.length">暂无</div>
                    </div>
                </div>
                <div class="u-m-b-20">
                    <div class="lh40 u-m-b-10 color-2">查阅要求</div>
                    <el-checkbox-group v-model="checkList">
                        <el-checkbox
                            disabled
                            v-for="(item, index) in checkBox"
                            :key="index"
                            class="u-m-b-20"
                            :label="item"
                        ></el-checkbox>
                    </el-checkbox-group>
                </div>
                <div class="u-flex u-row-center u-m-t-20">
                    <el-button type="primary" plain @click="cancel">
                        返回
                    </el-button>
                    <el-button
                        class="u-m-l-30"
                        type="primary"
                        @click="confirm"
                        :loading="loading"
                    >
                        确定
                    </el-button>
                </div>
            </div>
        </div>
        <add-job-app
            @confirm="getJobIds"
            :ids="jobIds"
            v-model="displayJopApp"
        ></add-job-app>
    </div>
</template>

<script lang="ts">
    import {
        buildFormSections,
        FormController,
        FormType,
    } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import JobItem from "@/views/pages/group-service/service-manage/service-detail/task-detail/components/job-item.vue"
    import AddJobApp from "@/views/pages/group-service/service-manage/service-detail/task-detail/content-job/add-job-pop.vue"
    import { closeCurrentTap } from "@/views/pages/single-page/components/tags-view"
    import { clone } from "lodash"
    import { Component } from "vue-property-decorator"

    export const predict = {
        name: "name",
        address_detail: "address_detail",
        salary: "",
        tags: "tags",
        company: "agent#agent_name",
        source: "source_from_type",
        time: "final_change_time",
        agent_name: "agent#agent_name",
        age_require: "",
        education: "",
        online_status: "label",
        position_id: "id",
    }

    @Component({
        name: routesMap.groupService.serviceManageDetail.taskContent.job,
        components: { FormBuilder, JobItem, AddJobApp },
    })
    export default class Index extends FormController {
        private checkList = ["显示答案", "公开已答题人数"]
        private checkBox = ["显示答案", "公开已答题人数", "显示咨询按钮"]

        private displayJopApp = false

        private id = 0

        private jobIds: string[] = []

        private cacheJobs: any[] = []
        private jobs: any[] = []

        created() {
            this.id = +(this.$route.query.id || 0)
            this.init()
        }

        private getJobIds(selectedIds: string[], cacheJobs: any[]) {
            this.jobIds = clone(selectedIds)
            const cache = [...this.cacheJobs, ...cacheJobs]
            const selectedJobs = this.jobIds
                .filter((i) => {
                    return cache.find((t) => t.id + "" === i + "")
                })
                .map((k) => cache.find((t) => t.id + "" === k + ""))
            this.jobs = selectedJobs
        }

        private onDelete(item: any) {
            this.jobIds = this.jobIds.filter((i) => i + "" !== item.id + "")
            this.jobs = this.jobs.filter((i) => i.id + "" !== item.id + "")
        }

        private init() {
            return buildFormSections({
                forms: [
                    {
                        label: "岗位推荐标题",
                        type: FormType.Text,
                        prop: "title",
                    },
                    {
                        label: "岗位推荐描述",
                        type: FormType.Text,
                        prop: "content",
                    },
                    {
                        label: "就业查阅配置",
                        type: FormType.Select,
                        prop: "c_employment_status",
                    },
                    {
                        label: "数据采集填写数据",
                        type: FormType.Text,
                        prop: "job_ids",
                        hide: true,
                    },
                ],
                sdkModel: "serve_task",
                sdkAction: "update_content_for_job",
                id: this.id,
            }).then((r) => {
                this.buildForm(r.forms)
                try {
                    this.jobIds = JSON.parse(r.data.job_ids || "[]").map(
                        (i: number) => i.toString()
                    )
                } catch {}
                this.jobIds.length && this.initJobs(this.jobIds)
            })
        }

        private initJobs(jobIds: string[]) {
            sdk.core
                .model("xg_company_position")
                .list("intent_search_list")
                .addPrefilter({
                    id__in: jobIds.toString(),
                })
                .query({ pageIndex: 1, item_size: 999 })
                .then((r) => {
                    const res = sdk.buildRows(r.pageData.rows, predict)
                    this.jobs = clone(res)
                    this.cacheJobs = clone(res)
                })
        }

        private confirm() {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    Object.assign(data, {
                        job_ids: this.jobIds.map((i) => +i),
                    })
                    this.submit(data)
                }
            })
        }

        loading = false
        private submit(data: any) {
            this.loading = true
            sdk.core
                .model("serve_task")
                .action("update_content_for_job")
                .addInputs_parameter(data)
                .updateInitialParams({
                    selected_list: [{ v: 0, id: this.id }],
                })
                .execute()
                .then(() => {
                    this.$message.success("编辑成功！")
                    closeCurrentTap()
                    this.callRefresh(
                        routesMap.groupService.serviceManageDetail.task
                    )
                })
                .finally(() => {
                    this.loading = false
                })
        }

        private cancel() {
            closeCurrentTap()
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .page-detail {
        background: #fff;
        padding: 20px;
        min-height: 72vh;
        display: flex;
        flex-direction: column;
        align-items: center;

        .form-title {
            width: 100%;
            height: 36px;
            background: #f8f8f8;
            padding-left: 20px;
            font-size: 14px;
            color: #222222;
            margin-bottom: 20px;

            .tips {
                color: #ff8b16;
            }
        }

        .form-content {
            width: 600px;
        }
    }

    .lh40 {
        line-height: 40px;
    }

    .slot-right {
        position: absolute;
        right: 15px;
        top: 15px;
    }
</style>
