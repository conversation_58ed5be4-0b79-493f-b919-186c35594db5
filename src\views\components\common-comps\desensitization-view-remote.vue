<template>
    <div class="u-flex u-row-center table-mobile-box" :key="rowId">
        <div class="display-value-box">
            {{ text }}
        </div>
        <el-button
            @click="onClick"
            class="u-p-0"
            v-if="value && showBtn"
            type="text"
            >{{ !showMobile ? "查看" : "隐藏" }}</el-button
        >
    </div>
</template>

<script lang='ts'>
    import { sdk } from "@/service"
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"

    @Component({ components: {} })
    export default class DesensitizationView extends Vue {
        @Prop({ default: "" })
        private value!: string

        @Prop({ default: "" })
        private rowId!: string

        @Prop({ default: "" })
        private modelName!: string

        @Prop({ default: "show_mobile" })
        private actionName!: string

        @Prop({ default: true })
        private showBtn!: boolean

        @Prop({ default: null })
        private handlerDisplay!: any

        @Prop({ default: "" })
        private cacheUId!: any

        @Watch("rowId", { immediate: true })
        private onRowIdChange() {
            this.showMobile = false
            this.displayMobile = ""
        }

        @Watch("cacheUId", { immediate: true })
        private onCacheUIdChange() {
            this.showMobile = false
        }

        private useCacheUId = ""

        private get text() {
            if (!this.handlerDisplay) {
                return !this.showMobile ? this.value : this.displayMobile
            } else {
                return this.handlerDisplay(
                    !this.showMobile ? this.value : this.displayMobile
                )
            }
        }

        private displayMobile = ""
        private cacheId = this.rowId
        private showMobile = false

        private onClick() {
            if (
                this.cacheUId === this.useCacheUId &&
                this.displayMobile &&
                this.cacheId === this.rowId
            ) {
                this.showMobile = !this.showMobile
                return
            }
            this.cacheId = this.rowId
            sdk.core
                .model(this.modelName)
                .action(this.actionName)
                .updateInitialParams({
                    selected_list: [{ id: +this.rowId, v: 0 }],
                })
                .query()
                .then((r) => {
                    this.displayMobile =
                        r.parameters.inputs_parameters[0].default_value || ""
                    this.showMobile = true
                    this.useCacheUId = this.cacheUId
                })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";

    .table-mobile-box {
        gap: 10px;
        align-items: baseline;
        .display-value-box {
            flex: none;
        }
    }
</style>
