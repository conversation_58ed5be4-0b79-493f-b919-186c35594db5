import { config, EnvProject } from "@/config"
import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"
function tableFilter(isPublic = false): TableFilter[] {
    const salary: any = [
        {
            label: "最低薪资",
            type: FormType.TextRange,
            option: { type: "number" },
            prop: "salary_min",
        },
        {
            label: "最高薪资",
            type: FormType.TextRange,
            option: { type: "number" },
            prop: "salary_max",
        },
    ]
    const age: any = [
        {
            label: "最低年龄",
            type: FormType.TextRange,
            option: { type: "number" },
            prop: "age_require_min",
        },
        {
            label: "最高年龄",
            type: FormType.TextRange,
            option: { type: "number" },
            prop: "age_require_max",
        },
    ]
    return [
        {
            label: "岗位名称",
            type: FormType.Text,
            prop: "name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        ...salary,
        ...age,
        {
            label: "结算方式",
            prop: "salary_type",
            type: FormType.Select,
        },
        {
            label: "工作地",
            type: FormType.Cascader,
            prop: "province_code",
            option: {
                filterable: true,
                elProps: { checkStrictly: true },
            },
        },
        {
            label: "来源",
            type: FormType.Select,
            option: {
                multiple: true,
            },
            prop: "create_type",
            hide: isPublic,
        },
        {
            label: "上架状态",
            type: FormType.Select,
            prop: "online_status",
        },
        {
            label: isPublic ? "所属单位" : "所属企业",
            type: FormType.Text,
            prop: "agent.agent_name",
            keyValueFilter: {
                match: ListTypes.filterMatchType.fuzzy,
            },
        },
        {
            label: "创建时间",
            type: FormType.DatePicker,
            prop: "create_time",
            col: { span: 16 },
            option: {
                type: "daterange",
            },
        },
        {
            label: "职能",
            type: FormType.Cascader,
            prop: "function_category_1",
            option: { elProps: { checkStrictly: true } },
            hide: isPublic,
        },
        {
            label: "工种",
            type: FormType.Select,
            prop: "industry",
            hide: !isPublic,
        },
        {
            label: "学历要求",
            type: FormType.Select,
            prop: "education",
        },
        {
            label: "性别要求",
            type: FormType.Select,
            prop: "gender_require",
            hide: config.projectConfig.hideGender,
        },
        {
            label: "更新时间",
            type: FormType.DatePicker,
            prop: "update_time",
            option: {
                type: "daterange",
            },
        },
        {
            label: "管理区域",
            type: FormType.Cascader,
            prop: "mgt_province_region_code",
            option: {
                elProps: {
                    checkStrictly: true,
                },
                filterable: true,
            },
            hide: isPublic || config.envProject === EnvProject.黄州项目,
        },

        // {
        //     label: "标签",
        //     type: FormType.Cascader,
        //     prop: "tag",
        //     useTag: "*",
        //     option: {
        //         filterable: true,
        //         multiple: true,
        //         collapseTags: true,
        //     },
        // },
    ]
}

/** 审核状态 */
export enum AuditStatus {
    草稿 = -1,
    待审核 = 0,
    审核通过 = 1,
    审核不通过 = 2,
}

/** 上架状态 */
export enum OnlineStatus {
    已下架 = 0,
    已上架 = 1,
}

export const predict = {
    name: "",
    salary: "label",
    address_detail: "address_detail",
    city: "city#region_name",
    province: "province#region_name",
    area: "area#region_name",
    agent_id: "",
    source_from_type: "label",
    online_status: "label",
    online_user_name: "online_user#info#name",
    online_operator_name: "",
    status: "label",
    position_type: "label",
    contact_person: "label",
    contact_mobile: "",
    company_name: "agent#agent_name",
    source_page_url: "",
    online_time: "label",
    offline_time: "label",
    recruit_start_time: "label",
    recruit_end_time: "label",
    update_time: "label",
    final_change_time: "label",
    audit_user_name: "audit_user#info#name",
    audit_time: "",
    audit_memo: "",
    education: "label",
    description: "",
    salary_pay_type: "label",
    experience_min: "label",
    experience_max: "label",
    source_from_logo: "label",
    create_type: "label",
    recruit_count: "label",
    cal_address_detail: "cal_address_detail_label",
    region_type: "label",
    has_recommend_1: "label",
    has_recommend_2: "label",
    has_recommend_3: "label",
    has_recommend_4: "label",
    creator: "creator_user#display_name",
    create_time: "label",
    age_require_min: "",
    age_require_max: "",
    industry: "label",
    salary_type: "label",
    salary_desc: "label",
    gender_require: "label",
    age_require: "label",
    experience: "label",
    expired_date: "label",
    function_categories: "",
    function_detail: "",
    work_type: "label",
    language: "label",
    language_level: "label",
    contact_email: "",
    tags: "tags",
    households: "cal_household_address_label",
    major: "label",
    major_desc: "label",
    is_create: "agent#is_create",
    object_data: "object_data",
    agent_access_key: "agent#_access_key",
    require_education: "label",
    require_profile: "label",
    require_work: "label",
    entry_sums: "label",
}

export interface Row {
    _access_key: string
    id: number
    name: string
    salary: string
    address_detail: string
    city: string
    province: string
    area: string
    agent_id: string
    source_from_type: string
    source_from_type_label: string
    create_type: string
    create_type_label: string
    online_status: OnlineStatus
    status: AuditStatus
    contact_person: string
    contact_mobile: string
    company_name: string
    source_page_url: string
    online_time: string
    offline_time: string
    online_user_name: string
    online_operator_name: string
    recruit_start_time: string
    recruit_end_time: string
    update_time: string
    final_change_time: string
    audit_user_name: string
    audit_time: string
    audit_memo: string
    education: string
    education_label: string
    gender_require_label: string
    description: string
    salary_pay_type: string
    salary_pay_type_label: string
    salary_type: string
    salary_type_label: string
    experience_min: string
    experience_min_label: string
    experience_max: string
    experience_max_label: string
    source_from_logo: string
    region_type: string
    region_type_label: string
    creator: string
    create_time: string
    industry: string
    salary_desc: string
    industry_label: string
    recruit_count: number
    experience: string

    age_require_min: number
    age_require_max: number
    expired_date_label: string
    [key: string]: any

    /** 职能 */
    function_categories: string

    /** 岗位职责 */
    function_detail: string

    /** 工作性质 */
    work_type: string

    /** 语言种类 */
    language: Language

    /** 语言种类[文本] */
    language_label: string

    /** 外语水平 */
    language_level: LanguageLevel

    /** 外语水平[文本] */
    language_level_label: string

    /** 招聘过期时间 */
    expired_date: string

    /** 联系邮箱 */
    contact_email: string
    cal_address_detail: string
    cal_address_detail_label: string
}

export function tableConfig(isPublic = false): TableConfig {
    return {
        model: sdk.core
            .model("xg_company_position")
            .list(isPublic ? "public_welfare_manage" : "manage"),
        filter: tableFilter(isPublic),
        outFilter: [
            {
                label: "职位福利：",
                type: FormType.Select2,
                prop: "tag1",
                useTag: "职位福利",
                option: {
                    filterable: true,
                    multiple: true,
                    collapseTags: true,
                    matchMode: "all",
                },
                keyValueFilter: {
                    match: ListTypes.filterMatchType.fuzzy,
                },
            },
            {
                label: "职位亮点：",
                type: FormType.Select2,
                prop: "tag2",
                useTag: "职位亮点",
                option: {
                    filterable: true,
                    multiple: true,
                    collapseTags: true,
                    matchMode: "all",
                },
            },
        ],
        tabPages: [
            "全部",
            "草稿",
            "待审核",
            "招聘结束已下架",
            "审核通过已上架",
            "审核不通过",
        ],
        column: columns(isPublic),
        defaultPageSize: 10,
        predict,
        oneTabFilter: true,
    }
}

export function columns(isPublic = false): TableColumn[] {
    return [
        {
            prop: "select",
            width: "58",
            type: "selection",
            selectable: (row: any) => {
                return (
                    (row.online_status === OnlineStatus.已上架 &&
                        row.status === AuditStatus.审核通过) ||
                    row.status === AuditStatus.待审核
                )
            },
        },
        {
            label: "岗位名称",
            prop: "name",
            minWidth: "160",
            showOverflowTip: true,
            fixed: "left",
        },
        {
            label: "招聘人数",
            prop: "recruit_count",
            minWidth: "100",
            fixed: "left",
            showOverflowTip: true,
        },
        {
            label: isPublic ? "补贴标准" : "薪资",
            prop: "salary",
            minWidth: "140",
            render(h, row) {
                return h("span", {}, row.salary_desc)
            },
            showOverflowTip: true,
        },
        {
            label: "工作地址",
            minWidth: "200",
            prop: "cal_address_detail",
            showOverflowTip: true,
        },
        {
            label: isPublic ? "所属单位" : "所属企业",
            minWidth: "200",
            prop: "company_name",
            showOverflowTip: true,
        },
        ...(isPublic
            ? []
            : [
                  {
                      label: "来源",
                      minWidth: "150",
                      prop: "create_type_label",
                      showOverflowTip: true,
                  },
              ]),
        {
            label: "年龄要求",
            minWidth: "80",
            prop: "age_require_label",
            showOverflowTip: true,
        },
        {
            label: "联系信息",
            minWidth: "100",
            prop: "info",
            showOverflowTip: true,
        },
        {
            label: "审核状态",
            minWidth: "80",
            prop: "status_label",
            showOverflowTip: true,
        },
        {
            label: "上架状态",
            minWidth: "80",
            prop: "online_status_label",
            showOverflowTip: true,
        },
        {
            label: "创建/更新时间",
            prop: "create_time",
            showOverflowTip: true,
            minWidth: "180",
            render(h, row) {
                return h(
                    "div",
                    [
                        h("div", "创建 " + formatTime.seconds(row.create_time)),
                        row.create_time !== row.update_time &&
                            h(
                                "div",
                                "更新 " + formatTime.seconds(row.update_time)
                            ),
                    ].filter(Boolean)
                )
            },
        },
        {
            label: "操作",
            prop: "h",
            minWidth: "120",
            fixed: "right",
        },
    ]
}
const enum Language {
    英语 = 1,
    日语 = 2,
    俄语 = 3,
    法语 = 4,
    意大利语 = 5,
    德语 = 6,
    韩语 = 7,
    蒙古语 = 8,
    葡萄牙语 = 9,
    西班牙语 = 10,
    巴士克语 = 11,
    冰岛语 = 12,
    丹麦语 = 13,
    法罗语 = 14,
    芬兰语 = 15,
    荷兰语 = 16,
    加泰隆语 = 17,
    马来语 = 18,
    南非语 = 19,
    挪威语 = 20,
    瑞典语 = 21,
    斯瓦西里语 = 22,
    印度尼西亚语 = 23,
    汉语 = 24,
    其他语言 = 90,
}

const enum LanguageLevel {
    一般 = 1,
    熟练 = 2,
    精通 = 3,
}
