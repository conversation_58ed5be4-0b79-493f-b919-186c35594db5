<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
        >
            <div slot="title" class="d-flex-item-center bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div slot="h" slot-scope="scope">
                        <div class="u-flex u-row-center">
                            <el-button type="text" @click="toDetail(scope.row)">
                                详情
                            </el-button>
                        </div>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { Component } from "vue-property-decorator"
    import { buildConfig4RemoteMeta } from "../collect-task-manage/components/build-table"
    import { updateTagItem } from "../single-page/components/tags-view"

    @Component({
        name: routesMap.policyDataBase.otherSource,
        components: { TableContainer, CommonTable },
    })
    export default class Template extends BaseTableController<any> {
        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.policyDataBase.otherSource,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "其他数据资源",
                    to: {
                        name: routesMap.policyDataBase.otherSource,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.policyDataBase.otherSource,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        toDetail(row: any) {
            this.$router.push({
                name: routesMap.policyDataBase.otherSourceDetail,
                query: { id: row._access_key + "", from: this.$route.name },
            })
        }

        mounted() {
            this.init()
        }

        private init() {
            this.setBreadcrumbs()
            pageLoading(() => {
                return buildConfig4RemoteMeta(
                    "common_check_table_source",
                    "list_data",
                    {
                        disabledOpt: false,
                        disabledFilter: true,
                        useLabelWidth: true,
                        optColumn: {
                            label: "操作",
                            prop: "h",
                            fixed: "right",
                            minWidth: "120px",
                        },
                        useTabs: true,
                        useRowFieldGroups: true,
                    }
                ).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            tableConfig.filter = r.filter
            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = r.columns
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
</style>
