<template>
    <Container>
        <div
            class="home-container flex-fill u-flex u-row-between"
            v-if="sessionKey"
        >
            <div class="content-top" v-if="info">
                <Header title="全市春风行动情况统计"></Header>
                <div class="d-flex top-item1">
                    <Card1 :info="info"></Card1>
                    <Card2 :info="info"></Card2>
                </div>
                <div class="d-flex top-item2">
                    <Card3 :info="info"></Card3>
                    <Card4 :info="info"></Card4>
                    <Card5 :info="info"></Card5>
                </div>
            </div>
            <div class="content-bottom" v-if="info">
                <Header title="春风行动启动会现场招聘会数据统计"></Header>
                <div class="bottom-content">
                    <Card6 class="bottom-item1" :info="info"></Card6>
                    <Card7 :info="info"></Card7>
                </div>
            </div>
        </div>
    </Container>
</template>

<script lang="ts">
    import { Component, Vue } from "vue-property-decorator"
    import Container from "./common/container.vue"
    import Header from "./common/header.vue"
    import Card1 from "./components/招聘活动情况/index.vue"
    import Card2 from "./components/返乡人员服务情况/index.vue"
    import Card3 from "./components/春风行动招聘会列表/index.vue"
    import Card4 from "./components/春风行动人岗匹配数据统计/index.vue"
    import Card5 from "./components/春风行动服务数据统计/index.vue"
    import Card6 from "./components/招聘会基本情况/index.vue"
    import Card7 from "./components/统计/index.vue"
    import { getJobFairDataV2, getValue } from "../../招聘会大屏/service"
    import { loopTimer } from "../../招聘会大屏/tools/looptimer"

    @Component({
        components: {
            Container,
            Header,
            Card1,
            Card2,
            Card3,
            Card4,
            Card5,
            Card6,
            Card7,
        },
    })
    export default class Template extends Vue {
        private sessionKey = "2024湖北春风行动"

        private info: any | null = null

        created() {
            this.sessionKey = (this.$route.query.type as string) || this.sessionKey
            sessionStorage.setItem("CUR_BIG_SCREEN_TYPE", this.sessionKey)
            this.init()
        }

        private buildInfo(result: any[]) {
            return {
                招聘会总场次: getValue("招聘会总场次", result) || 0,
                带岗直播场次: getValue("带岗直播场次", result) || 0,

                返乡总人数: getValue("返乡总人数", result) || 0,
                返乡人员服务人次: getValue("返乡人员服务人次", result) || 0,
                返乡人员投递人次: getValue("返乡人员投递人次", result) || 0,

                招聘企业: getValue("招聘企业", result) || 0,
                招聘岗位: getValue("招聘岗位", result) || 0,
                岗位类型: getValue("岗位类型", result) || 0,
                人岗比: getValue("人岗比", result) || "1:1",
                参与总人数: getValue("参与总人数", result) || 0,
                线上参与: getValue("线上参与", result) || 0,
                线下参与: getValue("线下参与", result) || 0,
                招聘会浏览人次: getValue("招聘会浏览人次", result) || 0,
                岗位浏览人次: getValue("岗位浏览人次", result) || 0,
                投递人次: getValue("投递人次", result) || 0,

                服务总人次: getValue("服务总人次", result) || 0,
                返乡慰问: getValue("返乡慰问", result) || 0,
                节日祝福: getValue("节日祝福", result) || 0,
                邀请注册居民数: getValue("邀请注册居民数", result) || 0,
                岗位推荐: getValue("岗位推荐", result) || 0,
                政策推荐次数: getValue("政策推荐次数", result) || 0,
                培训推荐次数: getValue("培训推荐次数", result) || 0,

                参会企业数量: getValue("参会企业数量", result) || 0,
                参会岗位数量: getValue("参会岗位数量", result) || 0,
                岗位类型数量: getValue("岗位类型数量", result) || 0,

                总参会人数: getValue("总参会人数", result) || 0,
                到场人数: getValue("到场人数", result) || 0,
                男性: getValue("男性", result) || 1,
                女性: getValue("女性", result) || 1,

                浏览岗位总数: getValue("浏览岗位总数", result) || 0,
                投递总人次: getValue("投递总人次", result) || 0,

                春风行动招聘会列表: getValue("春风行动招聘会列表", result) || "",
            }
        }

        @loopTimer(30 * 1000)
        private init() {
            getJobFairDataV2("position_group").then((r) => {
                this.info = this.buildInfo(r)
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .home-container {
        padding-left: 20px;
        padding-right: 20px;
        flex-direction: column;
        gap: 10px;
    }

    .content-top {
        height: 589px;
        background: rgba(17, 43, 184, 0.5);
        border-radius: 0px 0px 30px 30px;
    }

    .top-item1 {
        margin-bottom: 10px;
        gap: 20px;
        padding-left: 20px;
        padding-right: 20px;
        padding-top: 8px;
    }

    .top-item2 {
        gap: 20px;
        padding-left: 20px;
        padding-right: 20px;
    }

    .bottom-content {
        padding-top: 8px;
        margin-bottom: 10px;
        padding-left: 20px;
        padding-right: 20px;
    }

    .bottom-item1 {
        margin-bottom: 10px;
    }

    .content-bottom {
        height: 355px;
        background: rgba(17, 43, 184, 0.5);
        border-radius: 0px 0px 30px 30px;
    }
</style>
