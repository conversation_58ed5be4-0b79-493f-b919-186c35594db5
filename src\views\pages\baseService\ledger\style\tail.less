.flex {
    display: flex;
}
.flex-col {
    flex-direction: column;
}
.items-center {
    align-items: center;
}
.items-start {
    align-items: flex-start;
}
.items-end {
    align-items: flex-end;
}
.justify-between {
    justify-content: space-between;
}
.justify-center {
    justify-content: center;
}
.justify-around {
    justify-content: space-around;
}
.round-3 {
    border-radius: 3px;
}
.bg-white {
    background-color: #fff;
}
.gap-4 {
    gap: 4px;
}
.gap-20 {
    gap: 20px;
}
.font-600 {
    font-weight: 600;
}
.text-14 {
    font-size: 14px;
}
.text-16 {
    font-size: 16px;
}
.text-18 {
    font-size: 18px;
}
.text-24 {
    font-size: 24px;
}
.text-30 {
    font-size: 30px;
}
.grid {
    display: grid;
}
.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
}
.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}
.row-span-2 {
    grid-row: span 2;
}

.flex-1 {
    flex: 1;
}
.text-777E8E {
    color: #777e8e;
}
