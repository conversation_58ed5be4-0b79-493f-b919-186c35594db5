import {
    BuildFormConfig,
    buildSelectSource,
    FileType,
    FormType,
} from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { formatTime } from "@/utils/tools"
import { ListTypes } from "uniplat-sdk"

export const enum Status {
    下架 = 0,
    上架 = 1,
}

export interface Row {
    /** 政策名称 */
    policy_name: string

    /** 所属科室 */
    name: string

    /** 对外发布单位 */
    org_name_display: string

    /** 限制申报 */
    limit_times_display: string

    /** 最后操作人 */
    real_name: string

    /** 最后操作时间 */
    update_time: string

    /** 状态 */
    status: Status

    /** 状态[文本] */
    status_label: string
    id: number
    v: number
}

export const publishTableFilter: TableFilter[] = [
    {
        label: "政策名称",
        type: FormType.Text,
        prop: "policy_name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "对外发布单位",
        type: FormType.Text,
        prop: "org_name_display",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "状态",
        type: FormType.Select,
        prop: "status",
    },
]

export const columns: TableColumn[] = [
    {
        label: "政策名称",
        prop: "policy_name",
        showOverflowTip: true,
    },
    {
        label: "对外发布单位",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "所属科室",
        prop: "name2",
        showOverflowTip: true,
    },
    {
        label: "限制申报",
        prop: "limit_times_display",
        showOverflowTip: true,
    },
    {
        label: "最后操作人",
        prop: "real_name",
        showOverflowTip: true,
    },
    {
        label: "最后操作时间",
        prop: "update_time_label",
        showOverflowTip: true,
    },
    {
        label: "状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "浏览量",
        prop: "browse_num",
    },
    {
        label: "操作",
        prop: "h",
        width: "170px",
        fixed: "right",
        showOverflowTip: true,
    },
]

interface Item {
    value: any
    display?: any
}

export interface TemplateItem {
    keyValue: string
    rowData: {
        id: Item
        template_file: Item
        title: Item
    }
}

export const enum ServeTargetType {
    居民 = 1,
    企业 = 2,
}

export interface DetailRow {
    status: Status
    status_label: string
    serve_target_type: ServeTargetType
    serve_target_type_label: string
    policy_name: string
    name: string
    org_name_display: string
    limit_times_display: string
    content: string
    upload_desc: string
    question_id: string
    question_title: string
    question_access_key: string
    id: number
    v: number
}

export const detailPredict = {
    status: "label",
    serve_target_type: "label",
    policy_name: "",
    name: "org#name",
    org_name_display: "",
    limit_times_display: "",
    content: "",
    upload_desc: "",
    question_id: "question#id",
    question_title: "question#title",
    question_access_key: "question#_access_key",
}

const intentPredict = {
    title: "title",
    description: "",
    id: "",
}

const intentColumn: TableConfig["column"] = [
    {
        label: "ID",
        prop: "id",
        showOverflowTip: true,
    },
    {
        label: "问卷标题",
        prop: "title",
        showOverflowTip: true,
    },
    {
        label: "备注",
        prop: "description",
        showOverflowTip: true,
    },
]
export const intentFilter: TableFilter[] = [
    {
        label: "问卷标题",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
]
export function createFormConfig(id: string): BuildFormConfig {
    const forms: BuildFormConfig["forms"] = [] || [
        {
            label: "问卷标题",
            type: FormType.Text,
            prop: "title",
            required: true,
        },
        {
            label: "问卷是否启用",
            type: FormType.Switch,
            prop: "is_using",
            required: true,
            hide: !!id,
        },
        {
            label: "描述",
            type: FormType.Text,
            prop: "description",
        },
        {
            label: "答题截止时间",
            type: FormType.DatePicker,
            prop: "deadline",
            required: true,
        },
        {
            label: "问卷类型",
            type: FormType.Select,
            prop: "type",
            required: true,
            hide: !!id,
        },
        {
            label: "问卷对象类型",
            type: FormType.Select,
            prop: "target_type",
            required: true,
        },
        {
            label: "小程序分享图片",
            type: FormType.MyUpload,
            prop: "share_image",
            required: true,
            option: {
                fileType: [FileType.Image],
                listType: "picture-card",
                placeholder: "支持上传jpg、png等图片格式,最多支持三张",
                limit: 1,
                limitSize: 3072,
            },
        },
        {
            label: "问卷模版",
            type: FormType.IntentSearch,
            prop: "questionnaire_id",
            option: {
                dialogProp: {
                    width: "800px",
                },
                intentSearchConfig: {
                    tableConfig: () => ({
                        model: sdk.core
                            .model("general_questionnaire")
                            .list("intent_search_list"),
                        preFilter: {
                            is_using: "1",
                            type: "0",
                        },
                        predict: intentPredict,
                        column: intentColumn,
                        filter: intentFilter,
                    }),
                    template: "{title}",
                    valueKey: "id",
                },
            },
            hide: !!id,
        },
    ]
    return {
        sdkModel: "general_questionnaire",
        sdkAction: id ? "update_policy_form" : "insert_policy_form",
        id: +id,
        forms,
    }
}
const tableFilter: TableFilter[] = [
    {
        label: "表单标题",
        type: FormType.Text,
        prop: "title",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "状态",
        type: FormType.Select,
        prop: "is_using",
        sourceInputsParameter: buildSelectSource([
            {
                value: "已启用",
                key: "1",
            },
            {
                value: "已禁用",
                key: "0",
            },
        ]),
    },
    {
        label: "对象类型",
        type: FormType.Select,
        prop: "target_type",
    },
]
export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("general_questionnaire").list("policy_form"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: {
            title: "",
            deadline: "",
            type: "label",
            target_type: "label",
            share_image: "",
            share_h5_link: "",
            is_using: "is_using_label",
            update_time: "label",
            updator_real_name: "updator#real_name",
            policy_form: "",
        },
    }
}
export const columns2: TableColumn[] = [
    {
        label: "表单名",
        prop: "title",
        showOverflowTip: true,
    },
    // {
    //     label: "答题截止时间",
    //     prop: "deadline",
    //     showOverflowTip: true,
    //     formatter: (row) => {
    //         return formatTime.day(row.deadline)
    //     },
    // },
    // {
    //     label: "问卷类型",
    //     prop: "type_label",
    //     showOverflowTip: true,
    // },
    {
        label: "表单对象类型",
        prop: "target_type_label",
        showOverflowTip: true,
    },
    // {
    //     label: "小程序分享图片",
    //     prop: "share_image",
    //     showOverflowTip: true,
    //     render: (h, row) => {
    //         return h("img", {
    //             attrs: {
    //                 src: sdk.buildImage(row?.share_image || ""),
    //             },
    //             style: {
    //                 width: "100px",
    //                 height: "100px",
    //             },
    //         })
    //     },
    // },
    // {
    //     label: "h5地址",
    //     prop: "share_h5_link",
    //     width: "120px",
    //     showOverflowTip: true,
    // },
    {
        label: "最后操作人",
        prop: "updator_real_name",
        showOverflowTip: true,
    },
    {
        label: "最后操作时间",
        prop: "update_time_label",
        showOverflowTip: true,
    },
    {
        label: "状态",
        prop: "is_using",
        formatter(row) {
            return row.is_using ? "已启用" : "已禁用"
        },
    },
    {
        label: "操作",
        prop: "h",
        width: "160px",
        showOverflowTip: true,
    },
]
