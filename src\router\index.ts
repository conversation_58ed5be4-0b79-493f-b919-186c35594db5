import { AppName } from "@/config/config"
import { PassportTokenController } from "@/core-ui/service/passport/token"
import { buildRouteRole, checkRole, roleConfig } from "@/installer/role"
import { sdk } from "@/service"
import { userService } from "@/service/service-user"
import layout from "@/views/pages/single-page/index.vue"
import { Loading, MessageBox } from "element-ui"
import Vue from "vue"
import VueRouter, { Route, RouteConfig as VueRouteConfig } from "vue-router"
import {
    operateRouteRelationConfig,
    RouteRelationConfig,
    routesMap,
} from "./direction"
import { xg_project_org } from "./route-company"
import { xg_project_org_hr } from "./route-hr"
import { xg_project_operate } from "./router-operate"
import { omit } from "lodash"
import { bigScreenJobFairRouters } from "@/views/pages/big-screen/招聘会大屏/routers"
import { bigScreenServiceMonitor } from "@/views/pages/big-screen/智慧就业服务监测大屏/routers"
import { bigScreenPeopleRouters } from "@/views/pages/big-screen/人岗分析大屏/routers"
import { bigScreenRouters } from "@/views/pages/big-screen/router"
import { bigScreenProvinceRouters } from "@/views/pages/big-screen/省级大屏/routers"
import { config, EnvProject } from "@/config"
import { upgrade } from "@/service/upgrade"
import { sysConfigService } from "@/service/sys-config"
import { xg_project_train } from "./route-train"
import { clientConfigService } from "@/service/client-config"
import { xg_project_grid } from "./route-grid"

export type RouteConfig = VueRouteConfig & {
    meta: {
        title?: string
        icon?: string
        svgIcon?: string
        // 隐藏菜单
        hidden?: boolean
        // 只有一个子菜单，父菜单不会显示
        showOneChildren?: boolean
        // 有页面，不会显示菜单
        single?: boolean
        // 是否缓存不路由
        noCache?: boolean
        // 页签不展示
        hideTag?: boolean
        // 没有菜单二级页面，高亮菜单名字
        parentMenuName?: string
        // 权限规则
        role?: roleConfig
    }
    children?: RouteConfig[]
}

Vue.use(VueRouter)
const allRoute: Record<AppName, RouteConfig[]> = {
    xg_project_operate,
    xg_project_org: xg_project_org(),
    xg_project_org_hr: xg_project_org_hr(),
    xg_project_train: xg_project_train(),
    xg_project_grid: xg_project_grid(),
    // dh_wxmp: dh_wxmp(),
}
const routeRelationConfig: Record<AppName, RouteRelationConfig | null> = {
    xg_project_operate: operateRouteRelationConfig,
    xg_project_org: null,
    xg_project_org_hr: null,
    xg_project_train: null,
    xg_project_grid: null,
}

export const currentAppRoute = buildRouteRole(
    allRoute[process.env.VUE_APP_APP_NAME as AppName]
)

export const currentRouteRelationConfig =
    routeRelationConfig[process.env.VUE_APP_APP_NAME as AppName]
export const routes: RouteConfig[] = [
    { path: "*", redirect: "login", name: routesMap.root, meta: {} },
    {
        path: "/show",
        name: routesMap.showPage,
        meta: { single: true, hidden: true },
        ...([EnvProject.黄州项目].includes(config.envProject)
            ? { redirect: "login" }
            : {}),
        component: () => {
            if ([EnvProject.荆州项目].includes(config.envProject)) {
                // 荆州
                return import("@/views/pages/big-screen/views/show.vue")
            } else if (config.envProject === EnvProject.演示项目) {
                // 演示项目
                return import("@/views/pages/single-page/show-show.vue")
            } else {
                // 孝感项目
                return import("@/views/pages/single-page/show.vue")
            }
        },
    },
    {
        path: "/login",
        name: routesMap.login,
        meta: { single: true, hidden: true },
        component: () => import("@/views/pages/single-page/login.vue"),
    },
    {
        path: "/register",
        name: routesMap.register,
        meta: { single: true, hidden: true },
        component: () => import("@/views/pages/single-page/register.vue"),
    },
    {
        path: "/role",
        name: routesMap.role.root,
        component: layout,
        meta: {
            single: true,
            hidden: true,
        },
        children: [
            {
                path: "no",
                name: routesMap.role.no,
                meta: { single: true, hidden: true },
                component: () =>
                    import("@/views/pages/single-page/no-role.vue"),
            },
        ],
    },
    {
        path: "/redirect",
        component: layout,
        meta: {
            single: true,
            hidden: true,
            showOneChildren: true,
        },
        children: [
            {
                path: "/redirect/:path*",
                meta: { single: true, hidden: true, hideTag: true },
                component: () =>
                    import("@/views/pages/single-page/redirect.vue"),
            },
        ],
    },
    {
        path: "/upgrade",
        name: routesMap.upgrade,
        meta: { single: true, hidden: true },
        component: () => import("@/views/pages/single-page/upgrade.vue"),
    },
    {
        path: "/apply",
        name: routesMap.apply,
        meta: { single: true, hidden: true },
        component: () => import("@/views/pages/single-page/apply.vue"),
    },
    {
        path: "/apply-h5",
        name: routesMap.applyH5,
        meta: { single: true, hidden: true },
        component: () => import("@/views/pages/single-page/apply-h5.vue"),
    },
    // {
    //     path: "/questionnaire-design/detail",
    //     name: routesMap.questionnaireDesign.detail,
    //     meta: { single: true, hidden: true },
    //     component: () => import("@/views/common/form/detail.vue"),
    // },
    ...currentAppRoute,
    ...bigScreenJobFairRouters,
    ...bigScreenServiceMonitor,
    ...bigScreenPeopleRouters,
    ...bigScreenRouters,
    ...bigScreenProvinceRouters,
]
const base =
    process.env.NODE_ENV === "production"
        ? `/${process.env.BASE_URL || ""}`
        : `/`
const router = new VueRouter({
    base: base.replace("//", "/"),
    mode: "history",
    routes: routes,
})

async function handleKeepAlive(to: Route) {
    if (to.matched.length > 2) {
        for (let i = 0; i < to.matched.length; i++) {
            const element = to.matched[i]
            if ((element.components as any).default?.extendOptions?.name === "RouteView") {
                to.matched.splice(i, 1)
                handleKeepAlive(to)
            }
        }
    }
}

router.beforeEach((to, from, next) => {
    if (to.query.scene_key) {
        userService.logout()
        window.localStorage.setItem(
            "currentAgentIdKey",
            to.query.scene_key as string
        )
        return router.replace({
            path: to.path,
            query: omit(to.query, "scene_key"),
        })
    }
    handleKeepAlive(to)
    const replace2Login = () => {
        if (
            ["/", "/login"].includes(from.path) &&
            ["/", "/login"].includes(to.path)
        ) {
            return next()
        }
        return router.replace({
            name: routesMap.login,
            query: {
                redirectUrl: to.fullPath,
            },
        })
    }
    const checkRouteRoleAndNext = () => {
        if (checkRole(to.meta?.role)) {
            next()
        } else {
            router.replace({ name: routesMap.role.no })
        }
    }
    upgrade
        .setUp()
        .then(() => {
            sysConfigService.setup()
            clientConfigService.setup()
            const token =
                PassportTokenController.hasToken() || (to.query.token as string)
            const toDirection = to.name
            if (toDirection === routesMap.showPage) return next()
            if (
                toDirection === routesMap.apply ||
                toDirection === routesMap.applyH5
            ) {
                return next()
            }

            if (
                toDirection?.includes("bigScreen") ||
                toDirection?.includes("big-screen")
            ) {
                if (!to.meta || !to.meta.needLogin) {
                    return next()
                }
            }

            if (
                toDirection &&
                [routesMap.root, routesMap.login, routesMap.upgrade].includes(
                    toDirection
                )
            ) {
                if (toDirection === routesMap.login && token) {
                    const loading = Loading.service({})
                    sdk.setup({ jwt: token, is_super: false })
                        .then(async () => {
                            if (!(await userService.setup())) {
                                return next()
                            } else {
                                router.replace({ name: routesMap.home.page })
                            }
                        })
                        .finally(() => {
                            loading.close()
                        })
                } else {
                    return next()
                }
            }
            if (!token) {
                return MessageBox.confirm("登录过期，请重新登录", "提示", {
                    showCancelButton: false,
                    showClose: false,
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                }).then(() => {
                    userService.logout({
                        redirectUrl: to.fullPath,
                    })
                })
            }
            if (!sdk.getOrdId()) {
                const loading = Loading.service({})
                sdk.setup({ jwt: token, is_super: false })
                    .then(async () => {
                        if (!(await userService.setup())) {
                            return replace2Login()
                        } else {
                            checkRouteRoleAndNext()
                        }
                    })
                    .catch(() => {
                        replace2Login()
                    })
                    .finally(() => {
                        loading.close()
                    })
            } else {
                checkRouteRoleAndNext()
            }
        })
        .catch(() => {
            router.replace({ name: routesMap.upgrade })
        })
})

export default router
