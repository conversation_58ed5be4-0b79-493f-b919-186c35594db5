<template>
    <el-dialog
        append-to-body
        :visible="value"
        @close="close"
        title="添加管理员"
        width="600px"
        top="8vh"
    >
        <form-builder ref="formBuilder" labelWidth="90px"></form-builder>
        <div class="u-flex u-row-center">
            <el-button type="primary" plain @click="close">取消</el-button>
            <el-button type="primary" class="u-m-l-30" @click="confirm"
                >确定</el-button
            >
        </div>
    </el-dialog>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { DialogController } from "@/core-ui/controller/dialog-controller"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Mixins } from "vue-property-decorator"
    import { getAdminForm } from "./detail"

    @Component({ components: { FormBuilder } })
    export default class CreatePop extends Mixins(
        DialogController,
        FormController
    ) {
        onOpen() {
            this.init()
        }

        private init() {
            return buildFormSections(getAdminForm()).then((r) => {
                this.buildForm(r.forms)
            })
        }

        private confirm() {
            const data = this.getFormValues()
            console.log("d", data)
            this.validateForm((v: boolean) => {
                if (v) {
                    if (!_.isPhone(data.mobile)) {
                        return this.$message.error("请填写正确的手机号！")
                    }
                    this.submit(data)
                }
            })
        }

        private submit(data: any) {
            return pageLoading(() => {
                return sdk.core
                    .model("xg_agent_manager")
                    .action("add_agent_manager")
                    .addInputs_parameter(data)
                    .execute()
                    .then(() => {
                        this.$message.success("添加成功")
                        this.$emit("refresh")
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
</style>
