<template>
    <div v-if="tableColumns && tableColumns.length">
        <table-container
            v-if="tableConfig"
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            @getRows="getRows"
        >
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table
                    :data="getData(data)"
                    :columns="tableColumns"
                    :tableConfig="{
                        'row-class-name': tableRowClassName,
                        stripe: true,
                    }"
                >
                    <div
                        :slot="item.prop"
                        slot-scope="scope"
                        v-for="item in editColumns"
                        :key="item.prop"
                        class="table-column"
                    >
                        <div
                            v-if="
                                item.prop !== 'name' && scope.index && isEditing
                            "
                            class="data-input"
                        >
                            <el-input
                                ref="orderInput"
                                v-model="scope.row[item.prop]"
                                placeholder="请输入"
                            ></el-input>
                            <ErrorMessage
                                v-if="validateInput(scope.row, item)"
                                ref="error-message"
                                >{{ scope.row.errorMsg }}</ErrorMessage
                            >
                        </div>
                        <span v-else> {{ scope.row[item.prop] || "-" }}</span>
                    </div>
                </common-table>
            </div>
        </table-container>
    </div>
    <div v-else class="empty color-9 u-flex u-row-center u-p-30">暂无数据</div>
</template>

<script lang="ts">
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import { Component, Prop } from "vue-property-decorator"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { FillInStatus, Status } from "../../report-manage/"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { routesMap } from "@/router/direction"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { cloneDeep } from "lodash"
    import { MessageBox } from "element-ui"
    import { getShowBtn4List } from "../../collect-task-manage/components/build-table"
    import { ValueType } from "../../report-manage/list/index"
    import ErrorMessage from "../../report-manage/executor/components/error-message.vue"
    import { detailTableConfig } from "../index"

    export interface Row {
        [key: string]: any
        indicator_data: any
        isEdit: boolean
        id: number
        v: number
    }

    @Component({
        components: { CommonTable, TableContainer, ErrorMessage },
    })
    export default class DetailTableMultiple extends BaseTableController<Row> {
        @Prop({ default: () => [] })
        private readonly columns!: TableColumn[]

        @Prop({ default: Status.未开始 })
        private readonly taskStatus!: Status

        @Prop({ default: FillInStatus.待填报 })
        private readonly fillInStatus!: FillInStatus

        @Prop({ default: "" })
        private readonly id!: number

        @Prop({ default: false })
        private readonly isEditing!: boolean

        tableConfig: TableConfig | null = null

        refreshConfig = {
            fun: this.init,
            name: "reportManageDetailTable",
        }

        private checkEdIds: number[] = []

        private tableRows: any[] = []

        private inputsParameters: { property: string }[] = []

        tableData: Row[] = []

        private rows: any[] = []

        private tableColumns: TableColumn[] | any[] = []

        private editRow = ""

        private get isFinish() {
            return this.taskStatus === Status.已完成
        }

        private get editColumns() {
            const flattenObjectArray = (arr: TableColumn[]) => {
                let result = [] as TableColumn[]
                arr.forEach((item) => {
                    result.push(item)
                    if (item.children) {
                        result = result.concat(flattenObjectArray(item.children))
                    }
                })
                return result
            }
            return flattenObjectArray(this.tableColumns)
        }

        created() {
            this.tableConfig = detailTableConfig(this.id + "")
            this.init()
        }

        private init() {
            this.tableColumns = cloneDeep(this.columns)
            if (!this.tableColumns?.length) {
                return
            }
            this.tableColumns.unshift({
                label: "标题",
                prop: "name",
                showOverflowTip: true,
            })
        }

        private tableRowClassName({ rowIndex }: { rowIndex: number }) {
            if (rowIndex === 0) {
                return "description-row"
            }
        }

        // 未填报
        private isNoFillIn(status: FillInStatus) {
            return status < FillInStatus.已完成
        }

        private getData(data: Row[]) {
            const rows = data.map((i) => {
                data = Object.keys(i.indicator_data).reduce((acc: any, key) => {
                    acc[i.indicator_data[key].union_code] =
                        i.indicator_data[key].input_value
                    return acc
                }, {})
                return {
                    ...i,
                    ...data,
                }
            })

            const flattenArray = (arr: TableColumn[]) => {
                const result = [] as any[]
                function flatten(obj: any) {
                    result.push(obj)
                    if (obj.children && obj.children.length > 0) {
                        obj.children.forEach((child: any) => {
                            flatten(child)
                        })
                    }
                }
                arr.forEach((item) => {
                    flatten(item)
                })

                return result
            }
            const description = {} as any
            const simpleColumns = flattenArray(this.tableColumns)
            simpleColumns.forEach((i) => {
                description[i.prop] = i.description || ""
            })
            description.name = "填写说明"
            rows.unshift(description)
            this.tableData = rows
            const exportDescription = cloneDeep(description)
            simpleColumns.forEach((child) => {
                if (child.children?.length) {
                    delete exportDescription[child.prop]
                }
            })
            delete exportDescription.name
            delete exportDescription.h
            this.$emit("description", exportDescription)
            this.$emit("getRows", rows, simpleColumns)
            return rows
        }

        private getRows(rows: { intents: { name: string }[] }[]) {
            this.rows = rows
        }

        private getShowBtn4List(id: string, key: string) {
            return getShowBtn4List(this.rows, id, key)
        }

        private validateInput(row: Row, item: any) {
            if (
                +item.value_type === ValueType.整型 &&
                !/^[0-9]\d*$/.test(row[item.prop]) && row[item.prop]
            ) {
                row.errorMsg = "请输入正整数"
                return true
            } else if (
                +item.value_type === ValueType.浮点型 &&
                !/^\d*\.\d+$/.test(row[item.prop]) &&
                +row[item.prop] !== 0 && row[item.prop]
            ) {
                row.errorMsg = "请输入小数"
                return true
            } else {
                row.errorMsg = ""
            }
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
    /deep/.el-table {
        .description-row .table-column {
            color: red !important;
        }
        .el-table__row .el-table__cell {
            padding: 14px 0;
            position: relative;
            .error-message {
                font-size: 12px;
                position: absolute;
                bottom: -3px;
            }
        }
    }
    .empty {
        font-size: 14px;
    }
</style>
