import { FormType } from "@/core-ui/component/form"
import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"
import { renDesensitizationView } from "@/views/components/common-comps"
import { ListTypes } from "uniplat-sdk"

const tableFilter: TableFilter[] = [
    {
        label: "姓名",
        type: FormType.Text,
        prop: "name",
        keyValueFilter: {
            match: ListTypes.filterMatchType.fuzzy,
        },
    },
    {
        label: "性别",
        type: FormType.Select,
        prop: "sex",
    },
    // {
    //     label: "当前城市",
    //     type: FormType.Select,
    //     prop: "city",
    // },
    // {
    //     label: "年龄",
    //     type: FormType.TextRange,
    //     option: {
    //         type: "number",
    //     },
    //     prop: "age",
    // },
    // {
    //     label: "学历",
    //     type: FormType.Select,
    //     prop: "education",
    // },
    // {
    //     label: "当前状态",
    //     type: FormType.Select,
    //     prop: "age",
    // },
]
export const tableConfig = (created_from_id: string): TableConfig => ({
    model: sdk.core
        .model("xg_candidate_order")
        .list("company_recommend_profile_list"),
    filter: tableFilter,
    defaultPageSize: 10,
    preFilter: { created_from_id },
    predict: {
        name: "",
        sex: "label",
        age: "",
        id_card: "",
        mobile: "",
        mobile_encode: "",
        region_name: "permanent_city#region_name",
        update_time: "",
        contact_status: "label",
        province: "permanent_province#region_name_label",
        city: "permanent_city#region_name_label",
        area: "permanent_area#region_name_label",
        employment_status: "label",
        status: "label",
        education: "label",
        status1: "label",
        profile_id: "profile#id",
        household_address_detail: "label",
        permanent_detail: "label",
        show: false,
    },
    oneTabFilter: true,
})
export const columns: TableColumn[] = [
    {
        label: "姓名",
        prop: "name",
        showOverflowTip: true,
    },
    {
        label: "联系方式",
        prop: "mobile",
        showOverflowTip: true,
        render(h, row) {
            return renDesensitizationView(h, {
                value: row.mobile,
            })
        },
    },
    {
        label: "性别",
        prop: "sex_label",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "年龄",
        prop: "age",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "学历",
        prop: "education",
        minWidth: "80px",
        showOverflowTip: true,
    },
    {
        label: "民族",
        prop: "nation",
        width: "50px",
        showOverflowTip: true,
    },
    {
        label: "户籍城市",
        prop: "household_address_detail",
        showOverflowTip: true,
    },
    {
        label: "常住地址",
        prop: "permanent_detail",
        showOverflowTip: true,
    },
    {
        label: "就业状态",
        prop: "employment_status_label",
        showOverflowTip: true,
    },
    {
        label: "当前状态",
        prop: "status1_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        width: "175px",
        showOverflowTip: true,
    },
]

export interface Row {
    id: number
}
