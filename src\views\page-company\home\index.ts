import { config } from "@/config"
import { DrawerBox } from "@/core-ui/component/drawer-box"
import { FormType } from "@/core-ui/component/form"
import { TableConfig } from "@/core-ui/component/table"
import { PassportTokenController } from "@/core-ui/service/passport/token"
import router from "@/router"
import { routesMap } from "@/router/direction"
import { sdk } from "@/service"
import { userService } from "@/service/service-user"
import { object2UrlParams } from "@/utils/tools"

export const predict = {
    title: "",
    create_time: "label",
    to_model_name: "label",
    to_model_id: "label",
    status: "label",
    content: "",
    msg_type: "",
    from_model_id: "",
    from_model_name: "",
    attach_info: "",
    task_status: "task_serve_record#status",
    task_uid:
        "task_serve_record#questionnaire_record_log#questionnaire_answer#uniplat_uid",
}
export const enum Status {
    未读 = 0,
    已读 = 1,
}
export interface News {
    id: number
    title: string
    create_time: string
    to_model_name: string
    to_model_id: string
    status: Status
    status_label: string
    content: string
    msg_type: string
    from_model_id: string
    from_model_name: string
    attach_info: string
    task_status: number
    task_uid: string
}
export function newsTableConfig(): TableConfig {
    return {
        model: sdk.core.model("agent_msg").list("for_agent"),
        defaultPageSize: 10,
        predict: predict,
        filter: [
            {
                label: "消息状态",
                prop: "status",
                type: FormType.Select,
            },
            {
                label: "内容",
                prop: "title",
                type: FormType.Text,
            },
            {
                label: "发送时间",
                prop: "create_time",
                type: FormType.DatePicker,
                option: {
                    type: "datetimerange",
                },
            },
        ],
    }
}

const recommendRouteMap: Record<string, string> = {
    talent: routesMap.company.recruit.personDetail,
    socialize_group: routesMap.company.recruit.groupDetail,
    grid_user: routesMap.company.recruit.gridDetail,
    agent_bidding: routesMap.company.recruit.cooperationDetail,
    xg_candidate_order: routesMap.company.recruit.jobDeliver,
    share_employee_apply: routesMap.company.shareEmployment.applyDetail,
}

export function readMessage(id: number) {
    return sdk.core
        .model("agent_msg")
        .action("read_msg")
        .updateInitialParams({ selected_list: [{ id, v: 0 }] })
        .execute()
}

export function toNewsDetail(news: News, route: string) {
    let attach_info = {
        questionnaire_id: "",
        task_id: "",
    }
    if (news.msg_type === "questionnaire") {
        try {
            attach_info = JSON.parse(news.attach_info)
        } catch (e) {
            console.log(e)
        }

        if (news.task_status === 10) {
            return userService.setup().then((r) => {
                r?.key &&
                    DrawerBox.open({
                        title: "答题详情",
                        url: `${
                            config.h5
                        }/pages/mp-work/user-list/questionnaire-detail?${object2UrlParams(
                            {
                                questionnaire_id: attach_info.questionnaire_id,
                                uid: news.task_uid,
                                task_id: attach_info.task_id,
                                token: PassportTokenController.hasToken(),
                            }
                        )}`,
                    })
            })
        }

        return userService.setup().then((r) => {
            r?.key &&
                DrawerBox.open({
                    title: "问卷详情",
                    url: `${
                        config.h5
                    }/pages/sub/questionnaire/index?${object2UrlParams({
                        questionnaire_id: attach_info.questionnaire_id,
                        taskId: attach_info.task_id,
                        token: PassportTokenController.hasToken(),
                        agentId: r.key,
                        verify: false,
                    })}`,
                })
        })
    } else if (news.msg_type === "job_fair") {
        return router.push({
            name: routesMap.company.recruit.jobFairDetail,
            query: {
                id: news.from_model_id + "",
                from: routesMap.home.news,
            },
        })
    } else if (news.msg_type === "job_fair_agent_position") {
        return router.push({
            name: routesMap.company.recruit.jobDetail,
            query: {
                id: news.attach_info + "",
                from: routesMap.home.news,
                job_fair_agent_position_id: news.from_model_id + "",
            },
        })
    } else {
        router.push({
            name: recommendRouteMap[news.msg_type],
            query: {
                id: news.from_model_id,
                from: route,
            },
        })
    }
}
