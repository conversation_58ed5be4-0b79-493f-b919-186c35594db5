import { sdk } from "@/service"

export enum Status {
    正常 = 0,
    禁用,
}
export interface Row {
    id: number
    name: string
    mobile: string
    type: string
    status: Status
    create_time: string
    create_type: string
    activation_status: string
}

export const predict = {
    name: "",
    name_hide: "",
    mobile: "",
    mobile_hide: "",
    type: "label",
    status: "label",
    create_time: "label",
    create_type: "label",
    activation_status: "label",
    uniplat_uid: "",
}

export const tableConfig = {
    model: sdk.core.model("xg_agent_manager").list("company_back_list"),
    defaultPageSize: 10,
    predict,
}
export const columns = [
    {
        label: "序号",
        prop: "order",
        showOverflowTip: true,
    },
    {
        label: "姓名",
        prop: "name_hide",
        showOverflowTip: true,
    },
    {
        label: "登录账号",
        prop: "mobile_hide",
        showOverflowTip: true,
    },
    {
        label: "角色",
        prop: "type_label",
        showOverflowTip: true,
    },
    {
        label: "状态",
        prop: "status_label",
        showOverflowTip: true,
    },
    {
        label: "创建时间",
        prop: "create_time_label",
        showOverflowTip: true,
    },
    {
        label: "操作",
        prop: "h",
        showOverflowTip: true,
    },
]
