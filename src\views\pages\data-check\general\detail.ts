import {
    TableColumn,
    TableConfig,
    TableFilter,
} from "@/core-ui/component/table"
import { sdk } from "@/service"

export const enum CheckType {
    查询类 = 0,
    比对类 = 1,
}

export const enum Status {
    待处理 = 0,
    处理中 = 1,
    已完成 = 2,
    暂不支持 = 3,
}

export interface DetailRow {
    /** 是否删除 */
    is_del: number

    /** 创建时间 */
    create_time: string

    /** 更新人 */
    real_name: string

    /** 核查任务名称 */
    name: string

    /** 更新时间 */
    update_time: string

    /** 最后变更时间 */
    final_change_time: string

    /** 核查需求简述 */
    require_note: string

    /** 结果描述 */
    result_note: number

    /** 任务状态 */
    status: Status

    /** 任务状态[文本] */
    status_label: string

    /** 是否有异常数据 */
    is_exception: number

    /** 核查数据文件路径 */
    data_file_path: string
    id: number
    v: number
}

const tableFilter: TableFilter[] = []

export const columns: TableColumn[] = [
    {
        label: "下载时间",
        prop: "下载时间",
        minWidth: "80",
        showOverflowTip: true,
    },
    {
        label: "下载人姓名",
        prop: "下载人姓名",
        minWidth: "120",
        showOverflowTip: true,
    },
    {
        label: "身份证",
        prop: "身份证",
        showOverflowTip: true,
    },
    {
        label: "下载结果",
        prop: "下载结果",
        width: "150",
        showOverflowTip: true,
    },
    {
        label: "说明",
        prop: "说明",
        width: "150",
        showOverflowTip: true,
    },
]

export function tableConfig(): TableConfig {
    return {
        model: sdk.core.model("xg_odd_job").list("manage"),
        filter: tableFilter,
        defaultPageSize: 10,
        predict: serviceTaskRowPredict,
    }
}

export const serviceTaskRowPredict = {}
