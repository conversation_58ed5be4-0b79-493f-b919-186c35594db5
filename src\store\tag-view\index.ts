import { Tag } from "@/views/pages/single-page/components/tags-view"
import { Module } from "vuex"
import { RootStoreState } from "../model"
import { TagViewStore, TagViewStoreState } from "./model"
const visitedViews = JSON.parse(
    sessionStorage.getItem("visitedViews") || "[]"
) as Tag[]
const tagViewStore: Module<TagViewStoreState, RootStoreState> = {
    namespaced: true,
    state: () => ({
        [TagViewStore.STATE_VISITED_VIEWS]: visitedViews,
        [TagViewStore.STATE_CACHED_VIEWS]: [],
    }),
    mutations: {
        ADD_VISITED_VIEW: (state, view: Tag) => {
            if (state.visitedViews.some((v) => v.path === view.path)) return
            state.visitedViews.push(
                Object.assign({}, view, {
                    title: view.meta.title,
                })
            )
        },
        ADD_CACHED_VIEW: (state, view: Tag) => {
            if (state.cachedViews.includes(view.name)) return
            if (!view.meta.noCache) {
                state.cachedViews.push(view.name)
            }
        },
        DEL_VISITED_VIEW: (state, view: Tag) => {
            for (const [i, v] of state.visitedViews.entries()) {
                if (v.path === view.path) {
                    state.visitedViews.splice(i, 1)
                    break
                }
            }
        },
        DEL_CACHED_VIEW: (state, view: Tag) => {
            const index = state.cachedViews.indexOf(view.name)
            index > -1 && state.cachedViews.splice(index, 1)
        },

        DEL_OTHERS_VISITED_VIEWS: (state, view: Tag) => {
            state.visitedViews = state.visitedViews.filter((v) => {
                return v.path === view.path
            })
        },
        DEL_OTHERS_CACHED_VIEWS: (state, view: Tag) => {
            const index = state.cachedViews.indexOf(view.name)
            if (index > -1) {
                state.cachedViews = state.cachedViews.slice(index, index + 1)
            } else {
                state.cachedViews = []
            }
        },

        DEL_ALL_VISITED_VIEWS: (state) => {
            state.visitedViews = []
        },
        DEL_ALL_CACHED_VIEWS: (state) => {
            state.cachedViews = []
        },
        UPDATE_VISITED_VIEW: (state, view: Tag) => {
            for (let v of state.visitedViews) {
                if (view.path && v.path === view.path) {
                    v = Object.assign(v, view)
                    break
                }
                if (view.name && v.name === view.name) {
                    v = Object.assign(v, view)
                    break
                }
            }
        },
    },
    actions: {
        [TagViewStore.ACTION_ADD_VIEW]({ dispatch }, view) {
            dispatch("addVisitedView", view)
            dispatch("addCachedView", view)
        },
        [TagViewStore.ACTION_ADD_VISITED_VIEW]({ commit }, view) {
            commit("ADD_VISITED_VIEW", view)
        },
        [TagViewStore.ACTION_ADD_CACHED_VIEW]({ commit }, view) {
            commit("ADD_CACHED_VIEW", view)
        },
        [TagViewStore.ACTION_DEL_VIEW]({ dispatch, state }, view) {
            dispatch("delVisitedView", view)
            dispatch("delCachedView", view)
            return {
                visitedViews: [...state.visitedViews],
                cachedViews: [...state.cachedViews],
            }
        },
        [TagViewStore.ACTION_DEL_VISITED_VIEW]({ commit, state }, view) {
            return new Promise((resolve) => {
                commit("DEL_VISITED_VIEW", view)
                resolve([...state.visitedViews])
            })
        },
        [TagViewStore.ACTION_DEL_CACHED_VIEW]({ commit, state }, view) {
            return new Promise((resolve) => {
                commit("DEL_CACHED_VIEW", view)
                resolve([...state.cachedViews])
            })
        },

        [TagViewStore.ACTION_DEL_OTHER_VIEWS]({ dispatch, state }, view) {
            return new Promise((resolve) => {
                dispatch("delOthersVisitedViews", view)
                dispatch("delOthersCachedViews", view)
                resolve({
                    visitedViews: [...state.visitedViews],
                    cachedViews: [...state.cachedViews],
                })
            })
        },
        [TagViewStore.ACTION_DEL_OTHERS_VISITED_VIEWS](
            { commit, state },
            view
        ) {
            return new Promise((resolve) => {
                commit("DEL_OTHERS_VISITED_VIEWS", view)
                resolve([...state.visitedViews])
            })
        },
        [TagViewStore.ACTION_DEL_OTHERS_CACHED_VIEWS]({ commit, state }, view) {
            return new Promise((resolve) => {
                commit("DEL_OTHERS_CACHED_VIEWS", view)
                resolve([...state.cachedViews])
            })
        },

        [TagViewStore.ACTION_DEL_ALL_VIEWS]({ dispatch, state }, view) {
            return new Promise((resolve) => {
                dispatch("delAllVisitedViews", view)
                dispatch("delAllCachedViews", view)
                resolve({
                    visitedViews: [...state.visitedViews],
                    cachedViews: [...state.cachedViews],
                })
            })
        },
        [TagViewStore.ACTION_ALL_VISITED_VIEWS]({ commit, state }) {
            return new Promise((resolve) => {
                commit("DEL_ALL_VISITED_VIEWS")
                resolve([...state.visitedViews])
            })
        },
        [TagViewStore.ACTION_DEL_ALL_CACHED_VIEWS]({ commit, state }) {
            return new Promise((resolve) => {
                commit("DEL_ALL_CACHED_VIEWS")
                resolve([...state.cachedViews])
            })
        },

        [TagViewStore.ACTION_UPDATE_VISITED_VIEW]({ commit }, view) {
            commit("UPDATE_VISITED_VIEW", view)
        },
    },
}

export default tagViewStore
