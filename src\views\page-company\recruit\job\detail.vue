<template>
    <div
        class="core-ui-table-container container"
        :key="refreshQueryParams"
        v-if="row"
    >
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex" v-if="row">
                <template
                    v-if="
                        row.status === auditStatus.审核通过 &&
                        row.online_status === onlineStatus.已上架
                    "
                >
                    <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="toApply('applyPerson')"
                        v-if="checkDisplay('1')"
                        :disabled="row.has_recommend_1"
                    >
                        申请人才推荐服务
                    </el-button>
                    <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="toApply('applyCooperation')"
                        v-if="checkDisplay('4')"
                        :disabled="row.has_recommend_4"
                    >
                        申请人力资源机构代招服务
                    </el-button>
                    <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="toApply('applyGroup')"
                        v-if="checkDisplay('2')"
                        :disabled="row.has_recommend_2"
                    >
                        申请社群推广服务
                    </el-button>
                    <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="toApply('applyGrid')"
                        v-if="checkDisplay('3')"
                        :disabled="row.has_recommend_3"
                    >
                        申请网格推广服务
                    </el-button>
                </template>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    plain
                    v-if="
                        !(
                            row.status === auditStatus.审核通过 &&
                            row.online_status === onlineStatus.已上架
                        )
                    "
                    @click="toEdit"
                >
                    编辑
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    plain
                    @click="toCopyAndAdd"
                >
                    复制并创建新岗位
                </el-button>
            </div>
        </div>
        <div class="detail-index shadow">
            <job-detail-view :row="row"></job-detail-view>
            <list-view :id="row.id"></list-view>
            <list-view-2 :id="row.id"></list-view-2>
            <list-view-3 :id="row.id" :detail="row"></list-view-3>
        </div>
        <create-position-pop
            v-if="row"
            :rowId="row && row.id"
            v-model="showEdit"
            @refresh="init"
        ></create-position-pop>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { Component, Vue } from "vue-property-decorator"
    import { AuditStatus, OnlineStatus, predict, Row } from "."
    import JobDetailView from "./components/detail-view.vue"
    import ListView from "./components/list-view.vue"
    import ListView2 from "./components/list-view-2.vue"
    import ListView3 from "./components/list-view-3.vue"
    import { pageLoading } from "@/views/controller"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import CreatePositionPop from "./components/create-position-pop.vue"
    import { PositionRecommendType, sysConfigService } from "@/service/sys-config"

    @Component({
        name: routesMap.company.recruit.jobDetail,
        components: {
            JobDetailView,
            ListView,
            ListView2,
            ListView3,
            CreatePositionPop,
        },
    })
    export default class JobDetail extends Vue {
        private routesMap = routesMap.company.recruit
        private row: Row | null = null
        private id = ""
        private showEdit = false
        private onlineStatus = OnlineStatus
        private auditStatus = AuditStatus
        breadcrumbs: BreadcrumbItem[] = []
        private recommendMapping4KeyArr: PositionRecommendType[] = []
        private setBreadcrumbs() {
            const last = this.from
                ? getCacheBreadcrumbsByRoutePath(this.from)
                : [
                      {
                          label: "招聘岗位列表",
                          to: routesMap.company.recruit.job,
                      },
                  ]
            const d: BreadcrumbItem[] = [
                ...last,
                {
                    label: "招聘岗位详情",
                    to: {
                        name: routesMap.company.recruit.jobDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.company.recruit.jobDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string | undefined
        }

        private get job_fair_agent_position_id() {
            return this.$route.query.job_fair_agent_position_id as string
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.jobDetail,
        }

        mounted() {
            this.init()
            this.getRecommendMapping()
        }

        private checkDisplay(type: PositionRecommendType) {
            return this.recommendMapping4KeyArr.includes(type)
        }

        private async getRecommendMapping() {
            const positionRecommendMapping = await sysConfigService.data
                .position_recommend_type_mapping

            this.recommendMapping4KeyArr = positionRecommendMapping.map(
                (i) => i.key
            )
        }

        private init() {
            this.id = this.$route.query.id as string
            this.row = null
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("xg_company_position")
                    .detail(this.id, "company_detail")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                        this.getJobStatus()
                        console.log(JSON.parse(JSON.stringify(this.row)))
                    })
            })
        }

        private getJobStatus() {
            if (!this.job_fair_agent_position_id) return Promise.resolve({})
            return sdk.core
                .model("job_fair_agent_position")
                .detail(this.job_fair_agent_position_id, "for_agent")
                .query()
                .then((r) => {
                    const o = sdk.buildRow(r.row, {
                        audit_memo: "label",
                        audit_status: "label",
                    }) as any
                    this.row = {
                        ...this.row,
                        job_fair_audit_memo: o.audit_memo,
                        job_fair_audit_status: o.audit_status_label,
                    } as Row
                })
        }

        private toApply(v: string) {
            this.$router.push({
                name: (this.routesMap as any)[v],
                query: { id: this.row!.id + "", from: "job-detail" },
            })
        }

        private toEdit() {
            this.$router.push({
                name: routesMap.company.recruit.createJob,
                query: {
                    rowId: this.row!._access_key + "",
                    from: routesMap.company.recruit.jobDetail,
                    isAudit:
                        this.row!.status === this.auditStatus.审核通过 ? "1" : "0",
                },
            })
        }

        private toCopyAndAdd() {
            this.$router.push({
                name: routesMap.company.recruit.createJob,
                query: {
                    copyId: this.row!._access_key + "",
                    from: routesMap.company.recruit.jobDetail,
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .back-btn {
        min-width: 120px;
        height: 40px;
        padding: 0 20px !important;
    }
</style>
