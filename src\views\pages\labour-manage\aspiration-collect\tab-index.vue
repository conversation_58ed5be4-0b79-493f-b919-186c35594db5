<template>
    <div class="core-ui-table-container container">
        <div class="core-ui-custom-header">
            <div class="title">培训意愿收集</div>
            <el-button type="primary" @click="toExport">
                导出
            </el-button>
        </div>
        <el-tabs v-model="cur">
            <el-tab-pane label="培训意愿收集" name="1">
                <TrainList ref="trainList" />
            </el-tab-pane>
            <el-tab-pane label="创业意愿收集" name="2" lazy>
                <EntreList ref="entreList" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { Component } from "vue-property-decorator"
    import { Row } from "."
    import TrainList from "./train-index.vue"
    import EntreList from "./entre-index.vue"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "../../single-page/components/tags-view"

    @Component({
        name: routesMap.labourManage.aspirationCollect,
        components: { TableContainer, CommonTable, TrainList, EntreList },
    })
    export default class AspirationCollect extends BaseTableController<Row> {
        refreshConfig = {
            fun: this.refreshList,
            name: routesMap.labourManage.aspirationCollect,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "培训意愿收集",
                    to: {
                        name: routesMap.labourManage.aspirationCollect,
                        query: this.$route.query,
                    },
                },
            ]
            if (this.from) {
                d.unshift(...getCacheBreadcrumbsByRoutePath(this.from))
            }
            updateTagItem({
                name: routesMap.labourManage.aspirationCollect,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        private cur = "1"

        mounted() {
            this.setBreadcrumbs()
        }

        private toExport() {
            if (this.cur === "1") {
                ;(this.$refs.trainList as any).exportToExcel()
            } else {
                ;(this.$refs.entreList as any).exportToExcel()
            }
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";

    .container {
        background: #f6f7f9;
        ::v-deep.table {
            background: transparent;
            padding: 0;
        }
        .batch-btn {
            margin-left: 20px;
        }
    }
    .batch-btn {
        width: 120px;
        height: 40px;
        font-size: 14px;
    }
</style>
