<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            :alwaysShowPageIndex="false"
        >
            <div slot="title" class="d-flex-item-center bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div slot="header-right" class="u-flex">
                <div class="date-picker u-m-r-20 u-flex">
                    <el-date-picker
                        v-model="yearValue"
                        type="year"
                        range-separator="~"
                        start-placeholder="-"
                        end-placeholder="-"
                        value-format="yyyy"
                        @change="change"
                    >
                    </el-date-picker>
                </div>
                <el-button type="primary" @click="add" plain>
                    新建就业指标监测任务
                </el-button>
            </div>
            <div
                slot="table"
                slot-scope="{ data, currentPageName }"
                class="u-p-20 bg-white"
            >
                <div class="item-table">
                    <div v-for="item in data" :key="item.id" class="item">
                        <div class="item-title u-line-1">{{ item.title }}</div>
                        <div class="u-p-x-20 content w-100">
                            <div class="top u-flex w-100 u-col-center">
                                <div class="label">总体情况：</div>
                                <div
                                    class="u-flex u-flex-1 u-row-around u-col-top u-m-t-16"
                                >
                                    <div
                                        v-for="d in computeRows(
                                            currentPageName,
                                            item
                                        )"
                                        :key="d.key"
                                    >
                                        <div
                                            class="num"
                                            :class="{
                                                un: item.status === 1,
                                            }"
                                        >
                                            {{ d.value }}
                                        </div>
                                        <div class="num-text">{{ d.text }}</div>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="center u-flex u-col-top"
                                v-if="
                                    ['就业综合指数', '营商环境考核'].includes(
                                        currentPageName
                                    ) || !currentPageName
                                "
                            >
                                <div class="label">计分办法：</div>
                                <div class="info">
                                    {{ item.calc_score_method || "--" }}
                                </div>
                            </div>
                            <div class="center u-m-t-12 u-flex u-col-top">
                                <div class="label">数据来源：</div>
                                <div class="info">
                                    {{ item.data_source || "--" }}
                                </div>
                            </div>
                        </div>
                        <div
                            class="bottom"
                            @click="toDetail(item, currentPageName)"
                        >
                            查看详情
                        </div>
                    </div>
                    <div v-if="!data.length" class="color-8">暂无数据</div>
                </div>
            </div>
        </table-container>
        <common-pop
            v-model="showPop"
            title="设置状态"
            sdkModel="policy_info"
            sdkAction="set_status"
            :id="row && row.id"
            @refresh="reloadList"
        ></common-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { FormType } from "@/core-ui/component/form"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { pageLoading } from "@/views/controller"
    import moment from "moment"
    import { uuid } from "uniplat-sdk/build/main/helpers/uuid"
    import { Component } from "vue-property-decorator"
    import { buildConfig4RemoteMeta } from "../../collect-task-manage/components/build-table"
    import { updateTagItem } from "../../single-page/components/tags-view"

    @Component({
        name: routesMap.employmentTask.taskList,
        components: { TableContainer, CommonTable, CommonPop },
    })
    export default class Template extends BaseTableController<any> {
        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []
        private yearValue = moment().get("year") + ""
        private uuid = uuid

        refreshConfig = {
            fun: this.reloadList,
            name: routesMap.employmentTask.taskList,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "就业指标管理任务列表",
                    to: {
                        name: routesMap.employmentTask.taskList,
                    },
                },
            ]
            updateTagItem({
                name: routesMap.employmentTask.taskList,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        add() {
            this.$router.push({
                name: routesMap.employmentTask.addTask,
                query: { from: this.$route.name },
            })
        }

        toDetail(row: any, currentPageName: string) {
            this.$router.push({
                name: routesMap.employmentTask.taskDetail,
                query: {
                    id: row._access_key + "",
                    from: this.$route.name,
                    currentPageName,
                },
            })
        }

        private computeRows(currentPageName: string, item: any) {
            if (currentPageName === "就业综合指数" || !currentPageName) {
                return [
                    {
                        text: "当前得分",
                        value: `${item.calc_score || 0}分`,
                        key: this.uuid(),
                    },
                    {
                        text: item.field_name,
                        value: item.field_value || 0,
                        key: this.uuid(),
                    },
                ]
            } else if (currentPageName === "营商环境考核") {
                return [
                    {
                        text: item.field_name_label,
                        value: item.field_value,
                        key: this.uuid(),
                    },
                    {
                        text: item.field_name2_label,
                        value: item.field_value2,
                        key: this.uuid(),
                    },
                    {
                        text: item.field_name3_label,
                        value: item.field_value3,
                        key: this.uuid(),
                    },
                ]
            } else if (
                currentPageName === "人社事业发展计划" ||
                currentPageName === "湖北省2024年十大民生项目"
            ) {
                return [
                    {
                        text: item.field_name2_label,
                        value: item.field_value2,
                        key: this.uuid(),
                    },
                    {
                        text: item.field_name3_label,
                        value: item.field_value3,
                        key: this.uuid(),
                    },
                ]
            }

            return []
        }

        private showPop = false
        setStatus(row: any) {
            this.row = row
            this.showPop = true
        }

        mounted() {
            this.init()
        }

        private init() {
            this.setBreadcrumbs()
            pageLoading(() => {
                return buildConfig4RemoteMeta(
                    "employment_target_task",
                    "list_operate",
                    {
                        disabledOpt: false,
                        disabledFilter: true,
                        useLabelWidth: true,
                        optColumn: {
                            label: "操作",
                            prop: "h",
                            fixed: "right",
                            minWidth: "120px",
                        },
                        customLabelWidths: {
                            任务名称: 270,
                            所属区域: 360,
                            采集模板: 170,
                            任务起止时间: 200,
                        },
                        useTabs: true,
                        useRowFieldGroups: true,
                    }
                ).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private change() {
            this.reloadList()
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            tableConfig.filter = [
                {
                    label: "年份",
                    prop: "indicator_year",
                    type: FormType.TextRange,
                },
            ]
            tableConfig.handleFilterData = () => {
                return {
                    indicator_year: { max: this.yearValue, min: this.yearValue },
                }
            }
            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
                calc_full_score: "label",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = r.columns
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    /deep/.table {
        padding: 0;
        background: transparent;
    }
    .date-picker {
        height: 40px;
        /deep/ .el-date-editor {
            width: 100px !important;
            .el-input__icon {
                color: #077aec !important;
            }
            .el-input__inner {
                border-color: #077aec;
                color: #077aec !important;
                line-height: 36px !important;
                height: 36px !important;
            }
        }
    }
    /deep/ .filter-container-out {
        display: none;
    }
    .item-table {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        .item {
            height: 370px;
            border-radius: 6px;
            border: 1px solid #c7d0d9;
            overflow: hidden;
            position: relative;
            padding-bottom: 50px;
            .item-title {
                padding: 0 20px;
                line-height: 50px;
                font-size: 16px;
                background: #ecf2ff;
                color: #154cc0;
                font-weight: 600;
            }
            .label {
                line-height: 22px;
                font-size: 12px;
                color: #222;
                flex: none;
            }
            .info {
                color: #555;
                line-height: 22px;
                font-size: 12px;
            }
            .num {
                font-weight: 600;
                font-size: 20px;
                color: #00a25c;
                line-height: 20px;
                margin-bottom: 10px;
                text-align: center;
                &.un {
                    color: #f05246;
                }
            }
            .num-text {
                color: #7998b8;
                font-size: 12px;
                text-align: center;
                max-width: 100px;
                margin: auto;
                line-height: 1.2;
            }
            .content {
                overflow: scroll;
                height: 270px;
            }
            .top {
                height: 83px;
            }
            .bottom {
                height: 49px;
                border-top: 1px solid #f0f0f0;
                text-align: center;
                line-height: 49px;
                cursor: pointer;
                color: #5782ec;
                font-size: 14px;
                position: absolute;
                bottom: 0;
                width: 100%;
                background: #fff;
                z-index: 1;
            }
        }
    }
</style>
