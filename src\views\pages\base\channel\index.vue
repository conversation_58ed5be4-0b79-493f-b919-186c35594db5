<template>
    <div>
        <table-container
            v-if="tableConfig"
            showTableFilter
            filedWidth="200"
            ref="table"
            v-model="tableConfig"
            class="container"
            :useTab="true"
            :displayLeftTree="displayLeftTree"
        >
            <div slot="title" class="d-flex-item-center bold">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>

            <div slot="header-right">
                <el-button
                    type="primary"
                    @click="showDownload = true"
                    plain
                    v-role="['model.reg_channel_manage.action.download_qr']"
                >
                    下载二维码
                </el-button>
                <el-button
                    type="primary"
                    @click="showPop = true"
                    plain
                    v-role="['model.reg_channel_manage.action.insert_kv']"
                >
                    创建渠道
                </el-button>
            </div>
            <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                <common-table :data="data" :columns="columns">
                    <div
                        slot="状态"
                        class="u-flex u-row-center"
                        slot-scope="scope"
                    >
                        <el-switch
                            :value="scope.row.status"
                            :active-value="1"
                            :inactive-value="0"
                            @change="(e) => changeStatus(e, scope.row)"
                        ></el-switch>
                    </div>
                    <div slot="二维码" class="u-flex" slot-scope="scope">
                        <div v-if="scope.row.url">
                            <el-image
                                :src="createImg(scope.row.url)"
                                class="img"
                                :preview-src-list="[createImg(scope.row.url)]"
                            />
                        </div>
                        <el-button
                            type="text"
                            v-else
                            @click="createQr(scope.row)"
                        >
                            生成二维码
                        </el-button>
                    </div>
                    <div slot="h" slot-scope="scope">
                        <div>
                            <el-button type="text" @click="add2(scope.row)">
                                添加下级渠道
                            </el-button>
                            <el-button
                                type="text"
                                @click="showMoveF(scope.row)"
                            >
                                数据迁移
                            </el-button>
                        </div>
                        <div>
                            <el-button type="text" @click="edit(scope.row)">
                                编辑
                            </el-button>
                            <el-button type="text" @click="del(scope.row)">
                                删除
                            </el-button>
                        </div>
                    </div>
                </common-table>
            </div>
        </table-container>
        <common-pop
            v-model="showPop"
            title="创建渠道"
            sdkModel="reg_channel_manage"
            sdkAction="insert_kv"
            @refresh="reload"
        ></common-pop>
        <common-pop
            v-model="showPop2"
            title="编辑渠道"
            sdkModel="reg_channel_manage"
            sdkAction="update_kv"
            :id="rId"
            @refresh="reload"
        ></common-pop>
        <common-pop
            v-model="showPop3"
            title="添加下级渠道"
            sdkModel="reg_channel_manage"
            sdkAction="insert_children_kv"
            :id="rId"
            @refresh="reload"
        ></common-pop>
        <common-pop
            v-model="showDownload"
            title="下载二维码"
            sdkModel="reg_channel_manage"
            sdkAction="download_qr"
            @refresh="dQr"
        ></common-pop>
        <common-pop
            v-model="showMove"
            title="数据迁移"
            sdkModel="reg_channel_manage"
            sdkAction="data_migration"
            :id="rId"
            @refresh="reload"
        ></common-pop>
    </div>
</template>

<script lang='ts'>
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { buildConfig4RemoteMeta } from "@/views/common/list/build-table"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import CommonPop from "@/views/components/common-pop/index.vue"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { get } from "lodash"
    import { Component } from "vue-property-decorator"
    import { updateTagItem } from "../../single-page/components/tags-view"

    @Component({
        name: routesMap.base.channel.index,
        components: { TableContainer, CommonTable, CommonPop },
    })
    export default class Template extends BaseTableController<any> {
        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []
        rId = ""
        private displayLeftTree = true
        showDownload = false
        showMove = false

        refreshConfig = {
            fun: this.reload,
            name: routesMap.base.channel.index,
        }

        reload() {
            this.displayLeftTree = false
            this.$nextTick(() => {
                this.displayLeftTree = true
            })
            this.reloadList()
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                {
                    label: "注册渠道管理",
                },
            ]
            updateTagItem({
                name: routesMap.base.channel.index,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string
        }

        add() {
            this.$router.push({
                name: routesMap.policyDataBase.add,
                query: { from: this.$route.name },
            })
        }

        toDetail(row: any) {
            this.$router.push({
                name: routesMap.policyDataBase.detail,
                query: { id: row._access_key + "", from: this.$route.name },
            })
        }

        showPop = false
        showPop2 = false
        showPop3 = false

        mounted() {
            this.init()
        }

        private init() {
            this.setBreadcrumbs()
            pageLoading(() => {
                return buildConfig4RemoteMeta("reg_channel_manage", "manager", {
                    disabledOpt: true,
                    useRowFieldGroups: true,
                }).then((r) => {
                    this.buildConfig(r)
                })
            })
        }

        private createImg(v: string) {
            return sdk.buildImage(v)
        }

        private buildConfig(r: any) {
            const tableConfig: any = r.tableConfig

            tableConfig.predict = {
                ...r.tableConfig.predict,
                actions: "actions",
                status: "label",
                url: "",
            }
            this.tableConfig = null

            this.$nextTick(() => {
                this.tableConfig = tableConfig
            })

            this.columns = [
                ...r.columns,
                {
                    label: "操作",
                    prop: "h",
                    width: 220,
                    fixed: "right",
                },
            ]
        }

        changeStatus(v: string, row: any) {
            pageLoading(() => {
                return sdk.core
                    .model("reg_channel_manage")
                    .action(v ? "enable" : "forbidden")
                    .updateInitialParams({ selected_list: [{ id: row.id, v: 0 }] })
                    .execute()
                    .then(() => {
                        return this.reload()
                    })
            })
        }

        edit(row: any) {
            this.rId = row.id
            this.showPop2 = true
        }

        add2(row: any) {
            this.rId = row.id
            this.showPop3 = true
        }

        del(row: any) {
            MessageBox.confirm("是否确定删除", "删除").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("reg_channel_manage")
                        .action("delete_kv")
                        .updateInitialParams({
                            selected_list: [{ id: row.id, v: 0 }],
                        })
                        .execute()
                        .then(() => {
                            return this.reload()
                        })
                })
            })
        }

        showMoveF(row: any) {
            this.rId = row.id
            this.showMove = true
        }

        dQr(r: any) {
            console.log("r", JSON.parse(JSON.stringify(r)))
            const url = get(r, "data.file_url") || ""
            if (url) {
                window.open(url, "__blank")
            }
        }

        createQr(row: any) {
            pageLoading(() => {
                return sdk.core
                    .model("reg_channel_manage")
                    .action("update_qr_code")
                    .updateInitialParams({ selected_list: [{ id: row.id, v: 0 }] })
                    .execute()
                    .then(() => {
                        return this.reload()
                    })
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/common-table.less";
    .img {
        width: 80px;
        height: 80px;
    }
</style>
