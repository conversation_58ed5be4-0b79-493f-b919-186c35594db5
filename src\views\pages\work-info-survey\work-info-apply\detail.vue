<template>
    <div class="core-ui-table-container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    type="primary"
                    @click="action('push')"
                    class="custom-btn back-btn"
                    v-if="isWait"
                >
                    发布
                </el-button>
                <el-button
                    type="primary"
                    @click="action('end')"
                    class="custom-btn back-btn"
                    v-if="isAlready"
                >
                    结束
                </el-button>
            </div>
        </div>
        <div>
            <detail-view
                ref="view"
                :row="row"
                @refresh="refreshList"
            ></detail-view>
            <statics :row="row" :less="true" />
            <div class="item-title u-flex u-row-between" v-if="row">
                用工上报情况
                <el-button
                    type="primary"
                    class="custom-btn batch-btn"
                    @click="toExport"
                >
                    导出
                </el-button>
            </div>
            <list-view :detail="row" v-if="row" ref="listView" />
            <div class="item-title" v-if="row">操作记录</div>
            <list-view-2 :detail="row" v-if="row" ref="listView" />
        </div>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { pageLoading } from "@/views/controller"
    import { Component, Vue } from "vue-property-decorator"
    import { detailPredict, DetailRow, Status } from "./detail"
    import DetailView from "./components/detail-view.vue"
    import { updateTagItem } from "@/views/pages/single-page/components/tags-view"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Statics from "@/views/zq-operate/employment-manage/hr-info-manage/work-info-apply/components/statics.vue"
    import { MessageBox } from "element-ui"
    import ListView from "./components/list-view.vue"
    import ListView2 from "./components/list-view-2.vue"
    import { TableColumn } from "@/core-ui/component/table"
    import { ExcelColumnsGenerator } from "../../report-manage/excel-columns-generator"

    @Component({
        name: routesMap.employmentManage.hrInfoManage.workInfoApplyDetail,
        components: { DetailView, Statics, ListView, ListView2 },
    })
    export default class WorkInfoApplyDetail extends Vue {
        private row: DetailRow | null = null
        private id = ""
        refreshConfig = {
            fun: this.init,
            name: routesMap.employmentManage.hrInfoManage.workInfoApplyDetail,
        }

        private recordViewStatus = []
        private tabActionName = ""
        private listViewStatus = []

        breadcrumbs: BreadcrumbItem[] = []
        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `企业用工信息填报`,
                    to: {
                        name: routesMap.employmentManage.hrInfoManage.workInfoApply,
                    },
                },
            ]
            d = [
                ...d,
                {
                    label: "任务详情",
                    to: {
                        name: routesMap.employmentManage.hrInfoManage
                            .workInfoApplyDetail,
                        query: {
                            id: this.$route.query.id || this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.employmentManage.hrInfoManage.workInfoApplyDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        mounted() {
            this.init()
        }

        private get isWait() {
            return this.row && this.row.status === Status.待发布
        }

        private get isAlready() {
            return this.row && this.row.status === Status.已发布
        }

        private init() {
            if (this.$route.query.id) {
                this.id = this.$route.query.id as string
            }
            if (!this.id) return
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("company_task")
                    .detail(this.id, "report")
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, detailPredict)
                    })
            })
        }

        private action(action: string) {
            MessageBox.confirm(
                `${action === "push" ? "确认发布该任务？" : "确认结束该任务？"}`,
                "提示"
            ).then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("company_task")
                        .action(action)
                        .updateInitialParams({
                            selected_list: [
                                { v: this.row?.v || 0, id: this.row?.id || "" },
                            ],
                        })
                        .execute()
                        .then(() => {
                            this.callRefresh(
                                routesMap.employmentManage.hrInfoManage
                                    .workInfoApplyDetail
                            )
                        })
                })
            })
        }

        private refreshList() {
            this.callRefresh(
                routesMap.employmentManage.hrInfoManage.workInfoApplyDetail
            )
        }

        private getColumns() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_report_indicator_for_task"
                )
                .post<{ schema: any[] }>({
                    task_id: this.row?.id || "",
                })
        }

        private getData() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "company_task_api",
                    "get_task_record_report_import"
                )
                .post<{ schema: any[] }>({
                    task_id: this.row?.id,
                    page_index: 1,
                    page_size: 9999,
                })
        }

        private toExport() {
            pageLoading(() => {
                return Promise.all([this.getColumns(), this.getData()]).then(
                    (r: any) => {
                        const columns = r[0].schema
                        const data = r[1].reports

                        // 获取表格表头
                        const columnsLabel = [] as any[]
                        columns.forEach((item: any) => {
                            columnsLabel.push(item.display_name)
                            if (item.children?.length > 0) {
                                for (let i = 0; i < item.children.length - 1; i++) {
                                    columnsLabel.push("")
                                }
                            }
                        })

                        const childrenColumnsLabel = [] as any[]
                        columns.forEach((item: any) => {
                            if (item.children?.length === 0) {
                                childrenColumnsLabel.push("")
                            } else {
                                item.children?.forEach((child: any) => {
                                    childrenColumnsLabel.push(child.display_name)
                                })
                            }
                        })
                        // childrenColumnsLabel.unshift("")
                        const isEmptyString = (str: string) => {
                            return str === ""
                        }
                        if (childrenColumnsLabel.every(isEmptyString)) {
                            childrenColumnsLabel.length = 0 // 将数组置为空
                        }

                        const rows = data.map((item: any) => {
                            return item.report.reduce((acc: any, cur: any) => {
                                acc[cur.p_union_code] = cur.indicator_value
                                return acc
                            }, {})
                        })
                        //  [
                        //     data.reduce((acc: any, cur: any) => {
                        //         acc[cur.p_union_code] = cur.indicator_value
                        //         return acc
                        //     }, {}),
                        // ]

                        const tableColumns = columns?.map((i: any) => {
                            return {
                                label: i.display_name,
                                prop: i.p_union_code,
                                value_type: i.value_type,
                                showOverflowTip: true,
                                children:
                                    i.children?.map((j: any) => {
                                        return {
                                            label: j.display_name,
                                            prop: j.p_union_code,
                                            showOverflowTip: true,
                                            children: j.children || [],
                                        }
                                    }) || [],
                            }
                        })

                        // 将多级表头平铺成一级
                        const flattenArray = (arr: TableColumn[]) => {
                            const result = [] as any[]
                            function flatten(obj: any) {
                                result.push(obj)
                                if (obj.children && obj.children.length > 0) {
                                    obj.children.forEach((child: any) => {
                                        flatten(child)
                                    })
                                }
                            }
                            arr.forEach((item) => {
                                flatten(item)
                            })

                            return result
                        }
                        const flattenColumns = flattenArray(tableColumns).filter(
                            (i) => !i.children.length
                        )

                        const exportRows = rows.map((targetItem: any) => {
                            const values = [] as any[] // 将 name 值放在最前面
                            flattenColumns.forEach((sourceItem) => {
                                values.push(targetItem[sourceItem.prop] || "") // 提取其他属性值
                            })
                            return values // 返回组成的新数组
                        })
                        ExcelColumnsGenerator.execute({
                            primaryRows: [],
                            columns: columnsLabel,
                            childColumns: childrenColumnsLabel,
                            rows: exportRows as any[],
                            fileName: this.row?.title || "用工上报情况",
                            withoutDescription: true,
                        })
                    }
                )
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    @import "~@/css/table-container.less";
    .back-btn {
        width: 120px;
        height: 40px;
    }
    .batch-btn {
        width: 110px;
        height: 30px;
    }
    .item-title {
        font-weight: 600;
        color: #222;
        font-size: 18px;
        line-height: 18px;
        margin: 20px 0;
    }
</style>
