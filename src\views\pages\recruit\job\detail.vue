<template>
    <div
        class="core-ui-table-container container"
        :key="refreshQueryParams"
        v-if="row"
    >
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex">
                <el-button
                    type="primary"
                    plain
                    class="custom-btn back-btn"
                    @click="drop(row)"
                    v-role="[
                        'model.xg_company_position.action.online',
                        'model.xg_company_position.action.offline',
                    ]"
                    v-if="row.status === auditStatus.审核通过"
                >
                    {{
                        row.online_status === onlineStatus.已上架 ? "下" : "上"
                    }}架
                </el-button>
                <!-- <el-button
                    type="primary"
                    plain
                    class="custom-btn back-btn"
                    @click="copy"
                >
                    复制小程序地址
                </el-button> -->
                <el-button
                    type="primary"
                    @click="openRecommend"
                    class="custom-btn back-btn"
                    v-role="[
                        'domain.xg_project.service.back_api.recommend_profile_list_all',
                    ]"
                >
                    人才推荐
                </el-button>
                <el-button
                    type="primary"
                    @click="toEdit"
                    class="custom-btn back-btn"
                    v-if="
                        (row.status === auditStatus.待审核 ||
                            row.online_status !== onlineStatus.已上架) &&
                        !isOverdue
                    "
                    v-role="[
                        'model.xg_company_position.action.update_position',
                    ]"
                >
                    编辑岗位
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    v-if="row.status === auditStatus.待审核"
                    @click="toggleshowAuditPop(row)"
                    v-role="['model.xg_company_position.action.audit']"
                >
                    审核
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    v-if="
                        row.online_status === onlineStatus.已上架 &&
                        row.status === auditStatus.审核通过
                    "
                    v-role="[
                        'model.xg_company_position.action.recommend_apply',
                    ]"
                    @click="togglePopularizePop(row)"
                >
                    申请招聘服务
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    @click="showTagPop = true"
                    v-role="['model.xg_company_position.action.set_tags']"
                >
                    设置标签
                </el-button>
                <el-button
                    type="primary"
                    @click="toAudit(row)"
                    v-if="
                        row.status === auditStatus.草稿 &&
                        row.status !== auditStatus.待审核
                    "
                    v-role="['model.xg_company_position.action.submit_audit']"
                >
                    提交审核
                </el-button>
                <el-button
                    type="primary"
                    @click="toCopyAndAdd"
                    class="custom-btn back-btn"
                >
                    复制并创建新岗位
                </el-button>
            </div>
        </div>
        <template>
            <job-detail-view :row="row"></job-detail-view>
            <div class="u-p-t-20">
                <el-tabs :value="cur">
                    <el-tab-pane label="投递记录" name="1">
                        <list-view2
                            :id="row.id"
                            v-if="!unReviewed"
                        ></list-view2>
                    </el-tab-pane>
                    <el-tab-pane label="推广记录" name="2" lazy v-if="!isEZ">
                        <list-view :id="row.id" v-if="!unReviewed"></list-view>
                    </el-tab-pane>
                    <el-tab-pane label="操作记录" name="3" lazy>
                        <list-view3
                            :id="row.id"
                            v-if="!unReviewed"
                        ></list-view3>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </template>
        <audit-pop
            v-model="showAuditPop"
            :showTime="false"
            title="岗位审核"
            :row="row"
            @refresh="refresh"
        ></audit-pop>
        <popularize-pop
            v-model="showPopularizePop"
            :rowId="row && row._access_key"
            :name="row && row.name"
            @refresh="refresh"
        ></popularize-pop>
        <online-pop
            v-model="showOnlinePop"
            :row="row"
            @refresh="refresh"
        ></online-pop>
        <set-tag-dialog
            v-model="showTagPop"
            :row="row"
            @refresh="refresh"
        ></set-tag-dialog>
        <recommend-pop
            v-model="showRecommend"
            :row="row"
            :inPositionDetail="true"
            @refresh="refresh"
        ></recommend-pop>
    </div>
</template>

<script lang='ts'>
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import { MessageBox } from "element-ui"
    import { Component, Vue } from "vue-property-decorator"
    import { AuditStatus, OnlineStatus, predict, Row } from "."
    import AuditPop from "./components/audit-position-pop.vue"
    import JobDetailView from "./components/detail-view.vue"
    import ListView from "./components/list-view.vue"
    import ListView2 from "./components/list-view2.vue"
    import ListView3 from "./components/list-view3.vue"
    import PopularizePop from "./components/popularize-pop.vue"
    import RecommendPop from "./components/recommend-pop.vue"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import OnlinePop from "./components/online-pop.vue"
    import SetTagDialog from "./components/set-tag.vue"
    import { copyTextToClipboard } from "uniplat-sdk/build/main/helpers/clipboard"
    import { config, EnvProject } from "@/config"

    @Component({
        name: routesMap.recruit.jobDetail,
        components: {
            JobDetailView,
            ListView,
            ListView2,
            ListView3,
            AuditPop,
            PopularizePop,
            OnlinePop,
            SetTagDialog,
            RecommendPop,
        },
    })
    export default class JobDetail extends Vue {
        private row: Row | null = null
        private id = ""
        private onlineStatus = OnlineStatus
        private auditStatus = AuditStatus
        private showOnlinePop = false
        private showTagPop = false
        private showRecommend = false
        private cur = "1"
        private isEZ = config.envProject === EnvProject.鄂州项目
        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.jobDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const fromBreadcrumbs = getCacheBreadcrumbsByRoutePath(
                this.$route.query.from as string
            )
            const from = fromBreadcrumbs.length
                ? [...fromBreadcrumbs]
                : [
                      {
                          label: "招聘岗位列表",
                          to: routesMap.recruit.job,
                      },
                  ]
            const d: BreadcrumbItem[] = [
                ...from,
                {
                    label: "招聘岗位详情",
                    to: {
                        name: routesMap.recruit.jobDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.jobDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        mounted() {
            return this.init()
        }

        refresh() {
            this.init()
            this.callRefresh(routesMap.recruit.job)
        }

        private get unReviewed() {
            return this.row?.status === 0
        }

        private get isDd() {
            return [EnvProject.掇刀项目].includes(config.envProject)
        }

        private get isOverdue() {
            if (!this.row) return
            const expiryDate = new Date(this.row.expired_date_label + "T00:00:00")
            const today = new Date()
            today.setHours(0, 0, 0, 0)
            return new Date() > expiryDate
        }

        private toAudit(row: Row) {
            MessageBox.confirm("是否将该岗位提交审核？", "提交审核").then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_company_position")
                        .action("submit_audit")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row._access_key }],
                        })
                        .execute()
                        .then(() => {
                            this.init()
                        })
                })
            })
        }

        private init() {
            const id = (this.$route.query.id as string) || this.id
            if (!id) return
            this.id = id
            this.setBreadcrumbs()
            this.row = null
            pageLoading(() => {
                return sdk.core
                    .model("xg_company_position")
                    .detail(this.id)
                    .query()
                    .then((r) => {
                        this.row = sdk.buildRow(r.row, predict)
                    })
            })
        }

        private drop(row: Row) {
            const isDrop = row.online_status === this.onlineStatus.已上架
            if (!isDrop) {
                const isDateExpired = (date: string) => {
                    if (!date) return false
                    const inputDate = new Date(date)
                    const today = new Date()
                    today.setHours(0, 0, 0, 0)
                    inputDate.setHours(0, 0, 0, 0)
                    return inputDate.getTime() < today.getTime()
                }
                if (isDateExpired(row.expired_date_label)) {
                    return MessageBox.confirm(
                        "招聘已过期请重新创建岗位。",
                        "提示",
                        {
                            confirmButtonText: "复制并创建新岗位",
                        }
                    ).then(() => {
                        this.toCopyAndAdd()
                    })
                }
                this.$nextTick(() => {
                    this.showOnlinePop = true
                })
                return
            }
            const label = isDrop ? "下架" : "上架"
            const msg = [
                `岗位名称：${row.name}`,
                isDrop
                    ? "下架后用户将看不到此岗位，确认从个人端下架此岗位吗？"
                    : "上架后用户可以看到此岗位，确认上架到个人端吗？",
            ]
            MessageBox.confirm(msg.join("\n"), label).then(() => {
                pageLoading(() => {
                    return sdk.core
                        .model("xg_company_position")
                        .action(isDrop ? "offline" : "online")
                        .updateInitialParams({
                            selected_list: [{ v: 0, id: row._access_key }],
                        })
                        .execute()
                        .then(() => {
                            this.init()
                            this.callRefresh(routesMap.recruit.job)
                        })
                })
            })
        }

        private toEdit() {
            this.$router.push({
                name:
                    this.row!.position_type === 2
                        ? routesMap.recruit.publicJobEdit
                        : routesMap.recruit.jobEdit,
                query: {
                    id: this.id,
                    from: routesMap.recruit.jobDetail,
                    isAudit:
                        this.row!.status === this.auditStatus.审核通过 ? "1" : "0",
                },
            })
        }

        private toCopyAndAdd() {
            this.$router.push({
                name:
                    this.row!.position_type === 2
                        ? routesMap.recruit.publicJobEdit
                        : routesMap.recruit.jobEdit,
                query: {
                    copyId: this.id,
                    from: routesMap.recruit.jobDetail,
                },
            })
        }

        private openRecommend() {
            this.showRecommend = true
        }

        private showAuditPop = false

        private toggleshowAuditPop(row: Row) {
            this.row = row
            this.showAuditPop = true
        }

        private showPopularizePop = false

        private togglePopularizePop(row: Row) {
            this.row = row
            this.showPopularizePop = true
        }

        private copy() {
            copyTextToClipboard(`/pages/tabbar/job/career-detail?id=${this.id}`)
            this.$message.success("复制成功")
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";

    .back-btn {
        width: 120px;
        height: 40px;
    }
    ::v-deep .filter-container {
        box-shadow: none !important;
    }
</style>
