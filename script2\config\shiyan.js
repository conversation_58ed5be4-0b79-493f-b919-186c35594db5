const defaultDomainAllInOne = {
    VUE_APP_UNIPLAT: "/api",
    VUE_APP_UNIPLAT_WEB: "/uniplat",
    VUE_APP_H5: "/h5",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
}

const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "十堰",
    VUE_APP_CITY_SAMPLE_NAME2: "十堰市",
    VUE_APP_DEFAULT_REGION_CODE: "420300000000",
    VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
    VUE_APP_DEFAULT_REGION_NAME: "十堰市",
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "十堰市",
    VUE_APP_BAIDU_KEY: "Xa338L0G79uVCEmkg7tIXOZtCLEPFfMY",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "bM8s2UjSRSGrxlz4",
    VUE_APP_requestEncoder: "", // 入参加密 aes
    VUE_APP_responseEncoder: "", // 返回参加密 aes
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-shiyan",
    VUE_APP_BIGSCREEN_BI_PATH: "/bigScreen",
}
const { config } = require("./shiyan_p.js")
module.exports = {
    name: "十堰项目",
    env: {
        pro: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_requestEncoder: "aes", // 入参加密 aes
            VUE_APP_responseEncoder: "aes", // 返回参加密 aes
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "就业管理工作台",
                VUE_APP_APP_TITLE: "智慧就业服务工作台",
                BASE_URL: "platform/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                test: {
                    path: "/home/<USER>/web/platform",
                    host: "253",
                },
                pro: {
                    path: "/mnt/shiyan/web/platform",
                    host: "shiyan",
                },
            },
        },
        // 企业端
        {
            name: "企业端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "企业智慧就业服务平台",
                VUE_APP_HEADER_TITLE: "企业智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["企业端"],
            },
            deploy: {
                test: {
                    path: "/home/<USER>/web/org",
                    host: "253",
                },
            },
        },
        // 机构端
        {
            name: "机构端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "人力资源机构智慧就业服务平台",
                VUE_APP_HEADER_TITLE: "人力资源机构智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org_hr",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["机构端"],
            },
            deploy: {
                test: {
                    path: "/home/<USER>/web/hr",
                    host: "253",
                },
            },
        },
    ],
}
