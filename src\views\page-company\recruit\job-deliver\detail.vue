<template>
    <div
        class="core-ui-table-container container"
        :key="refreshQueryParams"
        v-if="row"
    >
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
            <div class="u-flex" v-show="!isDel">
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    @click="showContactPop = true"
                >
                    立即联系
                </el-button>
                <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    v-if="
                        row.status === status.待面试 &&
                        row.interview_type === 1 &&
                        !isXf &&
                        !isWx
                    "
                    @click="showVideoInterview = true"
                >
                    发起面试
                </el-button>
                <template v-if="pass">
                    <!-- <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="showInterviewPop = true"
                        v-if="row.status < status.待面试 && !isDd"
                    >
                        约面试
                    </el-button> -->
                    <el-button
                        type="primary"
                        class="custom-btn back-btn"
                        @click="showStatusPop = true"
                        v-if="isShowFeedback"
                    >
                        反馈结果
                    </el-button>
                </template>
                <!-- <el-button
                    type="primary"
                    class="custom-btn back-btn"
                    @click="showQualificationPop = true"
                    v-if="row.position_type === positionType.公益岗位"
                >
                    审核资格
                </el-button> -->
            </div>
        </div>
        <div class="detail-index shadow">
            <detail-view :row="row"></detail-view>
        </div>
        <template v-if="!isDel">
            <interview-pop
                v-model="showInterviewPop"
                :id="row.id"
                @refresh="refresh"
            ></interview-pop>
            <contact-pop
                v-model="showContactPop"
                :mobile="row.mobile"
                @refresh="refresh"
            ></contact-pop>
            <result-pop v-model="showResultPop"></result-pop>
            <status-pop
                v-model="showStatusPop"
                :id="row.id"
                @refresh="refresh"
            ></status-pop>
            <qualification-pop
                v-model="showQualificationPop"
                :id="row.id"
                @refresh="refresh"
            ></qualification-pop>
        </template>
    </div>
</template>

<script lang='ts'>
    import { config, EnvProject } from "@/config"
import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { Component, Vue } from "vue-property-decorator"
    import { PositionType, predict, Status } from "."
    import ContactPop from "./components/contact-pop.vue"
    import DetailView from "./components/detail-view.vue"
    import InterviewPop from "./components/interview-pop.vue"
    import QualificationPop from "./components/qualification-pop.vue"
    import ResultPop from "./components/result-pop.vue"
    import StatusPop from "./components/status-pop.vue"

    @Component({
        name: routesMap.company.recruit.jobDeliverDetail,
        components: {
            DetailView,
            InterviewPop,
            ContactPop,
            ResultPop,
            StatusPop,
            QualificationPop,
        },
    })
    export default class JobDeliverDetail extends Vue {
        private routesMap = routesMap
        private row: any | null = null
        private id = ""
        private showInterviewPop = false
        private showContactPop = false
        private showResultPop = false
        private showStatusPop = false
        private showQualificationPop = false

        private get isDel() {
            return this.row?.position_is_del === 1
        }

        private positionType = PositionType
        private status = Status

        private isXf = [EnvProject.咸丰项目].includes(config.envProject)

        private get pass() {
            // 企业端先默认显示
            return this.row?.qualification_audit_status === 1 || true
        }

        private get isShowFeedback() {
            return this.row?.status < Status.已入职
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.company.recruit.jobDeliverDetail,
        }

        breadcrumbs: BreadcrumbItem[] = []
        private setBreadcrumbs() {
            const last = this.from
                ? getCacheBreadcrumbsByRoutePath(this.from)
                : [
                      {
                          label: "岗位投递列表",
                          to: routesMap.company.recruit.jobDeliver,
                      },
                  ]
            const d: BreadcrumbItem[] = [
                ...last,
                {
                    label: "岗位投递详情",
                    to: {
                        name: routesMap.company.recruit.jobDeliverDetail,
                        query: {
                            id: this.id,
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.company.recruit.jobDeliverDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        private get from() {
            return this.$route.query.from as string | undefined
        }

        mounted() {
            this.init()
        }

        private refresh() {
            this.init()
            this.callRefresh(routesMap.company.recruit.jobDeliver)
        }

        private init() {
            this.id = this.$route.query.id as string
            this.row = null
            this.setBreadcrumbs()
            pageLoading(() => {
                return sdk.core
                    .model("xg_candidate_order")
                    .detail(this.id, "company")
                    .query()
                    .then((r) => {
                        sdk.core
                            .model("xg_candidate_order")
                            .action("company_view")
                            .updateInitialParams({
                                selected_list: [{ v: 0, id: this.id }],
                            })
                            .execute()
                            .then(() => {
                                this.callRefresh(routesMap.company.recruit.jobDeliver)
                            })
                        this.row = sdk.buildRow(r.row, predict)
                        console.log(JSON.parse(JSON.stringify(this.row)))
                        if (this.row.status === Status.待处理) {
                            this.showResultPop = true
                        }
                    })
            })
        }

        private toEdit() {}
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .back-btn {
        min-width: 120px;
        height: 40px;
        padding: 0 20px !important;
    }
</style>
