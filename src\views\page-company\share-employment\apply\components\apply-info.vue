<template>
    <div class="u-p-x-20 u-font-14 u-m-b-40">
        <div class="title u-m-b-20">申请信息</div>
        <detail-row-col
            class="u-p-x-20"
            :list="computeList"
            :labelStyle="labelStyle"
        ></detail-row-col>
        <div class="u-flex u-col-top bg1 u-p-l-10 u-p-x-20 u-p-b-20">
            <div :style="labelStyle" class="u-p-r-10">图片</div>
            <div class="u-flex">
                <el-image
                    :src="img"
                    v-for="img in images"
                    :key="img"
                    class="img"
                    :preview-src-list="images"
                />
                <div class="none-text" v-if="images && images.length === 0">
                    暂无图片
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { getImages } from "@/utils"
    import { ColItem } from "@/views/components/detail-row-col"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { RowType } from "../../info/components/detail"
    import { applyBaseInfo, handlerList, Row } from "./detail"

    @Component({ components: { DetailRowCol } })
    export default class DetailView extends Vue {
        detail: any = {}

        @Prop()
        private row!: Row

        private get labelStyle() {
            return {
                width: "80px",
                textAlign: "right",
                marginRight: "10px",
                color: "#9098A6",
                lineHeight: "20px",
            }
        }

        private get images() {
            if (!this.row) return []
            return getImages(this.row.images)
        }

        private computeList: ColItem[] = []

        mounted() {
            this.handlerList()
        }

        private handlerList() {
            const c = handlerList(this.row, applyBaseInfo, this.$createElement)
            if (this.row.type === RowType.人工富裕) {
                const index = c.findIndex((i) => i.label === "所在地")
                c.splice(index, 1)
            }
            this.computeList = c
        }

        private refreshList() {
            this.$emit("refresh")
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        background: #fff;
        .content {
            width: calc(800 / 1440 * 100vw);
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
        }
    }

    ::v-deep .detail-row {
        background: rgba(#f2f7ff, 0.5);
        border-radius: 5px;
        padding: 15px 0;
        line-height: 20px;
        .item:nth-of-type(3n-1) .label {
            width: 120px;
        }
        .item {
            line-height: 20px;
        }
    }

    .img {
        width: 80px;
        height: 80px;
        margin-right: 10px;
    }

    .bg1 {
        background-color: rgba(242, 247, 255, 0.5);
    }

    .none-text {
        line-height: 20px;
    }
</style>
