<template>
    <div class="card">
        <div class="card-label">{{ label }}</div>
        <slot class="w-100"></slot>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ name: "ItemBox", components: {} })
    export default class TemplateView extends Vue {
        @Prop()
        private label!: string
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 100%;
        background: rgba(13, 56, 193, 0.4);
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        padding: 14px 20px;
        .card-label {
            font-weight: 500;
            font-size: 22px;
            color: #ffffff;
            line-height: 26px;
            text-align: left;
            margin-bottom: 16px;
        }
    }
</style>
