<template>
    <div class="detail-container">
        <div
            class="statistics u-flex u-row-between"
            v-if="row.status === auditStatus.审核通过 && !isEZ"
        >
            <div class="item item1">
                <div class="num">{{ row.object_data.browse_num || 0 }}</div>
                <div>岗位总浏览数量</div>
            </div>
            <div class="item item2">
                <div class="num">
                    {{ row.object_data.browse_week_num || 0 }}
                </div>
                <div>近一周浏览数量</div>
            </div>
            <div class="item item4" @click="toContactDetail" v-if="isHz || isXg">
                <div class="num">
                    {{ row.object_data.contact_sum || 0 }}
                </div>
                <div class="label">岗位被联系总量</div>
            </div>
            <div class="item item5" @click="toPositionTrackDetail" v-if="isHz || isXg">
                <div class="num">
                    {{ row.entry_sums || 0 }}
                </div>
                <div class="label">跟踪入职人数</div>
            </div>
            <div class="item3 u-flex u-row-evenly">
                <div class="child-item">
                    <div class="num">{{ row.object_data.order_num || 0 }}</div>
                    <div>岗位投递数量</div>
                </div>
                <div class="line"></div>
                <template v-for="item in statistic">
                    <div class="child-item" :key="item.key" v-if="!item.hide">
                        <div class="num">
                            {{ row.object_data[item.key] || 0 }}
                        </div>
                        <div>{{ item.label }}</div>
                    </div>
                </template>
            </div>
        </div>
        <div class="content u-flex-none">
            <div class="title">岗位信息</div>
            <detail-row-col
                :labelStyle="{
                    width: '110px',
                    color: '#555',
                }"
                class="u-m-t-10 u-m-b-20 u-p-x-20 info"
                :list="detailRow"
            />
            <div v-if="!unReviewed">
                <div class="title">审核信息</div>
                <div class="u-p-t-10 u-p-x-20 u-p-b-20 info">
                    <div class="u-flex u-col-top">
                        <div class="u-flex-1">
                            <div class="u-flex">
                                <div class="label">审核状态：</div>
                                <div class="u-flex u-flex-none">
                                    {{ row.status_label || "-" }}
                                </div>
                            </div>
                            <div class="u-flex">
                                <div class="label">审核时间：</div>
                                <div class="u-flex u-flex-none">
                                    {{ row.audit_time | time2String }}
                                </div>
                            </div>
                        </div>
                        <div class="u-flex-1">
                            <div class="u-flex">
                                <div class="label">审核人：</div>
                                <div class="u-flex u-flex-none">
                                    {{ row.audit_user_name || "-" }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="u-flex">
                        <div class="label">审核意见：</div>
                        <div class="u-flex u-flex-none">
                            {{ row.audit_memo || "-" }}
                        </div>
                    </div>
                </div>
            </div>

            <template v-if="row.status_label === '审核通过' && !unReviewed">
                <div class="title">管理信息</div>
                <div class="u-p-t-10 u-p-x-20 u-p-b-20 info u-flex u-col-top">
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">上架状态：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.online_status_label || "-" }}
                            </div>
                        </div>
                        <div class="u-flex">
                            <div class="label">操作时间：</div>
                            <div class="u-flex u-flex-none">
                                {{ onOffTime }}
                            </div>
                        </div>
                    </div>
                    <div class="u-flex-1">
                        <div class="u-flex">
                            <div class="label">操作人：</div>
                            <div class="u-flex u-flex-none">
                                {{ row.online_operator_name || "-" }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script lang='ts'>
    import { Component, Prop, Vue } from "vue-property-decorator"
    import { AuditStatus, OnlineStatus, Row } from ".."
    import { copyTextToClipboard } from "uniplat-sdk/build/main/helpers/clipboard"
    import DetailRowCol from "@/views/components/detail-row-col/index.vue"
    import { ColItem } from "@/views/components/detail-row-col"
    import { config, EnvProject } from "@/config"
    import { renDesensitizationView } from "@/views/components/common-comps"
    import { formatTime } from "@/utils/tools"
    import { routesMap } from "@/router/direction"

    @Component({ components: { DetailRowCol } })
    export default class JobDetailView extends Vue {
        @Prop()
        private row!: Row

        private isHz =
            config.envProject === EnvProject.黄州项目 ||
            process.env.VUE_APP_ENV === "test"

        private auditStatus = AuditStatus
        private isEZ = config.envProject === EnvProject.鄂州项目
        private isXg = config.envProject === EnvProject.孝感项目

        get onOffTime() {
            if (
                this.row.status === AuditStatus.审核通过 &&
                this.row.online_status === OnlineStatus.已上架
            ) {
                return formatTime.default(this.row.online_time)
            } else {
                return formatTime.default(this.row.offline_time)
            }
        }

        private get unReviewed() {
            return this.row.status === 0
        }

        private get isPublic() {
            return this.row?.position_type === 2
        }

        private get statistic() {
            return [
                // { label: "岗位总浏览数量", key: "browse_num" },
                // { label: "近一周浏览数量", key: "browse_week_num" },
                // { label: "岗位投递数量", key: "order_num" },
                { label: "用户自行投递", key: "self_num" },
                {
                    label: "人才推荐",
                    key: "talent_num",
                    hide: [EnvProject.宜都项目].includes(config.envProject),
                },
                {
                    label: "社群推广",
                    key: "socialize_group_num",
                    hide: [EnvProject.黄州项目].includes(config.envProject),
                },
                { label: "网格推广", key: "grid_user_num" },
                {
                    label: "人资机构代招",
                    key: "agent_bidding_num",
                    hide: [EnvProject.黄州项目, EnvProject.宜都项目].includes(
                        config.envProject
                    ),
                },
                {
                    label: "直播招聘",
                    key: "live_num",
                    hide: [EnvProject.黄州项目, EnvProject.宜都项目].includes(
                        config.envProject
                    ),
                },
                // { label: "招聘会投递", key: "job_fair_num" },
            ]
        }

        private get detailRow(): ColItem[] {
            console.log(JSON.parse(JSON.stringify(this.row)))
            const h = this.$createElement
            const des2 = (this.row.function_detail || "")
                .split("\n")
                .filter(Boolean)
            const des3 = this.row.tags.职位福利
                .map((tag: any) => {
                    return tag.tagName
                })
                .join("、")
            const des4 = this.row.tags.职位亮点
                .map((tag: any) => {
                    return tag.tagName
                })
                .join("、")
            const des5 = this.row.is_create ? "已入驻" : "未入驻"
            const class5 = this.row.is_create ? "status-text2" : "status-text"
            const d: ColItem[] = [
                {
                    label: this.isPublic ? "所属单位：" : "所属企业：",
                    vNode: h("div", { class: "d-flex-item-center" }, [
                        h("div", { class: "" }, this.row.company_name),
                        h("div", { class: class5 }, des5),
                    ]),
                    span: 24,
                },
                {
                    label: "",
                    vNode: h("div", { class: "" }, [
                        h("div", { class: "tips-title" }, "岗位基本信息："),
                    ]),
                    span: 24,
                },
                {
                    label: "岗位名称：",
                    value: this.row.name,
                },
                {
                    label: "工作性质：",
                    value: this.row.work_type_label,
                },
                {
                    label: "薪资要求：",
                    value: this.row.salary_desc,
                },
                {
                    label: "工作地址：",
                    value: this.row.cal_address_detail_label,
                },
                {
                    label: "招聘人数：",
                    value: this.row.recruit_count,
                },
                {
                    label: "招聘过期时间：",
                    value: this.row.expired_date_label,
                },
                {
                    label: "岗位链接：",
                    vNode:
                        this.row.source_page_url &&
                        this.row.source_page_url.includes("http")
                            ? h("div", { class: "u-flex" }, [
                                  h(
                                      "div",
                                      { class: "url u-line-1" },
                                      this.row.source_page_url
                                  ),
                                  h(
                                      "div",
                                      {
                                          on: {
                                              click: () => {
                                                  this.copy()
                                              },
                                          },
                                          class: "primary pointer u-m-l-10 u-flex-none",
                                      },
                                      "复制链接"
                                  ),
                                  h(
                                      "div",
                                      {
                                          on: {
                                              click: () => {
                                                  this.viewPosition()
                                              },
                                          },
                                          class: "primary pointer u-m-l-10 u-flex-none",
                                      },
                                      "点击跳转"
                                  ),
                              ])
                            : undefined,
                    span: 16,
                },
                {
                    label: "岗位福利标签：",
                    value: des3,
                    span: 24,
                },
                {
                    label: "岗位亮点：",
                    value: des4,
                    span: 24,
                },
                this.isPublic
                    ? {
                          label: "工种：",
                          value: this.row.industry,
                          span: 24,
                      }
                    : {
                          label: "职能：",
                          value: this.row.function_categories,
                          span: 24,
                      },

                {
                    label: "岗位描述：",
                    vNode: h(
                        "span",
                        {
                            class: "pre-line des",
                        },
                        this.row?.description || ""
                    ),
                    span: 24,
                },
                // {
                //     label: "地址详情：",
                //     value: getAddress(this.row),
                // },
                {
                    label: "",
                    vNode: h("div", { class: "" }, [
                        h("div", { class: "tips-title" }, "任职信息："),
                    ]),
                    span: 24,
                },
                {
                    label: "岗位职责：",
                    vNode: h(
                        "div",
                        {},
                        des2.length
                            ? des2.map((e: string) => {
                                  return h("div", {}, e)
                              })
                            : "--"
                    ),
                    // value: this.row.description,
                    span: 24,
                },
                {
                    label: "年龄要求：",
                    value: [this.row.age_require_min, this.row.age_require_max]
                        .filter(Boolean)
                        .map((i) => `${i}岁`)
                        .join(" - "),
                },
                {
                    label: "工作年限：",
                    value: this.row.experience,
                },
                !config.projectConfig.hideGender
                    ? {
                          label: "性别要求：",
                          value: this.row.gender_require_label,
                      }
                    : { label: "", value: " " },
                {
                    label: "学历要求：",
                    value: this.row.education_label,
                },
                {
                    label: "户籍所在地：",
                    value: this.row.households_label,
                },
                {
                    label: "专业要求：",
                    value: this.row.major_desc_label,
                },
                {
                    label: "语言种类",
                    value: this.row.language_label,
                },
                {
                    label: "外语水平",
                    value: this.row.language_level_label,
                },
                {
                    label: "",
                    vNode: h("div", { class: "" }, [
                        h("div", { class: "tips-title" }, "联系信息："),
                    ]),
                    span: 24,
                },
                {
                    label: "联系人：",
                    value: this.row.contact_person,
                },

                {
                    label: "联系电话：",
                    // value: this.row.contact_mobile,
                    vNode: renDesensitizationView(h, {
                        value: this.row.contact_mobile,
                    }),
                },
                {
                    label: "联系邮箱：",
                    value: this.row.contact_email,
                },
                {
                    label: "",
                    vNode: h("div", { class: "" }, [
                        h("div", { class: "tips-title" }, "投递要求："),
                    ]),
                    span: 24,
                },
                {
                    label: "附件简历必填：",
                    value: this.row.require_profile_label,
                },
                {
                    label: "工作经历必填：",
                    value: this.row.require_work_label,
                },
                {
                    label: "学历信息必填：",
                    value: this.row.require_education_label,
                },
                // {
                //     label: "岗位行业：",
                //     value: this.row.industry_label,
                // },

                // {
                //     label: "岗位来源：",
                //     value:
                //         !this.row.source_from_logo &&
                //         this.row.source_from_type_label,
                //     vNode: this.row.source_from_logo
                //         ? h("img", {
                //               attrs: {
                //                   src: this.row.source_from_logo,
                //               },
                //               class: "logo",
                //           })
                //         : undefined,
                // },
                // {
                //     label: "创建人：",
                //     value: this.row.creator,
                // },
                // {
                //     label: "创建时间：",
                //     value: formatTime.seconds(this.row.create_time),
                // },
            ].map((i) => ({ ...i, span: i.span || 8 }))
            return d
        }

        private viewPosition() {
            console.log("viewPosition")
            window.open(this.row.source_page_url, "_blank")
            // DrawerBox.open({
            //     url: "http://localhost:8085/recruit/job",
            //     title: "职位详情",
            // })
        }

        private copy() {
            copyTextToClipboard(this.row.source_page_url)
            this.$message.success("复制成功")
        }

        private toContactDetail() {
            this.$router.push({
                name: routesMap.recruit.jobContactDetail,
                query: { id: this.row.id + "", from: this.$route.name },
            })
        }

        private toPositionTrackDetail() {
            this.$router.push({
                name: routesMap.recruit.jobTrackDetail,
                query: {
                    id: this.row.id + "",
                    agentId: this.row.agent_id + "",
                    from: this.$route.name,
                },
            })
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .detail-container {
        background: #fff;
        .content {
            padding: 20px;
            padding-bottom: 0;
            .title {
                width: 100%;
                height: 40px;
                background: #f8f8f8;
                color: #222;
                font-size: 18px;
                font-weight: 600;
                line-height: 40px;
                padding: 0 20px;
            }
            .label {
                width: 105px;
                margin-right: 10px;
                color: #555;
            }
            .info {
                line-height: 34px;
                color: #333;
                font-size: 14px;
            }
        }
        .url {
            max-width: 200px;
        }
        .logo {
            height: 25px;
        }
        .des {
            max-height: 388px;
            overflow: scroll;
        }

        .tips-title {
            margin-left: -100px;
            color: #222222;
            font-weight: 600;
        }

        .status-text {
            margin-left: 12px;
            width: 40px;
            height: 16px;
            text-align: center;
            line-height: 16px;
            background-color: #eff1f8;
            color: #7998b8;
            font-size: 12px;
        }

        .status-text2 {
            margin-left: 12px;
            width: 40px;
            height: 16px;
            text-align: center;
            line-height: 16px;
            background-color: #d7f8ea;
            color: #22bd7a;
            font-size: 12px;
        }
        .statistics {
            padding: 20px;
            .child-item,
            .item {
                display: flex;
                flex: none;
                flex-direction: column;
                align-items: center;
                border-radius: 8px;
                font-size: 14px;
                height: 80px;
                color: #333333;
                padding-top: 15px;
                .num {
                    font-weight: 600;
                    font-size: 24px;
                    margin-bottom: 11px;
                }
            }
            .line {
                width: 1px;
                height: 50px;
                background: #61dbdc;
            }
            .item1 {
                width: calc(160 / 1600 * 100vw);
                background: #f4f1ff;
                margin-right: 20px;
                .num {
                    color: #5759ec;
                }
            }
            .item2 {
                width: calc(160 / 1600 * 100vw);
                background: #f1f5ff;
                margin-right: 20px;
                .num {
                    color: #5782ec;
                }
            }
            .item3 {
                width: calc(910 / 1600 * 100vw);
                background: #ebf9f9;
                border-radius: 8px;
                flex: 1;
                .num {
                    color: #32b2b2;
                }
            }
            .item4 {
                width: calc(160 / 1600 * 100vw);
                background: #fff5f1;
                margin-right: 20px;
                cursor: pointer;
                .label {
                    text-decoration: underline;
                }
                .num {
                    color: #f07756;
                }
            }
            .item5 {
                width: calc(120 / 1600 * 100vw);
                background: #fff5f1;
                margin-right: 20px;
                cursor: pointer;
                .label {
                    text-decoration: underline;
                }
                .num {
                    color: #f07756;
                }
            }
        }
    }
</style>
