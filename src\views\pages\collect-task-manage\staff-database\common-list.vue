<template>
    <div class="content">
        <div class="core-ui-custom-header">
            <div class="title">{{ title }}</div>
            <div class="u-flex u-row-right"></div>
        </div>
        <div class="content1">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="area-select" v-show="region_code_name">
                        所在区域：
                    </div>
                    <RegionTree
                        v-if="!isXg"
                        :data="regionTreeData"
                        @change="regionTreeChange"
                        :maxLevel="2"
                    ></RegionTree>
                    <div v-else-if="region_code_name">
                        {{ region_code_name }}
                    </div>
                </div>

                <div>
                    <el-button type="primary" @click="toSearch">
                        信息查询
                    </el-button>
                </div>
            </div>
            <div class="info-items" v-for="(item, index) in data" :key="index">
                <div class="info-label">{{ item.label }}</div>
                <div class="info-box d-flex">
                    <div
                        class="left-box"
                        v-for="(data, subIndex) in item.leftBox"
                        :key="'left' + subIndex"
                    >
                        <div class="item">
                            <div
                                class="item-label"
                                :class="{ bigger: item.rightBox.length > 1 }"
                            >
                                {{ data.value }}
                            </div>
                            <div>{{ data.label }}</div>
                        </div>
                    </div>
                    <div class="right-box">
                        <div
                            class="right-box-line"
                            v-for="(line, lineIndex) in item.rightBox"
                            :key="'line' + lineIndex"
                        >
                            <div
                                class="item"
                                v-for="(subItem, subItemIndex) in line"
                                :key="'right' + subItemIndex"
                            >
                                <div class="item-label">
                                    {{ subItem.value }}
                                </div>
                                <div>{{ subItem.label }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="content2">
            <table-container
                v-if="tableConfig"
                filedWidth="200"
                :showPageIndex="false"
                ref="table"
                v-model="tableConfig"
                class="container"
            >
                <div slot="table" slot-scope="{ data }" class="u-p-20 bg-white">
                    <common-table :data="data" :columns="columns">
                    </common-table>
                </div>
            </table-container>
        </div>
    </div>
</template>

<script lang='ts'>
    import { sdk } from "@/service"
    // import { cloneDeep } from "lodash"
    import { Component, Prop } from "vue-property-decorator"
    import { TableColumn, TableConfig } from "@/core-ui/component/table"
    import TableContainer from "@/core-ui/component/table/container.vue"
    import { BaseTableController } from "@/core-ui/component/table/base-table"
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import RegionTree from "../labor-info-base-manage/components/region-tree.vue"
    import { userService } from "@/service/service-user"
    import { config, EnvProject } from "@/config"

    const isXg = config.envProject === EnvProject.孝感项目

    interface Row {
        id: number
    }

    @Component({ components: { TableContainer, CommonTable, RegionTree } })
    export default class CommonList extends BaseTableController<Row> {
        @Prop()
        private title!: string

        @Prop()
        private list_name!: string

        tableConfig: TableConfig | null = null
        private columns: TableColumn[] = []

        private firstData: any = []
        private dataInfo: any = []

        private data: any[] = []
        private code4CurAgent = userService.getCurAgent()?.data.region_code
        private region_code = ""
        private region_code_name = userService.getCurAgent()?.data.region_name

        private isXg = isXg

        private regionTreeData: any = {
            level: 3,
            type: "region_code",
            treeInfo: {
                manage_region_full_path:
                    userService.getCurAgent()?.data.region_code,
            },
        }

        private itemWidth = 0

        created() {
            const codes = (this.code4CurAgent || "").split(",")
            this.region_code = codes[codes.length - 1] || ""
            // console.log(userService.getCurAgent())
            this.getListData({})
            this.init()
        }

        private getListData(detail: any) {
            this.data = [
                {
                    label: "企业总体情况",
                    leftBox: [
                        {
                            label: "用工总人数",
                            value: detail["用工总人数"] || 0,
                        },
                    ],
                    rightBox: [
                        [
                            {
                                label: "企业职工",
                                value: detail["企业职工"] || 0,
                            },
                            {
                                label: "灵活就业",
                                value: detail["灵活就业"] || 0,
                            },
                            {
                                label: "本地就业",
                                value: detail["本地就业"] || 0,
                            },
                            {
                                label: "外来就业",
                                value: detail["外来就业"] || 0,
                            },
                        ],
                        [
                            {
                                label: "第一产业用工数",
                                value: detail["第一产业"] || 0,
                            },
                            {
                                label: "第二产业用工数",
                                value: detail["第二产业"] || 0,
                            },
                            {
                                label: "第三产业用工数",
                                value: detail["第三产业"] || 0,
                            },
                            {
                                label: "重点企业用工数",
                                value: detail["重点企业用工数量"] || 0,
                            },
                        ],
                    ],
                },
            ]
        }

        private init() {
            sdk.getDomainService(
                "getTgEmpStatics",
                "tg_enterprise_info_api",
                "xg_project"
            )
                .post({
                    city: this.region_code,
                })
                .then((res: any) => {
                    this.getListData(res.couts)

                    const tableData: any = {
                        // district: "下级区域",
                        // district_code: "下级区域",
                        region_name: "下级区域",
                        zj: "用工总人数",
                        qy: "企业职工总数",
                        lh: "灵活就业总数",
                        bd: "本地就业人数",
                        wd: "外来就业人数",
                    }

                    const query: any = {
                        request: () => {
                            return new Promise((resolve) => {
                                const columns: any[] = []
                                Object.keys(tableData).forEach((i: string) => {
                                    columns.push({
                                        label: tableData[i],
                                        prop: i,
                                        showOverflowTip: true,
                                    })
                                })
                                this.columns = columns
                                resolve({
                                    data: res.list,
                                    total_count: 10,
                                })
                            })
                        },
                    }

                    this.tableConfig = null

                    this.$nextTick(() => {
                        this.tableConfig = {
                            domainService: query,
                            defaultPageSize: 99,
                        }
                    })

                    console.log(res)
                })
        }

        private toSearch() {
            this.$emit("toSearch")
        }

        private regionTreeChange(r: any) {
            this.$emit("regionTreeChange", r)
            if (r.values && r.values.length > 0) {
                const codes = r.values[0].split(",")
                this.region_code = codes[codes.length - 1]
                this.init()
            }
        }
    }
</script>

<style lang='less' scoped>
    @import "~@/css/variables.less";
    .content {
        overflow: auto;
    }
    .content1 {
        background-color: #fff;
        padding: 20px;

        .area-select {
            width: 100px;
            color: #222222;
            font-size: 18px;
            word-break: keep-all;
        }

        .info-label {
            height: 50px;
            font-weight: 600;
            font-size: 18px;
            color: #000000;
            line-height: 50px;
        }

        .info-box {
            width: 100%;
            gap: 20px;

            .left-box {
                width: 22%;
                background: #f4f1ff;
                border-radius: 8px;

                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }

            .right-box {
                display: flex;
                flex-direction: column;
                gap: 20px;
                flex: 1;

                .right-box-line {
                    display: flex;
                    flex: 1;
                    gap: 20px;

                    .item {
                        background: #f1f5ff;
                    }

                    &:nth-child(2) {
                        .item {
                            background-color: #ebf9f9;

                            .item-label {
                                color: #32b2b2;
                            }
                        }
                    }
                }
            }

            .item {
                flex: 1;
                border-radius: 8px;
                height: 90px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 10px;
                color: #333333;

                .item-label {
                    font-weight: 600;
                    font-size: 30px;
                    color: #5782ec;
                    line-height: 30px;
                    text-align: center;
                    font-style: normal;

                    &.bigger {
                        font-size: 40px;
                        line-height: 40px;
                    }
                }
            }
        }
    }

    .content2 {
        background-color: #fff;
        margin-top: 20px;
    }
</style>
