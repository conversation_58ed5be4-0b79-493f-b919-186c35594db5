import { Loading } from "element-ui"
import { sdk } from "./sdk"
import {
    assign,
    cloneDeep,
    forEach,
    isArray,
    isBoolean,
    isNumber,
    keys,
    split,
} from "lodash"
import router from "@/router"
import { routesMap } from "@/router/direction"

export const enum PositionRecommendType {
    "人才推荐服务" = "1",
    "社群推广服务" = "2",
    "网格推广服务" = "3",
    "人力资源机构撮合服务" = "4",
}

const routerName2RecommendType = {
    [routesMap.recruit.person]: PositionRecommendType.人才推荐服务,
    [routesMap.recruit.grid]: PositionRecommendType.网格推广服务,
    [routesMap.recruit.group]: PositionRecommendType.社群推广服务,
    [routesMap.recruit.cooperation]: PositionRecommendType.人力资源机构撮合服务,
    [routesMap.company.recruit.person]: PositionRecommendType.人才推荐服务,
    [routesMap.company.recruit.grid]: PositionRecommendType.网格推广服务,
    [routesMap.company.recruit.group]: PositionRecommendType.社群推广服务,
    [routesMap.company.recruit.cooperation]:
        PositionRecommendType.人力资源机构撮合服务,
}

const defaultData = {
    client_id: { gov_wechat_mini_app: "" },
    adv_info: { mobile: "", name: "", qr_code: "" },
    profile_nation_mapping: {},

    position_recommend_type_mapping: [] as {
        key: PositionRecommendType
        value: string
    }[],
    // 专场类型
    job_fair_activity_type_mapping: [] as {
        key: string
        value: string
    }[],
    // 专项活动
    job_fair_theme_type_mapping: [] as {
        key: string
        value: string
    }[],
}

export type SysConfig = typeof defaultData

class SysConfigClass {
    private loaded = false

    public data = defaultData

    public setup() {
        const data = this.data
        if (this.loaded) {
            return Promise.resolve(this.data)
        }
        const loading = Loading.service({})
        return sdk.core
            .domainService(
                "xg_project",
                "anonymous/client_api",
                "get_group_codes"
            )
            .post<Record<string, { key: string; value: unknown }[]>>({
                category: keys(data),
            })
            .then((r) => {
                const d = cloneDeep(data) as unknown as Record<
                    string,
                    Record<string, unknown>
                >
                forEach(r, (v, k) => {
                    if (isArray(d[k])) {
                        d[k] = r[k] as any
                    } else {
                        d[k] = assign(d[k], {})
                        forEach(v, (item) => {
                            if (d[k][item.key] === "img") {
                                d[k][item.key] = sdk.buildImage(
                                    item.value as string
                                )
                            } else if (isBoolean(d[k][item.key])) {
                                d[k][item.key] = !!item.value
                            } else if (isNumber(d[k][item.key])) {
                                d[k][item.key] = +(item.value as string)
                            } else if (isArray(d[k][item.key])) {
                                d[k][item.key] = split(
                                    (item.value as string).toString(),
                                    /[\n\s+,，]/g
                                ).filter((i) => i)
                            } else {
                                d[k][item.key] = item.value
                            }
                        })
                    }
                })
                this.data = d as unknown as typeof data
                this.loaded = true
                const routers = router.getRoutes()
                routers.forEach((i) => {
                    if (i.name && routerName2RecommendType[i.name]) {
                        const key = routerName2RecommendType[i.name]
                        const mapping =
                            this.data.position_recommend_type_mapping
                        const t = !!mapping.find((j) => j.key === key)
                        if (t) {
                            Object.assign(i.meta, {
                                hidden: false,
                            })
                        }
                    }
                })
                return this.data
            })
            .catch(() => {
                this.loaded = true
                return this.data
            })
            .finally(() => {
                loading.close()
            })
    }
}

export const sysConfigService = new SysConfigClass()
