<template>
    <div class="content">
        <Header title="基本信息"></Header>
        <div class="info-box">
            <div class="top-label">
                <span class="name-label"
                    ><span>{{ name }}</span></span
                >
                <span class="item-info">
                    <span>{{ gender }}</span>
                    <span v-if="gender && age">|</span>
                    <span v-if="age">{{ age }}岁</span>
                </span>

                <span>{{ education }}</span>
            </div>
            <div
                v-for="(item, index) in infos"
                :key="index"
                class="info-item label d-flex align-items-center"
            >
                <div class="point-box"></div>
                <div class="item-label">{{ item.label }}</div>
                <div class="item-value" :title="item.value">
                    {{ item.value }}
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Watch } from "vue-property-decorator"
    import Header from "../../../common/header.vue"
    import BaseItem from "../../../common/base-item"
    import { getAddress } from "@/utils"
    import { cloneDeep, forEach, map, take } from "lodash"
    import { maskId } from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/tools"
    import { exportPdfService } from "@/views/common/exportPdf"

    @Component({ components: { Header } })
    export default class Template extends BaseItem {
        private name = ""
        private gender = ""
        private age = ""
        private education = ""

        private infos = [
            {
                label: "身份证号",
                value: "",
            },
            {
                label: "联系电话",
                value: "",
            },
            {
                label: "民族",
                value: "",
            },
            {
                label: "户籍性质",
                value: "",
            },
            {
                label: "户籍地",
                value: "",
            },
            {
                label: "常住地址",
                value: "",
            },
        ]

        private maskName(name: string) {
            if (!name) return ""
            return name.charAt(0) + "*".repeat(name.length - 1)
        }

        @Watch("row", { immediate: true, deep: true })
        private onRowChanged() {
            if (!this.row) {
                return
            }
            const row = this.row

            this.name = this.getInfo().base.name
            this.gender = this.getInfo().base.gender
            this.age = this.getInfo().base.age
            this.education = this.getInfo().base.education
            this.infos = this.getInfo().info
            let data: any = this.row.profile_labels || {}
            try {
                data = JSON.parse(this.row.profile_labels)
                const d = cloneDeep(data)
                forEach(d, (e, p) => {
                    data[p] = map(e, (a: any) => {
                        return {
                            tagGroupName: p,
                            tagName: a,
                        }
                    })
                })
            } catch {}
            const allTags = Object.values(data)
                .flat()
                .filter((item: any) => item?.tagName)
                .map((item: any) => item?.tagName)
            exportPdfService.pushItem({
                title: "基本信息",
                order: 1,
                data: [
                    { label: "姓名", value: this.row.name },
                    {
                        label: "年龄",
                        value: this.row.age || "--",
                    },
                    {
                        label: "性别",
                        value:
                            +this.row.sex === 1
                                ? "男"
                                : +this.row.sex === 2
                                ? "女"
                                : "--",
                    },
                    { label: "学历", value: this.row.education_label || "--" },
                    ...this.getInfo(true).info,
                    {
                        label: "标签",
                        value: allTags.join(",") || "--",
                    },
                ],
            })
        }

        private getInfo(unhidden?: boolean) {
            return {
                base: {
                    name: unhidden
                        ? this.row.name || "--"
                        : this.maskName(this.row.name) || "--",
                    gender:
                        +this.row.sex === 1
                            ? "男"
                            : +this.row.sex === 2
                            ? "女"
                            : "--",
                    age: this.row.age || "--",
                    education: this.row.education_label || "--",
                },
                info: [
                    {
                        label: "身份证号",
                        value: maskId(this.row.id_card) || "--",
                    },
                    {
                        label: "联系电话",
                        value: this.getMobile(this.row.mobile, unhidden),
                    },
                    {
                        label: "民族",
                        value: this.row.nation || "--",
                    },
                    {
                        label: "户籍性质",
                        value: this.row.registered_nature_label || "--",
                    },
                    {
                        label: "户籍地",
                        value:
                            getAddress(this.row, [
                                "household_province",
                                "household_city",
                                "household_area",
                                "household_countryside",
                                "household_village",
                            ]) || "--",
                    },
                    {
                        label: "常住地址",
                        value:
                            getAddress(this.row, [
                                "permanent_province",
                                "permanent_city",
                                "permanent_area",
                                "permanent_countryside",
                                "permanent_village",
                            ]) || "--",
                    },
                ],
            }
        }

        private getMobile(mobile: string, unhidden?: boolean) {
            if (!mobile) {
                return "--"
            }
            return unhidden ? mobile : take(mobile, 3).join("") + "********"
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 537px;
        height: 294px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;

        /deep/ .header-content {
            height: 40px;
            margin-bottom: 3px;
        }

        .info-box {
            background-image: url("@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/assets/base-company-detail-info.png");
            background-size: 100% 100%;
            width: 510px;
            height: 242px;
            padding: 9px 0 0 12px;
            opacity: 0.85;
            padding-right: 20px;
            padding-top: 0px;
            margin-left: 14px;

            .top-label {
                color: #ffffff;
                font-size: 16px;
                color: #ffffff;
                line-height: 19px;
                height: 38px;
                display: flex;
                align-items: center;
                gap: 14px;

                .name-label {
                    font-weight: 800;
                    font-size: 20px;
                    line-height: 23px;
                }

                .item-info {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }
            }

            .label {
                color: #ffffff;
                font-size: 16px;
                color: #ffffff;
                line-height: 19px;
            }

            .info-item {
                padding: 5px 14px;
                background: rgba(142, 191, 255, 0.2);
                margin-top: 4px;
                .point-box {
                    width: 8px;
                    height: 8px;
                    background: #6b99f6;
                    border-radius: 0px 0px 0px 0px;
                    margin-right: 14px;
                }
                .item-label {
                    width: 95px;
                }

                .item-value {
                    width: 320px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }
</style>
