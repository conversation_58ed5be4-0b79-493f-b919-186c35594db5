<template>
    <div class="h-100" :class="themeClassName">
        <v-header v-show="showHeader"></v-header>
        <Watermark
            v-if="watermark"
            :key="watermark"
            :value="true"
            class="container-body"
            :class="[routeCustomClass, { single: !showMenu }]"
            :text="watermark"
        >
            <v-menu
                ref="menu"
                v-if="menuList"
                v-show="showMenu"
                :menuList="menuList"
            />
            <div class="container-route" :class="{ single: !showMenu }">
                <tags-view v-show="showMenu && !hideTagView" />
                <el-scrollbar
                    id="pageMainContainer"
                    class="scrollbar overflow-x-hidden"
                >
                    <div class="router-view">
                        <router-view v-if="isDev" :key="$route.name" />
                        <keep-alive
                            v-else
                            :include="['RouteView', ...cachedViews]"
                        >
                            <router-view :key="$route.name" />
                        </keep-alive>
                    </div>
                </el-scrollbar>
            </div>
        </Watermark>
    </div>
</template>

<script lang="ts">
    import { config } from "@/config"
    import { UserInfo, userInfoService } from "@/core-ui/service/passport/user-info"
    import {
        currentAppRoute,
        currentRouteRelationConfig,
        RouteConfig,
    } from "@/router"
    import { routesMap } from "@/router/direction"
    import { userService } from "@/service/service-user"
    import { tagViewStore, TagViewStore } from "@/store/tag-view/model"
    import VMenu from "@/views/components/menu/index.vue"
    import { filter, find, get, slice } from "lodash"
    import { Component, Ref, Vue, Watch } from "vue-property-decorator"
    import VHeader from "./components/header.vue"
    import TagsView from "./components/tags-view/index.vue"
    import Watermark from "./components/watermark/index.vue"

    @Component({ components: { VHeader, VMenu, TagsView, Watermark } })
    export default class Index extends Vue {
        private menuList: RouteConfig[] | null = null
        private isDev = !config.isBuild
        private userInfo: UserInfo | null = null
        private label = ""
        @Ref("menu")
        private menuRef: any

        private get isInIframe() {
            // return true
            return self !== top || this.$route.query.iframe
        }

        private get showHeader() {
            return !this.isInIframe
        }

        private themeClassName = config.projectConfig.themeClass || ""

        @tagViewStore.State(TagViewStore.STATE_CACHED_VIEWS)
        private cachedViews!: TagViewStore.STATE_CACHED_VIEWS

        @tagViewStore.State(TagViewStore.STATE_VISITED_VIEWS)
        visitedViews!: TagViewStore.STATE_VISITED_VIEWS

        @Watch("visitedViews", { immediate: true })
        private onVisitedViewsChange() {
            sessionStorage.setItem(
                "visitedViews",
                JSON.stringify(
                    this.visitedViews.map((view) => ({
                        meta: view.meta,
                        query: view.query,
                        params: view.params,
                        path: view.path,
                        name: view.name,
                        title: view.title,
                        breadcrumb: view.breadcrumb,
                    }))
                )
            )
        }

        private get showMenu() {
            if (this.isInIframe && config.isOperate) {
                return false
            }

            return (
                !(this.$route as unknown as RouteConfig).meta?.single &&
                this.menuList?.length
            )
        }

        private get routeCustomClass() {
            return (this.$route as unknown as RouteConfig).meta?.customPageClassName
        }

        private get hideTagView() {
            return (this.$route as unknown as RouteConfig).meta?.hideTagView
        }

        get currentRoute() {
            return this.$route as RouteConfig
        }

        mounted() {
            userInfoService.get().then((r) => {
                this.userInfo = r
            })
            userService.setup().then((r) => {
                this.label = r?.label || ""
            })
        }

        @Watch("$route", { immediate: true })
        onRouteChange(cur: any, old: any) {
            if (this.$route.name === routesMap.home.page) {
                this.resetMenu(cur, old)
            }
            const matchedName = this.$route.matched[0].name!
            if (
                (this.menuList?.length || 0) < currentAppRoute.length &&
                find(this.menuList, { name: matchedName })
            ) {
                return
            }

            if (!currentRouteRelationConfig || !matchedName) {
                this.menuList = currentAppRoute
            } else {
                const c = currentRouteRelationConfig[matchedName]
                let rule = c?.rule
                if (!rule) {
                    rule = "all"
                }
                if (rule === "all") {
                    this.menuList = currentAppRoute
                } else {
                    this.menuList = filter(currentAppRoute, (item) => {
                        if (rule === "include") {
                            return c.route.includes(item.name!)
                        } else {
                            return !c.route.includes(item.name!)
                        }
                    })
                }
            }
        }

        private get watermark() {
            if (sessionStorage.getItem("enable-f12")) {
                return " "
            }
            if (!this.userInfo || !this.label) return ""
            return [
                this.userInfo?.realname,
                this.label,
                slice(this.userInfo?.mobile, -4).join(""),
            ]
                .filter(Boolean)
                .join(" ")
        }

        private menuReset = true
        private resetMenu(cur: any, old: any) {
            if (!config.isOperate) return
            console.log("reset", cur, old)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .container-body {
        height: calc(100% - @banner-height);
        display: flex;
        &.single {
            height: calc(100% - @banner-height);
        }

        &.home-page-cus-class {
            height: calc(100% - @banner-height);
            display: flex;

            .container-route {
                .scrollbar {
                    height: 100%;
                    width: 100%;
                    /deep/ & > .el-scrollbar__wrap {
                        height: 100%;
                        width: 100%;
                        overflow: hidden;

                        & > .el-scrollbar__view {
                            height: 100%;
                            width: 100%;

                            & > .router-view {
                                height: 100%;
                                width: 100%;
                            }
                        }
                    }
                }

                .router-view {
                    margin-left: 0px;
                    padding-bottom: 0px;
                    width: 100%;
                }
            }
        }
    }
    .tag-view {
        height: @tag-view-height;
        background: red;
    }

    .container-route {
        padding: 0 0px 0px 0px;
        background: #f6f7f9;
        width: calc(100% - @left-menu-width);
        &.single {
            width: 100%;
        }
        .scrollbar {
            height: calc(100% - @tag-view-height);
        }
        .router-view {
            margin-left: 30px;
            padding-bottom: 20px;
            width: calc(100% - 60px);
        }
    }
</style>

<style lang="scss">
    @import "@/css/theme/xg-project-org.scss";
    @import "@/css/theme/xg-project-operate.scss";
</style>
