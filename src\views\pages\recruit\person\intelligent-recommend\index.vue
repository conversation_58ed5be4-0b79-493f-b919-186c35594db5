<template>
    <div
        :key="refreshQueryParams"
        v-if="recommendRow"
        id="intelligent-recommend-container"
    >
        <div class="core-ui-custom-header" v-if="!row">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>

        <person-detail-view
            :row="recommendRow"
            v-if="!row"
        ></person-detail-view>
        <div class="u-flex u-row-right u-m-t-20">
            <el-button
                type="primary"
                v-if="items.length"
                plain
                @click="exportExcel"
            >
                导出
            </el-button>
            <el-button type="primary" plain @click="init()">
                重新匹配
            </el-button>
            <el-button
                v-if="items.length && !inPositionDetail"
                type="primary"
                class="u-m-l-30"
                @click="recommend()"
            >
                批量推荐
            </el-button>
        </div>
        <common-table
            class="u-m-t-20 u-p-20 bg-white"
            v-if="!loaded || items.length || inPositionDetail"
            :data="displayItems"
            :columns="getColumns()"
            @handleSelectionChange="handleSelectionChange"
        >
            <div slot="h" class="u-flex u-row-center" slot-scope="scope">
                <div class="handler-btn" @click="toDetail(scope.row)">详情</div>
                <div
                    class="handler-btn"
                    @click="recommend(scope.row)"
                    v-if="!inPositionDetail"
                >
                    推荐
                </div>
            </div>
        </common-table>

        <div class="u-flex u-row-center u-m-t-30" v-if="loaded && items.length">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-sizes="pageSizes"
                :current-page="page.index"
                :page-size="page.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="items.length"
            >
            </el-pagination>
        </div>
        <div
            v-if="loaded && !items.length && !inPositionDetail"
            class="u-text-center u-p-30 u-font-20"
        >
            当前无可推荐的人才，可去
            <span class="primary pointer" @click="toSeeker">
                注册居民列表
            </span>
            人工推荐
        </div>
    </div>
</template>

<script lang="ts">
    import CommonTable from "@/core-ui/component/common-table/index.vue"
    import { ExcelGenerator } from "@/core-ui/component/table/excel-generator"
    import { getTextFromVNode } from "@/core-ui/helpers/tools"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BatchPop } from "@/views/components/batch-pop"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import Breadcrumb from "@/views/components/breadcrumb/index.vue"
    import { pageLoading } from "@/views/controller"
    import {
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { MessageBox } from "element-ui"
    import { map } from "lodash"
    import { VNode } from "vue"
    import { Component, Mixins, Prop, Vue } from "vue-property-decorator"
    import { columns, columns2, Row } from "."
    import { predict, Row as RecommendRow } from ".."
    import PersonDetailView from "../components/detail-view.vue"

    @Component({
        name: routesMap.recruit.personIntelligentRecommendDetail,
        components: { CommonTable, Breadcrumb, PersonDetailView },
    })
    export default class IntelligentRecommend extends Mixins(Vue) {
        recommendId = ""
        isInDetail = true
        recommendRow: RecommendRow | null = null

        getColumns(isExp?: boolean) {
            return this.inPositionDetail ? columns2(isExp) : columns(isExp)
        }

        checkEdIds: Array<number | string> = []
        selected: { id: string; v: number }[] = []
        items: Row[] = []
        displayItems: Row[] = []
        breadcrumbs: BreadcrumbItem[] = []
        pageSizes = [10, 20, 50, 100]
        loaded = false
        page = {
            size: this.pageSizes[0],
            index: 1,
        }

        @Prop()
        private row?: RecommendRow

        @Prop({ default: true })
        private needReload?: boolean

        @Prop({ default: false })
        private inPositionDetail?: boolean

        private exportExcel() {
            const myColumns = this.getColumns(true).filter(
                (i) => !i.hide && i.prop !== "select" && i.prop !== "h"
            )
            const rows: Array<Array<number | string>> = []
            map(this.items, (i) => {
                const row: Array<number | string> = []
                map(myColumns, (c) => {
                    if (c.formatter) {
                        row.push(c.formatter(i, c))
                    } else if (c.render) {
                        const d = c.render(this.$createElement, i, c) as VNode
                        row.push(getTextFromVNode(d))
                    } else {
                        row.push((i as any)[c.prop])
                    }
                })
                rows.push(row)
            })
            ExcelGenerator.execute({
                primaryRows: [],
                columns: myColumns.map((i) => i.label),
                rows,
                fileName: `${this.recommendRow?.name}智能人才推荐导出`,
            })
        }

        setBreadcrumbs() {
            const d: BreadcrumbItem[] = [
                ...getCacheBreadcrumbsByRoutePath(this.$route.query.from + ""),
                {
                    label: "智能推荐",
                    to: {
                        name: routesMap.recruit.personIntelligentRecommendDetail,
                        query: {
                            id: this.recommendId + "",
                        },
                    },
                },
            ]
            updateTagItem({
                name: routesMap.recruit.personIntelligentRecommendDetail,
                breadcrumb: d,
            })
            this.breadcrumbs = d
        }

        protected handleSizeChange(size: number) {
            this.page.size = size
            this.handleDisplayItems()
        }

        public async handleCurrentChange(i: number) {
            this.page.index = i
            this.handleDisplayItems()
        }

        handleDisplayItems() {
            this.displayItems = this.items.slice(
                (this.page.index - 1) * this.page.size,
                this.page.index * this.page.size
            )
        }

        mounted() {
            this.recommendRow = this.row || null
            this.checkEdIds = []
            this.selected = []
            this.items = []
            this.init()
        }

        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.personIntelligentRecommendDetail,
        }

        async init() {
            this.checkEdIds = []
            this.selected = []
            this.items = []
            this.loaded = false
            this.recommendId = this.row?.id
                ? this.row.id + ""
                : this.$route.query.id + ""
            this.setBreadcrumbs()
            if (!this.row) {
                await this.getDetail()
            } else {
                this.recommendRow = this.row
            }
            return pageLoading(this.getList, {
                target: "#intelligent-recommend-container",
            })
        }

        getDetail() {
            return sdk.core
                .model("xg_company_position_recommend")
                .detail(this.recommendId, "manage_apply_detail")
                .query()
                .then((r) => {
                    this.recommendRow = sdk.buildRow(r.row, predict)
                })
        }

        getList() {
            return sdk.core
                .domainService(
                    "xg_project",
                    "back_api",
                    this.inPositionDetail
                        ? "recommend_profile_list_all"
                        : "recommend_profile_list"
                )
                .post<Row[]>({
                    position_id: this.inPositionDetail
                        ? this.row?.id
                        : this.recommendRow!.position_id,
                    reload: this.needReload ? 1 : 0,
                })
                .then((r) => {
                    this.loaded = true
                    this.items = r
                    this.page.index = 1
                    this.handleDisplayItems()
                })
        }

        private handleSelectionChange(d: { ids: string[]; rows: Row[] }) {
            this.checkEdIds = d.rows.map((e) => e.profile_id)
            this.selected = d.rows.map((i) => ({ id: i.profile_id, v: 0 }))
        }

        private recommend(row?: Row) {
            const selected_list = row
                ? [{ id: row.profile_id, v: 0 }]
                : this.selected
            if (!selected_list.length) {
                this.$message.error("请选择数据")
            }
            MessageBox.confirm(
                selected_list.length > 1
                    ? "确定推荐这些人才？"
                    : "确定推荐该人才？",
                "提示",
                {
                    beforeClose: (action, instance, done) => {
                        if (action === "confirm") {
                            instance.confirmButtonLoading = true
                            this.doHandelRecommend(selected_list)
                                .then(() => {
                                    done()
                                    map(this.items, (i) => {
                                        if (i.profile_id === row?.profile_id) {
                                            i.is_recommend = 1
                                        }
                                    })
                                })
                                .finally(() => {
                                    instance.confirmButtonLoading = false
                                })
                        } else {
                            done()
                        }
                    },
                }
            )
        }

        private doHandelRecommend(selected_list?: { id: string; v: number }[]) {
            let errorList: any = []
            const model = sdk.core
                .model("user_profile_basic")
                .action("recommend_profile")

            return model
                .updateInitialParams({
                    selected_list,
                })
                .query()
                .then(() => {
                    model
                        .executeEach({
                            inputs_parameters: [
                                {
                                    property: "recommend_id",
                                    value: this.recommendRow!.id,
                                },
                            ],
                            selected_list,
                        } as any)(
                            () => {},
                            (errorRow) => {
                                errorList = errorRow
                            }
                        )
                        .awaiting.then(() => {
                            this.callRefresh(routesMap.recruit.person)
                            this.callRefresh(routesMap.recruit.personDetail)

                            BatchPop.use({
                                list: selected_list as any[],
                                errorList,
                                successMessage: "推荐成功",
                                columns: [
                                    {
                                        label: "id",
                                        prop: "id",
                                    },
                                    {
                                        label: "姓名",
                                        prop: "name",
                                    },
                                    {
                                        label: "手机号",
                                        prop: "mobile",
                                    },
                                    {
                                        label: "错误原因",
                                        prop: "error",
                                        width: "120px",
                                        showOverflowTip: true,
                                    },
                                ],
                            })
                        })
                })
        }

        private toDetail(row: Row) {
            this.$router.push({
                name: routesMap.labourManage.seekerDetail,
                query: {
                    id: row.access_key || row.profile_id + "",
                },
            })
        }

        private toSeeker() {
            this.$router.push({
                name: routesMap.labourManage.userAccount,
            })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
</style>
