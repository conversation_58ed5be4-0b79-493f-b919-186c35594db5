<template>
    <Card label="招聘会基本情况">
        <div class="home1 left">
            <div class="home1-left-content">
                <div
                    class="home1-item"
                    v-for="(item, index) in items"
                    :key="index"
                    :class="{
                        separated: index !== items.length - 1,
                    }"
                >
                    <div class="icon"></div>
                    <div class="u-m-r-15">
                        {{ item.label }}
                    </div>
                    <scroll-number
                        :value="item.value"
                        class="text3 home-info-value"
                        :dot="false"
                    />
                </div>
            </div>
        </div>
    </Card>
</template>

<script lang="ts">
    import { Component, Prop, Vue, Watch } from "vue-property-decorator"
    import Card from "../../common/card.vue"
    import ScrollNumber from "@/views/pages/big-screen/common/number-scroll/scroll-number.vue"

    @Component({ components: { Card, ScrollNumber } })
    export default class Template extends Vue {
        @Prop({ default: {} })
        private info!: any

        @Watch("info", { immediate: true })
        private onInfoChange() {
            if (!this.info) {
                return
            }
            this.items = this.buildInfo()
        }

        private items = this.buildInfo()

        private buildInfo() {
            return [
                {
                    label: "参会企业数量",
                    value: this.info["参会企业数量"] || 0,
                },
                {
                    label: "参会岗位数量",
                    value: this.info["参会岗位数量"] || 0,
                },
                {
                    label: "岗位类型数量",
                    value: this.info["岗位类型数量"] || 0,
                },
            ]
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .card {
        width: 1838px;
        height: 165px;
    }

    .home1 {
        position: relative;
        width: 100%;
        height: 100px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 20px 20px 20px 20px;
        border: 2px solid #207fd1;
        overflow: hidden;

        .label {
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24px;
            text-align: left;
            white-space: nowrap;
            line-height: 24px;
            font-weight: 700;
            position: absolute;
            top: -45px;
        }

        .home1-left-content {
            display: flex;
            align-items: center;
            width: 100%;
            position: relative;
            z-index: 9;

            .home1-item {
                width: 33%;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                color: #ffffff;
                line-height: 16px;
                word-break: keep-all;

                .icon {
                    width: 50px;
                    height: 50px;
                    background-size: 100% 100%;
                    margin-right: 5px;
                }

                &:nth-child(1) {
                    .icon {
                        background-image: url("../../assets/home1-icon1.png");
                    }
                }

                &:nth-child(2) {
                    .icon {
                        background-image: url("../../assets/home1-icon2.png");
                    }
                }

                &:nth-child(3) {
                    .icon {
                        background-image: url("../../assets/home1-icon3.png");
                    }
                }
            }
        }
    }

    .text3 {
        color: #fdc850;
        font-weight: bold;
        line-height: 38px;
        text-align: center;
        height: 38px;
    }

    .home-info-value {
        position: relative;
        top: -3px;
    }
</style>
