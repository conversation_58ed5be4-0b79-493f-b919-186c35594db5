<template>
    <div class="table-box">
        <div class="line title">
            <div class="row" v-for="(item, index) in titles" :key="index">
                {{ item }}
            </div>
        </div>
        <div class="line-box">
            <div class="line" v-for="(items, idx) in data" :key="idx">
                <div
                    class="row text-truncate"
                    v-for="(item, i) in items"
                    :key="i"
                >
                    <span
                        class="data"
                        :class="{
                            pointer: checkIsCanClick(i, item),
                        }"
                        @click="onClick(titles[i], items[0], i, idx)"
                        :title="item"
                        >{{ item }}</span
                    >
                </div>
            </div>

            <div class="empty-box" v-if="!data.length && !loading">
                <div class="empty-img"></div>
                <div>暂无数据</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from "vue-property-decorator"

    @Component({ components: {} })
    export default class Template extends Vue {
        @Prop({ default: () => [] })
        private titles!: string[]

        @Prop({ default: () => [] })
        private data!: string[][]

        @Prop({ default: () => [] })
        private disabledClickItems!: string[]

        @Prop({ default: () => [] })
        private canClickItems!: string[]

        @Prop({ default: false })
        private defaultDisableCanClick!: boolean

        @Prop({ default: false })
        private isEmpty!: boolean

        @Prop({ default: false })
        private loading!: boolean

        private checkIsCanClick(i: number, item: string) {
            const t = this.titles[i]
            if (item === "--") {
                return false
            }
            if (this.canClickItems.includes(t)) {
                return true
            }
            if (this.defaultDisableCanClick) {
                return false
            }
            if (this.disabledClickItems.includes(t)) {
                return false
            }
            return true
        }

        private onClick(title: string, item: string, i: number, idx: number) {
            if (!this.checkIsCanClick(i, item)) {
                return
            }
            this.$emit("click", title, item, idx)
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";

    .table-box {
        padding-left: 20px;
        padding-right: 20px;
    }

    .line-box {
        overflow-y: auto;
        height: 180px;
    }

    .line-box::-webkit-scrollbar {
        scrollbar-width: none;
        display: none; /* 对于 Chrome, Safari 和 Opera */
    }

    .line {
        display: flex;
        height: 40px;
        border-bottom: 1px solid #003dbf;

        // &:last-child {
        //     border-bottom: 0;
        // }

        &.title {
            height: 40px;
            background: linear-gradient(
                90deg,
                #003dc7 0%,
                #185fff 50%,
                #003dc7 100%
            );
            border-radius: 4px 4px 4px 4px;
            background-size: 100% 100%;

            .row {
                color: rgba(255, 255, 255, 0.9);
                // width: 25%;
            }
        }

        .row {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.95);
            line-height: 40px;
            // width: 25%;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
            text-align: center;
            .data {
                &.pointer {
                    cursor: pointer;
                    text-decoration: underline;
                }
                // border-bottom: 1px solid #fff;
            }
        }
    }

    .empty-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;

        font-size: 14px;
        color: #74acff;
        line-height: 16px;

        position: relative;
        top: 50%;
        transform: translateY(-50%);

        .empty-img {
            width: 118px;
            height: 104px;
            background-image: url("./../assets/empty.png");
            background-size: 100% 100%;
        }
    }
</style>
