const defaultDomainAllInOne = {
    VUE_APP_UNIPLAT: "/api",
    VUE_APP_UNIPLAT_WEB: "/uniplat",
    VUE_APP_H5: "/h5",
    VUE_APP_ENTERPRISE_URL: "/org",
    VUE_APP_HR_URL: "/hr",
    VUE_APP_OPERATE_URL: "/platform",
}

const commonEnv = {
    VUE_APP_CITY_SAMPLE_NAME: "宜都",
    VUE_APP_CITY_SAMPLE_NAME2: "宜都市",
    VUE_APP_DEFAULT_REGION_CODE: "420581000000",
    VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
    VUE_APP_DEFAULT_REGION_NAME: "宜都市",
    VUE_APP_BM_AUTO_COMPLETE_LOCATION: "宜都市人民政府",
    VUE_APP_BAIDU_KEY: "htAMWewLzft00KzzTp0S9osnmPQLbXb6",
    VUE_APP_LOGIN_AES_ENCRYPT_KEY: "LYNBSP96NG248ZHE",
    VUE_APP_requestEncoder: "", // 入参加密 aes
    VUE_APP_responseEncoder: "", // 返回参加密 aes
    // 加密文件地址
    VUE_APP_AES_ENCRYPT_KEY_PATH: "./uniplat-key-yidu",
    VUE_APP_BIGSCREEN_BI_PATH: "/bigScreen",
}

const { config } = require("./yidu_p.js")
const jzConfig = require("./jz.js")
module.exports = {
    name: "宜都项目",
    env: {
        test: {
            ...jzConfig.env.test,
            VUE_APP_CITY_SAMPLE_NAME: "宜都",
            VUE_APP_CITY_SAMPLE_NAME2: "宜都市",
            VUE_APP_DEFAULT_REGION_CODE: "429005000000",
            VUE_APP_DEFAULT_REGION_CODE_MAX_LEVEL: 5,
            VUE_APP_DEFAULT_REGION_NAME: "宜都市",
            VUE_APP_BM_AUTO_COMPLETE_LOCATION: "宜都市人民政府",
        },
        pro: {
            ...commonEnv,
            ...defaultDomainAllInOne,
            VUE_APP_requestEncoder: "aes", // 入参加密 aes
            VUE_APP_responseEncoder: "aes", // 返回参加密 aes
        },
    },
    app: [
        // 局方端
        {
            name: "局方端",
            env: {
                VUE_APP_UNIPLAT_ENTRANCE: "智慧就业服务工作台",
                VUE_APP_HEADER_TITLE: "就业管理工作台",
                VUE_APP_APP_TITLE: "智慧就业服务工作台",
                BASE_URL: "platform/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["局方端"],
                VUE_APP_APP_NAME: "xg_project_operate",
            },
            deploy: {
                test: {
                    path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/platform-yidu",
                    host: "88",
                    env: {
                        BASE_URL: "platform-yidu/",
                        VUE_APP_OPERATE_URL: "/platform-yidu",
                        VUE_APP_UNIPLAT_ENTRANCE: "荆州市智慧就业服务工作台",
                        VUE_APP_HEADER_TITLE: "就业管理工作台",
                        VUE_APP_APP_TITLE: "智慧就业服务工作台",
                    },
                },
                pro: {
                    env: {
                        VUE_APP_UNIPLAT: "https://yidusc.beikesmart.com/api",
                    }, // 宜都环境配置
                },
            },
        }, // 企业端
        {
            name: "企业端",
            env: {
                VUE_APP_HEADER_TITLE: "企业智慧就业服务平台",
                VUE_APP_UNIPLAT_ENTRANCE: "企业智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org",
                BASE_URL: "org/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["企业端"],
            },
            deploy: {
                // test: {
                //     path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/org",
                //     host: "88",
                //     env: {},
                // },
                pro: {
                    env: {
                        VUE_APP_UNIPLAT: "https://yidusc.beikesmart.com/api",
                    }, // 宜都环境配置
                },
            },
        },
        // 机构端
        {
            name: "机构端",
            env: {
                VUE_APP_HEADER_TITLE: "人资机构就业服务平台",
                VUE_APP_UNIPLAT_ENTRANCE: "人力资源机构智慧就业服务平台",
                VUE_APP_APP_NAME: "xg_project_org_hr",
                BASE_URL: "hr/",
                VUE_APP_CUS_PROJECT_STYLE_CONFIG: config["机构端"],
            },
            deploy: {
                // test: {
                //     path: "/mnt/sdb/jingzhou/web-projects-all-in-one-domain/hr",
                //     host: "88",
                //     env: {},
                // },
                pro: {
                    env: {
                        VUE_APP_UNIPLAT: "https://yidusc.beikesmart.com/api",
                    }, // 宜都环境配置
                },
            },
        },
    ],
}
