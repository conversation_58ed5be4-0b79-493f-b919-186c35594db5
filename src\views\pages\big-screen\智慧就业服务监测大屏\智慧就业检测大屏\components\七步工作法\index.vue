<template>
    <div class="content">
        <Header :title="title"></Header>
        <Box :title="title"></Box>
    </div>
</template>

<script lang="ts">
    import { Component } from "vue-property-decorator"
    import Header from "../../common/header.vue"
    import BaseItem from "@/views/pages/big-screen/智慧就业服务监测大屏/智慧就业检测大屏/common/base-item"
    import Box from "./box.vue"

    @Component({ components: { Header, Box } })
    export default class Template extends BaseItem {
        private title = "七步工作法"

        protected refresh() {
            // this.query<ChartQueryResultItem[]>(
            //     `labor_count`,
            //     "dashboard_xg_data"
            // ).then((r) => {
            //     const obj = {
            //         TotalLaborForce: this.getV(r, "劳动力总数"),
            //         RegisteredUsersCount: this.getV(r, "已注册人数"),
            //         TotalServiceVisits: this.getV(r, "已服务人次"),
            //     }
            // })
        }
    }
</script>

<style lang="less" scoped>
    @import "~@/css/variables.less";
    .content {
        width: 1246px;
        height: 294px;
        background: rgba(1, 25, 155, 0.7);
        border-radius: 0px 0px 20px 20px;
    }
    /deep/.header-content {
        background-image: url("../../../assets/智慧就业监测/title-l.png");
    }
</style>
