<template>
    <div class="core-ui-table-container container" :key="refreshQueryParams">
        <div class="core-ui-custom-header">
            <div class="title">
                <bread-crumb :backRoute="true" :items="breadcrumbs" />
            </div>
        </div>
        <div class="u-p-x-20 create-position u-flex u-row-center u-p-y-30">
            <div class="content">
                <div v-loading="true" v-if="loading" class="loading"></div>
                <form-builder
                    ref="formBuilder"
                    labelWidth="120px"
                    :onValueChange="onValueChange"
                ></form-builder>
                <div class="u-flex u-m-t-20 u-row-center">
                    <el-button
                        type="primary"
                        @click="close()"
                        plain
                        class="custom-btn btn u-m-r-30"
                    >
                        取消
                    </el-button>
                    <el-button
                        type="primary"
                        @click="confirm()"
                        class="custom-btn btn u-m-r-30"
                    >
                        保存至草稿状态
                    </el-button>
                    <el-button
                        type="primary"
                        @click="submitValidate"
                        class="custom-btn btn u-m-0"
                    >
                        提交审核
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts'>
    import { buildFormSections, FormController } from "@/core-ui/component/form"
    import FormBuilder from "@/core-ui/component/form/index.vue"
    import { routesMap } from "@/router/direction"
    import { sdk } from "@/service"
    import { BreadcrumbItem } from "@/views/components/breadcrumb"
    import { pageLoading } from "@/views/controller"
    import {
        closeCurrentTap,
        getCacheBreadcrumbsByRoutePath,
        updateTagItem,
    } from "@/views/pages/single-page/components/tags-view"
    import { find, forEach, get, split } from "lodash"
    import { TagManagerTypes } from "uniplat-sdk"
    import { Component } from "vue-property-decorator"
    import {
        age,
        createPositionFormSections,
        experience,
    } from "./components/detail"

    @Component({ name: routesMap.recruit.jobEdit, components: { FormBuilder } })
    export default class JobEdit extends FormController {
        refreshConfig = {
            fun: this.init,
            name: routesMap.recruit.jobEdit,
        }

        breadcrumbs: BreadcrumbItem[] = []

        copyId = ""

        setBreadcrumbs() {
            let d: BreadcrumbItem[] = [
                {
                    label: `招聘岗位列表`,
                    to: {
                        name: routesMap.recruit.job,
                    },
                },
                {
                    label: "创建岗位",
                },
            ]
            if (this.id || this.copyId) {
                d = [
                    ...getCacheBreadcrumbsByRoutePath(this.from),
                    {
                        label: this.id ? "编辑岗位" : "创建岗位",
                    },
                ]
            }
            updateTagItem({
                name: routesMap.recruit.jobEdit,
                breadcrumb: d,
                title: this.id ? "编辑岗位" : "创建岗位",
            })
            this.breadcrumbs = d
        }

        get from() {
            return this.$route.query.from as string
        }

        get isAudit() {
            return this.$route.query.isAudit || "0"
        }

        mounted() {
            this.init()
        }

        private id = ""
        private loading = true

        private init() {
            this.id = (this.$route.query.id as string) || ""
            this.copyId = (this.$route.query.copyId as string) || ""
            this.setBreadcrumbs()
            return buildFormSections(
                createPositionFormSections(
                    this.id || this.copyId,
                    +this.isAudit,
                    this.copyId
                )
            ).then((r) => {
                age.min = r.data.age_require_min
                age.max = r.data.age_require_max
                experience.max = r.data.experience_max
                experience.min = r.data.experience_min
                const salaryEditField = find(r.forms, {
                    prop: "salaryEditField",
                })
                if (salaryEditField) {
                    const data: any = {}
                    const subForms: any = {}
                    ;["salary_min", "salary_max", "salary"].forEach((i) => {
                        const d = find(r.forms, { prop: i })
                        data[i] = r.data[i]
                        subForms[i] = d
                    })
                    salaryEditField.extendData = {
                        subForms,
                    }
                    salaryEditField.defaultValue = data
                }
                const addressEditFiled = find(r.forms, {
                    prop: "addressEditFiled",
                })
                if (addressEditFiled) {
                    const data: any = {}
                    const subForms: any = {}
                    ;["address_detail", "lat", "lng"].forEach((i) => {
                        const d = find(r.forms, { prop: i })
                        data[i] = r.data[i]
                        subForms[i] = d
                    })
                    addressEditFiled.extendData = {
                        subForms,
                    }
                    addressEditFiled.defaultValue = data
                }
                this.loading = false
                console.log("r====", JSON.parse(JSON.stringify(r.forms)))
                this.buildForm(r.forms)
            })
        }

        private onValueChange(prop: string) {
            if (["salary_min", "salary_max"].includes(prop)) {
                this.clearValidate(["salary_min", "salary_max"])
            }
        }

        private confirm(audit?: boolean) {
            const data = this.getFormValues()
            this.validateForm((v: boolean) => {
                if (v) {
                    this.submit({
                        ...data,
                        ...data.salaryEditField,
                        ...data.addressEditFiled,
                        salary_min: +data.salaryEditField.salary_min,
                        salary_max: +data.salaryEditField.salary_max,
                        age_require_max: +data.age_require_max,
                        age_require_min: +data.age_require_min,
                        experience_max: +data.experience_max,
                        experience_min: +data.experience_min,
                        is_submit_draft: audit ? "0" : "1",
                    })
                }
            })
        }

        private submitValidate() {
            this.confirm(true)
        }

        private close(name?: string) {
            closeCurrentTap({
                name:
                    name ||
                    (this.from === routesMap.recruit.jobDetail
                        ? routesMap.recruit.jobDetail
                        : routesMap.recruit.job),
                query: this.id
                    ? {
                          id: this.id,
                      }
                    : undefined,
            })
        }

        private submit(data: any) {
            const tagFormItems = this.formBuilder?.forms.filter((i) => i.useTag)

            const tags: TagManagerTypes.TagInfo[] = []
            forEach(tagFormItems, (item) => {
                forEach(split(data[item.useTag!], ","), (selectTag) => {
                    const sourceTagConfig = get(
                        item,
                        "sourceInputsParameter.ext_properties.extendData"
                    ) as TagManagerTypes.TagGroup
                    const currentTag = find(sourceTagConfig?.tags, {
                        tagName: selectTag,
                    })
                    if (currentTag) {
                        tags.push(currentTag)
                    }
                })
            })
            pageLoading(() => {
                return sdk.core
                    .model("xg_company_position")
                    .action(this.id ? "update_position" : "create_position")
                    .addInputs_parameter(data)
                    .updateInitialParams({
                        selected_list: this.id
                            ? [
                                  {
                                      id: this.id,
                                      v: 0,
                                  },
                              ]
                            : [],
                    })
                    .execute({
                        tagInfos: [{ behavior: "update", tags: tags }],
                    })
                    .then(() => {
                        this.$message.success(this.id ? "编辑成功" : "创建成功")
                        this.callRefresh(routesMap.recruit.jobDetail)
                        this.callRefresh(routesMap.recruit.job)
                        if (this.copyId) {
                            return this.close(routesMap.recruit.job)
                        }
                        this.close()
                    })
            })
        }
    }
</script>

<style lang='scss' scoped>
    @import "~@/css/variables.less";
    .label {
        margin-right: 10px;
        color: #555;
        font-size: 14px;
    }
    .loading {
        height: 400px;
    }
    .select {
        width: 320px;
    }
    .btn {
        min-width: 100px !important;
        padding: 0px 20px !important;
        height: 36px;
    }
    .content {
        padding: 60px;
        padding-top: 30px !important;
    }
    .create-position {
        background: #fff;
        ::v-deep .el-form-item__label {
            text-align: left;
            color: #555;
            font-size: 14px;
        }
    }
</style>
