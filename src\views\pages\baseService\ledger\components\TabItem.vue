<template >
    <div>
        <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            size="small"
            style="width: 100%"
        >
            <el-table-column
                v-for="column in columns"
                :key="column.prop"
                :label="column.label"
                :prop="column.prop"
                align="center"
            >
                <template slot-scope="scope">
                    <span v-if="column.formatter">
                        {{ formatColumnValue(scope.row, column) }}
                    </span>
                    <span v-else>{{ scope.row[column.prop] }}</span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            style="margin-top: 20px; text-align: center"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </div>
</template>

<script lang="ts">
    import { sdk } from "@/service"
    import { Component, Vue, Prop } from "vue-property-decorator"

    @Component
    export default class TabItem extends Vue {
        @Prop({ type: String, required: true })
        private name!: string

        @Prop({ type: Array, required: true })
        private columns!: any[]

        @Prop({ type: String, required: true })
        private region_code!: string

        @Prop({ type: String, required: true })
        private uniplat_uid!: string

        @Prop({ type: Boolean, required: false })
        private model_name!: boolean

        @Prop({ type: String, required: false })
        private id!: string

        private tableData: any[] = []
        private total = 0
        private page = 1
        private page_size = 10
        private loading = false

        // 格式化列值的方法
        private formatColumnValue(row: any, column: any) {
            const value = row[column.prop]
            // 处理JSON字符串
            if (column.formatter === "json") {
                try {
                    const jsonObj =
                        typeof value === "string" ? JSON.parse(value) : value
                    // 如果指定了jsonKey，则返回该字段值，否则返回JSON字符串化后的对象
                    return column.jsonKey
                        ? jsonObj[column.jsonKey] ?? "--"
                        : JSON.stringify(jsonObj)
                } catch (e) {
                    return value ?? "--"
                }
            }
            // 自定义格式化函数
            if (typeof column.formatter === "function") {
                return column.formatter(row, column)
            }
            return value ?? "--"
        }

        private fetchData() {
            const params: {
                item_size: number
                item_index: number
                region_code?: string
                uniplat_uid?: string
                model_name?: string
                id?: string
            } = {
                item_size: this.page_size,
                item_index: this.page,
            }

            if (this.region_code) {
                params.region_code = this.region_code
            }
            // 非社群
            if (this.uniplat_uid && this.name !== "get_grid_group_chat_members") {
                params.uniplat_uid = this.uniplat_uid
            }

            if (this.model_name) {
                params.model_name = this.name
            }
            // 社群
            if (this.name === "get_grid_group_chat_members") {
                params.id = this.id
            }

            this.loading = true
            sdk.core
                .domainService(
                    "xg_project",
                    "g_service_register",
                    this.model_name ? "get_grid_recommend_records" : this.name
                )
                .post(params)
                .then((res: any) => {
                    this.tableData = res.list || []
                    this.total = Number(res.total) || 0
                })
                .finally(() => {
                    this.loading = false
                })
        }

        mounted() {
            this.fetchData()
        }

        private handleSizeChange(val: number) {
            this.page_size = val
            this.page = 1
            this.fetchData()
        }

        private handleCurrentChange(val: number) {
            this.page = val
            this.fetchData()
        }
    }
</script>

<style lang="less" scoped>
    @import "../style/custom-table.less";
</style>
